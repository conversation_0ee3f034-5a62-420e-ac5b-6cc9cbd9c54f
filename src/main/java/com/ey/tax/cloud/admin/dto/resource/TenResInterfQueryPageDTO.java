package com.ey.tax.cloud.admin.dto.resource;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2023-09-07 12:24:58
 * 
 */
@Data
@Schema(description = "根据条件分页查询实体")
public class TenResInterfQueryPageDTO {
    /**
     * 资源id COLUMN:resource_id
     */
    @Schema(description = "资源id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String resourceId;

    /**
     * 接口id COLUMN:interface_id
     */
    @Schema(description = "接口id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String interfaceId;
}