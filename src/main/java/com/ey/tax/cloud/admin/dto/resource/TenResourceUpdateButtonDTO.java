package com.ey.tax.cloud.admin.dto.resource;

import com.ey.tax.cloud.admin.controller.validate.UpdateButtonGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2023-08-18 15:21:14
 *
 */
@Data
@Schema(description = "更新按钮实体")
public class TenResourceUpdateButtonDTO {

    /**
     * id COLUMN:id
     */
    @Schema(description = "按钮id")
    @NotBlank(message = "{TenResourceUpdateButtonDTO.id.NotBlank}", groups = {UpdateButtonGroup.class})
    private String id;

    /**
     * 父节点 COLUMN:parent_id
     */
    @Schema(description = "父节点", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String parentId;

    /**
     * 资源名称 COLUMN:resource_name
     */
    @Schema(description = "资源名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String resourceName;

}
