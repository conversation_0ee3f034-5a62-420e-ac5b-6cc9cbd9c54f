package com.ey.tax.cloud.admin.dto.inter;

import com.ey.cn.tax.framework.validator.QueryByIdGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2023-09-07 10:00:51
 * 
 */
@Data
@Schema(description = "根据id查询实体")
public class TenInterfaceQueryByIdDTO {
    
    /**
     * 数据id
     */
    @Schema(description = "数据id")
    @NotBlank(message = "{TenInterfaceQueryByIdDTO.id.NotBlank}", groups = {QueryByIdGroup.class})
    private String id;
}