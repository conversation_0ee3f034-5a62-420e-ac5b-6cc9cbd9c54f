package com.ey.tax.cloud.admin.controller.tenant;

import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.dto.SearchDTO;
import com.ey.cn.tax.framework.validator.*;
import com.ey.cn.tax.framework.web.annotation.EyPostMapping;
import com.ey.cn.tax.framework.web.base.BaseController;
import com.ey.tax.cloud.admin.controller.validate.AdminGroup;
import com.ey.tax.cloud.admin.controller.validate.BindAdminGroup;
import com.ey.tax.cloud.admin.dto.tenant.TenantDTO;
import com.ey.tax.cloud.admin.dto.tenant.UserTenantAddAdminDTO;
import com.ey.tax.cloud.admin.dto.tenant.UserTenantAdminDTO;
import com.ey.tax.cloud.admin.dto.tenant.UserTenantEnableDTO;
import com.ey.tax.cloud.tenant.dto.user.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2023-08-24 11:54:37
 *
 */
@Tag(name="租户用户")
public interface UserTenantController extends BaseController<UserTenantDTO> {

    @Operation(summary = "启用禁用", description="1启用，0禁用")
    @EyPostMapping(value = "/enableUser.do")
    ResponseDTO<Void> enableUser(@RequestBody @Validated({EnableGroup.class}) UserTenantEnableDTO dto);

    @Operation(summary = "设置取消超管", description="1设置，0取消")
    @EyPostMapping(value = "/adminUser.do")
    ResponseDTO<Void> adminUser(@RequestBody @Validated({AdminGroup.class}) UserTenantAdminDTO dto);

    @Operation(summary = "添加超管", description="添加超级管理员")
    @EyPostMapping(value = "/addAdminUser.do")
    ResponseDTO<Void> addAdminUser(@RequestBody @Validated({BindAdminGroup.class}) UserTenantAddAdminDTO dto);

    /**
     * Page TenUser from http
     */
    @Operation(summary = "分页查询", description = "根据条件分页查询")
    @EyPostMapping(value = "/queryPage.do")
    ResponseDTO<SearchDTO<UserTenantRepDTO>> queryPage(@RequestBody @Validated({QueryPageGroup.class}) SearchDTO<UserTenantQueryPageDTO> searchDTO);

    /**
     * 查询用户字典
     */
    @Operation(summary = "查询用户字典", description = "根据用户字典用于构建下拉菜单")
    @EyPostMapping(value = "/queryDictionary.do")
    ResponseDTO<List<TenUserRepDictDTO>> queryDictionary(@RequestBody TenUserQueryDictDTO tenantDTO);

}
