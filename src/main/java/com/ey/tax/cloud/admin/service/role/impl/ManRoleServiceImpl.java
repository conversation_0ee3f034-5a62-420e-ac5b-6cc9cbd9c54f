package com.ey.tax.cloud.admin.service.role.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.ey.cn.tax.framework.entity.Search;
import com.ey.cn.tax.framework.mybatisplus.extension.service.AbstractService;
import com.ey.cn.tax.framework.mybatisplus.utils.PageUtils;
import com.ey.cn.tax.framework.utils.EmptyUtils;
import com.ey.tax.cloud.admin.constants.ErrorCodeConstans;
import com.ey.tax.cloud.admin.entity.business.Bussiness;
import com.ey.tax.cloud.admin.entity.resource.ManResInterf;
import com.ey.tax.cloud.admin.entity.resource.ManResource;
import com.ey.tax.cloud.admin.entity.role.ManRole;
import com.ey.tax.cloud.admin.entity.role.ManRoleRes;
import com.ey.tax.cloud.admin.mapper.role.ManRoleMapper;
import com.ey.tax.cloud.admin.service.resource.ManResInterfService;
import com.ey.tax.cloud.admin.service.resource.ManResourceService;
import com.ey.tax.cloud.admin.service.role.ManRoleResService;
import com.ey.tax.cloud.admin.service.role.ManRoleService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * @date 2023-08-18 15:21:15
 */
@Service
public class ManRoleServiceImpl extends AbstractService<ManRoleMapper, ManRole> implements ManRoleService {

    private static final String REGEX = "^[\\u4E00-\\u9FA5A-Za-z0-9_]+$";

    @Autowired
    private ManRoleResService manRoleResService;

    @Autowired
    private ManResourceService manResourceService;

    /**
     * 角色绑定资源
     * <p>
     * 1.先删除所有绑定关系
     * 2.添加新的绑定关系
     *
     * @param manRole
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void bindResource(ManRole manRole) {
        ManRoleRes dManRoleRes = new ManRoleRes();
        dManRoleRes.setRoleId(manRole.getId());
        manRoleResService.deleteByPara(dManRoleRes);
        List<ManRoleRes> manRoleRess = new ArrayList<>();
        for (String resourceId : manRole.getResourceIds()) {
            ManRoleRes manRoleRes = new ManRoleRes();
            manRoleRes.setResourceId(resourceId);
            manRoleRes.setRoleId(manRole.getId());
            manRoleRess.add(manRoleRes);
        }
        manRoleResService.save(manRoleRess);
        this.update(manRole);
    }

    /**
     * query page by para
     */
    @Transactional(readOnly = true)
    @Override
    public Search<ManRole> queryPageByParaFuzzy(Search<ManRole> search) {
        logger.debug("queryPageByPara {}:{}", getEntitySimpleName(), search);
        QueryWrapper<ManRole> queryWrapper = Wrappers.query(search.getQueryParams());
        queryWrapper.orderByDesc("create_time");
        String roleName = search.getQueryParams().getRoleName();
        search.getQueryParams().setRoleName(null);
        if (EmptyUtils.isNotEmpty(roleName)) {
            queryWrapper.like("role_name", roleName);
        }
        IPage<ManRole> iPage = baseMapper.selectPage(PageUtils.convertPageToIPage(search.getPage()), queryWrapper);
        List<ManRole> records = iPage.getRecords();
        search.setEntities(records);
        if (CollectionUtils.isEmpty(records)) {
            search.getPage().setTotalCount(0L);
        } else {
            processUserName(records);
            search.getPage().setTotalCount(iPage.getTotal());
        }
        return search;
    }

    @Override
    public List<ManRole> queryByUserId(String userId) {
        return this.getBaseMapper().selectByUserId(userId);
    }

    /**
     * query by id
     */
    @Transactional(readOnly = true)
    @Override
    public ManRole queryById(Serializable id) {
        logger.debug("queryById {}:{}", getEntitySimpleName(), id);
        ManRole t = baseMapper.selectById(id);
        processUserName(t);
        //获取关联的资源
        ManRoleRes qManRoleRes = new ManRoleRes();
        qManRoleRes.setRoleId(id.toString());
        List<ManRoleRes> manRoleRess = manRoleResService.queryByPara(qManRoleRes);
        List<String> resourceIds = new ArrayList<>();
        if(EmptyUtils.isNotEmpty(manRoleRess)){
            for(ManRoleRes manRoleRes:manRoleRess){
                resourceIds.add(manRoleRes.getResourceId());
            }
            List<ManResource> manResources = manResourceService.queryByIds(resourceIds);
            if(EmptyUtils.isNotEmpty(manResources)){
                Map<String, ManResource> manResourceMap = new HashMap<>();
                for(ManResource manResource:manResources){
                    manResourceMap.put(manResource.getId(), manResource);
                    manResource.setIsLeaf(1);
                }
                for(ManResource manResource:manResources){
                    String parentId = manResource.getParentId();
                    ManResource parentManResource = manResourceMap.get(parentId);
                    if(EmptyUtils.isNotEmpty(parentManResource)){
                        parentManResource.setIsLeaf(0);
                    }
                }
            }
            t.setResourceList(manResources);
        }
        return t;
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public boolean save(ManRole entity) {
        logger.debug("save {}:{}", getEntitySimpleName(), entity);
        if (entity == null) {
            return false;
        }
        //校验名称规则
        String roleName = entity.getRoleName();
        if(!roleName.matches(REGEX)){
            throwBuildBusinessException(ErrorCodeConstans.MAN_ROLE_NAME_RULE);
        }
        //校验名称唯一性
        ManRole qManRole = new ManRole();
        qManRole.setRoleName(entity.getRoleName());
        if(this.existByPara(qManRole)){
            throwBuildBusinessException(ErrorCodeConstans.MAN_ROLE_NAME_EXIST);
        }
        return SqlHelper.retBool(baseMapper.insert(entity));
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public boolean update(ManRole entity) {
        logger.debug("update {}:{}", getEntitySimpleName(), entity);
        if (entity == null) {
            return false;
        }
        if (getId(entity) == null) {
            return false;
        }
        //校验名称唯一,根据角色名称查询,查询了数据并且不是他自己则抛出异常
        String roleName = entity.getRoleName();
        if(EmptyUtils.isNotEmpty(roleName)){
            if(!roleName.matches(REGEX)){
                throwBuildBusinessException(ErrorCodeConstans.MAN_ROLE_NAME_RULE);
            }
            ManRole qManRole = new ManRole();
            qManRole.setRoleName(roleName);
            ManRole rManRole = this.queryOneByPara(qManRole);
            if(EmptyUtils.isNotEmpty(rManRole)){
                if(!rManRole.getId().equals(entity.getId())){
                    throwBuildBusinessException(ErrorCodeConstans.MAN_ROLE_NAME_EXIST);
                }
            }
        }
        return baseMapper.updateById(entity) > 0;
    }

}
