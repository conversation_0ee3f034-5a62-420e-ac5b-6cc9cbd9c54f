package com.ey.tax.cloud.admin.dto.tenant;

import com.ey.cn.tax.framework.validator.DeleteByIdGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2023-09-07 12:50:52
 * 
 */
@Data
@Schema(description = "根据id删除实体")
public class TenantDeleteByIdDTO {
    
    /**
     * 数据id
     */
    @Schema(description = "数据id")
    @NotBlank(message = "{TenantDeleteByIdDTO.id.NotBlank}", groups = {DeleteByIdGroup.class})
    private String id;
}