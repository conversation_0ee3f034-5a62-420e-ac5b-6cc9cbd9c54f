package com.ey.tax.cloud.admin.dto.tenant;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2023-09-07 12:50:52
 * 
 */
@Data
@Schema(description = "启用禁用实体")
public class TenantBusinessEnableDTO {

    /**
     * id
     */
    @Schema(description = "数据id")
    private String id;

    /**
     * 状态 COLUMN:status
     */
    @Schema(description = "状态：1启用，0禁用")
    private Integer status;

}