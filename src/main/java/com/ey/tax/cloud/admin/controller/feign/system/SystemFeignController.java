package com.ey.tax.cloud.admin.controller.feign.system;

import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.web.annotation.EyPostMapping;
import com.ey.tax.cloud.admin.dto.system.SystemDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.RequestBody;


@Tag(name="系统管理feign")
public interface SystemFeignController {

    @Operation(summary = "初始化资源/角色", description = "初始化资源/角色")
    @EyPostMapping(value = "/initResourceRole.do")
    ResponseDTO<Void> initResourceRole(@RequestBody SystemDTO dto);


}
