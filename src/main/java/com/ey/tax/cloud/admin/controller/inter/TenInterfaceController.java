package com.ey.tax.cloud.admin.controller.inter;

import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.dto.SearchDTO;
import com.ey.cn.tax.framework.validator.AddGroup;
import com.ey.cn.tax.framework.validator.DeleteByIdGroup;
import com.ey.cn.tax.framework.validator.DeleteByIdListGroup;
import com.ey.cn.tax.framework.validator.QueryByIdGroup;
import com.ey.cn.tax.framework.validator.QueryByIdListGroup;
import com.ey.cn.tax.framework.validator.QueryPageGroup;
import com.ey.cn.tax.framework.validator.UpdateByIdGroup;
import com.ey.cn.tax.framework.web.annotation.EyPostMapping;
import com.ey.cn.tax.framework.web.base.BaseController;
import com.ey.tax.cloud.admin.dto.inter.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2023-09-07 09:38:08
 *
 */
@Tag(name="租户端接口管理")
public interface TenInterfaceController extends BaseController<TenInterfaceDTO> {
    /**
     * add TenInterface from http
     */
    @Operation(summary = "新增", description = "新增一条数据")
    @EyPostMapping(value = "/add.do")
    ResponseDTO<Void> add(@RequestBody @Validated({AddGroup.class}) TenInterfaceAddDTO dto);

    /**
     * addList TenInterface from http
     */
    @Operation(summary = "批量新增", description = "批量新增数据")
    @EyPostMapping(value = "/addList.do")
    ResponseDTO<Void> addList(@RequestBody List<TenInterfaceAddDTO> dtos);

    /**
     * delete TenInterface from http
     */
    @Operation(summary = "根据id删除", description = "根据id删除一条数据")
    @EyPostMapping(value = "/deleteById.do")
    ResponseDTO<Void> deleteById(@RequestBody @Validated({DeleteByIdGroup.class}) TenInterfaceDeleteByIdDTO dto);

    /**
     * deleteList TenInterface from http
     */
    @Operation(summary = "根据id列表删除", description = "根据id列表批量删除数据")
    @EyPostMapping(value = "/deleteByIdList.do")
    ResponseDTO<Void> deleteByIdList(@RequestBody @Validated({DeleteByIdListGroup.class}) TenInterfaceDeleteByIdListDTO dto);

    /**
     * update TenInterface from http
     */
    @Operation(summary = "根据id更新", description = "根据id更新一条数据")
    @EyPostMapping(value = "/updateById.do")
    ResponseDTO<Void> updateById(@RequestBody @Validated({UpdateByIdGroup.class}) TenInterfaceUpdateByIdDTO dto);

    /**
     * updateBatch TenInterface from http
     */
    @Operation(summary = "批量更新", description = "批量更新数据")
    @EyPostMapping(value = "/updateBatch.do")
    ResponseDTO<Void> updateBatch(@RequestBody List<TenInterfaceBatchUpdateDTO> dtos);

    /**
     * query TenInterface from http
     */
    @Operation(summary = "根据id查询", description = "根据id查询数据")
    @EyPostMapping(value = "/queryById.do")
    ResponseDTO<TenInterfaceRepDTO> queryById(@RequestBody @Validated({QueryByIdGroup.class}) TenInterfaceQueryByIdDTO dto);

    /**
     * queryByIdList TenInterface from http
     */
    @Operation(summary = "根据id列表查询", description = "根据id列表查询数据")
    @EyPostMapping(value = "/queryByIdList.do")
    ResponseDTO<List<TenInterfaceRepDTO>> queryByIdList(@RequestBody @Validated({QueryByIdListGroup.class}) TenInterfaceQueryByIdListDTO dto);

    /**
     * queryByIdList TenInterface from http
     */
    @Operation(summary = "根据用户id查询", description = "根据用户id查询数据")
    @EyPostMapping(value = "/queryByUserId.do")
    ResponseDTO<List<TenInterfaceRepDTO>> queryByUserId(@RequestBody @Validated({QueryByIdGroup.class}) TenInterfaceQueryByUserIdDTO dto);

    /**
     * queryList TenInterface from http
     */
    @Operation(summary = "查询列表", description = "查询数据列表")
    @EyPostMapping(value = "/queryList.do")
    ResponseDTO<List<TenInterfaceRepDTO>> queryList(@RequestBody TenInterfaceQueryDTO dto);

    /**
     * Page TenInterface from http
     */
    @Operation(summary = "分页查询", description = "根据条件分页查询")
    @EyPostMapping(value = "/queryPage.do")
    ResponseDTO<SearchDTO<TenInterfaceRepDTO>> queryPage(@RequestBody @Validated({QueryPageGroup.class}) SearchDTO<TenInterfaceQueryPageDTO> searchDTO);

    /**
     * Count TenInterface from http
     */
    @Operation(summary = "查询数量", description = "根据条件查询数量")
    @EyPostMapping(value = "/queryCount.do")
    ResponseDTO<Long> queryCount(@RequestBody TenInterfaceQueryDTO tenInterfaceDTO);

    /**
     * One TenInterface from http
     */
    @Operation(summary = "查询单个数据", description = "根据条件查询一条数据,如果查询到多条则返回异常.")
    @EyPostMapping(value = "/queryOne.do")
    ResponseDTO<TenInterfaceRepDTO> queryOne(@RequestBody TenInterfaceQueryDTO tenInterfaceDTO);

    /**
     * exist TenInterface from http
     */
    @Operation(summary = "查询是否存在", description = "根据条件查询是否存在")
    @EyPostMapping(value = "/exist.do")
    ResponseDTO<Boolean> exist(@RequestBody TenInterfaceQueryDTO tenInterfaceDTO);
}
