package com.ey.tax.cloud.admin.service.inter;

import com.ey.cn.tax.framework.context.EyUserContextHolder;
import com.ey.cn.tax.framework.dto.InterfaceDTO;
import com.ey.cn.tax.framework.service.BaseService;
import com.ey.tax.cloud.admin.entity.inter.ManInterface;

import java.util.List;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2023-08-15 17:03:55
 *
 */
public interface ManInterfaceService extends BaseService<ManInterface> {

    void initManInterface();

    List<InterfaceDTO> queryDTOByUserId(String userId);

}
