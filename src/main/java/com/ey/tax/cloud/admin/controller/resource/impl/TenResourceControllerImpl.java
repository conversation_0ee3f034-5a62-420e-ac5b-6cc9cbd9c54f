package com.ey.tax.cloud.admin.controller.resource.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.ey.cn.tax.framework.context.EyUserContextHolder;
import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.dto.SearchDTO;
import com.ey.cn.tax.framework.dto.TenResTreeDTO;
import com.ey.cn.tax.framework.entity.Search;
import com.ey.cn.tax.framework.utils.ConvertUtils;
import com.ey.cn.tax.framework.utils.DataUtils;
import com.ey.cn.tax.framework.utils.EmptyUtils;
import com.ey.cn.tax.framework.web.annotation.EyRestController;
import com.ey.cn.tax.framework.web.base.AbstractController;
import com.ey.tax.cloud.admin.constants.ResourceConstants;
import com.ey.tax.cloud.admin.controller.resource.TenResourceController;
import com.ey.tax.cloud.admin.dto.inter.TenInterfaceRepDTO;
import com.ey.tax.cloud.admin.dto.resource.*;
import com.ey.tax.cloud.admin.entity.resource.TenResource;
import com.ey.tax.cloud.admin.service.business.BussinessService;
import com.ey.tax.cloud.admin.service.resource.TenResourceService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

/**
 * <AUTHOR>
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * @date 2023-08-18 15:21:14
 */
@EyRestController(path = "/v1/resource")
public class TenResourceControllerImpl extends AbstractController<TenResourceService, TenResourceDTO, TenResource> implements TenResourceController {

    @Autowired
    BussinessService bussinessService;

    /**
     * add TenResource from http
     */
    @Override
    public ResponseDTO<Void> add(TenResourceAddDTO dto) {
        TenResource entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        service.save(entity);
        return ResponseDTO.success();
    }

    /**
     * addList TenResource from http
     */
    @Override
    public ResponseDTO<Void> addList(List<TenResourceAddDTO> dtos) {
        List<TenResource> entitys = ConvertUtils.convertDTOList2EntityList(dtos, entityClass);
        service.save(entitys);
        return ResponseDTO.success();
    }

    /**
     * delete TenResource from http
     */
    @Override
    public ResponseDTO<Void> deleteById(TenResourceDeleteByIdDTO dto) {
        service.deleteById(dto.getId());
        return ResponseDTO.success();
    }

    /**
     * deleteList TenResource from http
     */
    @Override
    public ResponseDTO<Void> deleteByIdList(TenResourceDeleteByIdListDTO dto) {
        getService().deleteByIds(dto.getIds());
        return ResponseDTO.success();
    }

    /**
     * update TenResource from http
     */
    @Override
    public ResponseDTO<Void> updateById(TenResourceUpdateByIdDTO dto) {
        TenResource entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        getService().update(entity);
        return ResponseDTO.success();
    }

    /**
     * updateBatch TenResource from http
     */
    @Override
    public ResponseDTO<Void> updateBatch(List<TenResourceBatchUpdateDTO> dtos) {
        List<TenResource> entities = ConvertUtils.convertDTOList2EntityList(dtos, entityClass);
        getService().updateBatchByIds(entities);
        return ResponseDTO.success();
    }

    /**
     * query TenResource from http
     */
    @Override
    public ResponseDTO<TenResourceQueryByIdRepDTO> queryById(TenResourceQueryByIdDTO dto) {
        TenResource entity = getService().queryById(dto.getId());
        TenResourceQueryByIdRepDTO rDTO = ConvertUtils.convertEntity2DTO(entity, TenResourceQueryByIdRepDTO.class);
        rDTO.setInterfaceList(ConvertUtils.convertEntityList2DTOList(entity.getInterfaceList(), TenInterfaceRepDTO.class));
        return ResponseDTO.success(rDTO);
    }

    /**
     * queryByIdList TenResource from http
     */
    @Override
    public ResponseDTO<List<TenResourceRepDTO>> queryByIdList(TenResourceQueryByIdListDTO dto) {
        List<TenResource> entities = getService().queryByIds(dto.getIds());
        return ResponseDTO.success(ConvertUtils.convertEntityList2DTOList(entities, TenResourceRepDTO.class));
    }

    /**
     * queryList TenResource from http
     */
    @Override
    public ResponseDTO<List<TenResourceRepDTO>> queryList(TenResourceQueryDTO dto) {
        // 临时兼容
        if (StringUtils.isBlank(dto.getParentId())) {
            dto.setParentId(null);
        }
        TenResource entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        List<TenResource> entities = getService().queryByPara(entity);
        return ResponseDTO.success(ConvertUtils.convertEntityList2DTOList(entities, TenResourceRepDTO.class));
    }

    /**
     * Page TenResource from http
     */
    @Override
    public ResponseDTO<SearchDTO<TenResourceRepDTO>> queryPage(SearchDTO<TenResourceQueryPageDTO> searchDTO) {
        Search<TenResource> search = ConvertUtils.convertSearchDTO2Search(searchDTO, entityClass);
//        getService().selectPageForTenResource(search);
//        SearchDTO<TenResourceRepDTO> rSearchDTO = ConvertUtils.convertSearch2SearchDTO(search, TenResourceRepDTO.class);
        return ResponseDTO.success(getService().selectPageForTenResource(search));
    }

    /**
     * Count TenResource from http
     */
    @Override
    public ResponseDTO<Long> queryCount(TenResourceQueryDTO dto) {
        TenResource entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        Long count = getService().queryCountByPara(entity);
        return ResponseDTO.success(count);
    }

    /**
     * One TenResource from http
     */
    @Override
    public ResponseDTO<TenResourceRepDTO> queryOne(TenResourceQueryDTO dto) {
        TenResource qEntity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        TenResource rEntity = getService().queryOneByPara(qEntity);
        TenResourceRepDTO rDTO = ConvertUtils.convertEntity2DTO(rEntity, TenResourceRepDTO.class);
        return ResponseDTO.success(rDTO);
    }

    /**
     * exist TenResource from http
     */
    @Override
    public ResponseDTO<Boolean> exist(TenResourceQueryDTO dto) {
        boolean resullt = getService().existByPara(ConvertUtils.convertDTO2Entity(dto, entityClass));
        return ResponseDTO.success(resullt);
    }

    @Override
    public ResponseDTO<Void> addButton(TenResourceAddButtonDTO dto) {
        TenResource entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        entity.setId(DataUtils.getUUID32());
        entity.setResourceType(ResourceConstants.SOURCE_TYPE_BUTTON);
        service.save(entity);
        bussinessService.bindResourceButton(entity.getParentId(), entity.getId());
        return ResponseDTO.success();
    }

    @Override
    public ResponseDTO<Void> addMenu(TenResourceAddMenuDTO dto) {
        TenResource entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        service.save(entity);
        return ResponseDTO.success();
    }

    @Override
    public ResponseDTO<Void> updateButton(TenResourceUpdateButtonDTO dto) {
        TenResource entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        service.update(entity);
        return ResponseDTO.success();
    }

    @Override
    public ResponseDTO<Void> updateMenu(TenResourceUpdateMenuDTO dto) {
        TenResource entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        service.update(entity);
        return ResponseDTO.success();
    }

    @Override
    public ResponseDTO<Void> bindInterface(TenResourceBindInterfaceDTO dto) {
        TenResource tenResource = ConvertUtils.convertDTO2Entity(dto, TenResource.class);
        service.bindInterface(tenResource);
        return ResponseDTO.success();
    }

    @Override
    public ResponseDTO<List<TenResTreeDTO>> queryTree(TenResourceQueryDTO dto) {
        //查询所有资源
        TenResource qTenResource = ConvertUtils.convertDTO2Entity(dto, TenResource.class);
        List<TenResource> tenResources = service.queryByPara(qTenResource);
        List<TenResTreeDTO> rootTenResTreeDTOs = this.buildTree(tenResources, false);
        return ResponseDTO.success(rootTenResTreeDTOs);
    }

    private List<TenResTreeDTO> buildTree(List<TenResource> tenResources, boolean showButton) {
        List<TenResTreeDTO> rootTenResTreeDTOs = new ArrayList<>();
        Map<String, TenResTreeDTO> tenResTreeDTOMap = new HashMap<>();
        for (TenResource tenResource : tenResources) {
            if (!showButton && ResourceConstants.SOURCE_TYPE_BUTTON.equals(tenResource.getResourceType())) {
                continue;
            }
            TenResTreeDTO tenResTreeDTO = new TenResTreeDTO();
            tenResTreeDTO.setId(tenResource.getId());
            tenResTreeDTO.setLabel(tenResource.getResourceName());
            tenResTreeDTO.setParentId(tenResource.getParentId());
            tenResTreeDTO.setResourceType(tenResource.getResourceType());
            tenResTreeDTO.setResourceOrder(tenResource.getResourceOrder());
            tenResTreeDTO.setNavigationView(tenResource.getNavigationView());
            tenResTreeDTO.setResourceCode(tenResource.getResourceCode());
            if (ResourceConstants.SOURCE_ROOT_PARENT_ID.equals(tenResource.getParentId())) {
                rootTenResTreeDTOs.add(tenResTreeDTO);
            }
            tenResTreeDTOMap.put(tenResTreeDTO.getId(), tenResTreeDTO);
        }
        for (Map.Entry<String, TenResTreeDTO> entry : tenResTreeDTOMap.entrySet()) {
            TenResTreeDTO tenResTreeDTO = entry.getValue();
            String parentId = tenResTreeDTO.getParentId();
            if (ResourceConstants.SOURCE_ROOT_PARENT_ID.equals(parentId)) {
                continue;
            }
            TenResTreeDTO parentTenResTreeDTO = tenResTreeDTOMap.get(parentId);
            if (EmptyUtils.isNotEmpty(parentTenResTreeDTO)) {
                parentTenResTreeDTO.getChildren().add(tenResTreeDTO);
                this.sort(parentTenResTreeDTO.getChildren());
            }
        }
        this.sort(rootTenResTreeDTOs);
        return rootTenResTreeDTOs;
    }


    private List<TenResourceTreeDTO> buildTree2(List<TenResource> tenResources, boolean showButton) {
        List<TenResourceTreeDTO> rootTenResTreeDTOs = new ArrayList<>();
        Map<String, TenResourceTreeDTO> tenResTreeDTOMap = new HashMap<>();
        for (TenResource tenResource : tenResources) {
            if (!showButton && ResourceConstants.SOURCE_TYPE_BUTTON.equals(tenResource.getResourceType())) {
                continue;
            }
            TenResourceTreeDTO tenResTreeDTO = new TenResourceTreeDTO();
            tenResTreeDTO.setId(tenResource.getId());
            tenResTreeDTO.setValue(tenResource.getId());
            tenResTreeDTO.setItemParent(tenResource.getParentId());
            tenResTreeDTO.setLabel(tenResource.getResourceName());
            tenResTreeDTO.setResourceOrder(tenResource.getResourceOrder());
            if (ResourceConstants.SOURCE_ROOT_PARENT_ID.equals(tenResource.getParentId())) {
                rootTenResTreeDTOs.add(tenResTreeDTO);
            }
            tenResTreeDTOMap.put(tenResTreeDTO.getId(), tenResTreeDTO);
        }
        for (Map.Entry<String, TenResourceTreeDTO> entry : tenResTreeDTOMap.entrySet()) {
            TenResourceTreeDTO tenResTreeDTO = entry.getValue();
            String parentId = tenResTreeDTO.getItemParent();
            if (ResourceConstants.SOURCE_ROOT_PARENT_ID.equals(parentId)) {
                continue;
            }
            TenResourceTreeDTO parentTenResTreeDTO = tenResTreeDTOMap.get(parentId);
            if (EmptyUtils.isNotEmpty(parentTenResTreeDTO)) {
                parentTenResTreeDTO.getChildren().add(tenResTreeDTO);
                this.sort2(parentTenResTreeDTO.getChildren());
            }
        }
        this.sort2(rootTenResTreeDTOs);
        return rootTenResTreeDTOs;
    }

    private void sort(List<TenResTreeDTO> tenResTrees) {
        tenResTrees.sort(new Comparator<TenResTreeDTO>() {
            @Override
            public int compare(TenResTreeDTO o1, TenResTreeDTO o2) {
                if (EmptyUtils.isNotEmpty(o1.getResourceOrder()) && EmptyUtils.isNotEmpty(o2.getResourceOrder())) {
                    return o1.getResourceOrder().compareTo(o2.getResourceOrder());
                } else {
                    return 0;
                }
            }
        });
    }

    private void sort2(List<TenResourceTreeDTO> tenResTrees) {
        tenResTrees.sort(new Comparator<TenResourceTreeDTO>() {
            @Override
            public int compare(TenResourceTreeDTO o1, TenResourceTreeDTO o2) {
                if (EmptyUtils.isNotEmpty(o1.getResourceOrder()) && EmptyUtils.isNotEmpty(o2.getResourceOrder())) {
                    return o1.getResourceOrder().compareTo(o2.getResourceOrder());
                } else {
                    return 0;
                }
            }
        });
    }

    /**
     * 查询租户资源
     *
     * @param dto
     * @return
     */
    @Override
    public ResponseDTO<List<TenResourceDTO>> queryTenantResource(TenResourceQueryDTO dto) {
        TenResource qTenResource = ConvertUtils.convertDTO2Entity(dto, TenResource.class);
        List<TenResource> tenResources = service.queryTenantResource(qTenResource, true);
        List<TenResourceDTO> tenResourceDTOs = ConvertUtils.convertEntityList2DTOList(tenResources, TenResourceDTO.class);
        return ResponseDTO.success(tenResourceDTOs);
    }

    /**
     * 查询租户资源树
     *
     * @param dto
     * @return
     */
    @Override
    public ResponseDTO<List<TenResTreeDTO>> queryTenantResourceTree(TenResourceQueryDTO dto) {
        TenResource qTenResource = ConvertUtils.convertDTO2Entity(dto, TenResource.class);
//        qTenResource.setSysCode(EyUserContextHolder.get().getSyscode());
        List<TenResource> tenResources = service.queryTenantResource(qTenResource, true);
        List<TenResTreeDTO> rootTenResTreeDTOs = this.buildTree(tenResources, true);
        return ResponseDTO.success(rootTenResTreeDTOs);
    }

    /**
     * 查询租户资源
     */
    @Override
    public ResponseDTO<List<TenResourceDTO>> queryTenantResourceByIds(TenResourceQueryByIdListDTO dto) {
        List<TenResource> tenResources = service.queryByIds(dto.getIds());
        List<TenResourceDTO> tenResourceDTOs = ConvertUtils.convertEntityList2DTOList(tenResources, TenResourceDTO.class);
        return ResponseDTO.success(tenResourceDTOs);
    }

    @Override
    public ResponseDTO<List<TenResourceShortDTO>> selectTenResourceByCurrentUser(String userId, String tenantId) {
        return ResponseDTO.success(service.selectTenResourceByCurrentUser(userId, tenantId));
    }

    /**
     * queryList TenResource from http
     */
    @Override
    public ResponseDTO<List<TenResourceRepDTO>> queryListDO(TenResourceQueryDTO dto) {
        // 临时兼容
        if (StringUtils.isBlank(dto.getParentId())) {
            dto.setParentId(null);
        }
        TenResource entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        List<TenResource> entities = getService().queryByPara(entity);
        return ResponseDTO.success(ConvertUtils.convertEntityList2DTOList(entities, TenResourceRepDTO.class));
    }

    @Override
    public ResponseDTO<List<TenResourceTreeDTO>> queryTreeListDtO(TenResourceQueryDTO dto) {
        // 临时兼容
        if (StringUtils.isBlank(dto.getParentId())) {
            dto.setParentId(null);
        }
        TenResource entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        List<TenResource> entities = getService().queryByPara(entity);
        List<TenResourceTreeDTO> rootTenResTreeDTOs = this.buildTree2(entities, false);
        return ResponseDTO.success(rootTenResTreeDTOs);
    }

}
