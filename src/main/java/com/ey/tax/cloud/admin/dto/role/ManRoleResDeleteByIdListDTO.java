package com.ey.tax.cloud.admin.dto.role;

import com.ey.cn.tax.framework.validator.DeleteByIdListGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2023-09-07 12:42:55
 * 
 */
@Data
@Schema(description = "根据id列表删除实体")
public class ManRoleResDeleteByIdListDTO {
    
    /**
     * 数据id列表
     */
    @Schema(description = "数据id列表")
    @NotNull(message = "{ManRoleResDeleteByIdListDTO.ids.NotNull}", groups = {DeleteByIdListGroup.class})
    @Size(min = 1, message = "{ManRoleResDeleteByIdListDTO.ids.Size}", groups = {DeleteByIdListGroup.class})
    private List<String> ids;
}