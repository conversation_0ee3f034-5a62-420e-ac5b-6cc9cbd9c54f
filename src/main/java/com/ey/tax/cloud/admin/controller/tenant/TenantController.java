package com.ey.tax.cloud.admin.controller.tenant;

import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.dto.SearchDTO;
import com.ey.cn.tax.framework.validator.*;
import com.ey.cn.tax.framework.web.annotation.EyPostMapping;
import com.ey.cn.tax.framework.web.base.BaseController;
import com.ey.tax.cloud.admin.dto.tenant.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import java.util.Map;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2023-09-07 12:50:52
 *
 */
@Tag(name="租户管理")
public interface TenantController extends BaseController<TenantDTO> {
    /**
     * add Tenant from http
     */
    @Operation(summary = "新增", description = "新增一条数据")
    @EyPostMapping(value = "/add.do")
    ResponseDTO<Void> add(@RequestBody @Validated({AddGroup.class}) TenantAddDTO dto);

    /**
     * addList Tenant from http
     */
    @Operation(summary = "批量新增", description = "批量新增数据")
    @EyPostMapping(value = "/addList.do")
    ResponseDTO<Void> addList(@RequestBody List<TenantAddDTO> dtos);

    /**
     * delete Tenant from http
     */
    @Operation(summary = "根据id删除", description = "根据id删除一条数据")
    @EyPostMapping(value = "/deleteById.do")
    ResponseDTO<Void> deleteById(@RequestBody @Validated({DeleteByIdGroup.class}) TenantDeleteByIdDTO dto);

    /**
     * deleteList Tenant from http
     */
    @Operation(summary = "根据id列表删除", description = "根据id列表批量删除数据")
    @EyPostMapping(value = "/deleteByIdList.do")
    ResponseDTO<Void> deleteByIdList(@RequestBody @Validated({DeleteByIdListGroup.class}) TenantDeleteByIdListDTO dto);

    /**
     * update Tenant from http
     */
    @Operation(summary = "根据id更新", description = "根据id更新一条数据")
    @EyPostMapping(value = "/updateById.do")
    ResponseDTO<Void> updateById(@RequestBody @Validated({UpdateByIdGroup.class}) TenantUpdateByIdDTO dto);

    /**
     * updateBatch Tenant from http
     */
    @Operation(summary = "批量更新", description = "批量更新数据")
    @EyPostMapping(value = "/updateBatch")
    ResponseDTO<Void> updateBatch(@RequestBody List<TenantBatchUpdateDTO> dtos);

    /**
     * query Tenant from http
     */
    @Operation(summary = "根据id查询", description = "根据id查询数据")
    @EyPostMapping(value = "/queryById.do")
    ResponseDTO<TenantRepDTO> queryById(@RequestBody @Validated({QueryByIdGroup.class}) TenantQueryByIdDTO dto);

    /**
     * queryByIdList Tenant from http
     */
    @Operation(summary = "根据id列表查询", description = "根据id列表查询数据")
    @EyPostMapping(value = "/queryByIdList.do")
    ResponseDTO<List<TenantRepDTO>> queryByIdList(@RequestBody @Validated({QueryByIdListGroup.class}) TenantQueryByIdListDTO dto);

    @Operation(summary = "查询租户信息Map", description = "查询租户信息Map")
    @EyPostMapping(value = "/queryMap.do")
    ResponseDTO<Map<String,String>  > queryMap(@RequestBody com.ey.tax.cloud.tenant.dto.tenant.TenantQueryDTO tenantQueryDTO);

    /**
     * queryList Tenant from http
     */
    @Operation(summary = "查询列表", description = "查询数据列表")
    @EyPostMapping(value = "/queryList.do")
    ResponseDTO<List<TenantRepDTO>> queryList(@RequestBody TenantQueryDTO dto);

    /**
     * Page Tenant from http
     */
    @Operation(summary = "分页查询", description = "根据条件分页查询")
    @EyPostMapping(value = "/queryPage.do")
    ResponseDTO<SearchDTO<TenantRepDTO>> queryPage(@RequestBody @Validated({QueryPageGroup.class}) SearchDTO<TenantQueryPageDTO> searchDTO);

    /**
     * Count Tenant from http
     */
    @Operation(summary = "查询数量", description = "根据条件查询数量")
    @EyPostMapping(value = "/queryCount.do")
    ResponseDTO<Long> queryCount(@RequestBody TenantQueryDTO tenantDTO);

    /**
     * One Tenant from http
     */
    @Operation(summary = "查询单个数据", description = "根据条件查询一条数据,如果查询到多条则返回异常.")
    @EyPostMapping(value = "/queryOne.do")
    ResponseDTO<TenantRepDTO> queryOne(@RequestBody TenantQueryDTO tenantDTO);

    /**
     * exist Tenant from http
     */
    @Operation(summary = "查询是否存在", description = "根据条件查询是否存在")
    @EyPostMapping(value = "/exist.do")
    ResponseDTO<Boolean> exist(@RequestBody TenantQueryDTO tenantDTO);

    @Operation(summary = "启用禁用", description="1启用，0禁用")
    @EyPostMapping(value = "/enableTenant.do")
    ResponseDTO<Void> enableTenant(@RequestBody @Validated({EnableGroup.class}) TenantEnableDTO dto);

    /**
     * 查询租户字典
     */
    @Operation(summary = "查询租户字典", description = "根据租户字典用于构建下拉菜单")
    @EyPostMapping(value = "/queryDictionary.do")
    ResponseDTO<List<TenantRepDictDTO>> queryDictionary(@RequestBody TenantQueryDictDTO tenantDTO);

}
