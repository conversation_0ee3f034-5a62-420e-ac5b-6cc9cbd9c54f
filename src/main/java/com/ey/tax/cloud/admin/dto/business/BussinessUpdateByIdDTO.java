package com.ey.tax.cloud.admin.dto.business;

import com.ey.cn.tax.framework.validator.UpdateByIdGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2023-08-18 15:21:14
 *
 */
@Data
@Schema(description = "根据id更新实体")
public class BussinessUpdateByIdDTO {

    /**
     * 数据id
     */
    @Schema(description = "数据id")
    @NotBlank(message = "{BusinessDTO.id.NotBlank}", groups = {UpdateByIdGroup.class})
    private String id;

    /**
     * 业务组名称 COLUMN:business_name
     */
    @Schema(description = "业务组名称")
    @NotBlank(message = "{BusinessDTO.businessName.NotBlank}", groups = {UpdateByIdGroup.class})
    private String businessName;

}
