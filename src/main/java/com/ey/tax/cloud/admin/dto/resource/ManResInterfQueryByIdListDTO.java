package com.ey.tax.cloud.admin.dto.resource;

import com.ey.cn.tax.framework.validator.QueryByIdListGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2023-09-07 12:24:57
 * 
 */
@Data
@Schema(description = "根据id列表查询实体")
public class ManResInterfQueryByIdListDTO {
    
    /**
     * 数据id列表
     */
    @Schema(description = "数据id列表")
    @NotNull(message = "{ManResInterfQueryByIdListDTO.ids.NotNull}", groups = {QueryByIdListGroup.class})
    @Size(min = 1, message = "{ManResInterfQueryByIdListDTO.ids.Size}", groups = {QueryByIdListGroup.class})
    private List<String> ids;
}