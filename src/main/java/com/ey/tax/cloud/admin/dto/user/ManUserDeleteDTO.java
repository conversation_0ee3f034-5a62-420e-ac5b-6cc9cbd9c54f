package com.ey.tax.cloud.admin.dto.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2023-09-07 12:55:35
 *
 */
@Data
@Schema(description = "根据条件删除实体")
public class ManUserDeleteDTO {
    /**
     * COLUMN:user_email
     */
    @Schema(description = "邮箱", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String userEmail;

    /**
     * COLUMN:user_status
     */
    @Schema(description = "状态", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer userStatus;

}
