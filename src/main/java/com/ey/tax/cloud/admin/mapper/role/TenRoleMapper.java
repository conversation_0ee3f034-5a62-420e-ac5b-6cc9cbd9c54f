package com.ey.tax.cloud.admin.mapper.role;

import com.ey.cn.tax.framework.mybatisplus.core.mapper.IEyBaseMapper;
import com.ey.tax.cloud.admin.entity.role.TenRole;
import org.apache.ibatis.annotations.Delete;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2023-08-21 16:01:32
 * 
 */
public interface TenRoleMapper extends IEyBaseMapper<TenRole> {

    @Delete("delete  from base_role where is_del =1 and id = #{id}")
    long deleteDel(String id);

}
