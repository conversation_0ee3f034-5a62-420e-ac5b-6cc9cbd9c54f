package com.ey.tax.cloud.admin.controller.tenant;

import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.dto.SearchDTO;
import com.ey.cn.tax.framework.validator.*;
import com.ey.cn.tax.framework.web.annotation.EyPostMapping;
import com.ey.cn.tax.framework.web.base.BaseController;
import com.ey.tax.cloud.admin.dto.tenant.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2023-09-07 12:50:52
 *
 */
@Tag(name="租户业务组关系")
public interface TenantBusinessController extends BaseController<TenantBusinessDTO> {

    /**
     * add TenantBusiness from http
     */
    @Operation(summary = "新增", description = "新增一条数据")
    @EyPostMapping(value = "/add.do")
    ResponseDTO<Void> add(@RequestBody @Validated({AddGroup.class}) TenantBusinessAddDTO dto);

    /**
     * addList TenantBusiness from http
     */
    @Operation(summary = "批量新增", description = "批量新增数据")
    @EyPostMapping(value = "/addList.do")
    ResponseDTO<Void> addList(@RequestBody List<TenantBusinessAddDTO> dtos);

    /**
     * delete TenantBusiness from http
     */
    @Operation(summary = "根据id删除", description = "根据id删除一条数据")
    @EyPostMapping(value = "/deleteById.do")
    ResponseDTO<Void> deleteById(@RequestBody @Validated({DeleteByIdGroup.class}) TenantBusinessDeleteByIdDTO dto);

    /**
     * deleteList TenantBusiness from http
     */
    @Operation(summary = "根据id列表删除", description = "根据id列表批量删除数据")
    @EyPostMapping(value = "/deleteByIdList.do")
    ResponseDTO<Void> deleteByIdList(@RequestBody @Validated({DeleteByIdListGroup.class}) TenantBusinessDeleteByIdListDTO dto);

    /**
     * update TenantBusiness from http
     */
    @Operation(summary = "根据id更新", description = "根据id更新一条数据")
    @EyPostMapping(value = "/updateById.do")
    ResponseDTO<Void> updateById(@RequestBody @Validated({UpdateByIdGroup.class}) TenantBusinessUpdateByIdDTO dto);

    /**
     * updateBatch TenantBusiness from http
     */
    @Operation(summary = "批量更新", description = "批量更新数据")
    @EyPostMapping(value = "/updateBatch.do")
    ResponseDTO<Void> updateBatch(@RequestBody List<TenantBusinessBatchUpdateDTO> dtos);

    /**
     * query TenantBusiness from http
     */
    @Operation(summary = "根据id查询", description = "根据id查询数据")
    @EyPostMapping(value = "/queryById.do")
    ResponseDTO<TenantBusinessRepDTO> queryById(@RequestBody @Validated({QueryByIdGroup.class}) TenantBusinessQueryByIdDTO dto);

    /**
     * queryByIdList TenantBusiness from http
     */
    @Operation(summary = "根据id列表查询", description = "根据id列表查询数据")
    @EyPostMapping(value = "/queryByIdList.do")
    ResponseDTO<List<TenantBusinessRepDTO>> queryByIdList(@RequestBody @Validated({QueryByIdListGroup.class}) TenantBusinessQueryByIdListDTO dto);

    /**
     * queryList TenantBusiness from http
     */
    @Operation(summary = "查询列表", description = "查询数据列表")
    @EyPostMapping(value = "/queryList.do")
    ResponseDTO<List<TenantBusinessRepDTO>> queryList(@RequestBody TenantBusinessQueryDTO dto);

    /**
     * Page TenantBusiness from http
     */
    @Operation(summary = "分页查询", description = "根据条件分页查询")
    @EyPostMapping(value = "/queryPage.do")
    ResponseDTO<SearchDTO<TenantBusinessRepDTO>> queryPage(@RequestBody @Validated({QueryPageGroup.class}) SearchDTO<TenantBusinessQueryPageDTO> searchDTO);

    /**
     * Count TenantBusiness from http
     */
    @Operation(summary = "查询数量", description = "根据条件查询数量")
    @EyPostMapping(value = "/queryCount.do")
    ResponseDTO<Long> queryCount(@RequestBody TenantBusinessQueryDTO tenantBusinessDTO);

    /**
     * One TenantBusiness from http
     */
    @Operation(summary = "查询单个数据", description = "根据条件查询一条数据,如果查询到多条则返回异常.")
    @EyPostMapping(value = "/queryOne.do")
    ResponseDTO<TenantBusinessRepDTO> queryOne(@RequestBody TenantBusinessQueryDTO tenantBusinessDTO);

    /**
     * exist TenantBusiness from http
     */
    @Operation(summary = "查询是否存在", description = "根据条件查询是否存在")
    @EyPostMapping(value = "/exist.do")
    ResponseDTO<Boolean> exist(@RequestBody TenantBusinessQueryDTO tenantBusinessDTO);

    /**
     * add TenantBusiness from http
     */
    @Operation(summary = "启用禁用", description = "启用禁用")
    @EyPostMapping(value = "/enable.do")
    ResponseDTO<Void> enable(@RequestBody @Validated({EnableGroup.class}) TenantBusinessEnableDTO dto);

}
