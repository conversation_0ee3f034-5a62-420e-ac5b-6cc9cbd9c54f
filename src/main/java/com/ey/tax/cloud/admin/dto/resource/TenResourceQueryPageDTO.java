package com.ey.tax.cloud.admin.dto.resource;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2023-09-07 10:30:43
 *
 */
@Data
@Schema(description = "根据条件分页查询实体")
public class TenResourceQueryPageDTO {
    /**
     * 父节点 COLUMN:parent_id
     */
    @Schema(description = "父节点", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String parentId;

    /**
     * 资源类型 COLUMN:resource_type
     */
    @Schema(description = "资源类型", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer resourceType;

    /**
     * 路由 COLUMN:resource_route
     */
    @Schema(description = "路由", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String resourceRoute;

    /**
     * 资源编码 COLUMN:resource_code
     */
    @Schema(description = "资源编码", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String resourceCode;

    /**
     * 资源名称 COLUMN:resource_name
     */
    @Schema(description = "资源名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String resourceName;

    /**
     * 资源图标 COLUMN:resource_icon
     */
    @Schema(description = "资源图标", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String resourceIcon;

    /**
     * 排序 COLUMN:resource_order
     */
    @Schema(description = "排序", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer resourceOrder;

    /**
     * 国际化code COLUMN:i18n_code
     */
    @Schema(description = "国际化code", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String i18nCode;

    @Schema(description = "子应用编码", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String subAppCode;
}
