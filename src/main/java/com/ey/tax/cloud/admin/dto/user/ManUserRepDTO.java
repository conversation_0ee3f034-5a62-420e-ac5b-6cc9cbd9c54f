package com.ey.tax.cloud.admin.dto.user;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ey.cn.tax.framework.dto.BaseRepDTO;
import com.ey.tax.cloud.admin.entity.role.ManRole;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2023-09-07 12:55:35
 *
 */
@Data
@Schema(description = "查询返回实体")
public class ManUserRepDTO extends BaseRepDTO {

    /**
     * COLUMN:user_email
     */
    @Schema(description = "邮箱")
    private String userEmail;

    /**
     * COLUMN:user_status
     */
    @Schema(description = "状态")
    private Integer userStatus;

    /**
     * 角色列表
     */
    @Schema(description = "角色id列表")
    private List<String> roleIds;

    /**
     * 角色列表
     */
    @Schema(description = "角色列表")
    private List<ManRole> roleList;

}
