package com.ey.tax.cloud.admin.dto.inter;

import com.ey.cn.tax.framework.validator.BatchUpdateGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2023-09-07 10:00:51
 * 
 */
@Data
@Schema(description = "根据id批量更新实体")
public class TenInterfaceBatchUpdateDTO {
    
    /**
     * 数据id
     */
    @Schema(description = "数据id")
    @NotBlank(message = "{TenInterfaceBatchUpdateDTO.id.NotBlank}", groups = {BatchUpdateGroup.class})
    private String id;

    /**
     * 接口名称 COLUMN:interface_name
     */
    @Schema(description = "接口名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String interfaceName;

    /**
     * 接口路径 COLUMN:interface_path
     */
    @Schema(description = "接口路径", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String interfacePath;

    /**
     * 接口所属子系统 COLUMN:interface_system
     */
    @Schema(description = "接口所属子系统", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String interfaceSystem;

    /**
     * 接口方法 COLUMN:interface_method
     */
    @Schema(description = "接口方法", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String interfaceMethod;

    /**
     * 接口权限 COLUMN:interface_auth
     */
    @Schema(description = "接口权限", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer interfaceAuth;
}