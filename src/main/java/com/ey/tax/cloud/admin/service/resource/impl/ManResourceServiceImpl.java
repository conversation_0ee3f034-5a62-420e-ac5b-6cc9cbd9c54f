package com.ey.tax.cloud.admin.service.resource.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.ey.cn.tax.framework.context.EyUserContextHolder;
import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.entity.Search;
import com.ey.cn.tax.framework.mybatisplus.extension.service.AbstractService;
import com.ey.cn.tax.framework.mybatisplus.utils.PageUtils;
import com.ey.cn.tax.framework.utils.ConvertUtils;
import com.ey.cn.tax.framework.utils.EmptyUtils;
import com.ey.tax.cloud.admin.constants.ErrorCodeConstans;
import com.ey.tax.cloud.admin.constants.ResourceConstants;
import com.ey.tax.cloud.admin.dto.resource.ManResTreeDTO;
import com.ey.tax.cloud.admin.dto.resource.ManResourceQueryMenuDTO;
import com.ey.tax.cloud.admin.dto.resource.ManResourceQueryMenuRepDTO;
import com.ey.tax.cloud.admin.dto.resource.ManResourceShortDTO;
import com.ey.tax.cloud.admin.entity.inter.ManInterface;
import com.ey.tax.cloud.admin.entity.resource.ManResInterf;
import com.ey.tax.cloud.admin.entity.resource.ManResource;
import com.ey.tax.cloud.admin.entity.role.ManRoleRes;
import com.ey.tax.cloud.admin.entity.user.ManUserRole;
import com.ey.tax.cloud.admin.mapper.resource.ManResourceMapper;
import com.ey.tax.cloud.admin.service.inter.ManInterfaceService;
import com.ey.tax.cloud.admin.service.resource.ManResInterfService;
import com.ey.tax.cloud.admin.service.resource.ManResourceService;
import com.ey.tax.cloud.admin.service.role.ManRoleResService;
import com.ey.tax.cloud.admin.service.user.ManUserRoleService;
import com.ey.tax.cloud.base.constants.LangCategoryCodeConstans;
import com.ey.tax.cloud.base.dto.LangItemDTO;
import com.ey.tax.cloud.base.remote.LangFeignClient;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.*;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2023-08-18 15:21:14
 *
 */
@Service
public class ManResourceServiceImpl extends AbstractService<ManResourceMapper, ManResource> implements ManResourceService {

    @Autowired
    private ManResInterfService manResInterfService;

    @Autowired
    private ManInterfaceService manInterfaceService;

    @Autowired
    private ManUserRoleService manUserRoleService;

    @Autowired
    private ManRoleResService manRoleResService;

    @Autowired
    private LangFeignClient langFeignClient;

    /**
     * 资源绑定接口
     *
     * 1.删除原有绑定关系
     * 2.重新添加绑定关系
     *
     * @param manResource
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void bindInterface(ManResource manResource) {
        ManResInterf dManResInterf = new ManResInterf();
        dManResInterf.setResourceId(manResource.getId());
        manResInterfService.deleteByPara(dManResInterf);
        List<ManResInterf> manResInterfs = new ArrayList<>();
        List<String> interfaceList = new ArrayList<>();
        for(String interfaceId:manResource.getInterfaceIds()){
            //校验接口是否存在
            ManInterface eManInterface = new ManInterface();
            eManInterface.setId(interfaceId);
            if (!manInterfaceService.existByPara(eManInterface)) {
                throwBuildBusinessException(ErrorCodeConstans.RESOURCE_NOT_EXIST, interfaceId);
            }
            if(interfaceList.contains(interfaceId)){
                throwBuildBusinessException(ErrorCodeConstans.RESOURCE_REPEAT, interfaceId);
            }else{
                interfaceList.add(interfaceId);
            }
            ManResInterf manResInterf = new ManResInterf();
            manResInterf.setResourceId(manResource.getId());
            manResInterf.setInterfaceId(interfaceId);
            manResInterfs.add(manResInterf);
        }
        manResInterfService.save(manResInterfs);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public boolean deleteById(Serializable id) {
        logger.info("deleteById {}:{}", getEntitySimpleName(), id);
        ManResource qManResource = new ManResource();
        qManResource.setParentId(id.toString());
        Long count = this.queryCountByPara(qManResource);
        if(count > 0){
            throwBuildBusinessException(ErrorCodeConstans.RESOURCE_HAS_CHILDREN);
        }
        return currentProxy().removeById(id);
    }

    /**
     * query by id
     */
    @Transactional(readOnly = true)
    @Override
    public ManResource queryById(Serializable id) {
        logger.debug("queryById {}:{}", getEntitySimpleName(), id);
        ManResource t = baseMapper.selectById(id);
        processUserName(t);
        //获取关联的资源
        ManResInterf qManResInterf = new ManResInterf();
        qManResInterf.setResourceId(id.toString());
        List<ManResInterf> manResInterfs = manResInterfService.queryByPara(qManResInterf);
        List<String> interfaceIds = new ArrayList<>();
        if(EmptyUtils.isNotEmpty(manResInterfs)){
            for(ManResInterf manResInterf:manResInterfs){
                interfaceIds.add(manResInterf.getInterfaceId());
            }
            List<ManInterface> manInterfaces = manInterfaceService.queryByIds(interfaceIds);
            t.setInterfaceList(manInterfaces);
        }
        return t;
    }

    /**
     * query page by para
     */
    @Transactional(readOnly = true)
    @Override
    public Search<ManResource> selectPageForManResource(Search<ManResource> search) {
        logger.debug("queryPageByPara {}:{}", getEntitySimpleName(), search);
        IPage<ManResource> iPage = baseMapper.selectPageForManResource(PageUtils.convertPageToIPage(search.getPage()), search.getQueryParams());
        List<ManResource> records = iPage.getRecords();
        search.setEntities(records);
        if (CollectionUtils.isEmpty(records)) {
            search.getPage().setTotalCount(0L);
        } else {
            processUserName(records);
            search.getPage().setTotalCount(iPage.getTotal());
        }
        return search;
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public boolean save(ManResource entity) {
        logger.debug("save {}:{}", getEntitySimpleName(), entity);
        if (entity == null) {
            return false;
        }
        String resourceCode = entity.getResourceCode();
        if(EmptyUtils.isNotEmpty(resourceCode)){
            //校验编码唯一性
            ManResource qManResource = new ManResource();
            qManResource.setResourceCode(resourceCode);
            if(this.existByPara(qManResource)){
                throwBuildBusinessException(ErrorCodeConstans.MAN_RESOURCE_CODE_EXIST);
            }
        }
        if(EmptyUtils.isEmpty(entity.getParentId())){
            entity.setParentId(ResourceConstants.SOURCE_ROOT_PARENT_ID);
        }
        return SqlHelper.retBool(baseMapper.insert(entity));
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public boolean update(ManResource entity) {
        logger.debug("update {}:{}", getEntitySimpleName(), entity);
        if (entity == null) {
            return false;
        }
        if (getId(entity) == null) {
            return false;
        }
        //校验名称唯一,根据code查询,查询了数据并且不是他自己则抛出异常
        String resourceCode = entity.getResourceCode();
        if(EmptyUtils.isNotEmpty(resourceCode)){
            ManResource qManResource = new ManResource();
            qManResource.setResourceCode(resourceCode);
            ManResource rManResource = this.queryOneByPara(qManResource);
            if(EmptyUtils.isNotEmpty(rManResource)){
                if(!rManResource.getId().equals(entity.getId())){
                    throwBuildBusinessException(ErrorCodeConstans.MAN_RESOURCE_CODE_EXIST);
                }
            }
        }
        return baseMapper.updateById(entity) > 0;
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public List<ManResource> queryMenu(ManResource manResource) {
        //获取用户id
        String userId = this.getUserId();
        //用户->角色
        ManUserRole qManUserRole = new ManUserRole();
        qManUserRole.setUserId(userId);
        List<ManUserRole> manUserRoles = manUserRoleService.queryByPara(qManUserRole);
        if(EmptyUtils.isEmpty(manUserRoles)){
            return new ArrayList<>();
        }
        //角色->资源
        List<String> roleIds = new ArrayList<>();
        for(ManUserRole manUserRole:manUserRoles){
            roleIds.add(manUserRole.getRoleId());
        }
        List<ManRoleRes> manRoleRess = manRoleResService.queryRoleResByRoleIds(roleIds);
        if(EmptyUtils.isEmpty(manRoleRess)){
            return new ArrayList<>();
        }
        List<String> resourceIds = new ArrayList<>();
        for(ManRoleRes manRoleRes:manRoleRess){
            resourceIds.add(manRoleRes.getResourceId());
        }
        //获取资源
        return this.queryByIds(resourceIds);
    }

    @Override
    public ManResourceQueryMenuRepDTO queryMenu(ManResource qManResource, HttpServletRequest request) {
        List<ManResource> manResources = this.queryMenu(qManResource);
        List<ManResTreeDTO> rootManResTreeDTOs = new ArrayList<>();
        Map<String, ManResTreeDTO> manResTreeDTOMap = new HashMap<>();

        LangItemDTO langItemDTO = new LangItemDTO();
        langItemDTO.setCategoryCode(LangCategoryCodeConstans.MENUS);
        langItemDTO.setAcceptLanguage(request.getLocale().toLanguageTag());
        Map<String, String> langMaps = langFeignClient.findByCategoryCode(langItemDTO).getData();

        List<String> buttons = new ArrayList<>();
        for(ManResource manResource:manResources){
            //过滤button
            Integer resourceType = manResource.getResourceType();
            if(ResourceConstants.SOURCE_TYPE_BUTTON.equals(resourceType)){
                buttons.add(manResource.getResourceCode());
                continue;
            }
            ManResTreeDTO manResTreeDTO = new ManResTreeDTO();
            manResTreeDTO.setId(manResource.getId());
            manResTreeDTO.setLabel(manResource.getResourceName());
            manResTreeDTO.setParentId(manResource.getParentId());
            manResTreeDTO.setResourceType(manResource.getResourceType());
            manResTreeDTO.setResourceIcon(manResource.getResourceIcon());
            manResTreeDTO.setResourceRoute(manResource.getResourceRoute());
            manResTreeDTO.setResourceOrder(manResource.getResourceOrder());
            manResTreeDTO.setNavigationView(manResource.getNavigationView());
            String i18nValue = langMaps.get(manResource.getI18nCode());
            manResTreeDTO.setResourceName(manResource.getResourceName());
            manResTreeDTO.setResourceViewName(StringUtils.isBlank(i18nValue) ? manResource.getResourceName() : i18nValue);
            if(ResourceConstants.SOURCE_ROOT_PARENT_ID.equals(manResource.getParentId())){
                rootManResTreeDTOs.add(manResTreeDTO);
            }
            manResTreeDTOMap.put(manResTreeDTO.getId(), manResTreeDTO);
        }
        for(Map.Entry<String, ManResTreeDTO> entry:manResTreeDTOMap.entrySet()){
            ManResTreeDTO manResTreeDTO = entry.getValue();
            String parentId = manResTreeDTO.getParentId();
            if(ResourceConstants.SOURCE_ROOT_PARENT_ID.equals(parentId)){
                continue;
            }
            ManResTreeDTO parentManResTreeDTO = manResTreeDTOMap.get(parentId);
            if(EmptyUtils.isNotEmpty(parentManResTreeDTO)){
                parentManResTreeDTO.getChildren().add(manResTreeDTO);
                parentManResTreeDTO.getChildren().sort(new Comparator<ManResTreeDTO>() {
                    @Override
                    public int compare(ManResTreeDTO o1, ManResTreeDTO o2) {
                        if(EmptyUtils.isNotEmpty(o1.getResourceOrder()) && EmptyUtils.isNotEmpty(o2.getResourceOrder())){
                            return o1.getResourceOrder().compareTo(o2.getResourceOrder());
                        }else{
                            return 0;
                        }
                    }
                });
            }
        }
        ManResourceQueryMenuRepDTO manResourceQueryMenuRepDTO = new ManResourceQueryMenuRepDTO();
        manResourceQueryMenuRepDTO.setMenus(rootManResTreeDTOs);
        manResourceQueryMenuRepDTO.setButtons(buttons);
        return manResourceQueryMenuRepDTO;
    }

    @Override
    public List<ManResourceShortDTO> selectManResourceByCurrentUser() {
        return baseMapper.selectManResourceByCurrentUser(EyUserContextHolder.get().getUserId());
    }

    @Override
    public List<ManResourceShortDTO> selectManResourceByCurrentUser(String userId) {
        return baseMapper.selectManResourceByCurrentUser(userId);
    }

    @Override
    public List<ManResource> queryMenu(String userId) {
        return baseMapper.selectManResourceByUserId(userId);
    }
}
