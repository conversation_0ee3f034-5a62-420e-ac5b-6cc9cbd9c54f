package com.ey.tax.cloud.admin.entity.role;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ey.cn.tax.framework.mybatisplus.core.entity.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2023-08-23 10:22:50
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("base_role_resource")
public class TenRoleResource extends TenantEntity {
    /**
     * 角色id COLUMN:role_id
     */
    private String roleId;

    /**
     * 资源id COLUMN:resource_id
     */
    private String resourceId;

    private String remark;

}
