package com.ey.tax.cloud.admin.dto.role;

import com.ey.tax.cloud.admin.controller.validate.BindResourceGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2023-09-07 12:31:51
 *
 */
@Data
@Schema(description = "绑定资源实体")
public class ManRoleBindResourceDTO {

    /**
     * 角色id
     */
    @Schema(description = "角色id")
    @NotBlank(message = "{ManRoleBindResourceDTO.id.Size}", groups = {BindResourceGroup.class})
    private String id;

    /**
     * 资源id列表
     */
    @Schema(description = "资源id列表")
    @NotNull(message = "{ManRoleBindResourceDTO.resourceIds.NotNull}", groups = {BindResourceGroup.class})
    @Size(min = 1, message = "{ManRoleBindResourceDTO.resourceIds.Size}", groups = {BindResourceGroup.class})
    private List<String> resourceIds;

}
