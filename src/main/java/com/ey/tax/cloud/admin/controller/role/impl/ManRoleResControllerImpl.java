package com.ey.tax.cloud.admin.controller.role.impl;

import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.dto.SearchDTO;
import com.ey.cn.tax.framework.entity.Search;
import com.ey.cn.tax.framework.utils.ConvertUtils;
import com.ey.cn.tax.framework.web.annotation.EyRestController;
import com.ey.cn.tax.framework.web.base.AbstractController;
import com.ey.tax.cloud.admin.controller.role.ManRoleResController;
import com.ey.tax.cloud.admin.dto.role.ManRoleResAddDTO;
import com.ey.tax.cloud.admin.dto.role.ManRoleResBatchUpdateDTO;
import com.ey.tax.cloud.admin.dto.role.ManRoleResDTO;
import com.ey.tax.cloud.admin.dto.role.ManRoleResDeleteByIdDTO;
import com.ey.tax.cloud.admin.dto.role.ManRoleResDeleteByIdListDTO;
import com.ey.tax.cloud.admin.dto.role.ManRoleResQueryByIdDTO;
import com.ey.tax.cloud.admin.dto.role.ManRoleResQueryByIdListDTO;
import com.ey.tax.cloud.admin.dto.role.ManRoleResQueryDTO;
import com.ey.tax.cloud.admin.dto.role.ManRoleResQueryPageDTO;
import com.ey.tax.cloud.admin.dto.role.ManRoleResRepDTO;
import com.ey.tax.cloud.admin.dto.role.ManRoleResUpdateByIdDTO;
import com.ey.tax.cloud.admin.entity.role.ManRoleRes;
import com.ey.tax.cloud.admin.service.role.ManRoleResService;
import java.util.List;
import java.util.Map;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2023-09-07 12:42:55
 * 
 */
@EyRestController(path ="/v1/manRoleRes")
public class ManRoleResControllerImpl extends AbstractController<ManRoleResService, ManRoleResDTO, ManRoleRes> implements ManRoleResController {

    /**
     * add ManRoleRes from http
     */
    @Override
    public ResponseDTO<Void> add(ManRoleResAddDTO dto) {
        ManRoleRes entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        service.save(entity);
        return ResponseDTO.success();
    }

    /**
     * addList ManRoleRes from http
     */
    @Override
    public ResponseDTO<Void> addList(List<ManRoleResAddDTO> dtos) {
        List<ManRoleRes> entitys = ConvertUtils.convertDTOList2EntityList(dtos, entityClass);
        service.save(entitys);
        return ResponseDTO.success();
    }

    /**
     * delete ManRoleRes from http
     */
    @Override
    public ResponseDTO<Void> deleteById(ManRoleResDeleteByIdDTO dto) {
        service.deleteById(dto.getId());
        return ResponseDTO.success();
    }

    /**
     * deleteList ManRoleRes from http
     */
    @Override
    public ResponseDTO<Void> deleteByIdList(ManRoleResDeleteByIdListDTO dto) {
        getService().deleteByIds(dto.getIds());
        return ResponseDTO.success();
    }

    /**
     * update ManRoleRes from http
     */
    @Override
    public ResponseDTO<Void> updateById(ManRoleResUpdateByIdDTO dto) {
        ManRoleRes entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        getService().update(entity);
        return ResponseDTO.success();
    }

    /**
     * updateBatch ManRoleRes from http
     */
    @Override
    public ResponseDTO<Void> updateBatch(List<ManRoleResBatchUpdateDTO> dtos) {
        List<ManRoleRes> entities = ConvertUtils.convertDTOList2EntityList(dtos, entityClass);
        getService().updateBatchByIds(entities);
        return ResponseDTO.success();
    }

    /**
     * query ManRoleRes from http
     */
    @Override
    public ResponseDTO<ManRoleResRepDTO> queryById(ManRoleResQueryByIdDTO dto) {
        ManRoleRes entity = getService().queryById(dto.getId());
        ManRoleResRepDTO rDTO = ConvertUtils.convertEntity2DTO(entity, ManRoleResRepDTO.class);
        return ResponseDTO.success(rDTO);
    }

    /**
     * queryByIdList ManRoleRes from http
     */
    @Override
    public ResponseDTO<List<ManRoleResRepDTO>> queryByIdList(ManRoleResQueryByIdListDTO dto) {
        List<ManRoleRes> entities = getService().queryByIds(dto.getIds());
        return ResponseDTO.success(ConvertUtils.convertEntityList2DTOList(entities, ManRoleResRepDTO.class));
    }

    /**
     * queryList ManRoleRes from http
     */
    @Override
    public ResponseDTO<List<ManRoleResRepDTO>> queryList(ManRoleResQueryDTO dto) {
        ManRoleRes entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        List<ManRoleRes> entities = getService().queryByPara(entity);
        return ResponseDTO.success(ConvertUtils.convertEntityList2DTOList(entities, ManRoleResRepDTO.class));
    }

    /**
     * Page ManRoleRes from http
     */
    @Override
    public ResponseDTO<SearchDTO<ManRoleResRepDTO>> queryPage(SearchDTO<ManRoleResQueryPageDTO> searchDTO) {
        Search<ManRoleRes> search = ConvertUtils.convertSearchDTO2Search(searchDTO, entityClass);
        getService().queryPageByPara(search);
        return ResponseDTO.success(ConvertUtils.convertSearch2SearchDTO(search, ManRoleResRepDTO.class));
    }

    /**
     * Count ManRoleRes from http
     */
    @Override
    public ResponseDTO<Long> queryCount(ManRoleResQueryDTO dto) {
        ManRoleRes entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        Long count = getService().queryCountByPara(entity);
        return ResponseDTO.success(count);
    }

    /**
     * One ManRoleRes from http
     */
    @Override
    public ResponseDTO<ManRoleResRepDTO> queryOne(ManRoleResQueryDTO dto) {
        ManRoleRes qEntity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        ManRoleRes rEntity = getService().queryOneByPara(qEntity);
        ManRoleResRepDTO rDTO = ConvertUtils.convertEntity2DTO(rEntity, ManRoleResRepDTO.class);
        return ResponseDTO.success(rDTO);
    }

    /**
     * exist ManRoleRes from http
     */
    @Override
    public ResponseDTO<Boolean> exist(ManRoleResQueryDTO dto) {
        boolean resullt = getService().existByPara(ConvertUtils.convertDTO2Entity(dto, entityClass));
        return ResponseDTO.success(resullt);
    }
}