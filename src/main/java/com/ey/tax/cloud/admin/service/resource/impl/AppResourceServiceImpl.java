package com.ey.tax.cloud.admin.service.resource.impl;

import com.ey.cn.tax.framework.mybatisplus.extension.service.AbstractService;
import com.ey.tax.cloud.admin.mapper.resource.AppResourceMapper;
import com.ey.tax.cloud.admin.service.resource.AppResourceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.ey.tax.cloud.admin.entity.resource.AppResource;

/**
 * <AUTHOR>
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * @date 2024-08-28 17:52:38
 */
@Slf4j
@Service
public class AppResourceServiceImpl extends AbstractService<AppResourceMapper, AppResource> implements AppResourceService {


}