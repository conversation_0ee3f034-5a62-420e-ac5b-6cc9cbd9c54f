package com.ey.tax.cloud.admin.service.system.magt;

import com.ey.cn.tax.framework.utils.ConvertUtils;
import com.ey.cn.tax.framework.utils.DataUtils;
import com.ey.cn.tax.framework.utils.ExceptionUtils;
import com.ey.tax.cloud.admin.constants.CommonConstants;
import com.ey.tax.cloud.admin.constants.EnableConstants;
import com.ey.tax.cloud.admin.constants.ErrorCodeConstans;
import com.ey.tax.cloud.admin.constants.ResourceConstants;
import com.ey.tax.cloud.admin.dto.resource.ManResTreeDTO;
import com.ey.tax.cloud.admin.dto.resource.ManResourceDTO;
import com.ey.tax.cloud.admin.dto.system.SystemDTO;
import com.ey.tax.cloud.admin.entity.business.Bussiness;
import com.ey.tax.cloud.admin.entity.business.BussinessRes;
import com.ey.tax.cloud.admin.entity.resource.ManResInterf;
import com.ey.tax.cloud.admin.entity.resource.ManResource;
import com.ey.tax.cloud.admin.entity.resource.TenResInterf;
import com.ey.tax.cloud.admin.entity.resource.TenResource;
import com.ey.tax.cloud.admin.entity.role.TenRole;
import com.ey.tax.cloud.admin.entity.role.TenRoleResource;
import com.ey.tax.cloud.admin.entity.tenant.Tenant;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date: 2024/7/24 10:52
 * @Mender:
 */
public class SystemMagt implements SystemInit{
    // 参数：新建系统
    SystemDTO dto;
    // 参数：已存在的所欲资源
    List<TenResource> oldResources;
    // 参数：全量管理端资源
    List<ManResource> allManResources;
    // 参数：全量管理端资源接口关系
    List<ManResInterf> allManResInterfs;
    // 参数：安永租户
    Tenant tenant;
    //参数：默认角色
    TenRole oldRole ;
    // 参数：安永业务组
    List<Bussiness> bussiness;

    String subAppCode;
    String prefix;
    String suffix;
    TenResource rootMenu;
    Map<String,String> newResouceIdMap = new HashMap<>();
    List<TenResource> tenResources = new ArrayList<>();
    List<TenResource> updateTenResources = new ArrayList<>();
    List<TenResInterf> tenResInterfs;
    List<BussinessRes> bussinessRess;
    TenRole tenRole;
    List<TenRoleResource> tenRoleResources;

    private final static String resourceCode = "systemBuildingCenter2.0";

    public SystemMagt(SystemDTO system,
                      List<TenResource> oldResources,
                      List<ManResource> allManResources,
                      List<ManResInterf> allManResInterfs,
                      Tenant tenant,
                      TenRole oldRole,
                      List<Bussiness> bussiness){
        this.dto = system;
        this.oldResources = oldResources;
        this.allManResources = allManResources;
        this.allManResInterfs = allManResInterfs;
        this.tenant = tenant;
        this.oldRole = oldRole;
        this.bussiness = bussiness;
    }

    /**
     * 1.同步系统目录
     * 2.同步“构建中心2.0”所有资源  （resource_cdoe="systemBuildingCenter2.0"）
     * 3.同步接口以及资源接口关系表
     * 4.绑定“安永”业务组
     * 5.创建一个“租户管理员角色”
     * 6.绑定 角色（租户管理员角色）-资源（构建中心2.0）
     */
    @Override
    public SystemInit build() {
        if(dto == null){
            return this;
        }
        subAppCode = dto.getSysCode()+"api";
        prefix = getIdPrefix();
        suffix = getIdSuffix();
        return filterRoot()
                .buildRootMenu()
                .buildTenResource()
                .filter()
                .buildTenResInterf()
                .buildBusiness()
                .buildTenRole()
                .buildTenRoleRes();
    }

    /**
     * 初始化资源：查找根目录
     * @return
     */
    private SystemMagt filterRoot(){
        if(CollectionUtils.isNotEmpty(oldResources)){
            for(TenResource t:oldResources){
                if(ResourceConstants.SOURCE_ROOT_PARENT_ID.equals(t.getParentId())){
                    rootMenu = t;
                    break;
                }
            }
        }
        return this;
    }

    /**
     * 初始化资源：系统目录
     * @return
     */
    private SystemMagt buildRootMenu(){
        if(rootMenu != null || dto == null){
            return this;
        }
        TenResource menu = new TenResource();
        menu.setId(getId(dto.getId()));
        menu.setParentId(ResourceConstants.SOURCE_ROOT_PARENT_ID);
        menu.setResourceType(0);
        menu.setIsAuth(true);
        menu.setResourceName(dto.getSysName());
        menu.setResourceRoute(dto.getRoute());
        menu.setSysCode(dto.getSysCode());
        menu.setResourceSource(ResourceConstants.RESOURCE_SOURCE_CONSTRUCT);
        menu.setRemark(ResourceConstants.RESOURCE_SOURCE_CONSTRUCT);
        menu.setSubAppCode(subAppCode);
        menu.setResourceOrder(99);
        menu.setResourceIcon("IcBase");
        rootMenu = menu;
        tenResources.add(rootMenu);
        newResouceIdMap.put(getPrefixId(dto.getId()),getId(dto.getId()));
        return this;
    }

    /**
     * 初始化资源：构建中心2.0
     * @return
     */
    private SystemMagt buildTenResource(){
        if(rootMenu == null || CollectionUtils.isEmpty(allManResources)){
            return this;
        }
        String rootId = null;
        Map<String, ManResTreeDTO> map = new HashMap<>();
        List<ManResTreeDTO> list = new ArrayList<>();
        for(ManResource manR:allManResources){
            ManResTreeDTO t = ConvertUtils.convertDTO2Entity(manR,ManResTreeDTO.class);
            map.put(t.getId(), t);
            if (resourceCode.equals(t.getResourceCode())) {
                rootId = t.getId();
            }
            if(map.containsKey(t.getParentId())){
                map.get(t.getParentId()).getChildren().add(t);
            }else{
                list.add(t);
            }
        }
        if(StringUtils.isBlank(rootId)){
            return this;
        }
        if(CollectionUtils.isNotEmpty(list)){
            for(ManResTreeDTO t:list){
                if(map.containsKey(t.getParentId())){
                    map.get(t.getParentId()).getChildren().add(t);
                }
            }
        }

        ManResTreeDTO m = map.get(rootId);
        m.setParentId(rootMenu.getId());
        List<TenResource> res = flat(m,false);
        tenResources.addAll(res);
        return this;
    }

    /**
     * 初始化资源：过滤出手动新增的资源,并替换父类id
     * @return
     */
    private SystemMagt filter(){
        if(MapUtils.isNotEmpty(newResouceIdMap) && CollectionUtils.isNotEmpty(oldResources)){
            updateTenResources = oldResources.stream()
                    .filter(e->!e.getId().startsWith(prefix) && newResouceIdMap.containsKey(substringId(e.getParentId())))
                    .map(e->{
                        e.setParentId(newResouceIdMap.get(substringId(e.getParentId())));
                        return e;
                    }).collect(Collectors.toList());
        }
        return this;
    }

    /**
     * 资源-接口关系表
     * @return
     */
    private SystemMagt buildTenResInterf(){
        if(CollectionUtils.isEmpty(tenResources)){
            return this;
        }
        Set<String> s = tenResources.stream().map(TenResource::getId).collect(Collectors.toSet());

        tenResInterfs = allManResInterfs.stream()
                .map(e->{
                    TenResInterf t = ConvertUtils.convertDTO2Entity(e,TenResInterf.class);
                    t.setId(getId(e.getId()));
                    t.setResourceId(getId(e.getResourceId()));
                    t.setRemark(ResourceConstants.RESOURCE_SOURCE_CONSTRUCT);
                    return t;
                })
                .filter(e->s.contains(e.getResourceId()))
                .collect(Collectors.toList());
        return this;
    }

    /**
     * 绑定安永租户
     * @return
     */
    private SystemMagt buildBusiness(){
        if(CollectionUtils.isEmpty(bussiness) || CollectionUtils.isEmpty(tenResources)){
            return this;
        }
        bussinessRess = tenResources.stream()
                .map(r->{
                    return bussiness.stream().map(b->{
                        BussinessRes br = new BussinessRes();
                        br.setId(getId(DataUtils.getUUID32()));
                        br.setBusinessId(b.getId());
                        br.setResourceId(r.getId());
                        br.setRemark(ResourceConstants.RESOURCE_SOURCE_CONSTRUCT);
                        return br;
                    }).collect(Collectors.toList());
                })
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        return this;
    }

    /**
     * 创建默认角色
     * @return
     */
    private SystemMagt buildTenRole(){
        if(oldRole != null){
            tenRole = oldRole;
            return this;
        }

        String roleCode = dto.getSysCode()+"_"+ResourceConstants.RESOURCE_SOURCE_CONSTRUCT+"_admin";
        TenRole r = new TenRole();
        r.setId(roleCode);
        r.setTenantId(tenant.getId());
        r.setRoleName(dto.getSysCode()+CommonConstants.TenRole.CONSTRUCT_ROLE_NAME);
        r.setRoleStatus(EnableConstants.ENABLE_TRUE);
        r.setAutoApprove(CommonConstants.TenRole.AUTO_APPROVE_FALSE);
        r.setIsApply(CommonConstants.TenRole.IS_APPLY_FALSE);
        r.setRoleCode(roleCode);
        r.setSysCode(dto.getSysCode());
        r.setRemark(ResourceConstants.RESOURCE_SOURCE_CONSTRUCT);
        tenRole = r;
        return this;
    }

    /**
     * 创建角色-资源关系
     * @return
     */
    private SystemMagt buildTenRoleRes(){
        if(tenant ==null || tenRole == null || CollectionUtils.isEmpty(tenResources)){
            return this;
        }
        tenRoleResources = tenResources.stream()
                .map(r->{
                    TenRoleResource trr = new TenRoleResource();
                    trr.setId(getId(DataUtils.getUUID32()) );
                    trr.setTenantId(tenant.getId());
                    trr.setRoleId(tenRole.getId());
                    trr.setResourceId(r.getId());
                    trr.setRemark(ResourceConstants.RESOURCE_SOURCE_CONSTRUCT);
                    return trr;
                })
                .collect(Collectors.toList());
        return this;
    }

    private List<TenResource> flat(ManResTreeDTO p,boolean replaceParent){
        List<TenResource> l = new ArrayList<>();
        flat(p,l,replaceParent);
        return l;
    }

    private void flat(ManResTreeDTO p,List<TenResource> l,boolean replaceParent){
        l.add(convert(p,replaceParent));
        if(CollectionUtils.isNotEmpty(p.getChildren())){
            for(ManResTreeDTO t:p.getChildren()){
                flat(t,l,true);
            }
        }
    }

    private TenResource convert(ManResTreeDTO e,boolean replaceParent){
        TenResource tenR = ConvertUtils.convertEntity2DTO(e,TenResource.class);
        tenR.setId(getId(e.getId()));
        tenR.setParentId(replaceParent ? getId(e.getParentId()) : e.getParentId() );
        tenR.setSysCode(dto.getSysCode());
        tenR.setSubAppCode(subAppCode);
        tenR.setIsAuth(true);
        tenR.setResourceSource(ResourceConstants.RESOURCE_SOURCE_CONSTRUCT);
        tenR.setRemark(ResourceConstants.RESOURCE_SOURCE_CONSTRUCT);
        newResouceIdMap.put(getPrefixId(e.getId()),getId(e.getId()));
        return tenR;
    }

    private String substringId(String id){
        // id格式：prefix_id_suffix
        if(id.startsWith(prefix) && id.split("_").length >2){
            return id.substring(0,id.lastIndexOf("_"));
        }
        return id;
    }

    private String getId(String id){
        return getPrefixId(id) + suffix;
    }

    private String getPrefixId(String id){
        return prefix + id;
    }

    private String getIdPrefix(){
        return subAppCode + "_";
    }

    private String getIdSuffix(){
        return "_" + getTime();
    }

    private String getTime() {
        Date now = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("HHmmss");
        String time = sdf.format(now);
        return time;
    }

    public TenResource getRootMenu() {
        return rootMenu;
    }

    public List<TenResource> getTenResources() {
        return tenResources;
    }

    @Override
    public List<TenResource> getUpdateTenResources() {
        return updateTenResources;
    }

    public List<TenResInterf> getTenResInterfs() {
        return tenResInterfs;
    }

    public List<BussinessRes> getBussinessRess() {
        return bussinessRess;
    }

    public TenRole getTenRole() {
        return tenRole;
    }

    public List<TenRoleResource> getTenRoleResources() {
        return tenRoleResources;
    }

    @Override
    public String getSubAppCode() {
        return subAppCode;
    }
}
