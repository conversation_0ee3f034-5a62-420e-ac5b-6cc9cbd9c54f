package com.ey.tax.cloud.admin.dto.business;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2023-09-06 11:08:31
 *
 */
@Data
@Schema(description = "根据条件查询实体")
public class BussinessResQueryDTO {
    /**
     * 业务id COLUMN:business_id
     */
    @Schema(description = "业务id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String businessId;

    /**
     * 资源id COLUMN:resource_id
     */
    @Schema(description = "资源id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String resourceId;
}
