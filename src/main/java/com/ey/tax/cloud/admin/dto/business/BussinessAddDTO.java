package com.ey.tax.cloud.admin.dto.business;

import com.ey.cn.tax.framework.validator.AddGroup;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * @date 2023-08-18 15:21:14
 */
@Data
@Schema(description = "新增实体")
public class BussinessAddDTO {

    /**
     * 业务组名称 COLUMN:business_name
     */
    @Schema(description = "业务组名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "{BusinessDTO.businessName.NotBlank}", groups = {AddGroup.class})
    private String businessName;

}
