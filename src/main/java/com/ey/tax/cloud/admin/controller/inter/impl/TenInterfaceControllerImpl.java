package com.ey.tax.cloud.admin.controller.inter.impl;

import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.dto.SearchDTO;
import com.ey.cn.tax.framework.entity.Search;
import com.ey.cn.tax.framework.utils.ConvertUtils;
import com.ey.cn.tax.framework.utils.GroovyUtils;
import com.ey.cn.tax.framework.web.annotation.EyRestController;
import com.ey.cn.tax.framework.web.base.AbstractController;
import com.ey.tax.cloud.admin.controller.inter.TenInterfaceController;
import com.ey.tax.cloud.admin.dto.inter.*;
import com.ey.tax.cloud.admin.entity.inter.TenInterface;
import com.ey.tax.cloud.admin.service.inter.TenInterfaceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2023-09-07 09:38:08
 * 
 */
@EyRestController(path ="/v1/tenInterface")
public class TenInterfaceControllerImpl extends AbstractController<TenInterfaceService, TenInterfaceDTO, TenInterface> implements TenInterfaceController {

    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * add TenInterface from http
     */
    @Override
    public ResponseDTO<Void> add(TenInterfaceAddDTO dto) {
        TenInterface entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        service.save(entity);
        return ResponseDTO.success();
    }

    /**
     * addList TenInterface from http
     */
    @Override
    public ResponseDTO<Void> addList(List<TenInterfaceAddDTO> dtos) {
        List<TenInterface> entitys = ConvertUtils.convertDTOList2EntityList(dtos, entityClass);
        service.save(entitys);
        return ResponseDTO.success();
    }

    /**
     * delete TenInterface from http
     */
    @Override
    public ResponseDTO<Void> deleteById(TenInterfaceDeleteByIdDTO dto) {
        service.deleteById(dto.getId());
        return ResponseDTO.success();
    }

    /**
     * deleteList TenInterface from http
     */
    @Override
    public ResponseDTO<Void> deleteByIdList(TenInterfaceDeleteByIdListDTO dto) {
        getService().realDeleteByIds(dto.getIds());
        return ResponseDTO.success();
    }

    /**
     * update TenInterface from http
     */
    @Override
    public ResponseDTO<Void> updateById(TenInterfaceUpdateByIdDTO dto) {
        TenInterface entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        getService().update(entity);
        return ResponseDTO.success();
    }

    /**
     * updateBatch TenInterface from http
     */
    @Override
    public ResponseDTO<Void> updateBatch(List<TenInterfaceBatchUpdateDTO> dtos) {
        List<TenInterface> entities = ConvertUtils.convertDTOList2EntityList(dtos, entityClass);
        getService().updateBatchByIds(entities);
        return ResponseDTO.success();
    }

    /**
     * query TenInterface from http
     */
    @Override
    public ResponseDTO<TenInterfaceRepDTO> queryById(TenInterfaceQueryByIdDTO dto) {
        TenInterface entity = getService().queryById(dto.getId());
        TenInterfaceRepDTO rDTO = ConvertUtils.convertEntity2DTO(entity, TenInterfaceRepDTO.class);
        return ResponseDTO.success(rDTO);
    }

    /**
     * queryByIdList TenInterface from http
     */
    @Override
    public ResponseDTO<List<TenInterfaceRepDTO>> queryByIdList(TenInterfaceQueryByIdListDTO dto) {
        List<TenInterface> entities = getService().queryByIds(dto.getIds());
        return ResponseDTO.success(ConvertUtils.convertEntityList2DTOList(entities, TenInterfaceRepDTO.class));
    }

    @Override
    public ResponseDTO<List<TenInterfaceRepDTO>> queryByUserId(TenInterfaceQueryByUserIdDTO dto) {
        List<TenInterface> tenInterfaces = getService().queryByUserId(dto.getUserId());
        List<TenInterfaceRepDTO> tenInterfaceRepDTOS = GroovyUtils.convertToList(TenInterfaceRepDTO.class, tenInterfaces);
        return ResponseDTO.success(tenInterfaceRepDTOS);
    }

    /**
     * queryList TenInterface from http
     */
    @Override
    public ResponseDTO<List<TenInterfaceRepDTO>> queryList(TenInterfaceQueryDTO dto) {
        TenInterface entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        List<TenInterface> entities = getService().queryByPara(entity);
        return ResponseDTO.success(ConvertUtils.convertEntityList2DTOList(entities, TenInterfaceRepDTO.class));
    }

    /**
     * Page TenInterface from http
     */
    @Override
    public ResponseDTO<SearchDTO<TenInterfaceRepDTO>> queryPage(SearchDTO<TenInterfaceQueryPageDTO> searchDTO) {
        Search<TenInterface> search = ConvertUtils.convertSearchDTO2Search(searchDTO, entityClass);
        getService().queryPageByPara(search);
        return ResponseDTO.success(ConvertUtils.convertSearch2SearchDTO(search, TenInterfaceRepDTO.class));
    }

    /**
     * Count TenInterface from http
     */
    @Override
    public ResponseDTO<Long> queryCount(TenInterfaceQueryDTO dto) {
        TenInterface entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        Long count = getService().queryCountByPara(entity);
        return ResponseDTO.success(count);
    }

    /**
     * One TenInterface from http
     */
    @Override
    public ResponseDTO<TenInterfaceRepDTO> queryOne(TenInterfaceQueryDTO dto) {
        TenInterface qEntity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        TenInterface rEntity = getService().queryOneByPara(qEntity);
        TenInterfaceRepDTO rDTO = ConvertUtils.convertEntity2DTO(rEntity, TenInterfaceRepDTO.class);
        return ResponseDTO.success(rDTO);
    }

    /**
     * exist TenInterface from http
     */
    @Override
    public ResponseDTO<Boolean> exist(TenInterfaceQueryDTO dto) {
        boolean resullt = getService().existByPara(ConvertUtils.convertDTO2Entity(dto, entityClass));
        return ResponseDTO.success(resullt);
    }
}