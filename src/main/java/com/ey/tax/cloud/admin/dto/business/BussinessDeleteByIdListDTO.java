package com.ey.tax.cloud.admin.dto.business;

import com.ey.cn.tax.framework.validator.DeleteByIdListGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2023-08-18 15:21:14
 *
 */
@Data
@Schema(description = "根据id列表删除实体")
public class BussinessDeleteByIdListDTO {

    /**
     * 数据id
     */
    @Schema(description = "数据id列表")
    @NotNull(message = "{BusinessDTO.ids.NotNull}", groups = {DeleteByIdListGroup.class})
    @Size(min = 1, message = "{BusinessDTO.ids.Size}", groups = {DeleteByIdListGroup.class})
    private List<String> ids;

}
