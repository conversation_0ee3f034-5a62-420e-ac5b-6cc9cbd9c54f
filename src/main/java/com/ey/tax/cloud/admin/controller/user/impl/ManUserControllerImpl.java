package com.ey.tax.cloud.admin.controller.user.impl;

import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.dto.SearchDTO;
import com.ey.cn.tax.framework.entity.Search;
import com.ey.cn.tax.framework.service.UserService;
import com.ey.cn.tax.framework.utils.ConvertUtils;
import com.ey.cn.tax.framework.utils.EmptyUtils;
import com.ey.cn.tax.framework.web.annotation.EyRestController;
import com.ey.cn.tax.framework.web.base.AbstractController;
import com.ey.tax.cloud.admin.constants.EnableConstants;
import com.ey.tax.cloud.admin.controller.user.ManUserController;
import com.ey.tax.cloud.admin.dto.role.ManRoleRepDTO;
import com.ey.tax.cloud.admin.dto.user.*;
import com.ey.tax.cloud.admin.entity.user.ManUser;
import com.ey.tax.cloud.admin.service.user.ManUserService;
import com.ey.tax.cloud.base.dto.log.BusLogsAddDTO;
import com.ey.tax.cloud.base.enums.log.BusSubTypeEnum;
import com.ey.tax.cloud.base.enums.log.BusTypeEnum;
import com.ey.tax.cloud.base.remote.log.BaseBusLogsFeignClient;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2023-08-18 15:21:15
 *
 */
@EyRestController(path ="/v1/manUser")
public class ManUserControllerImpl extends AbstractController<ManUserService, ManUserDTO, ManUser> implements ManUserController {

    @Autowired
    UserService userService;

    @Autowired
    private BaseBusLogsFeignClient baseBusLogsFeignClient;

    /**
     * add ManUser from http
     */
    @Override
    public ResponseDTO<Void> add(ManUserAddDTO dto) {
        ManUser entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        if(EmptyUtils.isNotEmpty(entity.getUserPass())){
            entity.setUserPass(DigestUtils.md5Hex(entity.getUserPass()));
        }
        boolean result = service.save(entity);
        if (result){
            // 添加业务日志
            BusLogsAddDTO busLogsAddDTO = new BusLogsAddDTO();
            busLogsAddDTO.setBusType(BusTypeEnum.ADMIN.getType());
            busLogsAddDTO.setBusSubType(BusSubTypeEnum.ADD_MAN_USER.getType());
            busLogsAddDTO.setBusLog(dto);
            baseBusLogsFeignClient.add(busLogsAddDTO);
        }
        return ResponseDTO.success();
    }

    /**
     * addList ManUser from http
     */
    @Override
    public ResponseDTO<Void> addList(List<ManUserAddDTO> dtos) {
        List<ManUser> entitys = ConvertUtils.convertDTOList2EntityList(dtos, entityClass);
        service.save(entitys);
        return ResponseDTO.success();
    }

    /**
     * delete ManUser from http
     */
    @Override
    public ResponseDTO<Void> deleteById(ManUserDeleteByIdDTO dto) {
        service.deleteById(dto.getId());
        return ResponseDTO.success();
    }

    /**
     * deleteList ManUser from http
     */
    @Override
    public ResponseDTO<Void> deleteByIdList(ManUserDeleteByIdListDTO dto) {
        getService().deleteByIds(dto.getIds());
        return ResponseDTO.success();
    }

    /**
     * update ManUser from http
     */
    @Override
    public ResponseDTO<Void> updateById(ManUserUpdateByIdDTO dto) {
        ManUser entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        getService().update(entity);
        return ResponseDTO.success();
    }

    /**
     * updateBatch ManUser from http
     */
    @Override
    public ResponseDTO<Void> updateBatch(List<ManUserBatchUpdateDTO> dtos) {
        List<ManUser> entities = ConvertUtils.convertDTOList2EntityList(dtos, entityClass);
        getService().updateBatchByIds(entities);
        return ResponseDTO.success();
    }

    /**
     * query ManUser from http
     */
    @Override
    public ResponseDTO<ManUserQueryByIdRepDTO> queryById(ManUserQueryByIdDTO dto) {
        ManUser entity = getService().queryById(dto.getId());
        ManUserQueryByIdRepDTO rDTO = ConvertUtils.convertEntity2DTO(entity, ManUserQueryByIdRepDTO.class);
        rDTO.setRoleList(ConvertUtils.convertEntityList2DTOList(entity.getRoleList(), ManRoleRepDTO.class));
        return ResponseDTO.success(rDTO);
    }

    /**
     * queryByIdList ManUser from http
     */
    @Override
    public ResponseDTO<List<ManUserRepDTO>> queryByIdList(ManUserQueryByIdListDTO dto) {
        List<ManUser> entities = getService().queryByIds(dto.getIds());
        return ResponseDTO.success(ConvertUtils.convertEntityList2DTOList(entities, ManUserRepDTO.class));
    }

    /**
     * queryList ManUser from http
     */
    @Override
    public ResponseDTO<List<ManUserRepDTO>> queryList(ManUserQueryDTO dto) {
        ManUser entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        List<ManUser> entities = getService().queryByPara(entity);
        return ResponseDTO.success(ConvertUtils.convertEntityList2DTOList(entities, ManUserRepDTO.class));
    }

    /**
     * Page ManUser from http
     */
    @Override
    public ResponseDTO<SearchDTO<ManUserRepDTO>> queryPage(SearchDTO<ManUserQueryPageDTO> searchDTO) {
        Search<ManUser> search = ConvertUtils.convertSearchDTO2Search(searchDTO, entityClass);
        getService().queryPageByParaFuzzy(search);
        return ResponseDTO.success(ConvertUtils.convertSearch2SearchDTO(search, ManUserRepDTO.class));
    }

    /**
     * Count ManUser from http
     */
    @Override
    public ResponseDTO<Long> queryCount(ManUserQueryDTO dto) {
        ManUser entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        Long count = getService().queryCountByPara(entity);
        return ResponseDTO.success(count);
    }

    /**
     * One ManUser from http
     */
    @Override
    public ResponseDTO<ManUserRepDTO> queryOne(ManUserQueryDTO dto) {
        ManUser qEntity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        ManUser rEntity = getService().queryOneByPara(qEntity);
        ManUserRepDTO rDTO = ConvertUtils.convertEntity2DTO(rEntity, ManUserRepDTO.class);
        return ResponseDTO.success(rDTO);
    }

    /**
     * exist ManUser from http
     */
    @Override
    public ResponseDTO<Boolean> exist(ManUserQueryDTO dto) {
        boolean resullt = getService().existByPara(ConvertUtils.convertDTO2Entity(dto, entityClass));
        return ResponseDTO.success(resullt);
    }

    @Override
    public ResponseDTO<String> login(ManUserLoginDTO dto, HttpServletResponse response) {
        ManUser manUser = ConvertUtils.convertDTO2Entity(dto, ManUser.class);
        ManToken token = service.login(manUser);
        Cookie tokenCookie = new Cookie("manToken", token.getToken());
        tokenCookie.setPath("/");
        tokenCookie.setSecure(true);
        tokenCookie.setHttpOnly(true);
        response.addCookie(tokenCookie);
        return ResponseDTO.success(token.getToken());
    }

    @Override
    public ResponseDTO<Void> bindRole(ManUserBindRoleDTO dto) {
        ManUser manUser = ConvertUtils.convertDTO2Entity(dto, ManUser.class);
        service.bindRole(manUser);
        return ResponseDTO.success();
    }

    @Override
    public ResponseDTO<Void> enable(ManUserEnableDTO dto) {
        ManUser manUser = ConvertUtils.convertDTO2Entity(dto, ManUser.class);
        boolean result = service.update(manUser);
        if (result){
            // 添加业务日志
            BusLogsAddDTO busLogsAddDTO = new BusLogsAddDTO();
            busLogsAddDTO.setBusType(BusTypeEnum.ADMIN.getType());
            busLogsAddDTO.setBusSubType(dto.getUserStatus().equals(EnableConstants.ENABLE_TRUE) ? BusSubTypeEnum.OPEN_MAN_USER.getType()
                    : BusSubTypeEnum.BAN_MAN_USER.getType());
            busLogsAddDTO.setBusLog(dto);
            baseBusLogsFeignClient.add(busLogsAddDTO);
        }
        return ResponseDTO.success();
    }

    @Override
    public ResponseDTO<ManUserQueryByIdRepDTO> queryCurrentUser(ManUserQueryCurrentDTO dto) {
        ManUser entity = getService().queryCurrentUser();
        ManUserQueryByIdRepDTO rDTO = ConvertUtils.convertEntity2DTO(entity, ManUserQueryByIdRepDTO.class);
        return ResponseDTO.success(rDTO);
    }

    @Override
    public ResponseDTO<String> logout(ManUserLogoutDTO dto, HttpServletRequest request, HttpServletResponse response) {
        ManUser manUser = ConvertUtils.convertDTO2Entity(dto, ManUser.class);
        service.logout(manUser);
        // 退出时，清除所有 cookie
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                cookie.setValue("");
                cookie.setMaxAge(0);
                cookie.setPath("/");
                response.addCookie(cookie);
            }
        }
        return ResponseDTO.success();
    }

    @Override
    public ResponseDTO<String> resetPassword(ManUserPasswordDTO dto) {
        ManUser manUser = ConvertUtils.convertDTO2Entity(dto, ManUser.class);
        service.resetPassword(manUser);
        return ResponseDTO.success();
    }

    @Override
    public ResponseDTO<ManUserPasswordDTO> queryUserByEmail(ManUserPasswordDTO dto) {
        ManUser qManUser = new ManUser();
        qManUser.setUserEmail(dto.getUserEmail());
        ManUser rManUser = service.queryOneByPara(qManUser);
        return ResponseDTO.success(ConvertUtils.convertEntity2DTO(rManUser, ManUserPasswordDTO.class));
    }

    @Override
    public ResponseDTO<Map<String, String>> getUserNamesByIds(ManUserGetUserNamesDTO dto) {
        Collection<String> userIds = dto.getUserIds();
        return ResponseDTO.success(userService.getUserNamesByIds(userIds));
    }

    @Override
    public ResponseDTO<Set<String>> queryRoleCodes() {
        return ResponseDTO.success(getService().queryRoleCodes());
    }
}
