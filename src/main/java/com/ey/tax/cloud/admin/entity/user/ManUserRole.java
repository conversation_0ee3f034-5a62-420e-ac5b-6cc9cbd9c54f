package com.ey.tax.cloud.admin.entity.user;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ey.cn.tax.framework.mybatisplus.core.entity.BaseEntity;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2023-08-18 15:21:13
 * 
 */
@Data
@TableName("base_man_user_role")
public class ManUserRole extends BaseEntity {
    /**
     * 用户id COLUMN:user_id
     */
    private String userId;

    /**
     * 角色id COLUMN:role_id
     */
    private String roleId;
}