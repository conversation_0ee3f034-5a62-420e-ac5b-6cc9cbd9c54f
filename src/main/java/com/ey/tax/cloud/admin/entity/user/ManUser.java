package com.ey.tax.cloud.admin.entity.user;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ey.cn.tax.framework.mybatisplus.core.entity.BaseEntity;
import com.ey.tax.cloud.admin.entity.role.ManRole;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2023-08-18 15:21:15
 *
 */
@Data
@TableName("base_man_user")
public class ManUser extends BaseEntity {

    /**
     * COLUMN:user_email
     */
    private String userEmail;

    /**
     * COLUMN:user_status
     */
    private Integer userStatus;

    /**
     * COLUMN:user_pass
     */
    private String userPass;

    /**
     * 角色列表
     */
    @TableField(exist = false)
    private List<String> roleIds;

    /**
     * 角色列表
     */
    @TableField(exist = false)
    private List<ManRole> roleList;

}
