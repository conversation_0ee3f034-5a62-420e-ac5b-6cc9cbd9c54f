package com.ey.tax.cloud.admin.dto.miss;

import com.ey.cn.tax.framework.validator.DeleteByIdListGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2025-02-10 15:16:17
 * 
 */
@Data
@Schema(description = "根据id列表删除实体")
public class ResInterfaceMissDeleteByIdListDTO {
    
    /**
     * 数据id列表
     */
    @Schema(description = "数据id列表")
    @NotNull(message = "{ResInterfaceMissDTO.ids.NotNull}", groups = {DeleteByIdListGroup.class})
    @Size(min = 1, message = "{ResInterfaceMissDTO.ids.Size}", groups = {DeleteByIdListGroup.class})
    private List<String> ids;
}