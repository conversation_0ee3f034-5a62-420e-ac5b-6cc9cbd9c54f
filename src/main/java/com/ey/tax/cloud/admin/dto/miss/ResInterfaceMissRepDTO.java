package com.ey.tax.cloud.admin.dto.miss;

import com.ey.cn.tax.framework.dto.BaseRepDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2025-02-10 15:16:17
 * 
 */
@Data
@Schema(description = "查询返回实体")
public class ResInterfaceMissRepDTO extends BaseRepDTO {
    /**
     * sysCode COLUMN:sys_code
     */
    @Schema(description = "sysCode")
    private String sysCode;

    /**
     * appCode COLUMN:app_code
     */
    @Schema(description = "appCode")
    private String appCode;

    /**
     * 接口path COLUMN:interface_path
     */
    @Schema(description = "接口path")
    private String interfacePath;

    /**
     * 页面referer COLUMN:page_referer
     */
    @Schema(description = "页面referer")
    private String pageReferer;
}