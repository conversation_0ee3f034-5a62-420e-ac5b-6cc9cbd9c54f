package com.ey.tax.cloud.admin.dto.tenant;

import com.ey.cn.tax.framework.validator.UpdateByIdGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2023-09-07 12:50:52
 *
 */
@Data
@Schema(description = "根据id批量更新实体")
public class TenantUpdateByIdDTO {

    /**
     * 数据id
     */
    @Schema(description = "数据id")
    @NotBlank(message = "{TenantUpdateByIdDTO.id.NotBlank}", groups = {UpdateByIdGroup.class})
    private String id;

    /**
     * 租户名称 COLUMN:lessee_name
     */
    @Schema(description = "租户名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String lesseeName;

    /**
     * 租户编码 COLUMN:lessee_code
     */
    @Schema(description = "租户编码", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String lesseeCode;

    /**
     * 租户身份0普通租户1安永 COLUMN:tenant_type
     */
    @Schema(description = "租户身份0普通租户1安永", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer tenantType;

    /**
     * 租户类型0临时1正式 COLUMN:lessee_type
     */
    @Schema(description = "租户类型0临时1正式", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer lesseeType;

    /**
     * 租户账户开始时间 COLUMN:start_date
     */
    @Schema(description = "租户账户开始时间", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDateTime startDate;

    /**
     * 租户账户结束时间 COLUMN:end_date
     */
    @Schema(description = "租户账户结束时间", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDateTime endDate;

    /**
     * 收费模式 COLUMN:charge_mode
     */
    @Schema(description = "收费模式", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer chargeMode;

    /**
     * 授权公司数量 COLUMN:company_num
     */
    @Schema(description = "授权公司数量", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer companyNum;

    /**
     * 授权用户数量 COLUMN:user_num
     */
    @Schema(description = "授权用户数量", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer userNum;

    /**
     * 租户状态 COLUMN:lessee_status
     */
    @Schema(description = "租户状态", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer lesseeStatus;

    /**
     * 联系电话 COLUMN:lessee_tel
     */
    @Schema(description = "联系电话", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String lesseeTel;

    /**
     * COLUMN:lessee_contact
     */
    @Schema(description = "联系人", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String lesseeContact;

    /**
     * COLUMN:lessee_address
     */
    @Schema(description = "地址", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String lesseeAddress;
}
