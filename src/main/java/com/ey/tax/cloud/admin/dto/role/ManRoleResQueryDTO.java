package com.ey.tax.cloud.admin.dto.role;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2023-09-07 12:42:55
 * 
 */
@Data
@Schema(description = "根据条件查询实体")
public class ManRoleResQueryDTO {
    /**
     * 资源id COLUMN:resource_id
     */
    @Schema(description = "资源id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String resourceId;

    /**
     * 角色id COLUMN:role_id
     */
    @Schema(description = "角色id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String roleId;
}