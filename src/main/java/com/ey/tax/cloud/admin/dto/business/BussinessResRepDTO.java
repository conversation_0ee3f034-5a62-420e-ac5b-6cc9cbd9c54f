package com.ey.tax.cloud.admin.dto.business;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2023-09-06 11:08:31
 *
 */
@Data
@Schema(description = "查询返回实体")
public class BussinessResRepDTO {
    /**
     * 业务id COLUMN:business_id
     */
    @Schema(description = "业务id")
    private String businessId;

    /**
     * 资源id COLUMN:resource_id
     */
    @Schema(description = "资源id")
    private String resourceId;
}
