package com.ey.tax.cloud.admin.dto.resource;

import com.ey.cn.tax.framework.dto.BaseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2023-09-07 12:24:58
 * 
 */
@Data
@Schema(description = "默认实体")
public class TenResInterfDTO extends BaseDTO {
    /**
     * 资源id COLUMN:resource_id
     */
    @Schema(description = "资源id")
    private String resourceId;

    /**
     * 接口id COLUMN:interface_id
     */
    @Schema(description = "接口id")
    private String interfaceId;
}