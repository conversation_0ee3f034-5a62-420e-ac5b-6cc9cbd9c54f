package com.ey.tax.cloud.admin.dto.miss;

import com.ey.cn.tax.framework.validator.UpdateByIdGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2025-02-10 15:16:17
 * 
 */
@Data
@Schema(description = "根据id批量更新实体")
public class ResInterfaceMissUpdateByIdDTO {
    
    /**
     * 数据id
     */
    @Schema(description = "数据id")
    @NotBlank(message = "{ResInterfaceMissDTO.id.NotBlank}", groups = {UpdateByIdGroup.class})
    private String id;

    /**
     * sysCode COLUMN:sys_code
     */
    @Schema(description = "sysCode", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String sysCode;

    /**
     * appCode COLUMN:app_code
     */
    @Schema(description = "appCode", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String appCode;

    /**
     * 接口path COLUMN:interface_path
     */
    @Schema(description = "接口path", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String interfacePath;

    /**
     * 页面referer COLUMN:page_referer
     */
    @Schema(description = "页面referer", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String pageReferer;
}