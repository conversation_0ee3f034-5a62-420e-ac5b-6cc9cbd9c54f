package com.ey.tax.cloud.admin.dto.resource;

import com.ey.tax.cloud.admin.controller.validate.AddMenuGroup;
import com.ey.tax.cloud.admin.controller.validate.UpdateButtonGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2023-08-18 15:21:14
 *
 */
@Data
@Schema(description = "更新菜单实体")
public class ManResourceUpdateMenuDTO {

    /**
     * id COLUMN:id
     */
    @Schema(description = "菜单id")
    @NotBlank(message = "{ManResourceMenuDTO.id.NotBlank}", groups = {UpdateButtonGroup.class})
    private String id;

    /**
     * 父节点 COLUMN:parent_id
     */
    @Schema(description = "父节点", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String parentId;

    /**
     * 资源类型 COLUMN:resource_type
     */
    @Schema(description = "资源类型", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer resourceType;

    /**
     * 资源名称 COLUMN:resource_name
     */
    @Schema(description = "资源名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String resourceName;

    /**
     * 国际化code COLUMN:resource_name
     */
    @Schema(description = "国际化code", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String i18nCode;

    /**
     * 资源图标 COLUMN:resource_icon
     */
    @Schema(description = "资源图标", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String resourceIcon;

    /**
     * 路由 COLUMN:resource_route
     */
    @Schema(description = "路由", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String resourceRoute;

    /**
     * 排序 COLUMN:resource_order
     */
    @Schema(description = "排序", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer resourceOrder;

    /**
     * 导航显示 COLUMN:navigation_view
     */
    @Schema(description = "导航显示", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer navigationView;

    private String remark;

}
