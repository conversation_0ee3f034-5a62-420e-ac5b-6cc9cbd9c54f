package com.ey.tax.cloud.admin.controller.business;

import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.dto.SearchDTO;
import com.ey.cn.tax.framework.validator.*;
import com.ey.cn.tax.framework.web.annotation.EyPostMapping;
import com.ey.cn.tax.framework.web.base.BaseController;
import com.ey.tax.cloud.admin.controller.validate.BindResourceGroup;
import com.ey.tax.cloud.admin.dto.business.*;
import com.ey.tax.cloud.admin.dto.tenant.TenantQueryDictDTO;
import com.ey.tax.cloud.admin.dto.tenant.TenantRepDictDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * @date 2023-08-18 15:21:14
 */
@Tag(name = "业务组")
public interface BussinessController extends BaseController<BussinessDTO> {

    /**
     * add from http
     */
    @Operation(summary = "新增", description = "新增一个业务组")
    @EyPostMapping(value = "/add.do")
    ResponseDTO<Void> add(@RequestBody @Validated({AddGroup.class}) BussinessAddDTO dto);

    /**
     * addList from http
     */
    @Operation(summary = "批量新增", description = "批量新增业务组")
    @EyPostMapping(value = "/addList.do")
    ResponseDTO<Void> addList(@RequestBody List<BussinessAddDTO> dtos);

    /**
     * delete from http
     */
    @Operation(summary = "根据id删除", description = "批量删除业务组")
    @EyPostMapping(value = "/deleteById.do")
    ResponseDTO<Void> deleteById(@RequestBody @Validated({DeleteByIdGroup.class}) BussinessDeleteByIdDTO dto);

    /**
     * deleteList from http
     */
    @Operation(summary = "根据id列表删除", description = "根据id列表批量删除业务组")
    @EyPostMapping(value = "/deleteByIdList.do")
    ResponseDTO<Void> deleteByIdList(@RequestBody @Validated({DeleteByIdListGroup.class}) BussinessDeleteByIdListDTO dto);

    /**
     * update from http
     */
    @Operation(summary = "根据id更新", description = "根据id更新业务组")
    @EyPostMapping(value = "/updateById.do")
    ResponseDTO<Void> updateById(@RequestBody @Validated({UpdateByIdGroup.class}) BussinessUpdateByIdDTO dto);

    /**
     * update from http
     */
    @Operation(summary = "批量更新", description = "批量更新业务组")
    @EyPostMapping(value = "/updateBatch.do")
    ResponseDTO<Void> updateBatch(@RequestBody List<BussinessBatchUpdateDTO> dto);

    /**
     * query from http
     */
    @Operation(summary = "根据id查询", description = "根据id查询业务组")
    @EyPostMapping(value = "/queryById.do")
    ResponseDTO<BussinessRepDTO> queryById(@RequestBody @Validated({QueryByIdGroup.class}) BussinessQueryByIdDTO dto);

    /**
     * query from http
     */
    @Operation(summary = "根据id列表查询", description = "根据Id列表查询业务组")
    @EyPostMapping(value = "/queryByIds.do")
    ResponseDTO<List<BussinessRepDTO>> queryByIds(@RequestBody @Validated({QueryByIdGroup.class}) BussinessDTO dto);

    /**
     * query from http
     */
    @Operation(summary = "根据id列表查询", description = "根据Id列表查询业务组")
    @EyPostMapping(value = "/queryByIdList.do")
    ResponseDTO<List<BussinessRepDTO>> queryByIdList(@RequestBody @Validated({QueryByIdListGroup.class}) BussinessQueryByIdListDTO dto);

    /**
     * List from http
     */
    @Operation(summary = "查询列表", description = "根据条件查询列表")
    @EyPostMapping(value = "/queryList.do")
    ResponseDTO<List<BussinessRepDTO>> queryList(@RequestBody BussinessQueryDTO dto);

    /**
     * Page from http
     */
    @Operation(summary = "分页查询", description = "根据条件分页查询")
    @EyPostMapping(value = "/queryPage.do")
    ResponseDTO<SearchDTO<BussinessRepDTO>> queryPage(@RequestBody @Validated({QueryPageGroup.class}) SearchDTO<BussinessQueryPageDTO> searchDTO);

    /**
     * Count from http
     */
    @Operation(summary = "查询数量", description = "根据条件查询数量")
    @EyPostMapping(value = "/queryCount.do")
    ResponseDTO<Long> queryCount(@RequestBody BussinessQueryDTO dto);

    /**
     * Exist from http
     */
    @Operation(summary = "查询是否存在", description = "根据条件查询是否存在")
    @EyPostMapping(value = "/exist.do")
    ResponseDTO<Boolean> exist(@RequestBody BussinessQueryDTO dto);

    /**
     * query from http
     */
    @Operation(summary = "查询单个数据", description = "根据条件查询一条数据,如果查询到多条则返回异常.")
    @EyPostMapping(value = "/queryOne.do")
    ResponseDTO<BussinessRepDTO> queryOne(@RequestBody BussinessQueryDTO dto);

    /**
     * query from http
     */
    @Operation(summary = "绑定资源", description = "业务组绑定资源")
    @EyPostMapping(value = "/bindResource.do")
    ResponseDTO<Void> bindResource(@RequestBody @Validated({BindResourceGroup.class}) BussinessBindResourceDTO dto);

    /**
     * enable from http
     */
    @Operation(summary = "启用禁用", description = "0:禁用,1:启用")
    @EyPostMapping(value = "/enable.do")
    ResponseDTO<Void> enable(@RequestBody @Validated({EnableGroup.class}) BussinessEnableDTO dto);

    /**
     * 查询业务组字典
     */
    @Operation(summary = "查询业务组字典", description = "根据业务组字典用于构建下拉菜单")
    @EyPostMapping(value = "/queryDictionary.do")
    ResponseDTO<List<BussinessRepDictDTO>> queryDictionary(@RequestBody BussinessQueryDictDTO tenantDTO);

}

