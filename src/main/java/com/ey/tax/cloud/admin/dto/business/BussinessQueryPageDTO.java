package com.ey.tax.cloud.admin.dto.business;

import com.ey.cn.tax.framework.validator.QueryPageGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2023-08-18 15:21:14
 *
 */
@Data
@Schema(description = "根据条件分页查询实体")
public class BussinessQueryPageDTO {

    /**
     * 业务组状态 COLUMN:business_status
     */
    @Schema(description = "业务组状态")
    @NotBlank(message = "{BusinessDTO.businessStatus.NotBlank}", groups = {QueryPageGroup.class})
    private Integer businessStatus;

    /**
     * 业务组名称 COLUMN:business_name
     */
    @Schema(description = "业务组名称")
    @NotBlank(message = "{BusinessDTO.businessName.NotBlank}", groups = {QueryPageGroup.class})
    private String businessName;

}
