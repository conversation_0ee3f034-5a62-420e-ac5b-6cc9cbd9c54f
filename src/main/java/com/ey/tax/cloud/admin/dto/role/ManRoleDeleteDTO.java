package com.ey.tax.cloud.admin.dto.role;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2023-09-07 12:31:51
 * 
 */
@Data
@Schema(description = "根据条件删除实体")
public class ManRoleDeleteDTO {
    /**
     * 角色名称 COLUMN:role_name
     */
    @Schema(description = "角色名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String roleName;
}