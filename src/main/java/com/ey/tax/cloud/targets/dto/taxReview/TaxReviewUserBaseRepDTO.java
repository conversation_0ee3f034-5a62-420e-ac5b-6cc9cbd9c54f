package com.ey.tax.cloud.targets.dto.taxReview;

import com.ey.cn.tax.framework.dto.BaseRepDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-02-26 10:51:54
 * 
 */
@Data
@Schema(description = "查询返回实体")
public class TaxReviewUserBaseRepDTO extends BaseRepDTO {
    /**
     * GPN COLUMN:gpn
     */
    @Schema(description = "GPN")
    private String gpn;
}