package com.ey.tax.cloud.targets.service.transfer.impl;

import com.ey.cn.tax.framework.utils.DataUtils;
import com.ey.cn.tax.framework.utils.EmptyUtils;
import com.ey.tax.cloud.targets.constant.DictConstants;
import com.ey.tax.cloud.targets.dto.transfer.*;
import com.ey.tax.cloud.targets.entity.data.Dictionary;
import com.ey.tax.cloud.targets.entity.data.DictionaryItem;
import com.ey.tax.cloud.targets.entity.transfer.SyncSmuMercury;
import com.ey.tax.cloud.targets.entity.transfer.SyncTaxLocation;
import com.ey.tax.cloud.targets.service.data.DictionaryItemService;
import com.ey.tax.cloud.targets.service.data.DictionaryService;
import com.ey.tax.cloud.targets.service.transfer.DataTransferService;
import com.ey.tax.cloud.targets.service.transfer.SyncSmuMercuryService;
import com.ey.tax.cloud.targets.service.transfer.SyncTaxLocationService;
import org.springframework.beans.factory.annotation.Qualifier;
import com.xxl.job.core.context.XxlJobHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("dictionary")
public class DictionaryTransferServiceImpl implements DataTransferService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Value("${tigeroar.url}")
    private String url;

    @Value("${tigeroar.appId}")
    private String appId;

    @Value("${tigeroar.clientId}")
    private String clientId;

    @Value("${tigeroar.appSecret}")
    private String appSecret;

    @Value("${tigeroar.tokenPath}")
    private String tokenPath;

    @Value("${tigeroar.categoryPath}")
    private String categoryPath;

    @Value("${tigeroar.dictPath}")
    private String dictPath;

    @Autowired
    @Qualifier("ignoreHttpsRestTemplate")
    private RestTemplate restTemplate;

    @Autowired
    private DictionaryService dictionaryService;

    @Autowired
    private DictionaryItemService dictionaryItemService;

    @Autowired
    private SyncTaxLocationService syncTaxLocationService;
    @Autowired
    private SyncSmuMercuryService syncSmuMercuryService;

    /**
     * 码表转换
     */
    @Override
    public void process(Map<String, String> params) {

        /*
        OpprStatus
        LegalEntity
SolutionSector
OpprRecurring
OpprAlliance
oppr_type
oppr_outcome
win_reason
lost_reason
decline_reason
ClientAreaRegion
AccountSegment
ClientIndustrySector
ClientRoleType
competitor
lead_source
ClientCompanyType
Country
EngagementStatus
bidding_method
oppEventDict
ContractType
OpprRiskLevel
high_risk_level
OppoStageStatus
OpprOverseasList
engBudgetStatus
ServiceLine
SubServiceLine
SubManagementUnit
OperatingUnit
MarketSegment
ManagementUnit
ManagementRegion
GeoRegion
CountryRegion
CompetencyMercury
Competency
City
BusinessUnit
Area
         */
        try {
            XxlJobHelper.log("process dict start");
            processCategory("OpprStatus");
            XxlJobHelper.log("process dict end");
        } catch (Exception e) {
            logger.error("process dict error", e);
        }
        try {
            XxlJobHelper.log("process dict start");
            processCategory("LegalEntity");
            XxlJobHelper.log("process dict end");
        } catch (Exception e) {
            logger.error("process dict error", e);
        }


        //******************************** 1.psmprimeoffice *******************************
        try {
            XxlJobHelper.log("process psmprimeoffice start");
            processCategoryWithParent(DictConstants.TIGER_DICT_CODE_PRIME_OFFICE, DictConstants.TARGET_DICT_CODE_PRIME_OFFICE);
            XxlJobHelper.log("process psmprimeoffice end");
        } catch (Exception e) {
            logger.error("process sector error", e);
        }

        //******************************** 2.win_reason *******************************
        try {
            XxlJobHelper.log("process win_reason start");
            processCategory(DictConstants.TIGER_DICT_CODE_WON_REASON, DictConstants.TARGET_DICT_CODE_WON_REASON);
            XxlJobHelper.log("process win_reason end");
        } catch (Exception e) {
            logger.error("process win_reason error", e);
        }

        //******************************** 3.Currency *******************************
        try {
            XxlJobHelper.log("process Currency start");
            processCategory(DictConstants.TIGER_DICT_CODE_CURRENCY, DictConstants.TARGET_DICT_CODE_CURRENCY);
            XxlJobHelper.log("process Currency end");
        } catch (Exception e) {
            logger.error("process Currency error", e);
        }

        //******************************** 4.ClientChannel *******************************
        try {
            XxlJobHelper.log("process ClientChannel start");
            processCategory(DictConstants.TIGER_DICT_CODE_CHANNEL, DictConstants.TARGET_DICT_CODE_CHANNEL);
            XxlJobHelper.log("process ClientChannel end");
        } catch (Exception e) {
            logger.error("process ClientChannel error", e);
        }

        //******************************** 5.ClientCustomerClassification *******************************
        try {
            XxlJobHelper.log("process ClientCustomerClassification start");
            processCategory(DictConstants.TIGER_DICT_CODE_CUSTOMER_CLASS, DictConstants.TARGET_DICT_CODE_CUSTOMER_CLASS);
            XxlJobHelper.log("process ClientCustomerClassification end");
        } catch (Exception e) {
            logger.error("process ClientCustomerClassification error", e);
        }

        //******************************** 6.Company_Type *******************************
        try {
            XxlJobHelper.log("process Company_Type start");
            processCategory(DictConstants.TIGER_DICT_CODE_CLIENT_TYPE, DictConstants.TARGET_DICT_CODE_CLIENT_TYPE);
            XxlJobHelper.log("process Company_Type end");
        } catch (Exception e) {
            logger.error("process clientType error", e);
        }

        //******************************** 7.stockmarket *******************************
        try {
            XxlJobHelper.log("process stockmarket start");
            processCategory(DictConstants.TIGER_DICT_CODE_STOCK_MARKET, DictConstants.TARGET_DICT_CODE_STOCK_MARKET);
            XxlJobHelper.log("process stockmarket end");
        } catch (Exception e) {
            logger.error("process stockmarket error", e);
        }

        //******************************** 8.clientarearegion *******************************
        try {
            XxlJobHelper.log("process clientarearegion start");
            processCategory(DictConstants.TIGER_DICT_CODE_CLIENT_REGION, DictConstants.TARGET_DICT_CODE_CLIENT_REGION);
            XxlJobHelper.log("process clientarearegion end");
        } catch (Exception e) {
            logger.error("process clientarearegion error", e);
        }

        //******************************** 9.sector *******************************
        try {
            XxlJobHelper.log("process sector start");
            processSector(DictConstants.TIGER_DICT_CODE_SECTOR, DictConstants.TARGET_DICT_CODE_SECTOR);
            XxlJobHelper.log("process sector end");
        } catch (Exception e) {
            logger.error("process ClientChannel error", e);
        }

        //******************************** 10.officeLocation *******************************
        try {
            XxlJobHelper.log("process officeLocation start");
            processOfficeLocation(DictConstants.TARGET_DICT_CODE_OFFICE_LOCATION);
            XxlJobHelper.log("process officeLocation end");
        } catch (Exception e) {
            logger.error("process officeLocation error", e);
        }
        //******************************** 11.source *******************************
        try {
            XxlJobHelper.log("process source start");
            processCategoryWithParent(DictConstants.TIGER_DICT_CODE_LEAD_SOURCE, DictConstants.TARGET_DICT_CODE_LEAD_SOURCE);
            XxlJobHelper.log("process source end");
        } catch (Exception e) {
            logger.error("process source error", e);
        }
        //******************************** 12.recurring *******************************
        try {
            XxlJobHelper.log("process recurring start");
            processCategory(DictConstants.TIGER_DICT_CODE_OPPR_RECURRING, DictConstants.TARGET_DICT_CODE_RECURRING);
            XxlJobHelper.log("process recurring end");
        } catch (Exception e) {
            logger.error("process recurring error", e);
        }
        //******************************** 13.OpprOverseasList *******************************
        try {
            XxlJobHelper.log("process OpprOverseasList start");
            processCategoryWithParent(DictConstants.TIGER_DICT_CODE_OPPR_OVERSEAS, DictConstants.TARGET_DICT_CODE_OPPR_OVERSEAS);
            XxlJobHelper.log("process OpprOverseasList end");
        } catch (Exception e) {
            logger.error("process OpprOverseasList error", e);
        }
        //******************************** 14.FinMarketSegment *******************************
        try {
            XxlJobHelper.log("process FinMarketSegment start");
            processFinMarketSegment();
            XxlJobHelper.log("process FinMarketSegment end");
        } catch (Exception e) {
            logger.error("process FinMarketSegment error", e);
        }
        //******************************** 15.GlobalServiceCode *******************************
        try {
            XxlJobHelper.log("process GlobalServiceCode start");
            processGlobalServiceCode();
            XxlJobHelper.log("process GlobalServiceCode end");
        } catch (Exception e) {
            logger.error("process GlobalServiceCode error", e);
        }
        //******************************** 16.SmuMccMscRef *******************************
        try {
            XxlJobHelper.log("process SmuMccMscRef start");
            processSmuMccMsc();
            XxlJobHelper.log("process SmuMccMscRef end");
        } catch (Exception e) {
            logger.error("process SmuMccMscRef error", e);
        }

    }

    private TokenResponseDTO getToken() {
        String tokenUrl = url + tokenPath;
        TokenRequestDTO tokenRequestDTO = new TokenRequestDTO();
        tokenRequestDTO.setClientId(clientId);
        tokenRequestDTO.setAppId(appId);
        tokenRequestDTO.setAppSecret(appSecret);
        return restTemplate.postForObject(tokenUrl, tokenRequestDTO, TokenResponseDTO.class);
    }

    private Dictionary getDictionary(String dictCode) {
        Dictionary dictionary = new Dictionary();
        dictionary.setDictCode(dictCode);
        Dictionary dictionary1 = dictionaryService.queryOneByPara(dictionary);
        if(EmptyUtils.isEmpty(dictionary1)){
            dictionary1 = new Dictionary();
            dictionary1.setDictCode(dictCode);
            dictionary1.setDictName(dictCode);
            dictionary1.setDictType(1);
            dictionary1.setStatus(1);
            dictionaryService.save(dictionary1);
        }
        return dictionary1;

    }

    private Map<String, DictionaryItem> getDictionaryItem(String dictCode) {
        Dictionary dictionary = new Dictionary();
        dictionary.setDictCode(dictCode);
        dictionary = dictionaryService.queryOneByPara(dictionary);
        DictionaryItem qDictionaryItem = new DictionaryItem();
        qDictionaryItem.setDictId(dictionary.getId());
        List<DictionaryItem> items = dictionaryItemService.queryByPara(qDictionaryItem);
        Map<String, DictionaryItem> itemMap = new HashMap<>();
        for (DictionaryItem item : items) {
            itemMap.put(item.getTag(), item);
        }
        return itemMap;
    }

    private CategoryResponseDTO getCategoryData(String categoryCode) {
        String tokenUrl = url + categoryPath;
        CategoryRequestDTO categoryRequestDTO = new CategoryRequestDTO();
        categoryRequestDTO.setCategoryCode(categoryCode);
        categoryRequestDTO.setAcceptLanguage("en-US");
        if(DictConstants.TIGER_DICT_CODE_SECTOR.equals(categoryCode)){
            categoryRequestDTO.setStatus(3);
        }

        TokenResponseDTO tokenResponseDTO = this.getToken();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Authorization", "Bearer " + tokenResponseDTO.getData().getAccessToken());
        headers.add("client-id", clientId);

        HttpEntity<CategoryRequestDTO> requestEntity = new HttpEntity<>(categoryRequestDTO, headers);
        CategoryResponseDTO categoryResponseDTO = restTemplate.postForObject(tokenUrl, requestEntity, CategoryResponseDTO.class);

        /*System.out.println("************************ "+ categoryCode + "************************");
        for(CategoryDTO categoryDTO:categoryResponseDTO.getData()){
            System.out.println(categoryDTO.getDataCode() + "\t" + categoryDTO.getDataName() + "\t" +
                    categoryDTO.getDataIdentifier() + "\t" + categoryDTO.getDataParentIdentifier());
        }*/
        return categoryResponseDTO;
    }

    private DictResponseDTO getDictData(String categoryCode) {
        String tokenUrl = url + dictPath;
        //将categoryCode作为param拼接到url上
        tokenUrl = tokenUrl + "?categoryCode=" + categoryCode;
        //拼接functionType=default
        tokenUrl = tokenUrl + "&functionType=default";

        TokenResponseDTO tokenResponseDTO = this.getToken();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Authorization", "Bearer " + tokenResponseDTO.getData().getAccessToken());
        headers.add("client-id", clientId);

        HttpEntity<CategoryRequestDTO> requestEntity = new HttpEntity<>(headers);
        DictResponseDTO categoryResponseDTO = restTemplate.postForObject(tokenUrl, requestEntity, DictResponseDTO.class);
        logger.info("categoryResponseDTO:{}", categoryResponseDTO);
        /*System.out.println("************************ "+ categoryCode + "************************");
        for(CategoryDTO categoryDTO:categoryResponseDTO.getData()){
            System.out.println(categoryDTO.getDataCode() + "\t" + categoryDTO.getDataName() + "\t" +
                    categoryDTO.getDataIdentifier() + "\t" + categoryDTO.getDataParentIdentifier());
        }*/
        return categoryResponseDTO;
    }

    private SmuResponseDTO getSmuData(String categoryCode) {
        String tokenUrl = url + dictPath;
        //将categoryCode作为param拼接到url上
        tokenUrl = tokenUrl + "?categoryCode=" + categoryCode;
        //拼接functionType=default
        tokenUrl = tokenUrl + "&functionType=default";

        TokenResponseDTO tokenResponseDTO = this.getToken();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Authorization", "Bearer " + tokenResponseDTO.getData().getAccessToken());
        headers.add("client-id", clientId);

        HttpEntity<CategoryRequestDTO> requestEntity = new HttpEntity<>(headers);
        SmuResponseDTO categoryResponseDTO = restTemplate.postForObject(tokenUrl, requestEntity, SmuResponseDTO.class);
        logger.info("categoryResponseDTO:{}", categoryResponseDTO);
        /*System.out.println("************************ "+ categoryCode + "************************");
        for(CategoryDTO categoryDTO:categoryResponseDTO.getData()){
            System.out.println(categoryDTO.getDataCode() + "\t" + categoryDTO.getDataName() + "\t" +
                    categoryDTO.getDataIdentifier() + "\t" + categoryDTO.getDataParentIdentifier());
        }*/
        return categoryResponseDTO;
    }

    private void processCategoryWithParent(String fromCode, String toCode) {
        //调用码表接口获取数据
        CategoryResponseDTO categoryResponseDTO = getCategoryData(fromCode);
        if (EmptyUtils.isEmpty(categoryResponseDTO.getData())) {
            logger.warn("tiger category data empty: {}", fromCode);
            return;
        }
        //获取数据字典
        Dictionary dictionary = getDictionary(toCode);
        Map<String, DictionaryItem> itemMap = getDictionaryItem(toCode);
        List<DictionaryItem> updateItems = new ArrayList<>();
        List<DictionaryItem> insertItems = new ArrayList<>();
        //记录id关系
        Map<String, String> idMap = new HashMap<>();
        List<DictionaryItem> hasParentList = new ArrayList<>();
        int orderNum = 0;
        for (CategoryDTO categoryDTO : categoryResponseDTO.getData()) {
            String dataCode = "tiger-" + categoryDTO.getDataCode();
            String oldId = categoryDTO.getDataIdentifier();
            String parentId = categoryDTO.getDataParentIdentifier();
            String newId;
            DictionaryItem dictionaryItem = itemMap.get(dataCode);
            if (EmptyUtils.isEmpty(dictionaryItem)) {
                orderNum = orderNum + 1;
                DictionaryItem newDictionaryItem = new DictionaryItem();
                newId = DataUtils.getUUID32();
                newDictionaryItem.setId(newId);
                newDictionaryItem.setDictId(dictionary.getId());
                newDictionaryItem.setItemKey(dataCode);
                newDictionaryItem.setItemValue(categoryDTO.getDataName());
                newDictionaryItem.setTag(dataCode);
                newDictionaryItem.setItemParent(parentId);
                newDictionaryItem.setOrderNum(orderNum);
                newDictionaryItem.setExtTwo("new");
                insertItems.add(newDictionaryItem);
                if(EmptyUtils.isNotEmpty(parentId)){
                    hasParentList.add(newDictionaryItem);
                }
            } else {
                newId = dictionaryItem.getId();
                dictionaryItem.setItemValue(categoryDTO.getDataName());
                dictionaryItem.setItemParent(parentId);
                dictionaryItem.setExtTwo("update");
                updateItems.add(dictionaryItem);
                if(EmptyUtils.isNotEmpty(parentId)){
                    hasParentList.add(dictionaryItem);
                }
            }
            idMap.put(oldId, newId);
        }
        for (DictionaryItem dictionaryItem : hasParentList) {
            dictionaryItem.setItemParent(idMap.get(dictionaryItem.getItemParent()));
        }
        XxlJobHelper.log("updateSize: " + updateItems.size());
        dictionaryItemService.updateBatchByIds(updateItems);
        XxlJobHelper.log("insertSize: " + insertItems.size());
        dictionaryItemService.saveForJob(insertItems);
    }
    private void processCategoryWithParent(String fromCode, CategoryResponseDTO categoryResponseDTO) {
        //获取数据字典
        Dictionary dictionary = getDictionary(fromCode);
        Map<String, DictionaryItem> itemMap = getDictionaryItem(fromCode);
        List<DictionaryItem> updateItems = new ArrayList<>();
        List<DictionaryItem> insertItems = new ArrayList<>();
        //记录id关系
        Map<String, String> idMap = new HashMap<>();
        List<DictionaryItem> hasParentList = new ArrayList<>();
        int orderNum = 0;
        for (CategoryDTO categoryDTO : categoryResponseDTO.getData()) {
            String dataCode = "tiger-" + categoryDTO.getDataCode();
            String oldId = categoryDTO.getDataIdentifier();
            String parentId = categoryDTO.getDataParentIdentifier();
            String newId;
            DictionaryItem dictionaryItem = itemMap.get(dataCode);
            if (EmptyUtils.isEmpty(dictionaryItem)) {
                orderNum = orderNum + 1;
                DictionaryItem newDictionaryItem = new DictionaryItem();
                newId = DataUtils.getUUID32();
                newDictionaryItem.setId(newId);
                newDictionaryItem.setDictId(dictionary.getId());
                newDictionaryItem.setItemKey(dataCode);
                newDictionaryItem.setItemValue(categoryDTO.getDataName());
                newDictionaryItem.setTag(dataCode);
                newDictionaryItem.setItemParent(parentId);
                newDictionaryItem.setOrderNum(orderNum);
                newDictionaryItem.setExtTwo("new");
                insertItems.add(newDictionaryItem);
                if(EmptyUtils.isNotEmpty(parentId)){
                    hasParentList.add(newDictionaryItem);
                }
            } else {
                newId = dictionaryItem.getId();
                dictionaryItem.setItemValue(categoryDTO.getDataName());
                dictionaryItem.setItemParent(parentId);
                dictionaryItem.setExtTwo("update");
                updateItems.add(dictionaryItem);
                if(EmptyUtils.isNotEmpty(parentId)){
                    hasParentList.add(dictionaryItem);
                }
            }
            idMap.put(oldId, newId);
        }
        for (DictionaryItem dictionaryItem : hasParentList) {
            dictionaryItem.setItemParent(idMap.get(dictionaryItem.getItemParent()));
        }
        XxlJobHelper.log("updateSize: " + updateItems.size());
        dictionaryItemService.updateBatchByIds(updateItems);
        XxlJobHelper.log("insertSize: " + insertItems.size());
        dictionaryItemService.saveForJob(insertItems);
    }

    private void processCategory(String fromCode, String toCode) {
        //调用码表接口获取数据
        CategoryResponseDTO categoryResponseDTO = getCategoryData(fromCode);
        if (EmptyUtils.isEmpty(categoryResponseDTO.getData())) {
            logger.warn("tiger category data empty: {}", fromCode);
            return;
        }
        //获取数据字典
        Dictionary dictionary = getDictionary(toCode);
        Map<String, DictionaryItem> itemMap = getDictionaryItem(toCode);
        List<DictionaryItem> updateItems = new ArrayList<>();
        List<DictionaryItem> insertItems = new ArrayList<>();
        int orderNum = 0;

        for (CategoryDTO categoryDTO : categoryResponseDTO.getData()) {
            String dataCode = "tiger-" + categoryDTO.getDataCode();
            DictionaryItem dictionaryItem = itemMap.get(dataCode);
            if (EmptyUtils.isEmpty(dictionaryItem)) {
                orderNum = orderNum + 1;
                DictionaryItem newDictionaryItem = new DictionaryItem();
                newDictionaryItem.setDictId(dictionary.getId());
                newDictionaryItem.setItemKey(dataCode);
                newDictionaryItem.setItemValue(categoryDTO.getDataName());
                newDictionaryItem.setTag(dataCode);
                newDictionaryItem.setOrderNum(orderNum);
                newDictionaryItem.setExtTwo("new");
                insertItems.add(newDictionaryItem);
            } else {
                String itemValue = dictionaryItem.getItemValue();
                if (!categoryDTO.getDataName().equals(itemValue)) {
                    dictionaryItem.setItemValue(categoryDTO.getDataName());
                    dictionaryItem.setExtTwo("update");
                    updateItems.add(dictionaryItem);
                }
            }
        }
        XxlJobHelper.log("updateSize: " + updateItems.size());
        XxlJobHelper.log("insertSize: " + insertItems.size());
        dictionaryItemService.updateBatchByIds(updateItems);
        dictionaryItemService.saveForJob(insertItems);
    }
    private void processCategory(String fromCode) {
        //调用码表接口获取数据
        CategoryResponseDTO categoryResponseDTO = getCategoryData(fromCode);
        if (EmptyUtils.isEmpty(categoryResponseDTO.getData())) {
            logger.warn("tiger category data empty: {}", fromCode);
            return;
        }
        //获取数据字典
        Dictionary dictionary = getDictionary(fromCode);
        Map<String, DictionaryItem> itemMap = getDictionaryItem(fromCode);
        List<DictionaryItem> updateItems = new ArrayList<>();
        List<DictionaryItem> insertItems = new ArrayList<>();
        int orderNum = 0;
        //检查是否有DataParentIdentifier
        boolean hasParent = false;
        for (CategoryDTO categoryDTO : categoryResponseDTO.getData()) {
            if(EmptyUtils.isNotEmpty(categoryDTO.getDataParentIdentifier())){
                hasParent = true;
                break;
            }
        }
        if(hasParent){
            processCategoryWithParent(fromCode, categoryResponseDTO);
            return;
        }
        for (CategoryDTO categoryDTO : categoryResponseDTO.getData()) {
            String dataCode = "tiger-" + categoryDTO.getDataCode();
            DictionaryItem dictionaryItem = itemMap.get(dataCode);
            if (EmptyUtils.isEmpty(dictionaryItem)) {
                orderNum = orderNum + 1;
                DictionaryItem newDictionaryItem = new DictionaryItem();
                newDictionaryItem.setDictId(dictionary.getId());
                newDictionaryItem.setItemKey(dataCode);
                newDictionaryItem.setItemValue(categoryDTO.getDataName());
                newDictionaryItem.setTag(dataCode);
                newDictionaryItem.setOrderNum(orderNum);
                newDictionaryItem.setExtTwo("new");
                insertItems.add(newDictionaryItem);
            } else {
                String itemValue = dictionaryItem.getItemValue();
                if (!categoryDTO.getDataName().equals(itemValue)) {
                    dictionaryItem.setItemValue(categoryDTO.getDataName());
                    dictionaryItem.setExtTwo("update");
                    updateItems.add(dictionaryItem);
                }
            }
        }
        XxlJobHelper.log("updateSize: " + updateItems.size());
        XxlJobHelper.log("insertSize: " + insertItems.size());
        dictionaryItemService.updateBatchByIds(updateItems);
        dictionaryItemService.saveForJob(insertItems);
    }

    private void processSector(String fromCode, String toCode) {
        //调用码表接口获取数据
        CategoryResponseDTO categoryResponseDTO = getCategoryData(fromCode);
        if (EmptyUtils.isEmpty(categoryResponseDTO.getData())) {
            logger.warn("tiger category data empty: {}", fromCode);
            return;
        }
        Dictionary dictionary = new Dictionary();
        dictionary.setDictCode(toCode);
        dictionary = dictionaryService.queryOneByPara(dictionary);
        DictionaryItem qDictionaryItem = new DictionaryItem();
        qDictionaryItem.setDictId(dictionary.getId());
        List<DictionaryItem> items = dictionaryItemService.queryByPara(qDictionaryItem);
        //获取数据字典 key为tigetCode
        Map<String, DictionaryItem> itemMap = new HashMap<>();
        Map<String, DictionaryItem> itemIdMap = new HashMap<>();
        Map<String, String> oldNewIdMap = new HashMap<>();
        for (DictionaryItem item : items) {
            itemMap.put(item.getTag(), item);
            itemIdMap.put(item.getId(), item);
        }

        //过滤出更新和新增的内容
        List<DictionaryItem> updateItems = new ArrayList<>();
        List<DictionaryItem> insertItems = new ArrayList<>();
        int orderNum = 0;
        for (CategoryDTO categoryDTO : categoryResponseDTO.getData()) {
            String dataCode = "tiger-" + categoryDTO.getDataCode();
            DictionaryItem dictionaryItem = itemMap.get(dataCode);
            if (EmptyUtils.isEmpty(dictionaryItem)) {
                orderNum = orderNum + 1;
                DictionaryItem newDictionaryItem = new DictionaryItem();
                newDictionaryItem.setItemKey(dataCode);
                newDictionaryItem.setItemValue(categoryDTO.getDataName());
                newDictionaryItem.setDictId(dictionary.getId());
                newDictionaryItem.setTag(dataCode);
                newDictionaryItem.setOrderNum(orderNum);
                newDictionaryItem.setItemParent(categoryDTO.getDataParentIdentifier());
                String id = DataUtils.getUUID32();
                newDictionaryItem.setId(id);
                oldNewIdMap.put(categoryDTO.getDataIdentifier(), id);
                newDictionaryItem.setExtTwo("new");
                insertItems.add(newDictionaryItem);
            } else {
                oldNewIdMap.put(categoryDTO.getDataIdentifier(), dictionaryItem.getId());
                String categoryParent = categoryDTO.getDataParentIdentifier();
                dictionaryItem.setItemValue(categoryDTO.getDataName());
                dictionaryItem.setItemParent(categoryParent);
                dictionaryItem.setExtTwo("update");
                updateItems.add(dictionaryItem);
            }
        }
        //处理parentId
        if (EmptyUtils.isNotEmpty(updateItems)) {
            for (DictionaryItem dictionaryItem : updateItems) {
                String itemParent = dictionaryItem.getItemParent();
                String parentId = oldNewIdMap.get(itemParent);
                dictionaryItem.setItemParent(parentId);
            }
        }
        if (EmptyUtils.isNotEmpty(insertItems)) {
            for (DictionaryItem dictionaryItem : insertItems) {
                String itemParent = dictionaryItem.getItemParent();
                String parentId = oldNewIdMap.get(itemParent);
                dictionaryItem.setItemParent(parentId);
            }
        }
        XxlJobHelper.log("updateSize: " + updateItems.size());
        XxlJobHelper.log("insertSize: " + insertItems.size());
        dictionaryItemService.updateBatchByIds(updateItems);
        dictionaryItemService.saveForJob(insertItems);
    }

    private void processOfficeLocation(String toCode) {
        //调用码表接口获取数据
        List<SyncTaxLocation> syncTaxLocationList = syncTaxLocationService.queryByPara(new SyncTaxLocation());
        if (EmptyUtils.isEmpty(syncTaxLocationList)) {
            logger.warn("officeLocation empty");
            return;
        }
        //获取数据字典
        Dictionary dictionary = getDictionary(toCode);
        Map<String, DictionaryItem> itemMap = getDictionaryItem(toCode);
        List<DictionaryItem> updateItems = new ArrayList<>();
        List<DictionaryItem> insertItems = new ArrayList<>();
        int orderNum = 100;
        for (SyncTaxLocation syncTaxLocation : syncTaxLocationList) {
            String dataCode = "tiger-" + syncTaxLocation.getCountryCode();
            DictionaryItem dictionaryItem = itemMap.get(dataCode);
            if (EmptyUtils.isEmpty(dictionaryItem)) {
                orderNum = orderNum + 1;
                DictionaryItem newDictionaryItem = new DictionaryItem();
                newDictionaryItem.setDictId(dictionary.getId());
                newDictionaryItem.setItemKey(dataCode);
                newDictionaryItem.setItemValue(syncTaxLocation.getCountryName());
                newDictionaryItem.setTag(dataCode);
                newDictionaryItem.setExtTwo("new");
                newDictionaryItem.setOrderNum(orderNum);
                insertItems.add(newDictionaryItem);
            } else {
                String itemValue = dictionaryItem.getItemValue();
                if (!syncTaxLocation.getCountryName().equals(itemValue)) {
                    dictionaryItem.setItemValue(syncTaxLocation.getCountryName());
                    dictionaryItem.setExtTwo("update");
                    updateItems.add(dictionaryItem);
                }
            }
        }
        XxlJobHelper.log("updateSize: " + updateItems.size());
        XxlJobHelper.log("insertSize: " + insertItems.size());
        dictionaryItemService.updateBatchByIds(updateItems);
        dictionaryItemService.saveForJob(insertItems);
    }


    private void processGlobalServiceCode(){
        String fromCode = DictConstants.TIGER_DICT_CODE_GLOBAL_SERVICE_CODE;
        String toCode = DictConstants.TARGET_DICT_CODE_GLOBAL_SERVICE_CODE;
        //调用码表接口获取数据
        DictResponseDTO dictResponseDTO = getDictData(fromCode);
        if (EmptyUtils.isEmpty(dictResponseDTO.getData())) {
            logger.warn("tiger category data empty: {}", fromCode);
            return;
        }
        //获取数据字典
        Dictionary dictionary = getDictionary(toCode);
        Map<String, DictionaryItem> itemMap = getDictionaryItem(toCode);
        List<DictionaryItem> updateItems = new ArrayList<>();
        List<DictionaryItem> insertItems = new ArrayList<>();
        int orderNum = 0;
        for (Map<String,Object> map : dictResponseDTO.getData()) {
            String dataCode = "tiger-" + map.get("gscCode").toString();
            DictionaryItem dictionaryItem = itemMap.get(dataCode);
            if (EmptyUtils.isEmpty(dictionaryItem)) {
                orderNum = orderNum + 1;
                DictionaryItem newDictionaryItem = new DictionaryItem();
                newDictionaryItem.setDictId(dictionary.getId());
                newDictionaryItem.setItemKey(dataCode);
                newDictionaryItem.setItemValue(map.get("gscName").toString());
                newDictionaryItem.setTag(dataCode);
                newDictionaryItem.setOrderNum(orderNum);
                newDictionaryItem.setExtTwo("new");
                insertItems.add(newDictionaryItem);
            } else {
                String itemValue = dictionaryItem.getItemValue();
                if (!map.get("gscName").toString().equals(itemValue)) {
                    dictionaryItem.setItemValue(map.get("gscName").toString());
                    dictionaryItem.setExtTwo("update");
                    updateItems.add(dictionaryItem);
                }
            }
        }
        XxlJobHelper.log("updateSize: " + updateItems.size());
        XxlJobHelper.log("insertSize: " + insertItems.size());
        dictionaryItemService.updateBatchByIds(updateItems);
        dictionaryItemService.saveForJob(insertItems);
    }
    //SmuMccMscRef
    private void processFinMarketSegment(){
        String fromCode = DictConstants.TIGER_DICT_CODE_FIN_MARKET_SEGMENT;
        String toCode = DictConstants.TARGET_DICT_CODE_FIN_MARKET_SEGMENT;
        //调用码表接口获取数据
        DictResponseDTO dictResponseDTO = getDictData(fromCode);
        if (EmptyUtils.isEmpty(dictResponseDTO.getData())) {
            logger.warn("tiger category data empty: {}", fromCode);
            return;
        }
        //获取数据字典
        Dictionary dictionary = getDictionary(toCode);
        Map<String, DictionaryItem> itemMap = getDictionaryItem(toCode);
        List<DictionaryItem> updateItems = new ArrayList<>();
        List<DictionaryItem> insertItems = new ArrayList<>();
        int orderNum = 0;
        for (Map<String,Object> map : dictResponseDTO.getData()) {
            String dataCode = "tiger-" + map.get("marketSegmentCode").toString();
            DictionaryItem dictionaryItem = itemMap.get(dataCode);
            if (EmptyUtils.isEmpty(dictionaryItem)) {
                orderNum = orderNum + 1;
                DictionaryItem newDictionaryItem = new DictionaryItem();
                newDictionaryItem.setDictId(dictionary.getId());
                newDictionaryItem.setItemKey(dataCode);
                newDictionaryItem.setItemValue(map.get("marketSegmentName").toString());
                newDictionaryItem.setTag(dataCode);
                newDictionaryItem.setOrderNum(orderNum);
                newDictionaryItem.setExtTwo("new");
                insertItems.add(newDictionaryItem);
            } else {
                String itemValue = dictionaryItem.getItemValue();
                if (!map.get("marketSegmentName").toString().equals(itemValue)) {
                    dictionaryItem.setItemValue(map.get("marketSegmentName").toString());
                    dictionaryItem.setExtTwo("update");
                    updateItems.add(dictionaryItem);
                }
            }
        }
        XxlJobHelper.log("updateSize: " + updateItems.size());
        XxlJobHelper.log("insertSize: " + insertItems.size());
        dictionaryItemService.updateBatchByIds(updateItems);
        dictionaryItemService.saveForJob(insertItems);
    }

    //SmuMccMsc
    private void processSmuMccMsc(){
        String fromCode = DictConstants.TIGER_DICT_CODE_SMU_MCC_MSC;
        String toCode = DictConstants.TARGET_DICT_CODE_SMU_MCC_MSC;
        //调用码表接口获取数据
        SmuResponseDTO smuData = getSmuData(fromCode);
        if (EmptyUtils.isEmpty(smuData.getData())) {
            logger.warn("tiger category data empty: {}", fromCode);
            return;
        }
        //先删再插
        List<SyncSmuMercury> list = smuData.getData();
        syncSmuMercuryService.deleteByPara(new SyncSmuMercury());
        syncSmuMercuryService.save(list);
        XxlJobHelper.log("insertSize: " + list.size());
    }


    @Override
    public void clear() {

    }

}
