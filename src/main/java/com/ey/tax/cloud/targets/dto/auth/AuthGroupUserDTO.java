package com.ey.tax.cloud.targets.dto.auth;

import com.ey.cn.tax.framework.dto.BaseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2023-12-22 10:32:04
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "默认实体")
public class AuthGroupUserDTO extends BaseDTO {
    /**
     * 权限组ID COLUMN:group_id
     */
    @Schema(description = "权限组ID")
    private String groupId;

    /**
     * 用户ID COLUMN:user_id
     */
    @Schema(description = "用户ID")
    private String userId;
}
