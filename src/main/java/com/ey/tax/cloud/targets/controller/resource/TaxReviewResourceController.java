package com.ey.tax.cloud.targets.controller.resource;

import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.dto.SearchDTO;
import com.ey.cn.tax.framework.validator.AddGroup;
import com.ey.cn.tax.framework.validator.DeleteByIdGroup;
import com.ey.cn.tax.framework.validator.DeleteByIdListGroup;
import com.ey.cn.tax.framework.validator.QueryByIdGroup;
import com.ey.cn.tax.framework.validator.QueryByIdListGroup;
import com.ey.cn.tax.framework.validator.QueryPageGroup;
import com.ey.cn.tax.framework.validator.UpdateByIdGroup;
import com.ey.cn.tax.framework.web.annotation.EyPostMapping;
import com.ey.cn.tax.framework.web.base.BaseController;
import com.ey.tax.cloud.targets.dto.resource.TaxReviewResourceAddDTO;
import com.ey.tax.cloud.targets.dto.resource.TaxReviewResourceBatchUpdateDTO;
import com.ey.tax.cloud.targets.dto.resource.TaxReviewResourceDTO;
import com.ey.tax.cloud.targets.dto.resource.TaxReviewResourceDeleteByIdDTO;
import com.ey.tax.cloud.targets.dto.resource.TaxReviewResourceDeleteByIdListDTO;
import com.ey.tax.cloud.targets.dto.resource.TaxReviewResourceQueryByIdDTO;
import com.ey.tax.cloud.targets.dto.resource.TaxReviewResourceQueryByIdListDTO;
import com.ey.tax.cloud.targets.dto.resource.TaxReviewResourceQueryDTO;
import com.ey.tax.cloud.targets.dto.resource.TaxReviewResourceQueryPageDTO;
import com.ey.tax.cloud.targets.dto.resource.TaxReviewResourceRepDTO;
import com.ey.tax.cloud.targets.dto.resource.TaxReviewResourceUpdateByIdDTO;
import com.ey.tax.cloud.targets.dto.user.UserAttributeQueryDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-01-17 14:35:37
 *
 */
@Tag(name="TaxReview导入表")
public interface TaxReviewResourceController extends BaseController<TaxReviewResourceDTO> {
    /**
     * add TaxReview from http
     */
    @Operation(summary = "新增", description = "新增一条数据")
    @EyPostMapping(value = "/add.do")
    ResponseDTO<Void> add(@RequestBody @Validated({AddGroup.class}) TaxReviewResourceAddDTO dto);

    /**
     * addList TaxReview from http
     */
    @Operation(summary = "批量新增", description = "批量新增数据")
    @EyPostMapping(value = "/addList.do")
    ResponseDTO<Void> addList(@RequestBody List<TaxReviewResourceAddDTO> dtos);

    /**
     * delete TaxReview from http
     */
    @Operation(summary = "根据id删除", description = "根据id删除一条数据")
    @EyPostMapping(value = "/deleteById.do")
    ResponseDTO<Void> deleteById(@RequestBody @Validated({DeleteByIdGroup.class}) TaxReviewResourceDeleteByIdDTO dto);

    /**
     * deleteList TaxReview from http
     */
    @Operation(summary = "根据id列表删除", description = "根据id列表批量删除数据")
    @EyPostMapping(value = "/deleteByIdList.do")
    ResponseDTO<Void> deleteByIdList(@RequestBody @Validated({DeleteByIdListGroup.class}) TaxReviewResourceDeleteByIdListDTO dto);

    /**
     * update TaxReview from http
     */
    @Operation(summary = "根据id更新", description = "根据id更新一条数据")
    @EyPostMapping(value = "/updateById.do")
    ResponseDTO<Void> updateById(@RequestBody @Validated({UpdateByIdGroup.class}) TaxReviewResourceUpdateByIdDTO dto);

    /**
     * updateBatch TaxReview from http
     */
    @Operation(summary = "批量更新", description = "批量更新数据")
    @EyPostMapping(value = "/updateBatch.do")
    ResponseDTO<Void> updateBatch(@RequestBody List<TaxReviewResourceBatchUpdateDTO> dtos);

    /**
     * query TaxReview from http
     */
    @Operation(summary = "根据id查询", description = "根据id查询数据")
    @EyPostMapping(value = "/queryById.do")
    ResponseDTO<TaxReviewResourceRepDTO> queryById(@RequestBody @Validated({QueryByIdGroup.class}) TaxReviewResourceQueryByIdDTO dto);

    /**
     * queryByIdList TaxReview from http
     */
    @Operation(summary = "根据id列表查询", description = "根据id列表查询数据")
    @EyPostMapping(value = "/queryByIdList.do")
    ResponseDTO<List<TaxReviewResourceRepDTO>> queryByIdList(@RequestBody @Validated({QueryByIdListGroup.class}) TaxReviewResourceQueryByIdListDTO dto);

    /**
     * queryList TaxReview from http
     */
    @Operation(summary = "查询列表", description = "查询数据列表")
    @EyPostMapping(value = "/queryList.do")
    ResponseDTO<List<TaxReviewResourceRepDTO>> queryList(@RequestBody TaxReviewResourceQueryDTO dto);

    /**
     * Page TaxReview from http
     */
    @Operation(summary = "分页查询", description = "根据条件分页查询")
    @EyPostMapping(value = "/queryPage.do")
    ResponseDTO<SearchDTO<TaxReviewResourceRepDTO>> queryPage(@RequestBody @Validated({QueryPageGroup.class}) SearchDTO<TaxReviewResourceQueryPageDTO> searchDTO);

    /**
     * Count TaxReview from http
     */
    @Operation(summary = "查询数量", description = "根据条件查询数量")
    @EyPostMapping(value = "/queryCount.do")
    ResponseDTO<Long> queryCount(@RequestBody TaxReviewResourceQueryDTO taxReviewDTO);

    /**
     * One TaxReview from http
     */
    @Operation(summary = "查询单个数据", description = "根据条件查询一条数据,如果查询到多条则返回异常.")
    @EyPostMapping(value = "/queryOne.do")
    ResponseDTO<TaxReviewResourceRepDTO> queryOne(@RequestBody TaxReviewResourceQueryDTO taxReviewDTO);

    /**
     * exist TaxReview from http
     */
    @Operation(summary = "查询是否存在", description = "根据条件查询是否存在")
    @EyPostMapping(value = "/exist.do")
    ResponseDTO<Boolean> exist(@RequestBody TaxReviewResourceQueryDTO taxReviewDTO);



}
