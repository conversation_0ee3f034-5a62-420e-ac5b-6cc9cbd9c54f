package com.ey.tax.cloud.targets.service.auth;

import com.ey.cn.tax.framework.service.BaseService;
import com.ey.tax.cloud.targets.entity.auth.AuthCacheResult;
import com.ey.tax.cloud.targets.entity.auth.AuthGroup;
import com.ey.tax.cloud.targets.entity.auth.AuthGroupDetail;

import java.util.List;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2023-12-22 10:32:03
 *
 */
public interface AuthGroupService extends BaseService<AuthGroup> {

    List<List<AuthGroupDetail>> getByUserIdAndMenu(String userId, String menuCode,String fiscalYear);


    List<AuthGroupDetail> setDetailMap(AuthGroupDetail authGroupDetail,String fiscalYear);

    AuthCacheResult getByUserIdAndMenuResult(String usrId, String menuCode, String fiscalYear);
}
