package com.ey.tax.cloud.targets.controller.taxReview;

import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.dto.SearchDTO;
import com.ey.cn.tax.framework.validator.AddGroup;
import com.ey.cn.tax.framework.validator.DeleteByIdGroup;
import com.ey.cn.tax.framework.validator.DeleteByIdListGroup;
import com.ey.cn.tax.framework.validator.QueryByIdGroup;
import com.ey.cn.tax.framework.validator.QueryByIdListGroup;
import com.ey.cn.tax.framework.validator.QueryPageGroup;
import com.ey.cn.tax.framework.validator.UpdateByIdGroup;
import com.ey.cn.tax.framework.web.annotation.EyPostMapping;
import com.ey.cn.tax.framework.web.base.BaseController;
import com.ey.tax.cloud.targets.dto.taxReview.TaxReviewMemberAddDTO;
import com.ey.tax.cloud.targets.dto.taxReview.TaxReviewMemberBatchUpdateDTO;
import com.ey.tax.cloud.targets.dto.taxReview.TaxReviewMemberDTO;
import com.ey.tax.cloud.targets.dto.taxReview.TaxReviewMemberDeleteByIdDTO;
import com.ey.tax.cloud.targets.dto.taxReview.TaxReviewMemberDeleteByIdListDTO;
import com.ey.tax.cloud.targets.dto.taxReview.TaxReviewMemberQueryByIdDTO;
import com.ey.tax.cloud.targets.dto.taxReview.TaxReviewMemberQueryByIdListDTO;
import com.ey.tax.cloud.targets.dto.taxReview.TaxReviewMemberQueryDTO;
import com.ey.tax.cloud.targets.dto.taxReview.TaxReviewMemberQueryPageDTO;
import com.ey.tax.cloud.targets.dto.taxReview.TaxReviewMemberRepDTO;
import com.ey.tax.cloud.targets.dto.taxReview.TaxReviewMemberUpdateByIdDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-02-26 11:15:06
 *
 */
@Tag(name="taxReview职员表")
public interface TaxReviewMemberController extends BaseController<TaxReviewMemberDTO> {
    /**
     * add TaxReviewMember from http
     */
    @Operation(summary = "新增", description = "新增一条数据")
    @EyPostMapping(value = "/add.do")
    ResponseDTO<Void> add(@RequestBody @Validated({AddGroup.class}) TaxReviewMemberAddDTO dto);

    /**
     * addList TaxReviewMember from http
     */
    @Operation(summary = "批量新增", description = "批量新增数据")
    @EyPostMapping(value = "/addList.do")
    ResponseDTO<Void> addList(@RequestBody List<TaxReviewMemberAddDTO> dtos);

    /**
     * delete TaxReviewMember from http
     */
    @Operation(summary = "根据id删除", description = "根据id删除一条数据")
    @EyPostMapping(value = "/deleteById.do")
    ResponseDTO<Void> deleteById(@RequestBody @Validated({DeleteByIdGroup.class}) TaxReviewMemberDeleteByIdDTO dto);

    /**
     * deleteList TaxReviewMember from http
     */
    @Operation(summary = "根据id列表删除", description = "根据id列表批量删除数据")
    @EyPostMapping(value = "/deleteByIdList.do")
    ResponseDTO<Void> deleteByIdList(@RequestBody @Validated({DeleteByIdListGroup.class}) TaxReviewMemberDeleteByIdListDTO dto);

    /**
     * update TaxReviewMember from http
     */
    @Operation(summary = "根据id更新", description = "根据id更新一条数据")
    @EyPostMapping(value = "/updateById.do")
    ResponseDTO<Void> updateById(@RequestBody @Validated({UpdateByIdGroup.class}) TaxReviewMemberUpdateByIdDTO dto);

    /**
     * updateBatch TaxReviewMember from http
     */
    @Operation(summary = "批量更新", description = "批量更新数据")
    @EyPostMapping(value = "/updateBatch.do")
    ResponseDTO<Void> updateBatch(@RequestBody List<TaxReviewMemberBatchUpdateDTO> dtos);

    /**
     * query TaxReviewMember from http
     */
    @Operation(summary = "根据id查询", description = "根据id查询数据")
    @EyPostMapping(value = "/queryById.do")
    ResponseDTO<TaxReviewMemberRepDTO> queryById(@RequestBody @Validated({QueryByIdGroup.class}) TaxReviewMemberQueryByIdDTO dto);

    /**
     * queryByIdList TaxReviewMember from http
     */
    @Operation(summary = "根据id列表查询", description = "根据id列表查询数据")
    @EyPostMapping(value = "/queryByIdList.do")
    ResponseDTO<List<TaxReviewMemberRepDTO>> queryByIdList(@RequestBody @Validated({QueryByIdListGroup.class}) TaxReviewMemberQueryByIdListDTO dto);

    /**
     * queryList TaxReviewMember from http
     */
    @Operation(summary = "查询列表", description = "查询数据列表")
    @EyPostMapping(value = "/queryList.do")
    ResponseDTO<List<TaxReviewMemberRepDTO>> queryList(@RequestBody TaxReviewMemberQueryDTO dto);

    /**
     * Page TaxReviewMember from http
     */
    @Operation(summary = "分页查询", description = "根据条件分页查询")
    @EyPostMapping(value = "/queryPage.do")
    ResponseDTO<SearchDTO<TaxReviewMemberRepDTO>> queryPage(@RequestBody @Validated({QueryPageGroup.class}) SearchDTO<TaxReviewMemberQueryPageDTO> searchDTO);

    /**
     * Count TaxReviewMember from http
     */
    @Operation(summary = "查询数量", description = "根据条件查询数量")
    @EyPostMapping(value = "/queryCount.do")
    ResponseDTO<Long> queryCount(@RequestBody TaxReviewMemberQueryDTO taxReviewMemberDTO);

    /**
     * One TaxReviewMember from http
     */
    @Operation(summary = "查询单个数据", description = "根据条件查询一条数据,如果查询到多条则返回异常.")
    @EyPostMapping(value = "/queryOne.do")
    ResponseDTO<TaxReviewMemberRepDTO> queryOne(@RequestBody TaxReviewMemberQueryDTO taxReviewMemberDTO);

    /**
     * exist TaxReviewMember from http
     */
    @Operation(summary = "查询是否存在", description = "根据条件查询是否存在")
    @EyPostMapping(value = "/exist.do")
    ResponseDTO<Boolean> exist(@RequestBody TaxReviewMemberQueryDTO taxReviewMemberDTO);
}
