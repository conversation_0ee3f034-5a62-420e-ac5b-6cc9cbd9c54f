package com.ey.tax.cloud.targets.controller.kpi.impl;

import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.dto.SearchDTO;
import com.ey.cn.tax.framework.entity.Search;
import com.ey.cn.tax.framework.utils.ConvertUtils;
import com.ey.cn.tax.framework.web.annotation.EyRestController;
import com.ey.cn.tax.framework.web.base.AbstractController;
import com.ey.tax.cloud.targets.controller.kpi.KpiBatchLogController;
import com.ey.tax.cloud.targets.dto.kpi.*;
import com.ey.tax.cloud.targets.entity.kpi.KpiBatchLog;
import com.ey.tax.cloud.targets.service.kpi.KpiBatchLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.List;
import java.util.Map;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-01-18 15:01:10
 *
 */
@EyRestController(path ="/v1/kpiBatchLog")
public class KpiBatchLogControllerImpl extends AbstractController<KpiBatchLogService, KpiBatchLogDTO, KpiBatchLog> implements KpiBatchLogController {


    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;
    @Override
    public ResponseDTO<Boolean> runKpiBatch(KpiBatchLogRunDTO kpiBatchLogRunDTO) throws Exception{
        KpiBatchLog entity = ConvertUtils.convertDTO2Entity(kpiBatchLogRunDTO, entityClass);
        KpiBatchTask task = new KpiBatchTask(entity, service);

        // 提交任务到线程池执行
        threadPoolTaskExecutor.submit(task);
        ResponseDTO<Boolean> success= ResponseDTO.success();
        success.setMessage("计算中，请稍后查看");
        return success;
    }



    /**
     * add KpiBatchLog from http
     */
    @Override
    public ResponseDTO<Void> add(KpiBatchLogAddDTO dto) {
        KpiBatchLog entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        service.save(entity);
        return ResponseDTO.success();
    }

    /**
     * addList KpiBatchLog from http
     */
    @Override
    public ResponseDTO<Void> addList(List<KpiBatchLogAddDTO> dtos) {
        List<KpiBatchLog> entitys = ConvertUtils.convertDTOList2EntityList(dtos, entityClass);
        service.save(entitys);
        return ResponseDTO.success();
    }

    /**
     * delete KpiBatchLog from http
     */
    @Override
    public ResponseDTO<Void> deleteById(KpiBatchLogDeleteByIdDTO dto) {
        service.deleteById(dto.getId());
        return ResponseDTO.success();
    }

    /**
     * deleteList KpiBatchLog from http
     */
    @Override
    public ResponseDTO<Void> deleteByIdList(KpiBatchLogDeleteByIdListDTO dto) {
        getService().deleteByIds(dto.getIds());
        return ResponseDTO.success();
    }

    /**
     * update KpiBatchLog from http
     */
    @Override
    public ResponseDTO<Void> updateById(KpiBatchLogUpdateByIdDTO dto) {
        KpiBatchLog entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        getService().update(entity);
        return ResponseDTO.success();
    }

    /**
     * updateBatch KpiBatchLog from http
     */
    @Override
    public ResponseDTO<Void> updateBatch(List<KpiBatchLogBatchUpdateDTO> dtos) {
        List<KpiBatchLog> entities = ConvertUtils.convertDTOList2EntityList(dtos, entityClass);
        getService().updateBatchByIds(entities);
        return ResponseDTO.success();
    }

    /**
     * query KpiBatchLog from http
     */
    @Override
    public ResponseDTO<KpiBatchLogRepDTO> queryById(KpiBatchLogQueryByIdDTO dto) {
        KpiBatchLog entity = getService().queryById(dto.getId());
        KpiBatchLogRepDTO rDTO = ConvertUtils.convertEntity2DTO(entity, KpiBatchLogRepDTO.class);
        return ResponseDTO.success(rDTO);
    }

    /**
     * queryByIdList KpiBatchLog from http
     */
    @Override
    public ResponseDTO<List<KpiBatchLogRepDTO>> queryByIdList(KpiBatchLogQueryByIdListDTO dto) {
        List<KpiBatchLog> entities = getService().queryByIds(dto.getIds());
        return ResponseDTO.success(ConvertUtils.convertEntityList2DTOList(entities, KpiBatchLogRepDTO.class));
    }

    /**
     * queryList KpiBatchLog from http
     */
    @Override
    public ResponseDTO<List<KpiBatchLogRepDTO>> queryList(KpiBatchLogQueryDTO dto) {
        KpiBatchLog entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        List<KpiBatchLog> entities = getService().queryByPara(entity);
        return ResponseDTO.success(ConvertUtils.convertEntityList2DTOList(entities, KpiBatchLogRepDTO.class));
    }

    /**
     * Page KpiBatchLog from http
     */
    @Override
    public ResponseDTO<SearchDTO<KpiBatchLogRepDTO>> queryPage(SearchDTO<KpiBatchLogQueryPageDTO> searchDTO) {
        Search<KpiBatchLog> search = ConvertUtils.convertSearchDTO2Search(searchDTO, entityClass);
        getService().queryPageByPara(search);
        return ResponseDTO.success(ConvertUtils.convertSearch2SearchDTO(search, KpiBatchLogRepDTO.class));
    }

    /**
     * Count KpiBatchLog from http
     */
    @Override
    public ResponseDTO<Long> queryCount(KpiBatchLogQueryDTO dto) {
        KpiBatchLog entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        Long count = getService().queryCountByPara(entity);
        return ResponseDTO.success(count);
    }

    /**
     * One KpiBatchLog from http
     */
    @Override
    public ResponseDTO<KpiBatchLogRepDTO> queryOne(KpiBatchLogQueryDTO dto) {
        KpiBatchLog qEntity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        KpiBatchLog rEntity = getService().queryOneByPara(qEntity);
        KpiBatchLogRepDTO rDTO = ConvertUtils.convertEntity2DTO(rEntity, KpiBatchLogRepDTO.class);
        return ResponseDTO.success(rDTO);
    }

    /**
     * exist KpiBatchLog from http
     */
    @Override
    public ResponseDTO<Boolean> exist(KpiBatchLogQueryDTO dto) {
        boolean resullt = getService().existByPara(ConvertUtils.convertDTO2Entity(dto, entityClass));
        return ResponseDTO.success(resullt);
    }
}
