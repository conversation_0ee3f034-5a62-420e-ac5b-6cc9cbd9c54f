package com.ey.tax.cloud.targets.dto.taxReview;

import com.ey.cn.tax.framework.dto.BaseRepDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-01-30 14:57:06
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "查询返回实体")
public class TaxReviewUserDictionaryDTO extends BaseRepDTO {

    /**
     * id
     */
    @Schema(description = "id")
    private String value;

    /**
     * 姓名
     */
    @Schema(description = "姓名")
    private String label;

    @Schema(description = "状态")
    private Integer status;

}
