package com.ey.tax.cloud.targets.service.resource;

import com.ey.cn.tax.framework.service.BaseService;
import com.ey.tax.cloud.targets.entity.resource.TaxReviewResource;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-01-17 14:35:37
 *
 */
public interface TaxReviewResourceService extends BaseService<TaxReviewResource> {

    List<Map<String,Object>> queryTaxReviewResourceList(String fiscalYear);

}
