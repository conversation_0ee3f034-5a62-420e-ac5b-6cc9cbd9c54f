package com.ey.tax.cloud.targets.dto.auth;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2023-12-22 10:32:03
 *
 */
@Data
@Schema(description = "根据条件查询实体")
public class AuthGroupTreeDTO {
    /**
     * 类型code:userScope、permission
     */
    @Schema(description = "类型code:userScope、permission", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String code;

    private String fiscalYear;

}
