package com.ey.tax.cloud.targets.dto.auth;

import com.ey.cn.tax.framework.validator.QueryByIdListGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2023-12-22 10:32:04
 * 
 */
@Data
@Schema(description = "根据id列表查询实体")
public class AuthGroupDetailQueryByIdListDTO {
    
    /**
     * 数据id列表
     */
    @Schema(description = "数据id列表")
    @NotNull(message = "{AuthGroupDetailDTO.ids.NotNull}", groups = {QueryByIdListGroup.class})
    @Size(min = 1, message = "{AuthGroupDetailDTO.ids.Size}", groups = {QueryByIdListGroup.class})
    private List<String> ids;
}