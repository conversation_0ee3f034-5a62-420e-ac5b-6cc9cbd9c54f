package com.ey.tax.cloud.targets.dto.auth;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2023-12-22 10:32:04
 *
 */
@Data
@Schema(description = "用户范围")
public class AuthGroupUserAddListDTO {


    /**
     * 用户ID COLUMN:user_id
     */
    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<String> userIds;
}
