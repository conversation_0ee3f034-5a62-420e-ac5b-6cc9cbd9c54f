package com.ey.tax.cloud.targets.dto.pipeline;

import com.ey.cn.tax.framework.validator.BatchUpdateGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-05-20 14:23:44
 * 
 */
@Data
@Schema(description = "根据id批量更新实体")
public class EngagementBatchUpdateDTO {
    
    /**
     * 数据id
     */
    @Schema(description = "数据id")
    @NotBlank(message = "{EngagementDTO.id.NotBlank}", groups = {BatchUpdateGroup.class})
    private String id;

    /**
     * pipeline的id COLUMN:pipeline_id
     */
    @Schema(description = "pipeline的id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String pipelineId;

    /**
     * code COLUMN:eng_code
     */
    @Schema(description = "code", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String engCode;

    /**
     * code名称 COLUMN:engagement_name
     */
    @Schema(description = "code名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String engagementName;

    /**
     * code状态 COLUMN:engagement_status
     */
    @Schema(description = "code状态", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String engagementStatus;

    /**
     * code时间 COLUMN:effective_date
     */
    @Schema(description = "code时间", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDateTime effectiveDate;

    /**
     * code创建时间 COLUMN:eng_create_date
     */
    @Schema(description = "code创建时间", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDateTime engCreateDate;

    /**
     * engagement partner gpn (ep) COLUMN:engagement_partner_gpn
     */
    @Schema(description = "engagement partner gpn (ep)", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String engagementPartnerGpn;

    /**
     * engagement partner id (ep) COLUMN:engagement_partner_id
     */
    @Schema(description = "engagement partner id (ep)", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String engagementPartnerId;

    /**
     * engagement manager gpn (em) COLUMN:engagement_manager_gpn
     */
    @Schema(description = "engagement manager gpn (em)", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String engagementManagerGpn;

    /**
     * engagement manager id (em) COLUMN:engagement_manager_id
     */
    @Schema(description = "engagement manager id (em)", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String engagementManagerId;
}