package com.ey.tax.cloud.targets.entity.taxReview;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ey.cn.tax.framework.mybatisplus.core.entity.BaseEntity;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-02-26 10:51:54
 * 
 */
@Data
@TableName("target_tax_review_user_base")
public class TaxReviewUserBase extends BaseEntity {
    /**
     * GPN COLUMN:gpn
     */
    private String gpn;
}