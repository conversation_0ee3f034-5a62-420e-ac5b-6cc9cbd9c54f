package com.ey.tax.cloud.targets.dto.resource;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;

import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-01-17 14:35:35
 *
 */
@Data
@Schema(description = "根据条件分页查询实体")
public class PerformanceQueryPageDTO {
    /**
     * COLUMN:user_id
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String userId;

    /**
     * COLUMN:pipeline_id
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String pipelineId;

    /**
     * COLUMN:name
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String name;

    /**
     * COLUMN:level
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String level;

    /**
     * COLUMN:week
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer week;

    /**
     * COLUMN:ter_usd
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal terUsd;

    /**
     * COLUMN:ner_usd
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal nerUsd;

    /**
     * COLUMN:margin
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal margin;

    /**
     * COLUMN:revenue_day
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer revenueDay;

    /**
     * COLUMN:ut
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal ut;

    /**
     * COLUMN:gds_penetration
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal gdsPenetration;

    /**
     * COLUMN:sales_usd
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal salesUsd;

    /**
     * COLUMN:em_efforts_new_client
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal emEffortsNewClient;

    /**
     * COLUMN:em_efforts_new_eng
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal emEffortsNewEng;

    /**
     * COLUMN:pipelines_usd
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal pipelinesUsd;

    /**
     * COLUMN:opportunity_type
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String opportunityType;

    @Schema(description = "kpi得分", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal kpiScore;

    @Schema(description = "财年", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String fiscalYear;

    @Schema(description = "subServiceLineList(字典，subServiceLine)", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<String> subServiceLineList;

    @Schema(description = "competencyList(字典competency)", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<String> competencyList;

    /**
     * market
     */
    @Schema(description = "market列表（字典market）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<String> marketList;
    /**
     * City
     */
    @Schema(description = "city列表(字典city)", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<String> cityList;

    @Schema(description = "级别list（rankLevel)", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<String> levelList;

    private BigDecimal epOpportunity;
    private BigDecimal plOpportunity;

    private BigDecimal opportunityPartner;
    private BigDecimal opportunityPursuitLeader;
}
