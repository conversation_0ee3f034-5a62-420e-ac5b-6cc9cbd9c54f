package com.ey.tax.cloud.targets.dto.transfer;

import com.ey.cn.tax.framework.validator.BatchUpdateGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-04-17 16:03:29
 * 
 */
@Data
@Schema(description = "根据id批量更新实体")
public class SyncTaxLocationBatchUpdateDTO {
    
    /**
     * 数据id
     */
    @Schema(description = "数据id")
    @NotBlank(message = "{SyncTaxLocationDTO.id.NotBlank}", groups = {BatchUpdateGroup.class})
    private String id;

    /**
     * COLUMN:country_code
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String countryCode;

    /**
     * COLUMN:country_name
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String countryName;

    /**
     * COLUMN:ds
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String ds;
}