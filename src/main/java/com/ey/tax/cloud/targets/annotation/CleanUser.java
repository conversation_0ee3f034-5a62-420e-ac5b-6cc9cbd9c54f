package com.ey.tax.cloud.targets.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
/**
 * 用户清洗注解
 * 用于标记需要进行用户清洗的字段
 * 通过field指定取数字段
 * 通过dictCode指定字典code
 * 通过resource指定取数字段
 */
public @interface CleanUser {
    /**
     * userId取值字段
     * @return
     */
    String resource() default "";

    /**
     * 用户属性字段
     * @return
     */
    String field() default "";

    /**
     * 字典code
     * @return
     */
    String dictCode() default "";
}
