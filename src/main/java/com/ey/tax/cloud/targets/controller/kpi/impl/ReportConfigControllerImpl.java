package com.ey.tax.cloud.targets.controller.kpi.impl;

import com.ey.cn.tax.framework.context.EyUserContextHolder;
import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.dto.SearchDTO;
import com.ey.cn.tax.framework.entity.Search;
import com.ey.cn.tax.framework.utils.ConvertUtils;
import com.ey.cn.tax.framework.utils.EmptyUtils;
import com.ey.cn.tax.framework.web.annotation.EyRestController;
import com.ey.cn.tax.framework.web.base.AbstractController;
import com.ey.tax.cloud.targets.constant.BooleanConstants;
import com.ey.tax.cloud.targets.constant.TargetAuthConstants;
import com.ey.tax.cloud.targets.controller.kpi.ReportConfigController;
import com.ey.tax.cloud.targets.dto.data.DictionaryItemRepDTO;
import com.ey.tax.cloud.targets.dto.kpi.ReportConfigAddDTO;
import com.ey.tax.cloud.targets.dto.kpi.ReportConfigBatchUpdateDTO;
import com.ey.tax.cloud.targets.dto.kpi.ReportConfigDTO;
import com.ey.tax.cloud.targets.dto.kpi.ReportConfigDeleteByIdDTO;
import com.ey.tax.cloud.targets.dto.kpi.ReportConfigDeleteByIdListDTO;
import com.ey.tax.cloud.targets.dto.kpi.ReportConfigQueryByIdDTO;
import com.ey.tax.cloud.targets.dto.kpi.ReportConfigQueryByIdListDTO;
import com.ey.tax.cloud.targets.dto.kpi.ReportConfigQueryDTO;
import com.ey.tax.cloud.targets.dto.kpi.ReportConfigQueryPageDTO;
import com.ey.tax.cloud.targets.dto.kpi.ReportConfigRepDTO;
import com.ey.tax.cloud.targets.dto.kpi.ReportConfigUpdateByIdDTO;
import com.ey.tax.cloud.targets.entity.kpi.ReportConfig;
import com.ey.tax.cloud.targets.service.data.DictionaryService;
import com.ey.tax.cloud.targets.service.kpi.ReportConfigService;
import com.ey.tax.cloud.tenant.dto.role.TenRoleDTO;
import com.ey.tax.cloud.tenant.dto.role.TenRoleQueryByUserDTO;
import com.ey.tax.cloud.tenant.dto.role.TenRoleRepDTO;
import com.ey.tax.cloud.tenant.remote.tenRole.TenRoleFeignClient;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-03-07 17:05:09
 *
 */
@EyRestController(path ="/v1/reportConfig")
public class ReportConfigControllerImpl extends AbstractController<ReportConfigService, ReportConfigDTO, ReportConfig> implements ReportConfigController {

    @Autowired
    private DictionaryService dictionaryService;
    @Autowired
    private TenRoleFeignClient tenRoleFeignClient;

    /**
     * add ReportConfig from http
     */
    @Override
    public ResponseDTO<Void> add(ReportConfigAddDTO dto) {
        ReportConfig entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        ReportConfig reportConfig = new ReportConfig();
        reportConfig.setIsUserControl(BooleanConstants.BOOLEAN_1);
        List<ReportConfig> reportConfigs = getService().queryByPara(reportConfig);
        String show = "0";
        if (reportConfigs.size() > 0) {
            show = reportConfigs.get(0).getIsShow();
        }
        if(EmptyUtils.isNotEmpty(entity.getIsUserControl())&&BooleanConstants.BOOLEAN_1.equals(entity.getIsUserControl())){
            entity.setIsShow(show);
        }
        service.save(entity);
        return ResponseDTO.success();
    }

    /**
     * addList ReportConfig from http
     */
    @Override
    public ResponseDTO<Void> addList(List<ReportConfigAddDTO> dtos) {
        List<ReportConfig> entitys = ConvertUtils.convertDTOList2EntityList(dtos, entityClass);
        service.save(entitys);
        return ResponseDTO.success();
    }

    /**
     * delete ReportConfig from http
     */
    @Override
    public ResponseDTO<Void> deleteById(ReportConfigDeleteByIdDTO dto) {
        service.deleteById(dto.getId());
        return ResponseDTO.success();
    }

    /**
     * deleteList ReportConfig from http
     */
    @Override
    public ResponseDTO<Void> deleteByIdList(ReportConfigDeleteByIdListDTO dto) {
        getService().deleteByIds(dto.getIds());
        return ResponseDTO.success();
    }

    /**
     * update ReportConfig from http
     */
    @Override
    public ResponseDTO<Void> updateById(ReportConfigUpdateByIdDTO dto) {
        ReportConfig entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        ReportConfig reportConfig = new ReportConfig();
        reportConfig.setIsUserControl(BooleanConstants.BOOLEAN_1);
        List<ReportConfig> reportConfigs = getService().queryByPara(reportConfig);
        String show = "0";
        if (reportConfigs.size() > 0) {
            show = reportConfigs.get(0).getIsShow();
        }
        if(EmptyUtils.isNotEmpty(entity.getIsUserControl())&&BooleanConstants.BOOLEAN_1.equals(entity.getIsUserControl())){
            entity.setIsShow(show);
        }
        getService().update(entity);
        return ResponseDTO.success();
    }

    /**
     * updateBatch ReportConfig from http
     */
    @Override
    public ResponseDTO<Void> updateBatch(List<ReportConfigBatchUpdateDTO> dtos) {
        List<ReportConfig> entities = ConvertUtils.convertDTOList2EntityList(dtos, entityClass);
        getService().updateBatchByIds(entities);
        return ResponseDTO.success();
    }

    /**
     * query ReportConfig from http
     */
    @Override
    public ResponseDTO<ReportConfigRepDTO> queryById(ReportConfigQueryByIdDTO dto) {
        ReportConfig entity = getService().queryById(dto.getId());
        ReportConfigRepDTO rDTO = ConvertUtils.convertEntity2DTO(entity, ReportConfigRepDTO.class);
        return ResponseDTO.success(rDTO);
    }

    /**
     * queryByIdList ReportConfig from http
     */
    @Override
    public ResponseDTO<List<ReportConfigRepDTO>> queryByIdList(ReportConfigQueryByIdListDTO dto) {
        List<ReportConfig> entities = getService().queryByIds(dto.getIds());
        return ResponseDTO.success(ConvertUtils.convertEntityList2DTOList(entities, ReportConfigRepDTO.class));
    }

    /**
     * queryList ReportConfig from http
     */
    @Override
    public ResponseDTO<List<ReportConfigRepDTO>> queryList(ReportConfigQueryDTO dto) {
        ReportConfig entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        //获取当前用户角色
        String userId = EyUserContextHolder.get().getAuthUserId();
        if(EmptyUtils.isNotEmpty(dto.getUserId())){
            userId = dto.getUserId();
        }
        TenRoleQueryByUserDTO tenRoleQueryByUserDTO = new TenRoleQueryByUserDTO();
        tenRoleQueryByUserDTO.setUserId(userId);
        List<TenRoleRepDTO> data = tenRoleFeignClient.queryUserRole(tenRoleQueryByUserDTO).getData();
        List<String> roles = data.stream().map(TenRoleRepDTO::getId).collect(Collectors.toList());
        if(EmptyUtils.isNotEmpty(roles)){
            entity.setRoleList(roles);
        }
        List<ReportConfig> entities = getService().queryByPara(entity);
        //根据column去重
        entities = entities.stream().collect(Collectors.toMap(ReportConfig::getColumnCode, p -> p, (p1, p2) -> p1)).values().stream().collect(Collectors.toList());
        //按照orderNum从小到大排序
        entities = entities.stream().sorted((o1, o2) -> o1.getOrderNum().compareTo(o2.getOrderNum())).collect(Collectors.toList());
        return ResponseDTO.success(ConvertUtils.convertEntityList2DTOList(entities, ReportConfigRepDTO.class));
    }

    /**
     * Page ReportConfig from http
     */
    @Override
    public ResponseDTO<SearchDTO<ReportConfigRepDTO>> queryPage(SearchDTO<ReportConfigQueryPageDTO> searchDTO) {
        Search<ReportConfig> search = ConvertUtils.convertSearchDTO2Search(searchDTO, entityClass);
        if(EmptyUtils.isEmpty(search.getQueryParams().getRole())){
            search.getQueryParams().setRole(null);
        }
        getService().queryPageByPara(search);
        List<DictionaryItemRepDTO> role = dictionaryService.queryRole();
        Map<String, String> roleMap =new HashMap<>();
        if(EmptyUtils.isNotEmpty(role)){
            for (DictionaryItemRepDTO dictionaryItemRepDTO : role) {
                roleMap.put(dictionaryItemRepDTO.getValue().toString(),dictionaryItemRepDTO.getLabel());
            }
        }
        SearchDTO<ReportConfigRepDTO> result = ConvertUtils.convertSearch2SearchDTO(search, ReportConfigRepDTO.class);
        for (ReportConfigRepDTO record : result.getRecords()) {
            if(EmptyUtils.isNotEmpty(record.getIsShow())){
                record.setIsShowLabel(dictionaryService.getDictName(TargetAuthConstants.AUTH_GROUP_DICT_YSE_NO, record.getIsShow()));
            }
            if(EmptyUtils.isNotEmpty(record.getIsUserControl())){
                record.setIsUserControlLabel(dictionaryService.getDictName(TargetAuthConstants.AUTH_GROUP_DICT_YSE_NO, record.getIsUserControl()));
            }
            if(EmptyUtils.isNotEmpty(record.getRole())){
                record.setRoleLabel(roleMap.get(record.getRole()));
            }
            record.setFiscalYearLabel(dictionaryService.getDictName(TargetAuthConstants.FISCAL_YEAR, record.getFiscalYear()));
        }
        return ResponseDTO.success(result);
    }

    /**
     * Count ReportConfig from http
     */
    @Override
    public ResponseDTO<Long> queryCount(ReportConfigQueryDTO dto) {
        ReportConfig entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        Long count = getService().queryCountByPara(entity);
        return ResponseDTO.success(count);
    }

    /**
     * One ReportConfig from http
     */
    @Override
    public ResponseDTO<ReportConfigRepDTO> queryOne(ReportConfigQueryDTO dto) {
        ReportConfig qEntity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        ReportConfig rEntity = getService().queryOneByPara(qEntity);
        ReportConfigRepDTO rDTO = ConvertUtils.convertEntity2DTO(rEntity, ReportConfigRepDTO.class);
        return ResponseDTO.success(rDTO);
    }

    /**
     * exist ReportConfig from http
     */
    @Override
    public ResponseDTO<Boolean> exist(ReportConfigQueryDTO dto) {
        boolean resullt = getService().existByPara(ConvertUtils.convertDTO2Entity(dto, entityClass));
        return ResponseDTO.success(resullt);
    }
}
