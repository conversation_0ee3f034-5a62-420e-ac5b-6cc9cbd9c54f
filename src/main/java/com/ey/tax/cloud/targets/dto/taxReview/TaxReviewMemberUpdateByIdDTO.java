package com.ey.tax.cloud.targets.dto.taxReview;

import com.ey.cn.tax.framework.validator.UpdateByIdGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-02-26 11:15:06
 * 
 */
@Data
@Schema(description = "根据id批量更新实体")
public class TaxReviewMemberUpdateByIdDTO {
    
    /**
     * 数据id
     */
    @Schema(description = "数据id")
    @NotBlank(message = "{TaxReviewMemberDTO.id.NotBlank}", groups = {UpdateByIdGroup.class})
    private String id;

    /**
     * Tax review id COLUMN:tax_review_id
     */
    @Schema(description = "Tax review id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String taxReviewId;

    /**
     * 参与人员类型（staff、senior） COLUMN:type
     */
    @Schema(description = "参与人员类型（staff、senior）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String type;

    /**
     * 参与人员id COLUMN:user_id
     */
    @Schema(description = "参与人员id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String userId;
}