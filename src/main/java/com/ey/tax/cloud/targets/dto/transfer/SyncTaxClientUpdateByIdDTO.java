package com.ey.tax.cloud.targets.dto.transfer;

import com.ey.cn.tax.framework.validator.UpdateByIdGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-05-30 17:59:57
 * 
 */
@Data
@Schema(description = "根据id批量更新实体")
public class SyncTaxClientUpdateByIdDTO {
    
    /**
     * 数据id
     */
    @Schema(description = "数据id")
    @NotBlank(message = "{SyncTaxClientDTO.id.NotBlank}", groups = {UpdateByIdGroup.class})
    private String id;

    /**
     * client id COLUMN:client_id
     */
    @Schema(description = "client id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String clientId;

    /**
     * english name COLUMN:english_name
     */
    @Schema(description = "english name", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String englishName;

    /**
     * duns number COLUMN:duns_number
     */
    @Schema(description = "duns number", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String dunsNumber;

    /**
     * customer classification COLUMN:customer_classification
     */
    @Schema(description = "customer classification", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String customerClassification;

    /**
     * industry sector COLUMN:industry_sector
     */
    @Schema(description = "industry sector", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String industrySector;

    /**
     * industry sub sector COLUMN:industry_sub_sector
     */
    @Schema(description = "industry sub sector", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String industrySubSector;

    /**
     * company type COLUMN:company_type
     */
    @Schema(description = "company type", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String companyType;

    /**
     * stock exchange COLUMN:stock_exchange
     */
    @Schema(description = "stock exchange", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String stockExchange;

    /**
     * channel COLUMN:channel
     */
    @Schema(description = "channel", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String channel;

    /**
     * is sec COLUMN:is_sec
     */
    @Schema(description = "is sec", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String isSec;

    /**
     * client region COLUMN:client_region
     */
    @Schema(description = "client region", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String clientRegion;

    /**
     * location COLUMN:country_region
     */
    @Schema(description = "location", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String countryRegion;

    /**
     * ds COLUMN:ds
     */
    @Schema(description = "ds", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String ds;
}