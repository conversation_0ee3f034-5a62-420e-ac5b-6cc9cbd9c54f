package com.ey.tax.cloud.targets.job;

import com.ey.cn.tax.framework.utils.EmptyUtils;
import com.ey.cn.tax.framework.utils.JacksonUtils;
import com.ey.tax.cloud.targets.service.transfer.DataTransferService;
import com.ey.tax.cloud.targets.utils.ExceptionUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class DictTransferJobHandler {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private Map<String, DataTransferService> dataTransferServiceMap;

    public void dictionaryTransfer(Map<String, String> paramMap){
        DataTransferService dataTransferService = dataTransferServiceMap.get("dictionary");
        dataTransferService.process(paramMap);
    }

    @XxlJob("DICT_TRANSFER_JOB")
    public ReturnT<String> processDictionary(){
        try {
            String param = XxlJobHelper.getJobParam();
            Map<String, String> paramMap = null;
            if(EmptyUtils.isNotEmpty(param)){
                paramMap = JacksonUtils.readValue(param, Map.class);
            }
            dictionaryTransfer(paramMap);
            return ReturnT.SUCCESS;
        }catch (Exception e){
            logger.error("dictionary transfer job error", e);
            XxlJobHelper.log("dictionary transfer job error");
            XxlJobHelper.log(ExceptionUtils.getFullStackTrace(e));
            return ReturnT.FAIL;
        }
    }

}
