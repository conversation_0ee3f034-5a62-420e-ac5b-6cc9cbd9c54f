package com.ey.tax.cloud.targets.dto.product;

import com.ey.cn.tax.framework.validator.DeleteByIdListGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-01-11 14:46:33
 *
 */
@Data
@Schema(description = "根据id列表删除实体")
public class ProductDeleteByIdListDTO {

    /**
     * 数据id列表
     */
    @Schema(description = "数据id列表")
    @NotNull(message = "{ProductDTO.ids.NotNull}", groups = {DeleteByIdListGroup.class})
    @Size(min = 1, message = "{ProductDTO.ids.Size}", groups = {DeleteByIdListGroup.class})
    private List<String> ids;
}
