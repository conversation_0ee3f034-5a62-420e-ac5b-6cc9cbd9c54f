package com.ey.tax.cloud.targets.dto.kpi;

import com.ey.cn.tax.framework.dto.BaseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-01-22 10:49:40
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "默认实体")
public class KpiExeConfigDTO extends BaseDTO {
    /**
     * 表名 COLUMN:table_name
     */
    @Schema(description = "表名")
    private String tableName;

    /**
     * 字段名 COLUMN:field_name
     */
    @Schema(description = "字段名")
    private String fieldName;

    /**
     * 模版id COLUMN:temp_id
     */
    @Schema(description = "模版id")
    private String tempId;

    /**
     * 状态 COLUMN:status
     */
    @Schema(description = "状态")
    private Integer status;
}
