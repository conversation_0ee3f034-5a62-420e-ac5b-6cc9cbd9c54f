package com.ey.tax.cloud.targets.service.transfer.impl;

import com.ey.cn.tax.framework.utils.EmptyUtils;
import com.ey.cn.tax.framework.utils.JacksonUtils;
import com.ey.tax.cloud.targets.constant.DictConstants;
import com.ey.tax.cloud.targets.entity.data.ExchangeRate;
import com.ey.tax.cloud.targets.entity.transfer.SyncTaxHldbNew;
import com.ey.tax.cloud.targets.service.data.ExchangeRateService;
import com.ey.tax.cloud.targets.service.data.FiscalCalenderService;
import com.ey.tax.cloud.targets.service.transfer.DataTransferService;
import com.ey.tax.cloud.targets.service.transfer.SyncTaxHldbNewService;
import com.xxl.job.core.context.XxlJobHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("exchange")
public class ExchangeTransferServiceImpl extends AbstractTransferService implements DataTransferService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    private final static String MBHB_CNY = "1";

    private final static Integer TIGER_SOURCE_TYPE = 0;

    @Autowired
    private SyncTaxHldbNewService syncTaxHldbNewService;

    @Autowired
    private ExchangeRateService exchangeRateService;

    @Autowired
    private FiscalCalenderService fiscalCalenderService;

    /**
     * 汇率转换
     */
    @Override
    public void process(Map<String, String> params) {
        //获取最新批次
        SyncTaxHldbNew rSyncTaxHldbNew = syncTaxHldbNewService.queryOneForTranfer(new SyncTaxHldbNew());
        if(EmptyUtils.isEmpty(rSyncTaxHldbNew)){
            return;
        }
        SyncTaxHldbNew qSyncTaxHldbNew = new SyncTaxHldbNew();
        qSyncTaxHldbNew.setMbhb(MBHB_CNY);
        qSyncTaxHldbNew.setDs(rSyncTaxHldbNew.getDs());
        List<SyncTaxHldbNew> syncTaxHldbNewList = syncTaxHldbNewService.queryByPara(qSyncTaxHldbNew);
        if(EmptyUtils.isEmpty(syncTaxHldbNewList)){
            logger.warn("exchange rate empty");
            return;
        }
        String currentFy = fiscalCalenderService.getCurrentFiscalCalender();
        //获取币种字典
        Map<String, String> dictCodeMap = getDictionaryMapping(DictConstants.TARGET_DICT_CODE_CURRENCY);
        //加载现有汇率数据
        ExchangeRate qExchangeRate = new ExchangeRate();
        qExchangeRate.setFiscalYear(currentFy);
        List<ExchangeRate> exchangeRateList = exchangeRateService.queryByPara(qExchangeRate);
        Map<String, ExchangeRate> exchangeRateMap = new HashMap<>();
        for(ExchangeRate exchangeRate:exchangeRateList){
            exchangeRateMap.put(exchangeRate.getExchangeCurrency(), exchangeRate);
        }
        List<ExchangeRate> insertExchangeRate = new ArrayList<>();
        List<ExchangeRate> updateExchangeRate = new ArrayList<>();
        //取最新的一条
        Map<String, SyncTaxHldbNew> syncTaxHldbNewMap = new HashMap<>();
        for(SyncTaxHldbNew syncTaxHldbNew:syncTaxHldbNewList){
            syncTaxHldbNewMap.put(syncTaxHldbNew.getBbCurrencyname(), syncTaxHldbNew);
        }
        for(SyncTaxHldbNew syncTaxHldbNew:syncTaxHldbNewMap.values()){
            XxlJobHelper.log("syncTaxHldbNew: " + syncTaxHldbNew.getBbCurrencyname() + " -> " + syncTaxHldbNew.getMbhbCurrencyname()
                + " constant_dhje_report: " + syncTaxHldbNew.getConstantDhjeReport() + " week_dhje_report: " + syncTaxHldbNew.getWeekDhjeReport());
            String bb = syncTaxHldbNew.getBb();
            String targetKey = dictCodeMap.get("tiger-"+bb);
            if(EmptyUtils.isEmpty(targetKey)){
                XxlJobHelper.log("currency not exist bb: {} name: {}", bb, syncTaxHldbNew.getBbCurrencyname());
                logger.error("currency not exist bb: {} name: {}", bb, syncTaxHldbNew.getBbCurrencyname());
                continue;
            }
            ExchangeRate exchangeRate = exchangeRateMap.get(targetKey);
            String sConstantDhjeReport = syncTaxHldbNew.getConstantDhjeReport();
            if(EmptyUtils.isEmpty(sConstantDhjeReport)){
                sConstantDhjeReport = syncTaxHldbNew.getWeekDhjeReport();
            }
            if(EmptyUtils.isEmpty(sConstantDhjeReport)){
                XxlJobHelper.log("rate is null");
                logger.error("rate is null");
                continue;
            }
            if(EmptyUtils.isEmpty(exchangeRate)){
                //新增
                ExchangeRate newExchangeRate = new ExchangeRate();
                newExchangeRate.setFiscalYear(currentFy);
                newExchangeRate.setExchangeRate(new BigDecimal(sConstantDhjeReport));
                newExchangeRate.setExchangeCurrency(targetKey);
                newExchangeRate.setExchangeSource(TIGER_SOURCE_TYPE);
                insertExchangeRate.add(newExchangeRate);
            }else{
                //更新
                BigDecimal exchangeRateValue = exchangeRate.getExchangeRate();
                BigDecimal newExchangeRateValue = new BigDecimal(sConstantDhjeReport);
                if(exchangeRateValue.compareTo(newExchangeRateValue) != 0){
                    exchangeRate.setExchangeRate(newExchangeRateValue);
                    updateExchangeRate.add(exchangeRate);
                }
            }
        }
        exchangeRateService.save(insertExchangeRate);
        exchangeRateService.updateBatchByIds(updateExchangeRate);
    }

    @Override
    public void clear() {

    }
}
