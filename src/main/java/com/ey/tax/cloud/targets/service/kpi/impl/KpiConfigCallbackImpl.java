package com.ey.tax.cloud.targets.service.kpi.impl;

import com.ey.tax.cloud.targets.constant.KpiBatchLogConstants;
import com.ey.tax.cloud.targets.entity.kpi.KpiConfigParam;
import com.ey.tax.cloud.targets.service.kpi.AbstractKpiConfigCallback;
import com.ey.tax.cloud.targets.service.kpi.KpiCalculateService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class KpiConfigCallbackImpl extends AbstractKpiConfigCallback {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private KpiCalculateService kpiCalculateService;

    @Override
    public void process(KpiConfigParam kpiConfigParam) throws Exception{
        //这里触发kpi计算,
        logger.info("submit task to kpi batch");
        kpiCalculateService.submitTask(KpiBatchLogConstants.SOURCE_TYPE_MONITOR, kpiConfigParam);
    }

    @Override
    public void ExceptionHandler(KpiConfigParam kpiConfigParam, Throwable e) {
        logger.error("kpi config call back error", e);
    }
}
