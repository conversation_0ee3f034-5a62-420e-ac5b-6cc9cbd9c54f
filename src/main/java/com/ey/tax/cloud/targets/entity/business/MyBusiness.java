package com.ey.tax.cloud.targets.entity.business;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ey.cn.tax.framework.mybatisplus.core.entity.BaseEntity;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-07-29 14:23:08
 *
 */
@Data
@TableName("target_my_business")
public class MyBusiness extends BaseEntity {
    /**
     * Fiscal year COLUMN:fiscal_year
     */
    private String fiscalYear;

    /**
     * Fiscal week COLUMN:fiscal_week
     */
    private String fiscalWeek;

    /**
     * 项目code COLUMN:engagement_code
     */
    @TableField(value = "engagement_code",condition = "%s ILIKE CONCAT('%%',#{%s},'%%')")
    private String engagementCode;

    /**
     * 项目名称 COLUMN:engagement_name
     */
    @TableField(value = "engagement_name",condition = "%s ILIKE CONCAT('%%',#{%s},'%%')")
    private String engagementName;

    /**
     * 项目状态 COLUMN:engagement_status
     */
    private String engagementStatus;

    /**
     * 项目合伙人 COLUMN:engagement_partner_gpn
     */
    private String engagementPartnerGpn;

    /**
     * 项目经理 COLUMN:engagement_manager_gpn
     */
    private String engagementManagerGpn;

    /**
     * 客户编号 COLUMN:client_id
     */
    private String clientId;

    /**
     * 客户名称 COLUMN:client_name
     */
    private String clientName;

    /**
     * 集团大客户编号 COLUMN:account_id
     */
    private String accountId;

    /**
     * 集团大客户名称 COLUMN:account_name
     */
    private String accountName;

    /**
     * SMU COLUMN:sub_mgmt_unit_code
     */
    private String subMgmtUnitCode;

    /**
     * COLUMN:sub_mgmt_unit_name
     */
    private String subMgmtUnitName;

    /**
     * 币种 COLUMN:base_currency
     */
    private String baseCurrency;

    /**
     * COLUMN:base_currency_name
     */
    private String baseCurrencyName;

    /**
     * pipelinecode COLUMN:opportunity_id
     */
    private String opportunityId;

    /**
     * 项目预算 COLUMN:planned_total_engagement_revenue
     */
    private BigDecimal plannedTotalEngagementRevenue;

    /**
     * amount COLUMN:contract_amount
     */
    private BigDecimal contractAmount;

    /**
     * ETD Net Unbilled Inventory COLUMN:e_net_unbld_inv
     */
    private BigDecimal eNetUnbldInv;

    /**
     * COLUMN:f_net_unbld_inv
     */
    private BigDecimal fNetUnbldInv;

    /**
     * Total Expense COLUMN:e_tot_exp
     */
    private BigDecimal eTotExp;

    /**
     * COLUMN:f_tot_exp
     */
    private BigDecimal fTotExp;

    /**
     * Ter COLUMN:e_ter
     */
    private BigDecimal eTer;

    /**
     * COLUMN:f_ter
     */
    private BigDecimal fTer;

    /**
     * Ner COLUMN:e_ner
     */
    private BigDecimal eNer;

    /**
     * COLUMN:f_ner
     */
    private BigDecimal fNer;

    /**
     * charge rate*金额（Standard） COLUMN:e_ser
     */
    private BigDecimal eSer;

    /**
     * COLUMN:f_ser
     */
    private BigDecimal fSer;

    /**
     * Margin COLUMN:e_margin
     */
    private BigDecimal eMargin;

    /**
     * COLUMN:f_margin
     */
    private BigDecimal fMargin;

    /**
     * 百分比 COLUMN:e_margin_pct
     */
    private BigDecimal eMarginPct;

    /**
     * COLUMN:f_margin_pct
     */
    private BigDecimal fMarginPct;

    /**
     * COLUMN:e_dir_cost
     */
    private BigDecimal eDirCost;

    /**
     * COLUMN:f_dir_cost
     */
    private BigDecimal fDirCost;

    /**
     * ner per hour COLUMN:e_net_rev_per_hour
     */
    private BigDecimal eNetRevPerHour;

    /**
     * COLUMN:f_net_rev_per_hour
     */
    private BigDecimal fNetRevPerHour;

    /**
     * Charged Hours COLUMN:e_chg_hrs
     */
    private BigDecimal eChgHrs;

    /**
     * COLUMN:f_chg_hrs
     */
    private BigDecimal fChgHrs;

    /**
     * AR COLUMN:e_tot_ar
     */
    private BigDecimal eTotAr;

    /**
     * COLUMN:f_tot_ar
     */
    private BigDecimal fTotAr;

    /**
     * COLUMN:e_ar_tax
     */
    private BigDecimal eArTax;

    /**
     * COLUMN:f_ar_tax
     */
    private BigDecimal fArTax;

    /**
     * total billings (INCL. tax) COLUMN:e_bld_fee_exp
     */
    private BigDecimal eBldFeeExp;

    /**
     * COLUMN:f_bld_fee_exp
     */
    private BigDecimal fBldFeeExp;

    /**
     * COLUMN:e_bld_tax
     */
    private BigDecimal eBldTax;

    /**
     * COLUMN:f_bld_tax
     */
    private BigDecimal fBldTax;

    /**
     * total Collections COLUMN:e_col_fee_exp
     */
    private BigDecimal eColFeeExp;

    /**
     * COLUMN:f_col_fee_exp
     */
    private BigDecimal fColFeeExp;

    /**
     * COLUMN:e_col_tax
     */
    private BigDecimal eColTax;

    /**
     * COLUMN:f_col_tax
     */
    private BigDecimal fColTax;

    /**
     * COLUMN:e_ar_rsv
     */
    private BigDecimal eArRsv;

    /**
     * COLUMN:f_ar_rsv
     */
    private BigDecimal fArRsv;

    /**
     * COLUMN:e_ar_vat_rsv
     */
    private BigDecimal eArVatRsv;

    /**
     * COLUMN:f_ar_vat_rsv
     */
    private BigDecimal fArVatRsv;

    /**
     * COLUMN:e_ar_write_off
     */
    private BigDecimal eArWriteOff;

    /**
     * COLUMN:f_ar_write_off
     */
    private BigDecimal fArWriteOff;

    /**
     * COLUMN:e_ar_vat_write_off
     */
    private BigDecimal eArVatWriteOff;

    /**
     * COLUMN:f_ar_vat_write_off
     */
    private BigDecimal fArVatWriteOff;

    /**
     * Revenue Days COLUMN:e_revenue_days
     */
    private BigDecimal eRevenueDays;

    /**
     * COLUMN:etl_time
     */
    private LocalDateTime etlTime;

    /**
     * COLUMN:ds
     */
    private String ds;

    /**
     * 项目合伙人ID COLUMN:engagement_partner_id
     */
    private String engagementPartnerId;

    /**
     * 项目经理ID COLUMN:engagement_manager_id
     */
    private String engagementManagerId;

    /**
     * pipelineid COLUMN:pipeline_id
     */
    private String pipelineId;

    /**
     * pipeline 的主 ep 用户 id COLUMN:pipeline_ep_id
     */
    private String pipelineEpId;

    /**
     * pipeline 的主 em 用户 id COLUMN:pipeline_em_id
     */
    private String pipelineEmId;
    @TableField(exist = false)
    private String userId;

    @TableField(exist = false)
    private String tag;

    @TableField(exist = false)
    private String type;
    @TableField(exist = false)
    private List<String> statusList;

    private String mercuryServiceCode;
    private String mercuryServiceName;
    @TableField(exist = false)
    private List<String> fiscalYearList;
    @TableField(exist = false)
    private String engCodeLike;
    @TableField(exist = false)
    private String pipelineCode;

}
