package com.ey.tax.cloud.targets.service.data.impl;

import com.ey.cn.tax.framework.mybatisplus.extension.service.AbstractService;
import com.ey.tax.cloud.targets.entity.data.Template;
import com.ey.tax.cloud.targets.mapper.data.TemplateMapper;
import com.ey.tax.cloud.targets.service.data.TemplateService;
import org.springframework.stereotype.Service;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-02-19 11:47:19
 * 
 */
@Service
public class TemplateServiceImpl extends AbstractService<TemplateMapper, Template> implements TemplateService {
}