package com.ey.tax.cloud.targets.dto.transfer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 前端公共字典表接口请求DTO
 */
@Data
@Schema(description = "前端公共字典表接口请求参数")
public class DictListAllSimpleRequestDTO {

    /**
     * 应用标识
     */
    @Schema(description = "应用标识", required = true)
    private String app;

    /**
     * 字典数据状态
     */
    @Schema(description = "字典数据状态", defaultValue = "active", allowableValues = {"all", "active", "inactive"})
    private String status = "active";

}
