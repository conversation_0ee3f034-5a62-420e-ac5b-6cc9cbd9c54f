package com.ey.tax.cloud.targets.dto.forecast;

import com.ey.cn.tax.framework.dto.BaseRepDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-01-17 14:35:37
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "查询返回实体")
public class ForecastBatchRepDTO extends BaseRepDTO {
    /**
     * COLUMN:user_id
     */
    @Schema(description = "null")
    private String userId;

    /**
     * COLUMN:status
     */
    @Schema(description = "0:等待,1:初始化,2:运行中,3:结束,-1:异常")
    private Integer status;

    /**
     * COLUMN:log_text
     */
    @Schema(description = "null")
    private String logText;

    /**
     * 计算状态
     */
    @Schema(description = "null")
    private String executeStatus;
}
