package com.ey.tax.cloud.targets.dto.taxReview;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-01-30 14:57:07
 * 
 */
@Data
@Schema(description = "根据条件删除实体")
public class TaxReviewDeleteDTO {
    /**
     * Engagement Code项目code COLUMN:engagement_code
     */
    @Schema(description = "Engagement Code项目code", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String engagementCode;

    /**
     * 客户编号 COLUMN:no_of_entities
     */
    @Schema(description = "客户编号", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String noOfEntities;

    /**
     * 客户名 COLUMN:entity_name
     */
    @Schema(description = "客户名", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String entityName;

    /**
     * Review 范围 COLUMN:review_scope
     */
    @Schema(description = "Review 范围", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String reviewScope;

    /**
     * 备注 COLUMN:remarks
     */
    @Schema(description = "备注", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String remarks;

    /**
     * Tax EP用户id COLUMN:tax_ep_id
     */
    @Schema(description = "Tax EP用户id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String taxEpId;

    /**
     * Tax EM用户id COLUMN:tax_em_id
     */
    @Schema(description = "Tax EM用户id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String taxEmId;

    /**
     * Audit EP用户id COLUMN:audit_ep_id
     */
    @Schema(description = "Audit EP用户id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String auditEpId;

    /**
     * Audit EM用户id COLUMN:audit_em_id
     */
    @Schema(description = "Audit EM用户id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String auditEmId;

    /**
     * Eng Code + EP 的合计金额 COLUMN:imputed_ner
     */
    @Schema(description = "Eng Code + EP 的合计金额", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal imputedNer;
}