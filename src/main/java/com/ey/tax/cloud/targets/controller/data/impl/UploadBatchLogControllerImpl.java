package com.ey.tax.cloud.targets.controller.data.impl;

import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.dto.SearchDTO;
import com.ey.cn.tax.framework.entity.Search;
import com.ey.cn.tax.framework.utils.ConvertUtils;
import com.ey.cn.tax.framework.utils.EmptyUtils;
import com.ey.cn.tax.framework.web.annotation.EyRestController;
import com.ey.cn.tax.framework.web.base.AbstractController;
import com.ey.tax.cloud.targets.controller.data.UploadBatchLogController;
import com.ey.tax.cloud.targets.dto.data.UploadBatchLogAddDTO;
import com.ey.tax.cloud.targets.dto.data.UploadBatchLogBatchUpdateDTO;
import com.ey.tax.cloud.targets.dto.data.UploadBatchLogDTO;
import com.ey.tax.cloud.targets.dto.data.UploadBatchLogDeleteByIdDTO;
import com.ey.tax.cloud.targets.dto.data.UploadBatchLogDeleteByIdListDTO;
import com.ey.tax.cloud.targets.dto.data.UploadBatchLogQueryByIdDTO;
import com.ey.tax.cloud.targets.dto.data.UploadBatchLogQueryByIdListDTO;
import com.ey.tax.cloud.targets.dto.data.UploadBatchLogQueryDTO;
import com.ey.tax.cloud.targets.dto.data.UploadBatchLogQueryPageDTO;
import com.ey.tax.cloud.targets.dto.data.UploadBatchLogRepDTO;
import com.ey.tax.cloud.targets.dto.data.UploadBatchLogUpdateByIdDTO;
import com.ey.tax.cloud.targets.entity.data.UploadBatchLog;
import com.ey.tax.cloud.targets.service.data.DictionaryService;
import com.ey.tax.cloud.targets.service.data.UploadBatchLogService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-05-23 16:20:39
 *
 */
@EyRestController(path ="/v1/uploadBatchLog")
public class UploadBatchLogControllerImpl extends AbstractController<UploadBatchLogService, UploadBatchLogDTO, UploadBatchLog> implements UploadBatchLogController {

    @Autowired
    private DictionaryService dictionaryService;

    /**
     * add UploadBatchLog from http
     */
    @Override
    public ResponseDTO<Void> add(UploadBatchLogAddDTO dto) {
        UploadBatchLog entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        service.save(entity);
        return ResponseDTO.success();
    }

    /**
     * addList UploadBatchLog from http
     */
    @Override
    public ResponseDTO<Void> addList(List<UploadBatchLogAddDTO> dtos) {
        List<UploadBatchLog> entitys = ConvertUtils.convertDTOList2EntityList(dtos, entityClass);
        service.save(entitys);
        return ResponseDTO.success();
    }

    /**
     * delete UploadBatchLog from http
     */
    @Override
    public ResponseDTO<Void> deleteById(UploadBatchLogDeleteByIdDTO dto) {
        service.deleteById(dto.getId());
        return ResponseDTO.success();
    }

    /**
     * deleteList UploadBatchLog from http
     */
    @Override
    public ResponseDTO<Void> deleteByIdList(UploadBatchLogDeleteByIdListDTO dto) {
        getService().deleteByIds(dto.getIds());
        return ResponseDTO.success();
    }

    /**
     * update UploadBatchLog from http
     */
    @Override
    public ResponseDTO<Void> updateById(UploadBatchLogUpdateByIdDTO dto) {
        UploadBatchLog entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        getService().update(entity);
        return ResponseDTO.success();
    }

    /**
     * updateBatch UploadBatchLog from http
     */
    @Override
    public ResponseDTO<Void> updateBatch(List<UploadBatchLogBatchUpdateDTO> dtos) {
        List<UploadBatchLog> entities = ConvertUtils.convertDTOList2EntityList(dtos, entityClass);
        getService().updateBatchByIds(entities);
        return ResponseDTO.success();
    }

    /**
     * query UploadBatchLog from http
     */
    @Override
    public ResponseDTO<UploadBatchLogRepDTO> queryById(UploadBatchLogQueryByIdDTO dto) {
        UploadBatchLog entity = getService().queryById(dto.getId());
        UploadBatchLogRepDTO rDTO = ConvertUtils.convertEntity2DTO(entity, UploadBatchLogRepDTO.class);
        return ResponseDTO.success(rDTO);
    }

    /**
     * queryByIdList UploadBatchLog from http
     */
    @Override
    public ResponseDTO<List<UploadBatchLogRepDTO>> queryByIdList(UploadBatchLogQueryByIdListDTO dto) {
        List<UploadBatchLog> entities = getService().queryByIds(dto.getIds());
        return ResponseDTO.success(ConvertUtils.convertEntityList2DTOList(entities, UploadBatchLogRepDTO.class));
    }

    /**
     * queryList UploadBatchLog from http
     */
    @Override
    public ResponseDTO<List<UploadBatchLogRepDTO>> queryList(UploadBatchLogQueryDTO dto) {
        UploadBatchLog entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        List<UploadBatchLog> entities = getService().queryByPara(entity);
        return ResponseDTO.success(ConvertUtils.convertEntityList2DTOList(entities, UploadBatchLogRepDTO.class));
    }

    /**
     * Page UploadBatchLog from http
     */
    @Override
    public ResponseDTO<SearchDTO<UploadBatchLogRepDTO>> queryPage(SearchDTO<UploadBatchLogQueryPageDTO> searchDTO) {
        Search<UploadBatchLog> search = ConvertUtils.convertSearchDTO2Search(searchDTO, entityClass);
        getService().queryPageByPara(search);
        //字典 uploadStatus、uploadMode、uploadType
        SearchDTO<UploadBatchLogRepDTO> uploadBatchLogRepDTOSearchDTO = ConvertUtils.convertSearch2SearchDTO(search, UploadBatchLogRepDTO.class);
        for (UploadBatchLogRepDTO record : uploadBatchLogRepDTOSearchDTO.getRecords()) {
            if(EmptyUtils.isNotEmpty(record.getMode())) {
                record.setModeLabel(dictionaryService.getDictName("uploadMode", record.getMode()));
            }
            if(EmptyUtils.isNotEmpty(record.getStatus())) {
                record.setStatusLabel(dictionaryService.getDictName("uploadStatus", record.getStatus()));
            }
            if(EmptyUtils.isNotEmpty(record.getType())) {
                record.setTypeLabel(dictionaryService.getDictName("uploadType", record.getType()));
            }

        }

        return ResponseDTO.success(uploadBatchLogRepDTOSearchDTO);
    }

    /**
     * Count UploadBatchLog from http
     */
    @Override
    public ResponseDTO<Long> queryCount(UploadBatchLogQueryDTO dto) {
        UploadBatchLog entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        Long count = getService().queryCountByPara(entity);
        return ResponseDTO.success(count);
    }

    /**
     * One UploadBatchLog from http
     */
    @Override
    public ResponseDTO<UploadBatchLogRepDTO> queryOne(UploadBatchLogQueryDTO dto) {
        UploadBatchLog qEntity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        UploadBatchLog rEntity = getService().queryOneByPara(qEntity);
        UploadBatchLogRepDTO rDTO = ConvertUtils.convertEntity2DTO(rEntity, UploadBatchLogRepDTO.class);
        return ResponseDTO.success(rDTO);
    }

    /**
     * exist UploadBatchLog from http
     */
    @Override
    public ResponseDTO<Boolean> exist(UploadBatchLogQueryDTO dto) {
        boolean resullt = getService().existByPara(ConvertUtils.convertDTO2Entity(dto, entityClass));
        return ResponseDTO.success(resullt);
    }
}
