package com.ey.tax.cloud.targets.constant;

import java.util.List;

public class CommonConstants {


    //字典线程变量名称前缀
    public static final String DICT_VALUE_THREAD_LOCAL_PREFIX = "dictValue_";

    //字典线程变量名称前缀
    public static final String DICT_NAME_LOCAL_PREFIX = "dictName_";
    public static final String DICT_PARENT_NAME_LOCAL_PREFIX = "dictParentName_";
    public static final String DICT_TAG_LOCAL_PREFIX = "dictTag_";

    //老板姓名的list
    public static final List<String> BOSS_NAME_LIST = List.of("<EMAIL>","<PERSON>.<PERSON>@cn.ey.com","Skylar.<PERSON>@cn.ey.com");
    public static final List<String> BOSS_NAME_LIST_TPC_PERF = List.of("<EMAIL>","<PERSON><PERSON><PERSON>@cn.ey.com","<EMAIL>","<PERSON>-<PERSON><PERSON><PERSON><PERSON>@cn.ey.com,<PERSON><PERSON>.<PERSON>@cn.ey.com");
    //去掉25年财年条件，哪年结束再补充结束逻辑
    public static final List<String> BOSS_NAME_LIST_25 = List.of("William-WL.Zhang", "Audrie.Xia");
    public static final List<String> MEMO_BOSS_NAME_LIST = List.of( "CN010772090");
    //弃用jesse
    public static final String Pipe_Jesse = "CN99999999999999999";
    public static final List<String> Pipe_Paul = List.of("HK010407787","CN010642396");

    public static final String IS_ACTIVE_STR = "1";
    public static final int IS_ACTIVE = 1;



    public static final String EM_KPI_TEMPLATE = "EM_KPI";
    public static final String ED_KPI_TEMPLATE = "ED_KPI";
    public static final String EP_KPI_TEMPLATE = "EP_KPI";



    public static final String kpi_supple =
            " [" +
            "    {" +
            "      \"title\": \"People - Teaming\"," +
            "      \"children\": [" +
            "        {" +
            "          \"title\": \"Events organized by Firm/Location\"," +
            "          \"children\": [" +
            "            {\"title\": \"\", \"desc\": \"\"}" +
            "          ]" +
            "        }," +
            "        {" +
            "          \"title\": \"Events organized by Tax/SSL/Group\"," +
            "          \"children\": [" +
            "            {\"title\": \"\", \"desc\": \"\"}" +
            "          ]" +
            "        }" +
            "      ]" +
            "    }," +
            "    {" +
            "      \"title\": \"People - Training\"," +
            "      \"children\": [" +
            "        {\"title\": \"Training arranged by Talent Team\", " +
                    "          \"children\": [" +
                    "            {\"title\": \"\", \"desc\": \"\"}" +
                    "          ]" +
                    "}," +
            "        {\"title\": \"Internal share and learn events arranged by each SSL\", " +
                    "          \"children\": [" +
                    "            {\"title\": \"\", \"desc\": \"\"}" +
                    "          ]" +
                    "}," +
            "        {\"title\": \"lunch & learn events arranged by each SSL\", " +
                    "          \"children\": [" +
                    "            {\"title\": \"\", \"desc\": \"\"}" +
                    "          ]" +
                    "}" +
            "      ]" +
            "    }," +
            "    {" +
            "      \"title\": \"Knowledge\"," +
            "      \"children\": [" +
            "        {\"title\": \"Knowledge sharing （i.e., business topics, local practices, case studies）\", " +
                    "          \"children\": [" +
                    "            {\"title\": \"\", \"desc\": \"\"}" +
                    "          ]" +
                    "}," +
            "        {\"title\": \"Contribution to innovative culture （i.e., ideas & solutions）\", " +
                    "          \"children\": [" +
                    "            {\"title\": \"\", \"desc\": \"\"}" +
                    "          ]" +
                    "}," +
            "        {\"title\": \"Intelligence sharing (i.e., sharing of latest information regarding industries, SOE, POE at the national or local level)\"," +
                    "          \"children\": [" +
                    "            {\"title\": \"\", \"desc\": \"\"}" +
                    "          ]" +
                    "}" +
            "      ]" +
            "    }," +
            "    {" +
            "      \"title\": \"Innovative Products\"," +
            "      \"children\": [" +
            "        {\"title\": \"Contribution of Knowledge ideas/initiatives\", " +
                    "          \"children\": [" +
                    "            {\"title\": \"\", \"desc\": \"\"}" +
                    "          ]" +
                    "}," +
            "        {\"title\": \"Solution maintenance (i.e., Maintain tax product or marketing and promotional materials, which need to be updated every quarter)\", " +
                    "          \"children\": [" +
                    "            {\"title\": \"\", \"desc\": \"\"}" +
                    "          ]" +
                    "}," +
            "        {\"title\": \"Solution development (i.e., Develop/Upgrade Tax Products)\", " +
                    "          \"children\": [" +
                    "            {\"title\": \"\", \"desc\": \"\"}" +
                    "          ]" +
                    "}" +
            "      ]" +
            "    }," +
            "    {" +
            "      \"title\": \"Marketing and Branding\"," +
            "      \"children\": [" +
            "        {\"title\": \"EY marketing events (hosted/co-hosted by EY)\", " +
                    "          \"children\": [" +
                    "            {\"title\": \"\", \"desc\": \"\"}" +
                    "          ]" +
                    "}," +
            "        {\"title\": \"EY marketing events (hosted by third party)\", " +
                    "          \"children\": [" +
                    "            {\"title\": \"\", \"desc\": \"\"}" +
                    "          ]" +
                    "}," +
            "        {" +
            "          \"title\": \"Articles or content published in EY's internal/external publications\"," +
            "          \"children\": [" +
            "            {\"title\": \"\", " +
                    "          \"children\": [" +
                    "            {\"title\": \"\", \"desc\": \"\"}" +
                    "          ]" +
                    "}" +

            "          ]" +
            "        }" +
            "      ]" +
            "    }" +
            "  ]";
}
