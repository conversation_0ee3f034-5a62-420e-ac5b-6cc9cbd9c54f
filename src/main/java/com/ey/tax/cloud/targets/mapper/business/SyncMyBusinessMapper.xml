<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ey.tax.cloud.targets.mapper.business.SyncMyBusinessMapper" >
  
  <resultMap id="EyTaxResultMap" type="com.ey.tax.cloud.targets.entity.business.SyncMyBusiness" >
    <id column="id" property="id" jdbcType="OTHER" />
    <result column="is_del" property="isDel" jdbcType="INTEGER" />
    <result column="auto_test" property="autoTest" jdbcType="SMALLINT" />
    <result column="create_uid" property="createUid" jdbcType="VARCHAR" />
    <result column="update_uid" property="updateUid" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="fiscal_year" property="fiscalYear" jdbcType="VARCHAR" />
    <result column="fiscal_week" property="fiscalWeek" jdbcType="VARCHAR" />
    <result column="engagement_code" property="engagementCode" jdbcType="VARCHAR" />
    <result column="engagement_name" property="engagementName" jdbcType="VARCHAR" />
    <result column="engagement_status" property="engagementStatus" jdbcType="VARCHAR" />
    <result column="engagement_partner_gpn" property="engagementPartnerGpn" jdbcType="VARCHAR" />
    <result column="engagement_manager_gpn" property="engagementManagerGpn" jdbcType="VARCHAR" />
    <result column="client_id" property="clientId" jdbcType="VARCHAR" />
    <result column="client_name" property="clientName" jdbcType="VARCHAR" />
    <result column="account_id" property="accountId" jdbcType="VARCHAR" />
    <result column="account_name" property="accountName" jdbcType="VARCHAR" />
    <result column="sub_mgmt_unit_code" property="subMgmtUnitCode" jdbcType="VARCHAR" />
    <result column="sub_mgmt_unit_name" property="subMgmtUnitName" jdbcType="VARCHAR" />
    <result column="base_currency" property="baseCurrency" jdbcType="VARCHAR" />
    <result column="base_currency_name" property="baseCurrencyName" jdbcType="VARCHAR" />
    <result column="opportunity_id" property="opportunityId" jdbcType="VARCHAR" />
    <result column="planned_total_engagement_revenue" property="plannedTotalEngagementRevenue" jdbcType="NUMERIC" />
    <result column="contract_amount" property="contractAmount" jdbcType="NUMERIC" />
    <result column="e_net_unbld_inv" property="eNetUnbldInv" jdbcType="NUMERIC" />
    <result column="f_net_unbld_inv" property="fNetUnbldInv" jdbcType="NUMERIC" />
    <result column="e_tot_exp" property="eTotExp" jdbcType="NUMERIC" />
    <result column="f_tot_exp" property="fTotExp" jdbcType="NUMERIC" />
    <result column="e_ter" property="eTer" jdbcType="NUMERIC" />
    <result column="f_ter" property="fTer" jdbcType="NUMERIC" />
    <result column="e_ner" property="eNer" jdbcType="NUMERIC" />
    <result column="f_ner" property="fNer" jdbcType="NUMERIC" />
    <result column="e_ser" property="eSer" jdbcType="NUMERIC" />
    <result column="f_ser" property="fSer" jdbcType="NUMERIC" />
    <result column="e_margin" property="eMargin" jdbcType="NUMERIC" />
    <result column="f_margin" property="fMargin" jdbcType="NUMERIC" />
    <result column="e_margin_pct" property="eMarginPct" jdbcType="NUMERIC" />
    <result column="f_margin_pct" property="fMarginPct" jdbcType="NUMERIC" />
    <result column="e_dir_cost" property="eDirCost" jdbcType="NUMERIC" />
    <result column="f_dir_cost" property="fDirCost" jdbcType="NUMERIC" />
    <result column="e_net_rev_per_hour" property="eNetRevPerHour" jdbcType="NUMERIC" />
    <result column="f_net_rev_per_hour" property="fNetRevPerHour" jdbcType="NUMERIC" />
    <result column="e_chg_hrs" property="eChgHrs" jdbcType="NUMERIC" />
    <result column="f_chg_hrs" property="fChgHrs" jdbcType="NUMERIC" />
    <result column="e_tot_ar" property="eTotAr" jdbcType="NUMERIC" />
    <result column="f_tot_ar" property="fTotAr" jdbcType="NUMERIC" />
    <result column="e_ar_tax" property="eArTax" jdbcType="NUMERIC" />
    <result column="f_ar_tax" property="fArTax" jdbcType="NUMERIC" />
    <result column="e_bld_fee_exp" property="eBldFeeExp" jdbcType="NUMERIC" />
    <result column="f_bld_fee_exp" property="fBldFeeExp" jdbcType="NUMERIC" />
    <result column="e_bld_tax" property="eBldTax" jdbcType="NUMERIC" />
    <result column="f_bld_tax" property="fBldTax" jdbcType="NUMERIC" />
    <result column="e_col_fee_exp" property="eColFeeExp" jdbcType="NUMERIC" />
    <result column="f_col_fee_exp" property="fColFeeExp" jdbcType="NUMERIC" />
    <result column="e_col_tax" property="eColTax" jdbcType="NUMERIC" />
    <result column="f_col_tax" property="fColTax" jdbcType="NUMERIC" />
    <result column="e_ar_rsv" property="eArRsv" jdbcType="NUMERIC" />
    <result column="f_ar_rsv" property="fArRsv" jdbcType="NUMERIC" />
    <result column="e_ar_vat_rsv" property="eArVatRsv" jdbcType="NUMERIC" />
    <result column="f_ar_vat_rsv" property="fArVatRsv" jdbcType="NUMERIC" />
    <result column="e_ar_write_off" property="eArWriteOff" jdbcType="NUMERIC" />
    <result column="f_ar_write_off" property="fArWriteOff" jdbcType="NUMERIC" />
    <result column="e_ar_vat_write_off" property="eArVatWriteOff" jdbcType="NUMERIC" />
    <result column="f_ar_vat_write_off" property="fArVatWriteOff" jdbcType="NUMERIC" />
    <result column="e_revenue_days" property="eRevenueDays" jdbcType="NUMERIC" />
    <result column="etl_time" property="etlTime" jdbcType="TIMESTAMP" />
    <result column="ds" property="ds" jdbcType="VARCHAR" />
  </resultMap>
  
</mapper>