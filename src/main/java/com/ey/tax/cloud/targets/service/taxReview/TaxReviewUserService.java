package com.ey.tax.cloud.targets.service.taxReview;

import com.ey.cn.tax.framework.service.BaseService;
import com.ey.tax.cloud.targets.entity.taxReview.TaxReviewUser;

import java.util.List;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-01-30 14:57:06
 *
 */
public interface TaxReviewUserService extends BaseService<TaxReviewUser> {

    void updateUserToAdmin(List<TaxReviewUser> userAttributes);
}
