package com.ey.tax.cloud.targets.service.pipeline.impl;

import com.ey.cn.tax.framework.mybatisplus.extension.service.AbstractService;
import com.ey.tax.cloud.targets.entity.pipeline.Pipeline;
import com.ey.tax.cloud.targets.entity.pipeline.PipelineForTransfer;
import com.ey.tax.cloud.targets.mapper.pipeline.PipelineForTransferMapper;
import com.ey.tax.cloud.targets.service.pipeline.PipelineForTransferService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2023-12-25 11:19:56
 *
 */
@Service
public class PipelineForTransferServiceImpl extends AbstractService<PipelineForTransferMapper, PipelineForTransfer> implements PipelineForTransferService {

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void save(Collection<PipelineForTransfer> entities) {
        logger.debug("save {}:{}", getEntitySimpleName(), entities);

        if (CollectionUtils.isEmpty(entities)) {
            return;
        }
        for (List<PipelineForTransfer> partition : ListUtils.partition(entities.stream().toList(), 500)) {
            baseMapper.insertBatch(partition);
        }
    }

}
