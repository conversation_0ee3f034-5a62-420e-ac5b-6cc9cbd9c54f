package com.ey.tax.cloud.targets.job;

import com.ey.cn.tax.framework.utils.EmptyUtils;
import com.ey.cn.tax.framework.utils.JacksonUtils;
import com.ey.tax.cloud.targets.constant.BusFaultConstants;
import com.ey.tax.cloud.targets.service.transfer.DataTransferService;
import com.ey.tax.cloud.targets.utils.ExceptionUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class BusinessTransferJobHandler extends AbstractJobHandler{

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private Map<String, DataTransferService> dataTransferServiceMap;

    public void businessTransfer(Map<String, String> paramMap){
        DataTransferService dataTransferService = dataTransferServiceMap.get("business");
        dataTransferService.process(paramMap);
    }

    @XxlJob("BUSINESS_TRANSFER_JOB")
    public ReturnT<String> businessPipeline(){
        try {
            String param = XxlJobHelper.getJobParam();
            Map<String, String> paramMap = null;
            if(EmptyUtils.isNotEmpty(param)){
                paramMap = JacksonUtils.readValue(param, Map.class);
            }
            businessTransfer(paramMap);
            return ReturnT.SUCCESS;
        }catch (Exception e){
            logger.error("business transfer job error", e);
            XxlJobHelper.log("business transfer job error");
            String error = ExceptionUtils.getFullStackTrace(e);
            XxlJobHelper.log(error);
            return ReturnT.FAIL;
        }
    }

}
