package com.ey.tax.cloud.targets.dto.business;

import com.ey.cn.tax.framework.validator.DeleteByIdListGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-07-29 16:59:07
 * 
 */
@Data
@Schema(description = "根据id列表删除实体")
public class MyBusinessScopeDeleteByIdListDTO {
    
    /**
     * 数据id列表
     */
    @Schema(description = "数据id列表")
    @NotNull(message = "{MyBusinessScopeDTO.ids.NotNull}", groups = {DeleteByIdListGroup.class})
    @Size(min = 1, message = "{MyBusinessScopeDTO.ids.Size}", groups = {DeleteByIdListGroup.class})
    private List<String> ids;
}