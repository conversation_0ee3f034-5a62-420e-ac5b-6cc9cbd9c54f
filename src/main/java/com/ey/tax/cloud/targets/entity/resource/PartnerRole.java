package com.ey.tax.cloud.targets.entity.resource;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ey.cn.tax.framework.mybatisplus.core.entity.BaseEntity;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-01-17 14:35:33
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("target_resource_partner_role")
public class PartnerRole extends BaseEntity {
    /**
     * COLUMN:ssl_id
     */
    private String ssl;

    /**
     * COLUMN:user_id
     */
    private String userId;

    /**
     * COLUMN:fiscal_year
     */
    private String fiscalYear;

    /**
     * COLUMN:name
     */
    private String name;

    /**
     * COLUMN:gpn
     */
    private String gpn;


    /**
     * COLUMN:group_name
     */
    private String groupName;

    /**
     * COLUMN:role_name
     */
    private String roleName;

    /**
     * COLUMN:role_coefficient
     */
    private BigDecimal roleCoefficient;

    /**
     * COLUMN:role_target
     */
    private String roleTarget;

    /**
     * COLUMN:role_actual
     */
    private BigDecimal roleActual;

    /**
     * COLUMN:completion
     */
    private BigDecimal completion;

    /**
     * COLUMN:score
     */
    private BigDecimal score;
}
