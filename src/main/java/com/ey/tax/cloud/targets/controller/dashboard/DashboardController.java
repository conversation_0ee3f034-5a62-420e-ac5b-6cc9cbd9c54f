package com.ey.tax.cloud.targets.controller.dashboard;

import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.web.annotation.EyPostMapping;
import com.ey.cn.tax.framework.web.base.BaseController;
import com.ey.cn.tax.lite.impl.port.input.dto.LiteDatasetExecutionDTO;
import com.ey.tax.cloud.targets.dto.dashboard.DashboardDTO;
import com.ey.tax.cloud.targets.dto.dashboard.DashboardQueryDataDTO;
import com.ey.tax.cloud.targets.dto.dashboard.DashboardRepDTO;
import com.ey.tax.cloud.targets.dto.pipeline.PipelineDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.RequestBody;

@Tag(name="Dashboard接口")
public interface DashboardController extends BaseController<DashboardDTO> {

    /**
     * 根据code获取图表数据
     *
     */
    @Operation(summary = "通用接口", description = "根据code获取Dashboard图表数据")
    @EyPostMapping(value = "/queryData.do")
    ResponseDTO<DashboardRepDTO> queryData(@RequestBody DashboardQueryDataDTO dto);

    /**
     * 根据code获取图表数据
     *
     */
    @Operation(summary = "forecast接口", description = "获取forecast图表数据")
    @EyPostMapping(value = "/queryForecastData.do")
    ResponseDTO<DashboardRepDTO> queryForecastData(@RequestBody DashboardQueryDataDTO dto);

    @Operation(summary = "test", description = "test")
    @EyPostMapping(value = "/test.do")
    ResponseDTO<Boolean> test(@RequestBody LiteDatasetExecutionDTO liteDatasetExecutionDTO);

}
