package com.ey.tax.cloud.targets.service.kpi.impl;

import com.ey.cn.tax.framework.mybatisplus.extension.service.AbstractService;
import com.ey.tax.cloud.targets.entity.kpi.KpiResult;
import com.ey.tax.cloud.targets.mapper.kpi.KpiResultMapper;
import com.ey.tax.cloud.targets.service.kpi.KpiResultService;
import org.springframework.stereotype.Service;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-01-17 14:35:37
 * 
 */
@Service
public class KpiResultServiceImpl extends AbstractService<KpiResultMapper, KpiResult> implements KpiResultService {
}