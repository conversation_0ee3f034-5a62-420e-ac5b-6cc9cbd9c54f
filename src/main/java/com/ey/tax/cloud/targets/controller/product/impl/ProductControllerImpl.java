package com.ey.tax.cloud.targets.controller.product.impl;

import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheManager;
import com.alicp.jetcache.anno.CacheInvalidate;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.dto.SearchDTO;
import com.ey.cn.tax.framework.entity.Search;
import com.ey.cn.tax.framework.utils.ConvertUtils;
import com.ey.cn.tax.framework.utils.EmptyUtils;
import com.ey.cn.tax.framework.web.annotation.EyRestController;
import com.ey.cn.tax.framework.web.base.AbstractController;
import com.ey.tax.cloud.targets.constant.ErrorCodeConstans;
import com.ey.tax.cloud.targets.constant.TargetAuthConstants;
import com.ey.tax.cloud.targets.controller.product.ProductController;
import com.ey.tax.cloud.targets.dto.data.DictionaryItemRepDTO;
import com.ey.tax.cloud.targets.dto.product.*;
import com.ey.tax.cloud.targets.entity.data.DictionaryItem;
import com.ey.tax.cloud.targets.entity.pipeline.Pipeline;
import com.ey.tax.cloud.targets.entity.product.Product;
import com.ey.tax.cloud.targets.entity.product.ProductPath;
import com.ey.tax.cloud.targets.service.data.DictionaryItemService;
import com.ey.tax.cloud.targets.service.data.DictionaryService;
import com.ey.tax.cloud.targets.service.data.FiscalCalenderService;
import com.ey.tax.cloud.targets.service.pipeline.PipelineService;
import com.ey.tax.cloud.targets.service.product.ProductService;
import com.ey.tax.cloud.targets.utils.CommonUtils;
import com.grapecity.documents.excel.Workbook;
import com.grapecity.documents.excel.template.DataSource.JsonDataSource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-01-09 11:49:16
 *
 */
@EyRestController(path ="/v1/product")
public class ProductControllerImpl extends AbstractController<ProductService, ProductDTO, Product> implements ProductController {


    @Autowired
    private DictionaryService dictionaryService;

    @Autowired
    private PipelineService pipelineService;

    @Autowired
    private FiscalCalenderService fiscalCalenderService;
    @Autowired
    private DictionaryItemService dictionaryItemService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;


    @Autowired
    private CacheManager cacheManager;
    /**
     * add product from http
     */
    @Override
    public ResponseDTO<Void> add(ProductAddDTO dto) {
        String key2 = dto.getFiscalYear();
        //"#fiscalYear + ':' + #id"
        String key3 = dto.getFiscalYear()+":" + dto.getProductId();
        Cache cache2 = cacheManager.getCache("productPath:");
        Cache cache3 = cacheManager.getCache("product:");
        Cache cache4 = cacheManager.getCache("productPara:");
        if(EmptyUtils.isNotEmpty(cache2)){
            cache2.remove(key2);
        }
        if(EmptyUtils.isNotEmpty(cache3)){
            cache3.REMOVE(key3);
        }
        if(EmptyUtils.isNotEmpty(cache4)){
            cache4.remove(key2+":" + "1");
            cache4.remove(key2+":" + "0");
        }
        Product entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        entity.setProductId(UUID.randomUUID().toString());
        //根据level0+level1+productCode判断是否重复
        Product productPath = new Product();
        productPath.setLevel0(entity.getLevel0());
        productPath.setLevel1(entity.getLevel1());
        productPath.setProductNameClear(entity.getProductName());
        productPath.setFiscalYear(entity.getFiscalYear());
        List<Product> products = service.queryByPara(productPath);
        if(EmptyUtils.isNotEmpty(products)){
            throwBuildBusinessException(ErrorCodeConstans.DATA_REPEAT);
        }
        entity.setType(1);
        service.save(entity);
        return ResponseDTO.success();
    }


    /**
     * delete product from http
     */
    @Override
    public ResponseDTO<Void> deleteById(ProductDeleteByIdDTO dto) {
        //如果pipeline用到了这个product，就不能删除
        Pipeline pipeline = new Pipeline();
        pipeline.setProductId(dto.getId());
        List<Pipeline> pipelines = pipelineService.queryByPara(pipeline);
        if(EmptyUtils.isNotEmpty(pipelines)){
            throwBuildBusinessException(ErrorCodeConstans.PRODUCT_USED_BY_PIPELINE_ERROR);
        }
        Product p = service.queryById(dto.getId());
        p.setStatus(0);
        service.update(p);
        return ResponseDTO.success();
    }

    /**
     * deleteList product from http
     */
    @Override
    public ResponseDTO<Void> deleteByIdList(ProductDeleteByIdListDTO dto) {
        getService().deleteByIds(dto.getIds());
        return ResponseDTO.success();
    }

    /**
     * update product from http
     */
    @Override
    public ResponseDTO<Void> updateById(ProductUpdateByIdDTO dto) {
        String key2 = dto.getFiscalYear();
        //"#fiscalYear + ':' + #id"
        String key3 = dto.getFiscalYear()+":" + dto.getProductId();
        Cache cache2 = cacheManager.getCache("productPath:");
        Cache cache3 = cacheManager.getCache("product:");
        Cache cache4 = cacheManager.getCache("productPara:");
        if(EmptyUtils.isNotEmpty(cache2)){
            cache2.remove(key2);
        }
        if(EmptyUtils.isNotEmpty(cache3)){
            cache3.REMOVE(key3);
        }
        if(EmptyUtils.isNotEmpty(cache4)){
            cache4.remove(key2+":" + "1");
            cache4.remove(key2+":" + "0");
        }
        Product entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        //根据level0+level1+productCode判断是否重复
        Product productPath = new Product();
        productPath.setLevel0(entity.getLevel0());
        productPath.setLevel1(entity.getLevel1());
        productPath.setProductNameClear(entity.getProductName());
        productPath.setFiscalYear(entity.getFiscalYear());
        List<Product> products = service.queryByPara(productPath);
        if(EmptyUtils.isNotEmpty(products)&&!products.get(0).getId().equals(entity.getId())){
            throwBuildBusinessException(ErrorCodeConstans.DATA_REPEAT);
        }
        getService().update(entity);
        return ResponseDTO.success();
    }



    /**
     * query product from http
     */
    @Override
    public ResponseDTO<ProductRepDTO> queryById(ProductQueryByIdDTO dto) {
        Product entity = getService().queryById(dto.getId());
        ProductRepDTO rDTO = ConvertUtils.convertEntity2DTO(entity, ProductRepDTO.class);
        return ResponseDTO.success(rDTO);
    }

    /**
     * queryList product from http
     */

    @Override
    public ResponseDTO<List<ProductRepTreeDTO>> queryList(ProductQueryDTO dto) {

        List<ProductRepTreeDTO> productRepTreeDTOS = new ArrayList<>();
        Product entity = null;
        String productId = null;
        if(EmptyUtils.isNotEmpty(dto.getProductId())){
            productId = dto.getProductId();
            dto.setProductId(null);
        }
        if (Objects.isNull(dto)){
            entity = new Product();
        } else {
            entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        }
        String currentFiscalCalender = fiscalCalenderService.getCurrentFiscalCalender();
        if(EmptyUtils.isEmpty(entity.getFiscalYear())){
            entity.setFiscalYear(currentFiscalCalender);
        }else {
            currentFiscalCalender = entity.getFiscalYear();
        }
        List<Product> entities = getService().queryByPara(entity);
        //entity.setType(1);
        List<ProductRepDTO> dtos = ConvertUtils.convertDTOList2EntityList(entities, ProductRepDTO.class);
        if(Integer.valueOf(currentFiscalCalender)<25){

            /**
             * 根据product的ssl分组同时根据orderNum排序
             */
            Map<String, List<ProductRepDTO>> map = dtos.stream().filter(d->EmptyUtils.isNotEmpty(d.getSsl())).sorted(Comparator.comparing(ProductRepDTO::getOrderNum)).collect(Collectors.groupingBy(ProductRepDTO::getSsl));
            /**
             * 提取出hotProduct为1的数据也添加到map2里,放在首位
             */
            List<ProductRepDTO> hotProducts = dtos.stream().filter(ProductRepDTO -> EmptyUtils.isNotEmpty(ProductRepDTO.getHotProduct())&&ProductRepDTO.getHotProduct() == 1).toList();
            List<ProductRepTreeDTO> hotProductTree = new ArrayList<>();
            hotProducts.forEach(productRepDTO -> {
                ProductRepTreeDTO productRepTreeDTO = new ProductRepTreeDTO();
                productRepTreeDTO.setLabel(productRepDTO.getProductName());
                productRepTreeDTO.setValue(productRepDTO.getProductId());
                productRepTreeDTO.setId(productRepDTO.getId()+"hotProduct");
                hotProductTree.add(productRepTreeDTO);
            });
            if(EmptyUtils.isNotEmpty(hotProductTree)){
                ProductRepTreeDTO productRepTreeDTO = new ProductRepTreeDTO();
                productRepTreeDTO.setLabel("hotProduct");
                productRepTreeDTO.setValue("hotProduct");
                productRepTreeDTO.setId("hotProduct");
                productRepTreeDTO.setChildren(hotProductTree);
                productRepTreeDTOS.add(productRepTreeDTO);
            }
            /**
             * map的key为ssl的id，查询出id对应的值替换掉key,key要根据orderNum排序
             */
            for (String s : map.keySet()) {
                ProductRepTreeDTO productRepTreeDTO1 = new ProductRepTreeDTO();
                productRepTreeDTO1.setLabel(dictionaryService.getDictName(TargetAuthConstants.PRODUCTLEVEL0, s));
                productRepTreeDTO1.setValue(dictionaryService.getDictName(TargetAuthConstants.PRODUCTLEVEL0, s));
                //根据level1分组
                Map<String, List<ProductRepDTO>> map1 = map.get(s).stream().filter(d->EmptyUtils.isNotEmpty(d.getLevel1())).sorted(Comparator.comparing(ProductRepDTO::getOrderNum)).collect(Collectors.groupingBy(ProductRepDTO::getLevel1));
                List<ProductRepTreeDTO> productRepTreeDTOS1 = new ArrayList<>();
                for (String s1 : map1.keySet()) {
                    ProductRepTreeDTO productRepTreeDTO2 = new ProductRepTreeDTO();
                    productRepTreeDTO2.setLabel(dictionaryService.getDictName(TargetAuthConstants.PRODUCT_SSL, s1));
                    productRepTreeDTO2.setValue(dictionaryService.getDictName(TargetAuthConstants.PRODUCT_SSL, s1));
                    List<ProductRepTreeDTO> productRepTreeDTOS2 = new ArrayList<>();
                    map1.get(s1).forEach(productRepDTO -> {
                        ProductRepTreeDTO productRepTreeDTO3 = new ProductRepTreeDTO();
                        productRepTreeDTO3.setLabel(productRepDTO.getProductName());
                        productRepTreeDTO3.setValue(productRepDTO.getProductId());
                        productRepTreeDTOS2.add(productRepTreeDTO3);
                    });
                    productRepTreeDTO2.setChildren(productRepTreeDTOS2);
                    productRepTreeDTOS1.add(productRepTreeDTO2);
                }
                productRepTreeDTO1.setChildren(productRepTreeDTOS1);
                productRepTreeDTOS.add(productRepTreeDTO1);
            }
            return ResponseDTO.success(productRepTreeDTOS);
        }else {
            //过滤level1为空的数据
            dtos = dtos.stream().filter(d->EmptyUtils.isNotEmpty(d.getLevel1())).collect(Collectors.toList());
            /**
             * 根据product的level0分组同时根据orderNum排序
             *
             */
            Map<String, List<ProductRepDTO>> map = new LinkedHashMap<>();
            //查询字典product_Level0
            List<DictionaryItem> dictionaryItems = dictionaryService.buildList("product_Level0");
            for (DictionaryItem dictionaryItem : dictionaryItems) {//根据level0分组插入map,从小到大排序
                List<ProductRepDTO> productRepDTOS = dtos.stream().filter(productRepDTO -> productRepDTO.getLevel0().equals(dictionaryItem.getItemKey())).sorted(Comparator.comparing(ProductRepDTO::getOrderNum)).collect(Collectors.toList());
              if(EmptyUtils.isNotEmpty(productRepDTOS)){
                  map.put(dictionaryItem.getItemKey(),productRepDTOS);
              }
            }

            /**
             * level0为空时，使用level1分组
             *
             *
             */
            Map<String, List<ProductRepDTO>> map2 = new LinkedHashMap<>();
            //查询字典product_SSL
            List<DictionaryItem> dictionaryItems1 = dictionaryService.buildList("product_ssl");
            for (DictionaryItem dictionaryItem : dictionaryItems1) {//根据level1分组插入map2
                List<ProductRepDTO> productRepDTOS = dtos.stream().filter(productRepDTO ->EmptyUtils.isEmpty(productRepDTO.getLevel0())&& productRepDTO.getLevel1().equals(dictionaryItem.getItemKey())).sorted(Comparator.comparing(ProductRepDTO::getOrderNum)).collect(Collectors.toList());
                if(EmptyUtils.isNotEmpty(productRepDTOS)){
                    map2.put(dictionaryItem.getItemKey(),productRepDTOS);
                }
            }

            /**
             * 提取出hotProduct为1的数据也添加到map2里,放在首位
             */
            List<ProductRepDTO> hotProducts = dtos.stream().filter(ProductRepDTO -> EmptyUtils.isNotEmpty(ProductRepDTO.getHotProduct())&&ProductRepDTO.getHotProduct() == 1).toList();
            List<ProductRepTreeDTO> hotProductTree = new ArrayList<>();
            hotProducts.forEach(productRepDTO -> {
                ProductRepTreeDTO productRepTreeDTO = new ProductRepTreeDTO();
                productRepTreeDTO.setLabel(productRepDTO.getProductName());
                productRepTreeDTO.setValue(productRepDTO.getProductId());
                productRepTreeDTO.setId(productRepDTO.getId()+"hotProduct");
                productRepTreeDTO.setShow(true);
                hotProductTree.add(productRepTreeDTO);
            });
            if(EmptyUtils.isNotEmpty(hotProductTree)){
                ProductRepTreeDTO productRepTreeDTO = new ProductRepTreeDTO();
                productRepTreeDTO.setLabel("hotProduct");
                productRepTreeDTO.setValue("hotProduct");
                productRepTreeDTO.setId("hotProduct");
                productRepTreeDTO.setChildren(hotProductTree);
                productRepTreeDTO.setShow(true);
                productRepTreeDTOS.add(productRepTreeDTO);
            }
            for (String s : map.keySet()) {
                ProductRepTreeDTO productRepTreeDTO1 = new ProductRepTreeDTO();
                productRepTreeDTO1.setLabel(dictionaryService.getDictName(TargetAuthConstants.PRODUCTLEVEL0, s));
                productRepTreeDTO1.setValue(dictionaryService.getDictName(TargetAuthConstants.PRODUCTLEVEL0, s));
                //根据level1分组
                Map<String, List<ProductRepDTO>> map1 = new LinkedHashMap<>();
                for (DictionaryItem dictionaryItem : dictionaryItems1) {
                    List<ProductRepDTO> productRepDTOS = map.get(s).stream().filter(productRepDTO ->productRepDTO.getLevel1().equals(dictionaryItem.getItemKey())).sorted(Comparator.comparing(ProductRepDTO::getOrderNum)).collect(Collectors.toList());
                    if(EmptyUtils.isNotEmpty(productRepDTOS)){
                        map1.put(dictionaryItem.getItemKey(),productRepDTOS);
                    }
                }
                List<ProductRepTreeDTO> productRepTreeDTOS1 = new ArrayList<>();
                for (String s1 : map1.keySet()) {
                    ProductRepTreeDTO productRepTreeDTO2 = new ProductRepTreeDTO();
                    productRepTreeDTO2.setLabel(dictionaryService.getDictName(TargetAuthConstants.PRODUCT_SSL, s1));
                    productRepTreeDTO2.setValue(dictionaryService.getDictName(TargetAuthConstants.PRODUCT_SSL, s1));
                    List<ProductRepTreeDTO> productRepTreeDTOS2 = new ArrayList<>();
                    map1.get(s1).forEach(productRepDTO -> {
                        ProductRepTreeDTO productRepTreeDTO3 = new ProductRepTreeDTO();
                        productRepTreeDTO3.setLabel(productRepDTO.getProductName());
                        productRepTreeDTO3.setValue(productRepDTO.getProductId());
                        productRepTreeDTO3.setShow(true);
                        productRepTreeDTOS2.add(productRepTreeDTO3);
                    });
                    productRepTreeDTO2.setChildren(productRepTreeDTOS2);
                    productRepTreeDTO2.setShow(true);
                    productRepTreeDTOS1.add(productRepTreeDTO2);
                }
                productRepTreeDTO1.setChildren(productRepTreeDTOS1);
                productRepTreeDTO1.setShow(true);
                productRepTreeDTOS.add(productRepTreeDTO1);
            }
            for (String s : map2.keySet()) {
                ProductRepTreeDTO productRepTreeDTO1 = new ProductRepTreeDTO();
                productRepTreeDTO1.setLabel(dictionaryService.getDictName(TargetAuthConstants.PRODUCT_SSL, s));
                productRepTreeDTO1.setValue(dictionaryService.getDictName(TargetAuthConstants.PRODUCT_SSL, s));
                List<ProductRepTreeDTO> productRepTreeDTOS1 = new ArrayList<>();
                map2.get(s).forEach(productRepDTO -> {
                    ProductRepTreeDTO productRepTreeDTO2 = new ProductRepTreeDTO();
                    productRepTreeDTO2.setLabel(productRepDTO.getProductName());
                    productRepTreeDTO2.setValue(productRepDTO.getProductId());
                    productRepTreeDTO2.setShow(true);
                    productRepTreeDTOS1.add(productRepTreeDTO2);
                });
                productRepTreeDTO1.setShow(true);
                productRepTreeDTO1.setChildren(productRepTreeDTOS1);
                productRepTreeDTOS.add(productRepTreeDTO1);
            }
            if(EmptyUtils.isEmpty(dto.getFiscalYear())){
                List<Product> products = service.selectProductListByFiscalYear();
                for (Product product : products) {
                    ProductRepTreeDTO productRepTreeDTO2 = new ProductRepTreeDTO();
                    productRepTreeDTO2.setLabel(product.getProductName());
                    productRepTreeDTO2.setValue(product.getProductId());
                    if(product.getProductId().equals(productId)){
                        productRepTreeDTO2.setShow(true);
                    }else {
                        productRepTreeDTO2.setShow(false);
                    }
                    productRepTreeDTOS.add(productRepTreeDTO2);
                }

            }

            return ResponseDTO.success(productRepTreeDTOS);

        }


    }

    /**
     * Page product from http
     */
    @Override
    public ResponseDTO<SearchDTO<ProductRepDTO>> queryPage(SearchDTO<ProductQueryPageDTO> searchDTO) {
        Search<Product> search = ConvertUtils.convertSearchDTO2Search(searchDTO, entityClass);
        getService().queryPageByPara(search);
        SearchDTO<ProductRepDTO> searchDTO1 = ConvertUtils.convertSearch2SearchDTO(search, ProductRepDTO.class);
        searchDTO1.getRecords().forEach(productRepDTO -> {
            productRepDTO.setHotProductLabel(dictionaryService.getDictName(TargetAuthConstants.AUTH_GROUP_DICT_YSE_NO, String.valueOf(productRepDTO.getHotProduct())));
            productRepDTO.setIsForwardLabel(dictionaryService.getDictName(TargetAuthConstants.AUTH_GROUP_DICT_YSE_NO, String.valueOf(productRepDTO.getIsForward())));
            productRepDTO.setStatusLabel(dictionaryService.getDictName(TargetAuthConstants.AUTH_GROUP_DICT_GROUP_STATUS, String.valueOf(productRepDTO.getStatus())));
            productRepDTO.setSslLabel(dictionaryService.getDictName(TargetAuthConstants.PRODUCT_SSL, productRepDTO.getLevel1()));
            if(EmptyUtils.isNotEmpty(productRepDTO.getLevel0())){
                productRepDTO.setLevel0Label(dictionaryService.getDictName(TargetAuthConstants.PRODUCTLEVEL0, productRepDTO.getLevel0()));
            }
            //fiscalYear
            productRepDTO.setFiscalYearLabel(dictionaryService.getDictName(TargetAuthConstants.FISCAL_YEAR, productRepDTO.getFiscalYear()));
        });
        return ResponseDTO.success(searchDTO1);
    }

    @Override
    public void exportProduct(SearchDTO<ProductQueryPageDTO> searchDTO) {
        Search<Product> search = ConvertUtils.convertSearchDTO2Search(searchDTO, entityClass);
        List<Product> products = getService().queryByPara(search.getQueryParams());
        search.setRecords(products);
        SearchDTO<ProductRepDTO> searchDTO1 = ConvertUtils.convertSearch2SearchDTO(search, ProductRepDTO.class);
        searchDTO1.getRecords().forEach(productRepDTO -> {
            productRepDTO.setHotProductLabel(dictionaryService.getDictName(TargetAuthConstants.AUTH_GROUP_DICT_YSE_NO, String.valueOf(productRepDTO.getHotProduct())));
            productRepDTO.setIsForwardLabel(dictionaryService.getDictName(TargetAuthConstants.AUTH_GROUP_DICT_YSE_NO, String.valueOf(productRepDTO.getIsForward())));
            productRepDTO.setStatusLabel(dictionaryService.getDictName(TargetAuthConstants.AUTH_GROUP_DICT_GROUP_STATUS, String.valueOf(productRepDTO.getStatus())));
            productRepDTO.setSslLabel(dictionaryService.getDictName(TargetAuthConstants.PRODUCT_SSL, productRepDTO.getLevel1()));
            if(EmptyUtils.isNotEmpty(productRepDTO.getLevel0())){
                productRepDTO.setLevel0Label(dictionaryService.getDictName(TargetAuthConstants.PRODUCTLEVEL0, productRepDTO.getLevel0()));
            }
            productRepDTO.setFiscalYear(dictionaryService.getDictName(TargetAuthConstants.FISCAL_YEAR, productRepDTO.getFiscalYear()));
        });
        Workbook workbook = new Workbook();
        //打开resource下的模板文件
        Resource resource = new ClassPathResource("excel/product_template.xlsx");
        try {
            InputStream inputStream = resource.getInputStream();
            workbook.open(inputStream);
            JsonDataSource a = new JsonDataSource(JSONObject.toJSONString(searchDTO1.getRecords()));
            workbook.addDataSource("DS", a);
            HttpServletResponse response = getCurrentResponse();
            response.setContentType("application/vnd.ms-excel");
            //生成yyyyMMdd格式的文件名
            SimpleDateFormat sdf = new SimpleDateFormat("MMdd");
            String fileName = "Target Product" + sdf.format(System.currentTimeMillis()) + ".xlsx";
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName + "");
            //添加fileName header
            response.setHeader("fileName", fileName);
            workbook.processTemplate();
            workbook.save(response.getOutputStream());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public ResponseDTO test(ProductQueryByIdDTO dto) {
        return ResponseDTO.success();
    }

    public HttpServletResponse getCurrentResponse() {
        ServletRequestAttributes attrs = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attrs != null) {
            return attrs.getResponse();
        } else {
            // 在某些情况下，RequestContextHolder中可能没有response，例如非请求线程中
            throw new IllegalStateException("No HttpServletResponse currently bound to the thread");
        }
    }
}
