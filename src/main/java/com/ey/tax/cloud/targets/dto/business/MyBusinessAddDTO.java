package com.ey.tax.cloud.targets.dto.business;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-07-29 14:23:08
 *
 */
@Data
@Schema(description = "新增实体")
public class MyBusinessAddDTO {
    /**
     * Fiscal year COLUMN:fiscal_year
     */
    @Schema(description = "Fiscal year", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String fiscalYear;

    /**
     * Fiscal week COLUMN:fiscal_week
     */
    @Schema(description = "Fiscal week", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String fiscalWeek;

    /**
     * 项目code COLUMN:engagement_code
     */
    @Schema(description = "项目code", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String engagementCode;

    /**
     * 项目名称 COLUMN:engagement_name
     */
    @Schema(description = "项目名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String engagementName;

    /**
     * 项目状态 COLUMN:engagement_status
     */
    @Schema(description = "项目状态", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String engagementStatus;

    /**
     * 项目合伙人 COLUMN:engagement_partner_gpn
     */
    @Schema(description = "项目合伙人", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String engagementPartnerGpn;

    /**
     * 项目经理 COLUMN:engagement_manager_gpn
     */
    @Schema(description = "项目经理", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String engagementManagerGpn;

    /**
     * 客户编号 COLUMN:client_id
     */
    @Schema(description = "客户编号", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String clientId;

    /**
     * 客户名称 COLUMN:client_name
     */
    @Schema(description = "客户名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String clientName;

    /**
     * 集团大客户编号 COLUMN:account_id
     */
    @Schema(description = "集团大客户编号", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String accountId;

    /**
     * 集团大客户名称 COLUMN:account_name
     */
    @Schema(description = "集团大客户名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String accountName;

    /**
     * SMU COLUMN:sub_mgmt_unit_code
     */
    @Schema(description = "SMU", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String subMgmtUnitCode;

    /**
     * COLUMN:sub_mgmt_unit_name
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String subMgmtUnitName;

    /**
     * 币种 COLUMN:base_currency
     */
    @Schema(description = "币种", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String baseCurrency;

    /**
     * COLUMN:base_currency_name
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String baseCurrencyName;

    /**
     * pipelinecode COLUMN:opportunity_id
     */
    @Schema(description = "pipelinecode", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String opportunityId;

    /**
     * 项目预算 COLUMN:planned_total_engagement_revenue
     */
    @Schema(description = "项目预算", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal plannedTotalEngagementRevenue;

    /**
     * amount COLUMN:contract_amount
     */
    @Schema(description = "amount", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal contractAmount;

    /**
     * ETD Net Unbilled Inventory COLUMN:e_net_unbld_inv
     */
    @Schema(description = "ETD Net Unbilled Inventory", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal eNetUnbldInv;

    /**
     * COLUMN:f_net_unbld_inv
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal fNetUnbldInv;

    /**
     * Total Expense COLUMN:e_tot_exp
     */
    @Schema(description = "Total Expense", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal eTotExp;

    /**
     * COLUMN:f_tot_exp
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal fTotExp;

    /**
     * Ter COLUMN:e_ter
     */
    @Schema(description = "Ter", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal eTer;

    /**
     * COLUMN:f_ter
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal fTer;

    /**
     * Ner COLUMN:e_ner
     */
    @Schema(description = "Ner", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal eNer;

    /**
     * COLUMN:f_ner
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal fNer;

    /**
     * charge rate*金额（Standard） COLUMN:e_ser
     */
    @Schema(description = "charge rate*金额（Standard）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal eSer;

    /**
     * COLUMN:f_ser
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal fSer;

    /**
     * Margin COLUMN:e_margin
     */
    @Schema(description = "Margin", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal eMargin;

    /**
     * COLUMN:f_margin
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal fMargin;

    /**
     * 百分比 COLUMN:e_margin_pct
     */
    @Schema(description = "百分比", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal eMarginPct;

    /**
     * COLUMN:f_margin_pct
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal fMarginPct;

    /**
     * COLUMN:e_dir_cost
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal eDirCost;

    /**
     * COLUMN:f_dir_cost
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal fDirCost;

    /**
     * ner per hour COLUMN:e_net_rev_per_hour
     */
    @Schema(description = "ner per hour", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal eNetRevPerHour;

    /**
     * COLUMN:f_net_rev_per_hour
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal fNetRevPerHour;

    /**
     * Charged Hours COLUMN:e_chg_hrs
     */
    @Schema(description = "Charged Hours", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal eChgHrs;

    /**
     * COLUMN:f_chg_hrs
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal fChgHrs;

    /**
     * AR COLUMN:e_tot_ar
     */
    @Schema(description = "AR", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal eTotAr;

    /**
     * COLUMN:f_tot_ar
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal fTotAr;

    /**
     * COLUMN:e_ar_tax
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal eArTax;

    /**
     * COLUMN:f_ar_tax
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal fArTax;

    /**
     * total billings (INCL. tax) COLUMN:e_bld_fee_exp
     */
    @Schema(description = "total billings (INCL. tax)", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal eBldFeeExp;

    /**
     * COLUMN:f_bld_fee_exp
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal fBldFeeExp;

    /**
     * COLUMN:e_bld_tax
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal eBldTax;

    /**
     * COLUMN:f_bld_tax
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal fBldTax;

    /**
     * total Collections COLUMN:e_col_fee_exp
     */
    @Schema(description = "total Collections", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal eColFeeExp;

    /**
     * COLUMN:f_col_fee_exp
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal fColFeeExp;

    /**
     * COLUMN:e_col_tax
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal eColTax;

    /**
     * COLUMN:f_col_tax
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal fColTax;

    /**
     * COLUMN:e_ar_rsv
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal eArRsv;

    /**
     * COLUMN:f_ar_rsv
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal fArRsv;

    /**
     * COLUMN:e_ar_vat_rsv
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal eArVatRsv;

    /**
     * COLUMN:f_ar_vat_rsv
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal fArVatRsv;

    /**
     * COLUMN:e_ar_write_off
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal eArWriteOff;

    /**
     * COLUMN:f_ar_write_off
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal fArWriteOff;

    /**
     * COLUMN:e_ar_vat_write_off
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal eArVatWriteOff;

    /**
     * COLUMN:f_ar_vat_write_off
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal fArVatWriteOff;

    /**
     * Revenue Days COLUMN:e_revenue_days
     */
    @Schema(description = "Revenue Days", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal eRevenueDays;

    /**
     * COLUMN:etl_time
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDateTime etlTime;

    /**
     * COLUMN:ds
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String ds;

    /**
     * 项目合伙人ID COLUMN:engagement_partner_id
     */
    @Schema(description = "项目合伙人ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String engagementPartnerId;

    /**
     * 项目经理ID COLUMN:engagement_manager_id
     */
    @Schema(description = "项目经理ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String engagementManagerId;

    /**
     * pipelineid COLUMN:pipeline_id
     */
    @Schema(description = "pipelineid", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String pipelineId;

    /**
     * pipeline 的主 ep 用户 id COLUMN:pipeline_ep_id
     */
    @Schema(description = "pipeline 的主 ep 用户 id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String pipelineEpId;

    /**
     * pipeline 的主 em 用户 id COLUMN:pipeline_em_id
     */
    @Schema(description = "pipeline 的主 em 用户 id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String pipelineEmId;
}
