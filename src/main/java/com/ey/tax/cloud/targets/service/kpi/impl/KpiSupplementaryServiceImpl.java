package com.ey.tax.cloud.targets.service.kpi.impl;

import com.ey.cn.tax.framework.mybatisplus.extension.service.AbstractService;
import com.ey.tax.cloud.targets.entity.kpi.KpiSupplementary;
import com.ey.tax.cloud.targets.mapper.kpi.KpiSupplementaryMapper;
import com.ey.tax.cloud.targets.service.kpi.KpiSupplementaryService;
import org.springframework.stereotype.Service;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-03-14 09:47:48
 * 
 */
@Service
public class KpiSupplementaryServiceImpl extends AbstractService<KpiSupplementaryMapper, KpiSupplementary> implements KpiSupplementaryService {
}