package com.ey.tax.cloud.targets.utils;

import com.ey.cn.tax.framework.context.EyUserContextHolder;
import com.ey.cn.tax.framework.utils.EmptyUtils;
import com.ey.tax.cloud.targets.annotation.CleanField;
import com.ey.tax.cloud.targets.annotation.CleanUser;
import com.ey.tax.cloud.targets.entity.user.UserAttribute;
import com.ey.tax.cloud.targets.service.data.DictionaryService;
import com.ey.tax.cloud.targets.service.data.FiscalCalenderService;
import com.ey.tax.cloud.targets.service.user.UserAttributeService;
import com.ey.tax.cloud.targets.service.user.UserGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.List;

@Component
public class CleanAnnotationProcessor {

    @Autowired
    private DictionaryService dictionaryService;
    @Autowired
    private UserAttributeService userAttributeService;
    @Autowired
    private FiscalCalenderService fiscalCalenderService;
    @Autowired
    private UserGroupService userGroupService;

    public  <T> void processAnnotations(List<T> dtos) {
        for (T dto : dtos) {
            for (Field field : dto.getClass().getDeclaredFields()) {
                if (field.isAnnotationPresent(CleanField.class)) {
                    CleanField annotation = field.getAnnotation(CleanField.class);
                    field.setAccessible(true);
                    try {
                        Object value = field.get(dto);
                        if (value instanceof String && EmptyUtils.isNotEmpty(annotation.dictCode())) {
                           // 字典清洗逻辑
                            String dictValue = dictionaryService.getDictName(annotation.dictCode(), (String) value);
                            //field设置属性值，属性为字段名+Name，值为字典值
                            Field nameField = dto.getClass().getDeclaredField(field.getName() + "Name");
                            nameField.setAccessible(true);
                            nameField.set(dto, dictValue);
                        }
                        // 可以添加其他类型的清洗逻辑
                    } catch (IllegalAccessException e) {
                        // 处理异常
                        e.printStackTrace();
                    } catch (NoSuchFieldException e) {
                        throw new RuntimeException(e);
                    }
                } else if ( field.isAnnotationPresent(CleanUser.class)) {
                    CleanUser annotation = field.getAnnotation(CleanUser.class);
                    field.setAccessible(true);
                    try {
                      // 如果annotation的reource有值，则根据resource取对应字段的值作为userId，否则取当前用户
                        String userId = EyUserContextHolder.get().getAuthUserId();
                        if (EmptyUtils.isNotEmpty(annotation.resource())) {
                           userId = (String) dto.getClass().getDeclaredField(annotation.resource()).get(dto);
                        }
                        //如果dto中没有fiscalYear字段，则取当前财年
                        String fiscalYear = fiscalCalenderService.getCurrentFiscalCalender();
                        if(dto.getClass().getDeclaredField("fiscalYear") != null) {
                            fiscalYear = (String) dto.getClass().getDeclaredField("fiscalYear").get(dto);
                        }
                        //查询用户
                        UserAttribute userAttribute = new UserAttribute();
                        userAttribute.setUserId(userId);
                        userAttribute.setFiscalYear(fiscalYear);
                        userAttribute = userAttributeService.queryOneByPara(userAttribute);
                        //根据field从userAttribute取值
                        if (userAttribute != null) {
                            Field userField = userAttribute.getClass().getDeclaredField(annotation.field());
                            userField.setAccessible(true);
                            String value = userField.get(userAttribute).toString();
                            //根据dictCode取字典值
                            if (EmptyUtils.isNotEmpty(annotation.dictCode())) {
                                //如果dictCode为rank
                                if ("rank".equals(annotation.dictCode())) {
                                    value = userGroupService.getNameByIdInThreadLocal(value).getGroupName();
                                    field.set(dto, value);
                                } else {
                                    String dictValue = dictionaryService.getDictName(annotation.dictCode(), value);
                                    field.set(dto, dictValue);
                                }

                            } else {
                                field.set(dto, value);
                            }
                        }

                        // 可以添加其他类型的清洗逻辑
                    } catch (IllegalAccessException e) {
                        // 处理异常
                        e.printStackTrace();
                    } catch (NoSuchFieldException e) {
                        throw new RuntimeException(e);
                    }
                }
            }

        }
    }
}
