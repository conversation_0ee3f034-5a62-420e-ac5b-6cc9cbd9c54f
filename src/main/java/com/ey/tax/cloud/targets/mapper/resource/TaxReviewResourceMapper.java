package com.ey.tax.cloud.targets.mapper.resource;

import com.ey.cn.tax.framework.mybatisplus.core.mapper.IEyBaseMapper;
import com.ey.tax.cloud.targets.entity.resource.TaxReviewResource;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-01-17 14:35:37
 *
 */
public interface TaxReviewResourceMapper extends IEyBaseMapper<TaxReviewResource> {
    @Select("with rate as (\n" +
            "    select exchange_rate\n" +
            "    from target_exchange_rate\n" +
            "    where exchange_currency = 'USD' and fiscal_year = #{fiscalYear} and is_del = 0\n" +
            "    limit 1\n" +
            ")\n" +
            "select\n" +
            "    ROW_NUMBER() OVER (ORDER BY t.engNumber) as sn,\n" +
            "    #{fiscalYear} as fiscalYear,\n" +
            "    t.*\n" +
            "from (\n" +
            "    SELECT\n" +
            "        ttr.engagement_code as engNumber,\n" +
            "        ttr.entity_name AS entityName,\n" +
            "        (select at.user_name from tax_cloud_target.target_user_attribute at where at.user_id = ttr.tax_ep_id limit 1) AS taxPartnerName,\n" +
            "        (select at.gpn from tax_cloud_target.target_user_attribute at where at.user_id = ttr.tax_ep_id limit 1) AS taxPartnerGpn,\n" +
            "        a.gpn AS personGpn,\n" +
            "        a.user_name AS personName,\n" +
            "        a.user_email AS personEmail,\n" +
            "        'EM' AS grade,\n" +
            "        COALESCE(r.sum_of_net_fees_usd, 0) as resourceNer,\n" +
            "        COALESCE(ttr.imputed_ner, 0) * rate.exchange_rate as personNer,\n" +
            "        COALESCE(ttr.sum_of_hours, 0) / nums as personHours,\n" +
            "        COALESCE(ttr.ep_imputed_ner, 0) * rate.exchange_rate as taxPartnerNer,\n" +
            "        COALESCE(ttr.sum_of_hours, 0) as taxPartnerHours\n" +
            "    FROM\n" +
            "        tax_cloud_target.target_tax_review ttr\n" +
            "    CROSS JOIN rate  \n" +
            "    left join (select * from tax_cloud_target.target_resource_tax_review where is_del = 0) r\n" +
            "        on ttr.engagement_code = r.eng_code and ttr.tax_em_id = r.user_id and ttr.fiscal_year = r.fiscal_year\n" +
            "    left join tax_cloud_target.target_user_attribute a\n" +
            "        on ttr.tax_em_id = a.user_id and a.fiscal_year = #{fiscalYear} and a.is_del=0\n" +
            "    left join (\n" +
            "        select tr.engagement_code, count(1) as nums\n" +
            "        from tax_cloud_target.target_tax_review tr\n" +
            "        where tr.fiscal_year=#{fiscalYear} and tr.is_del=0\n" +
            "        group by tr.engagement_code\n" +
            "    ) eps on ttr.engagement_code = eps.engagement_code\n" +
            "    where ttr.fiscal_year=#{fiscalYear} and ttr.is_del=0\n" +
            "\n" +
            "    UNION ALL\n" +
            "\n" +
            "    SELECT\n" +
            "        ttr.engagement_code as engNumber,\n" +
            "        ttr.entity_name AS entityName,\n" +
            "        a.user_name AS taxPartnerName,\n" +
            "        a.gpn AS taxPartnerGpn,\n" +
            "        a.gpn AS personGpn,\n" +
            "        a.user_name AS personName,\n" +
            "        a.user_email AS personEmail,\n" +
            "        'EP' AS grade,\n" +
            "        COALESCE(r.sum_of_net_fees_usd, 0) as resourceNer,\n" +
            "        COALESCE(ttr.imputed_ner, 0) * rate.exchange_rate as personNer,\n" +
            "        COALESCE(ttr.sum_of_hours, 0) as personHours,\n" +
            "        COALESCE(ttr.ep_imputed_ner, 0) * rate.exchange_rate as taxPartnerNer,\n" +
            "        COALESCE(ttr.sum_of_hours, 0) as taxPartnerHours\n" +
            "    FROM\n" +
            "        tax_cloud_target.target_tax_review ttr\n" +
            "    CROSS JOIN rate " +
            "    left join (select * from tax_cloud_target.target_resource_tax_review where is_del = 0) r\n" +
            "        on ttr.engagement_code = r.eng_code and ttr.tax_ep_id = r.user_id and ttr.fiscal_year = r.fiscal_year\n" +
            "    left join tax_cloud_target.target_user_attribute a\n" +
            "        on ttr.tax_ep_id = a.user_id and a.fiscal_year = #{fiscalYear} and a.is_del=0\n" +
            "    where ttr.fiscal_year=#{fiscalYear} and ttr.is_del=0\n" +
            "\n" +
            "    UNION ALL\n" +
            "\n" +
            "    SELECT\n" +
            "        ttr.engagement_code as engNumber,\n" +
            "        ttr.entity_name AS entityName,\n" +
            "        (select at.user_name from tax_cloud_target.target_user_attribute at where at.user_id = ttr.tax_ep_id limit 1) AS taxPartnerName,\n" +
            "        (select at.gpn from tax_cloud_target.target_user_attribute at where at.user_id = ttr.tax_ep_id limit 1) AS taxPartnerGpn,\n" +
            "        u.gpn AS personGpn,\n" +
            "        u.name AS personName,\n" +
            "        u.email AS personEmail,\n" +
            "        u.level AS grade,\n" +
            "        COALESCE(r.sum_of_net_fees_usd, 0) as resourceNer,\n" +
            "        COALESCE(ttr.imputed_ner, 0) * rate.exchange_rate as personNer, \n" +
            "        COALESCE(r.sum_of_hours, 0) as personHours,\n" +
            "        COALESCE(ttr.ep_imputed_ner, 0) * rate.exchange_rate as taxPartnerNer,\n" +
            "        COALESCE(ttr.sum_of_hours, 0) as taxPartnerHours\n" +
            "    FROM\n" +
            "        tax_cloud_target.target_tax_review ttr\n" +
            "    CROSS JOIN rate \n" +
            "    LEFT JOIN tax_cloud_target.target_tax_review_member mem\n" +
            "        ON ttr.id = mem.tax_review_id and mem.is_del=0\n" +
            "    left join (select * from tax_cloud_target.target_resource_tax_review where is_del = 0) r\n" +
            "        on ttr.engagement_code = r.eng_code and mem.user_id = r.user_id and ttr.fiscal_year = r.fiscal_year and r.is_del=0\n" +
            "    left join tax_cloud_target.target_tax_review_user u\n" +
            "        on mem.user_id = u.user_id and u.is_del=0 and u.fiscal_year=#{fiscalYear}\n" +
            "    where ttr.fiscal_year=#{fiscalYear} and ttr.is_del=0\n" +
            ") t;")
    List<Map<String,Object>> queryTaxReviewResourceList(@Param("fiscalYear")String fiscalYear);







}
