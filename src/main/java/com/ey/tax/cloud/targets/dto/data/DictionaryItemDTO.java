package com.ey.tax.cloud.targets.dto.data;

import com.ey.cn.tax.framework.dto.BaseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-01-26 15:17:22
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "默认实体")
public class DictionaryItemDTO extends BaseDTO {
    /**
     * 字典id COLUMN:dict_id
     */
    @Schema(description = "字典id")
    private String dictId;

    /**
     * 字典key COLUMN:item_key
     */
    @Schema(description = "字典key")
    private String itemKey;

    /**
     * 字典value COLUMN:item_value
     */
    @Schema(description = "字典value")
    private String itemValue;

    /**
     * 父节点 COLUMN:item_parent
     */
    @Schema(description = "父节点")
    private String itemParent;

    /**
     * 扩展字段1 COLUMN:ext_one
     */
    @Schema(description = "扩展字段1")
    private String extOne;

    /**
     * 扩展字段2 COLUMN:ext_two
     */
    @Schema(description = "扩展字段2")
    private String extTwo;

    /**
     * 字典排序，由大到小 COLUMN:order_num
     */
    @Schema(description = "字典排序，由大到小")
    private Integer orderNum;

    /**
     * 状态 COLUMN:status
     */
    @Schema(description = "状态")
    private Integer status;

    @Schema(description = "标签")
    private String tag;

    @Schema(description = "备注")
    private String remark;
}
