package com.ey.tax.cloud.targets.dto.taxReview;

import com.ey.cn.tax.framework.dto.BaseRepDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-02-26 11:15:06
 * 
 */
@Data
@Schema(description = "查询返回实体")
public class TaxReviewMemberRepDTO extends BaseRepDTO {
    /**
     * Tax review id COLUMN:tax_review_id
     */
    @Schema(description = "Tax review id")
    private String taxReviewId;

    /**
     * 参与人员类型（staff、senior） COLUMN:type
     */
    @Schema(description = "参与人员类型（staff、senior）")
    private String type;

    /**
     * 参与人员id COLUMN:user_id
     */
    @Schema(description = "参与人员id")
    private String userId;
}