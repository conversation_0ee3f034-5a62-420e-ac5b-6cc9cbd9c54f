package com.ey.tax.cloud.targets.dto.forecast;

import com.ey.cn.tax.framework.dto.BaseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-01-17 14:35:37
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "默认实体")
public class ForecastBatchDTO extends BaseDTO {
    /**
     * COLUMN:user_id
     */
    @Schema(description = "null")
    private String userId;

    /**
     * COLUMN:status
     */
    @Schema(description = "null")
    private Integer status;

    /**
     * COLUMN:log_text
     */
    @Schema(description = "null")
    private String logText;
}
