package com.ey.tax.cloud.targets.controller.pipeline.impl;

import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.dto.SearchDTO;
import com.ey.cn.tax.framework.entity.Search;
import com.ey.cn.tax.framework.utils.ConvertUtils;
import com.ey.cn.tax.framework.web.annotation.EyRestController;
import com.ey.cn.tax.framework.web.base.AbstractController;
import com.ey.tax.cloud.targets.controller.pipeline.SalesDeliveryDataController;
import com.ey.tax.cloud.targets.dto.pipeline.SalesDeliveryDataAddDTO;
import com.ey.tax.cloud.targets.dto.pipeline.SalesDeliveryDataBatchUpdateDTO;
import com.ey.tax.cloud.targets.dto.pipeline.SalesDeliveryDataDTO;
import com.ey.tax.cloud.targets.dto.pipeline.SalesDeliveryDataDeleteByIdDTO;
import com.ey.tax.cloud.targets.dto.pipeline.SalesDeliveryDataDeleteByIdListDTO;
import com.ey.tax.cloud.targets.dto.pipeline.SalesDeliveryDataQueryByIdDTO;
import com.ey.tax.cloud.targets.dto.pipeline.SalesDeliveryDataQueryByIdListDTO;
import com.ey.tax.cloud.targets.dto.pipeline.SalesDeliveryDataQueryDTO;
import com.ey.tax.cloud.targets.dto.pipeline.SalesDeliveryDataQueryPageDTO;
import com.ey.tax.cloud.targets.dto.pipeline.SalesDeliveryDataRepDTO;
import com.ey.tax.cloud.targets.dto.pipeline.SalesDeliveryDataUpdateByIdDTO;
import com.ey.tax.cloud.targets.entity.pipeline.SalesDeliveryData;
import com.ey.tax.cloud.targets.service.pipeline.SalesDeliveryDataService;
import java.util.List;
import java.util.Map;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-01-30 14:57:08
 * 
 */
@EyRestController(path ="/v1/salesDeliveryData")
public class SalesDeliveryDataControllerImpl extends AbstractController<SalesDeliveryDataService, SalesDeliveryDataDTO, SalesDeliveryData> implements SalesDeliveryDataController {

    /**
     * add SalesDeliveryData from http
     */
    @Override
    public ResponseDTO<Void> add(SalesDeliveryDataAddDTO dto) {
        SalesDeliveryData entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        service.save(entity);
        return ResponseDTO.success();
    }

    /**
     * addList SalesDeliveryData from http
     */
    @Override
    public ResponseDTO<Void> addList(List<SalesDeliveryDataAddDTO> dtos) {
        List<SalesDeliveryData> entitys = ConvertUtils.convertDTOList2EntityList(dtos, entityClass);
        service.save(entitys);
        return ResponseDTO.success();
    }

    /**
     * delete SalesDeliveryData from http
     */
    @Override
    public ResponseDTO<Void> deleteById(SalesDeliveryDataDeleteByIdDTO dto) {
        service.deleteById(dto.getId());
        return ResponseDTO.success();
    }

    /**
     * deleteList SalesDeliveryData from http
     */
    @Override
    public ResponseDTO<Void> deleteByIdList(SalesDeliveryDataDeleteByIdListDTO dto) {
        getService().deleteByIds(dto.getIds());
        return ResponseDTO.success();
    }

    /**
     * update SalesDeliveryData from http
     */
    @Override
    public ResponseDTO<Void> updateById(SalesDeliveryDataUpdateByIdDTO dto) {
        SalesDeliveryData entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        getService().update(entity);
        return ResponseDTO.success();
    }

    /**
     * updateBatch SalesDeliveryData from http
     */
    @Override
    public ResponseDTO<Void> updateBatch(List<SalesDeliveryDataBatchUpdateDTO> dtos) {
        List<SalesDeliveryData> entities = ConvertUtils.convertDTOList2EntityList(dtos, entityClass);
        getService().updateBatchByIds(entities);
        return ResponseDTO.success();
    }

    /**
     * query SalesDeliveryData from http
     */
    @Override
    public ResponseDTO<SalesDeliveryDataRepDTO> queryById(SalesDeliveryDataQueryByIdDTO dto) {
        SalesDeliveryData entity = getService().queryById(dto.getId());
        SalesDeliveryDataRepDTO rDTO = ConvertUtils.convertEntity2DTO(entity, SalesDeliveryDataRepDTO.class);
        return ResponseDTO.success(rDTO);
    }

    /**
     * queryByIdList SalesDeliveryData from http
     */
    @Override
    public ResponseDTO<List<SalesDeliveryDataRepDTO>> queryByIdList(SalesDeliveryDataQueryByIdListDTO dto) {
        List<SalesDeliveryData> entities = getService().queryByIds(dto.getIds());
        return ResponseDTO.success(ConvertUtils.convertEntityList2DTOList(entities, SalesDeliveryDataRepDTO.class));
    }

    /**
     * queryList SalesDeliveryData from http
     */
    @Override
    public ResponseDTO<List<SalesDeliveryDataRepDTO>> queryList(SalesDeliveryDataQueryDTO dto) {
        SalesDeliveryData entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        List<SalesDeliveryData> entities = getService().queryByPara(entity);
        return ResponseDTO.success(ConvertUtils.convertEntityList2DTOList(entities, SalesDeliveryDataRepDTO.class));
    }

    /**
     * Page SalesDeliveryData from http
     */
    @Override
    public ResponseDTO<SearchDTO<SalesDeliveryDataRepDTO>> queryPage(SearchDTO<SalesDeliveryDataQueryPageDTO> searchDTO) {
        Search<SalesDeliveryData> search = ConvertUtils.convertSearchDTO2Search(searchDTO, entityClass);
        getService().queryPageByPara(search);
        return ResponseDTO.success(ConvertUtils.convertSearch2SearchDTO(search, SalesDeliveryDataRepDTO.class));
    }

    /**
     * Count SalesDeliveryData from http
     */
    @Override
    public ResponseDTO<Long> queryCount(SalesDeliveryDataQueryDTO dto) {
        SalesDeliveryData entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        Long count = getService().queryCountByPara(entity);
        return ResponseDTO.success(count);
    }

    /**
     * One SalesDeliveryData from http
     */
    @Override
    public ResponseDTO<SalesDeliveryDataRepDTO> queryOne(SalesDeliveryDataQueryDTO dto) {
        SalesDeliveryData qEntity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        SalesDeliveryData rEntity = getService().queryOneByPara(qEntity);
        SalesDeliveryDataRepDTO rDTO = ConvertUtils.convertEntity2DTO(rEntity, SalesDeliveryDataRepDTO.class);
        return ResponseDTO.success(rDTO);
    }

    /**
     * exist SalesDeliveryData from http
     */
    @Override
    public ResponseDTO<Boolean> exist(SalesDeliveryDataQueryDTO dto) {
        boolean resullt = getService().existByPara(ConvertUtils.convertDTO2Entity(dto, entityClass));
        return ResponseDTO.success(resullt);
    }
}