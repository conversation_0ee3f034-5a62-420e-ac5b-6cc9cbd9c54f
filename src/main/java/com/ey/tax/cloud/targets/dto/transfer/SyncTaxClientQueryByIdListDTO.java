package com.ey.tax.cloud.targets.dto.transfer;

import com.ey.cn.tax.framework.validator.QueryByIdListGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-05-30 17:59:57
 * 
 */
@Data
@Schema(description = "根据id列表查询实体")
public class SyncTaxClientQueryByIdListDTO {
    
    /**
     * 数据id列表
     */
    @Schema(description = "数据id列表")
    @NotNull(message = "{SyncTaxClientDTO.ids.NotNull}", groups = {QueryByIdListGroup.class})
    @Size(min = 1, message = "{SyncTaxClientDTO.ids.Size}", groups = {QueryByIdListGroup.class})
    private List<String> ids;
}