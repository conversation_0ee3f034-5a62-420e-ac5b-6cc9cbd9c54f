package com.ey.tax.cloud.targets.dto.transfer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 前端公共字典表接口响应DTO
 */
@Data
@Schema(description = "前端公共字典表接口响应")
public class DictListAllSimpleResponseDTO {

    /**
     * 响应码
     */
    @Schema(description = "响应码")
    private Integer code;

    /**
     * 响应消息
     */
    @Schema(description = "响应消息")
    private String msg;

    /**
     * 字典数据列表
     */
    @Schema(description = "字典数据列表")
    private List<DictSimpleDataDTO> data;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String errorMessage;

}
