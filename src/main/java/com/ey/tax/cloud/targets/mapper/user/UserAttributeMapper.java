package com.ey.tax.cloud.targets.mapper.user;

import com.ey.cn.tax.framework.mybatisplus.core.mapper.IEyBaseMapper;
import com.ey.tax.cloud.targets.entity.user.UserAttribute;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2023-12-20 10:41:44
 *
 */
public interface UserAttributeMapper extends IEyBaseMapper<UserAttribute> {



    List<UserAttribute> selectByKeyword(@Param("keyword") String keyword);
}
