package com.ey.tax.cloud.targets.dto.data;

import com.ey.cn.tax.framework.dto.BaseRepDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-02-19 11:47:19
 *
 */
@Data
@Schema(description = "查询返回实体")
public class TemplateInstanceRepDTO extends BaseRepDTO {
    /**
     * 模板编码 COLUMN:temp_code
     */
    @Schema(description = "模板编码")
    private String tempCode;

    /**
     * 模板名称 COLUMN:temp_name
     */
    @Schema(description = "模板名称")
    private String tempName;

    /**
     * 状态 COLUMN:status
     */
    @Schema(description = "状态")
    private String status;
    @Schema(description = "状态")
    private String statusLabel;

    /**
     * 文件ID COLUMN:file_id
     */
    @Schema(description = "文件ID")
    private String fileId;

    /**
     * 年度 COLUMN:fiscal_year
     */
    @Schema(description = "年度")
    private String fiscalYear;
    @Schema(description = "年度")
    private String fiscalYearLabel;

    /**
     * 模板ID COLUMN:temp_id
     */
    @Schema(description = "模板ID")
    private String tempId;

    @Schema(description = "备注")
    private String remark;
}
