package com.ey.tax.cloud.targets.dto.kpi;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;

import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-01-18 15:01:10
 *
 */
@Data
@Schema(description = "根据条件分页查询实体")
public class KpiBatchLogQueryPageDTO {
    /**
     * 用户ID COLUMN:user_id
     */
    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String userId;

    /**
     * 状态 0:初始化,1:运行中,3:结束,-1:异常 COLUMN:status
     */
    @Schema(description = "状态 0:初始化,1:运行中,3:结束,-1:异常", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer status;

    /**
     * 总数量 COLUMN:total_count
     */
    @Schema(description = "总数量", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer totalCount;

    /**
     * 完成数量 COLUMN:finish_count
     */
    @Schema(description = "完成数量", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer finishCount;

    /**
     * 开始时间 COLUMN:start_time
     */
    @Schema(description = "时间范围", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<LocalDateTime> timeRange;

    /**
     * 开始时间 COLUMN:start_time
     */
    @Schema(description = "开始时间", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDateTime startTime;

    /**
     * 结束时间 COLUMN:end_time
     */
    @Schema(description = "结束时间", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDateTime endTime;

    /**
     * 类型 0:手动执行,1:系统执行 COLUMN:source_type
     */
    @Schema(description = "类型 0:手动执行,1:系统执行", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer sourceType;
    //财年
    @Schema(description = "财年", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String fiscalYear;
}
