package com.ey.tax.cloud.targets.dto.pipeline;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-01-23 16:33:32
 *
 */
@Data
@Schema(description = "根据条件删除实体")
public class PipelineDeleteDTO {
    /**
     * 客户名称 COLUMN:client_name
     */
    @Schema(description = "客户名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String clientName;

    /**
     * 客户编码 COLUMN:client_code
     */
    @Schema(description = "客户编码", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String clientCode;

    /**
     * 行业部门 COLUMN:industry_sector
     */
    @Schema(description = "行业部门", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String industrySector;

    /**
     * 子行业部门 COLUMN:industry_sub_sector
     */
    @Schema(description = "子行业部门", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String industrySubSector;

    /**
     * 公司类型 COLUMN:company_type
     */
    @Schema(description = "公司类型", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Object companyType;

    /**
     * 上市信息 COLUMN:stock_exchange
     */
    @Schema(description = "上市信息", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String stockExchange;

    /**
     * 证券交易委员会信息 COLUMN:is_sec
     */
    @Schema(description = "证券交易委员会信息", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer isSec;

    /**
     * 总部所在地 COLUMN:head_office_location
     */
    @Schema(description = "总部所在地", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String headOfficeLocation;

    /**
     * 客户渠道 COLUMN:client_channel
     */
    @Schema(description = "客户渠道", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String clientChannel;

    /**
     * 客户分类 COLUMN:customer_classification
     */
    @Schema(description = "客户分类", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String customerClassification;

    /**
     * pipeline编码 COLUMN:pipeline_code
     */
    @Schema(description = "pipeline编码", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String pipelineCode;

    /**
     * 机会名称 COLUMN:pipeline_name
     */
    @Schema(description = "机会名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String pipelineName;

    /**
     * 本地服务代码 COLUMN:local_service_code
     */
    @Schema(description = "本地服务代码", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String localServiceCode;

    /**
     * 产品id COLUMN:product_id
     */
    @Schema(description = "产品id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String productId;

    /**
     * 预计赢率 COLUMN:win_rate
     */
    @Schema(description = "预计赢率", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer winRate;

    /**
     * 预计赢得日期 COLUMN:anticipated_win_date
     */
    @Schema(description = "预计赢得日期", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDateTime anticipatedWinDate;

    /**
     * 是否为竞争性投标 COLUMN:lead_source
     */
    @Schema(description = "是否为竞争性投标", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String leadSource;

    /**
     * 原因 COLUMN:won_reason
     */
    @Schema(description = "原因", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String wonReason;

    /**
     * 备注 COLUMN:description
     */
    @Schema(description = "备注", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String description;

    /**
     * eic单人 COLUMN:eic_own_effort
     */
    @Schema(description = "eic单人", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer eicOwnEffort;

    /**
     * 描述 COLUMN:efforts_description
     */
    @Schema(description = "描述", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String effortsDescription;

    /**
     * 结果 COLUMN:status
     */
    @Schema(description = "结果", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String status;

    /**
     * 主要ey办公室 COLUMN:psm_prime_office
     */
    @Schema(description = "主要ey办公室", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String psmPrimeOffice;

    /**
     * 预计开始日期 COLUMN:kickoff_week
     */
    @Schema(description = "预计开始日期", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDateTime kickoffWeek;

    /**
     * 预计关闭日期 COLUMN:closing_week
     */
    @Schema(description = "预计关闭日期", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDateTime closingWeek;

    /**
     * 交付风险 COLUMN:delivery_risk
     */
    @Schema(description = "交付风险", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String deliveryRisk;

    /**
     * 领导gpn COLUMN:pursuit_leader_id
     */
    @Schema(description = "领导gpn", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String pursuitLeaderId;

    /**
     * 参与合伙人gpn COLUMN:oppr_partner_id
     */
    @Schema(description = "参与合伙人gpn", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String opprPartnerId;

    /**
     * 预期的团队经理gpn COLUMN:eng_manager_id
     */
    @Schema(description = "预期的团队经理gpn", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String engManagerId;

    /**
     * 金额（价值cny） COLUMN:amount
     */
    @Schema(description = "金额（价值cny）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal amount;

    /**
     * 金额（价值usd） COLUMN:amount_usd
     */
    @Schema(description = "金额（价值usd）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal amountUsd;

    /**
     * 货币 COLUMN:currency
     */
    @Schema(description = "货币", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String currency;

    /**
     * 是否包括vat COLUMN:is_inclusive_vat
     */
    @Schema(description = "是否包括vat", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer isInclusiveVat;

    /**
     * pace状态 COLUMN:pace_status
     */
    @Schema(description = "pace状态", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String paceStatus;

    /**
     * 位置 COLUMN:location_code
     */
    @Schema(description = "位置", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String locationCode;

    /**
     * 事件状态 COLUMN:event
     */
    @Schema(description = "事件状态", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String event;

    /**
     * 子代码标识 COLUMN:is_subcode
     */
    @Schema(description = "子代码标识", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String isSubcode;

    /**
     * 合同 COLUMN:contract
     */
    @Schema(description = "合同", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String contract;

    /**
     * 是否关闭 COLUMN:closed
     */
    @Schema(description = "是否关闭", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer closed;

    /**
     * 使用承包商 COLUMN:contractor_used
     */
    @Schema(description = "使用承包商", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String contractorUsed;

    /**
     * 外部占比 COLUMN:outsource
     */
    @Schema(description = "外部占比", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal outsource;

    /**
     * 审批状态 COLUMN:confirm_status
     */
    @Schema(description = "审批状态", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String confirmStatus;

    /**
     * 创建财年 COLUMN:pursuit_fy
     */
    @Schema(description = "创建财年", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String pursuitFy;

    /**
     * 赢单财年 COLUMN:won_fy
     */
    @Schema(description = "赢单财年", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String wonFy;

    /**
     * efforts状态 COLUMN:efforts_approve
     */
    @Schema(description = "efforts状态", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String effortsApprove;



    /**
     * 其他event、event选other时填写 COLUMN:other_event
     */
    @Schema(description = "其他event、event选other时填写", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String otherEvent;

    /**
     * COLUMN:crm
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String crm;

    /**
     * tiger:stock_exchange、is_sec 生成 COLUMN:listed
     */
    @Schema(description = "tiger:stock_exchange、is_sec 生成", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String listed;

    /**
     * 地区 COLUMN:location
     */
    @Schema(description = "地区", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String location;
}
