package com.ey.tax.cloud.targets.dto.user;

import com.ey.cn.tax.framework.validator.UpdateByIdGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-01-09 11:49:16
 *
 */
@Data
@Schema(description = "根据id批量更新实体")
public class UserGroupUpdateByIdDTO {

    /**
     * 数据id
     */
    @Schema(description = "数据id")
    @NotBlank(message = "{UserGroupDTO.id.NotBlank}", groups = {UpdateByIdGroup.class})
    private String id;

    /**
     * 类型 COLUMN:type
     */
    @Schema(description = "类型", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String type;

    /**
     * 父级 COLUMN:parent_id
     */
    @Schema(description = "父级", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String parentId;

    /**
     * 名称 COLUMN:group_name
     */
    @Schema(description = "名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String groupName;

    /**
     * 关联角色 COLUMN:role_id
     */
    @Schema(description = "关联角色", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String roleId;

    /**
     * 关联邮件模板 COLUMN:mail_id
     */
    @Schema(description = "关联邮件模板", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String mailId;


    @Schema(description = "启用禁用", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer isActive;

    @Schema(description = "编码", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String code;

    @Schema(description = "排序", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer orderNum;
}
