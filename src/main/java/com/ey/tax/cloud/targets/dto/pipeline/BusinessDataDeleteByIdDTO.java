package com.ey.tax.cloud.targets.dto.pipeline;

import com.ey.cn.tax.framework.validator.DeleteByIdGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2025-03-04 15:06:12
 * 
 */
@Data
@Schema(description = "根据id删除实体")
public class BusinessDataDeleteByIdDTO {
    
    /**
     * 数据id
     */
    @Schema(description = "数据id")
    @NotBlank(message = "{BusinessDataDTO.id.NotBlank}", groups = {DeleteByIdGroup.class})
    private String id;
}