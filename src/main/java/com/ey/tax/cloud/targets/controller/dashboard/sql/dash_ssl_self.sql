select sum(sales.sales_delivery_credit_amount_usd) as amount, sales.ssl3 as ssl3,
       sales.fiscal_year as year, sales.product_id as product
from tax_cloud_target.target_pipeline_sales_delivery_data sales
    left join tax_cloud_target.target_pipeline pipeline on sales.pipeline_id = pipeline.id
where sales.sales_delivery_credit = #{args.userId}
    and sales.product_id is not null
    and pipeline.status = 'Won'
  and pipeline.confirm_status in ('Processed','Amendment Submitted','Returned')
    and sales.is_del = 0
    and pipeline.is_del = 0
    AND sales.ssl3 in
    <foreach collection="args.ssl3" index="index" item="item" open="(" separator="," close=")" >
    #{item}
    </foreach>
  and pipeline.won_fy in
    <foreach collection="args.year" index="index" item="item" open="(" separator="," close=")" >
    #{item}
    </foreach>
group by sales.ssl3,sales.fiscal_year,sales.product_id order by amount desc;