package com.ey.tax.cloud.targets.dto.forecast;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-01-17 14:35:34
 * 
 */
@Data
@Schema(description = "根据条件删除实体")
public class ForecastConfigDeleteDTO {
    /**
     * COLUMN:pipeline_id
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String pipelineId;

    /**
     * COLUMN:user_id
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String userId;

    /**
     * COLUMN:forecast_start_date
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDateTime forecastStartDate;

    /**
     * COLUMN:forecast_end_date
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDateTime forecastEndDate;

    /**
     * COLUMN:completion_rate
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal completionRate;

    /**
     * COLUMN:win_possibility
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal winPossibility;
}