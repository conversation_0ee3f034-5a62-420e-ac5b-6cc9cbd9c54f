package com.ey.tax.cloud.targets.controller.pipeline.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheManager;
import com.ey.cn.tax.framework.context.EyCommonContextHolder;
import com.ey.cn.tax.framework.context.EyUserContextHolder;
import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.dto.SearchDTO;
import com.ey.cn.tax.framework.entity.Search;
import com.ey.cn.tax.framework.exception.EyTaxBusinessException;
import com.ey.cn.tax.framework.utils.ConvertUtils;
import com.ey.cn.tax.framework.utils.EmptyUtils;
import com.ey.cn.tax.framework.utils.JacksonUtils;
import com.ey.cn.tax.framework.web.annotation.EyRestController;
import com.ey.cn.tax.framework.web.base.AbstractController;
import com.ey.cn.tax.lite.impl.port.input.controller.LiteDatasetController;
import com.ey.cn.tax.lite.impl.port.input.dto.LiteDatasetExecutionDTO;
import com.ey.tax.cloud.base.remote.task.BaseTaskInstFeignClient;
import com.ey.tax.cloud.targets.constant.*;
import com.ey.tax.cloud.targets.controller.pipeline.PipelineApproveController;
import com.ey.tax.cloud.targets.controller.pipeline.PipelineController;
import com.ey.tax.cloud.targets.dto.pipeline.*;
import com.ey.tax.cloud.targets.dto.taxReview.TaxReviewRepDTO;
import com.ey.tax.cloud.targets.entity.business.MyBusinessSelf;
import com.ey.tax.cloud.targets.entity.data.DictionaryItem;
import com.ey.tax.cloud.targets.entity.data.ExchangeRate;
import com.ey.tax.cloud.targets.entity.pipeline.*;
import com.ey.tax.cloud.targets.entity.product.Product;
import com.ey.tax.cloud.targets.entity.user.UserAttribute;
import com.ey.tax.cloud.targets.service.business.MyBusinessSelfService;
import com.ey.tax.cloud.targets.service.data.DictionaryItemService;
import com.ey.tax.cloud.targets.service.data.DictionaryService;
import com.ey.tax.cloud.targets.service.data.ExchangeRateService;
import com.ey.tax.cloud.targets.service.data.FiscalCalenderService;
import com.ey.tax.cloud.targets.service.pipeline.*;
import com.ey.tax.cloud.targets.service.product.ProductService;
import com.ey.tax.cloud.targets.service.user.UserAttributeService;
import com.ey.tax.cloud.targets.utils.CommonUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.channels.Pipe;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2023-12-25 11:19:56
 *
 */
@EyRestController(path ="/v1/pipeline")
public class PipelineControllerImpl extends AbstractController<PipelineService, PipelineDTO, Pipeline> implements PipelineController {


    @Autowired
    private PipelineConfigService pipelineConfigService;
    @Autowired
    private PipelineApproveService approveService;

    @Autowired
    private SalesDeliveryService salesDeliveryService;

    @Autowired
    private UserAttributeService  userAttributeService;

    @Autowired
    private ProductService productService;

    @Autowired
    private PipelineApproveService pipelineApproveService;
    @Autowired
    private DictionaryService dictionaryService;
    @Autowired
    private SalesDeliveryApproveService salesDeliveryServiceApprove;
    @Autowired
    private DictionaryItemService dictionaryItemService;
    @Autowired
    private FiscalCalenderService fiscalCalenderService;
    @Autowired
    private ExchangeRateService exchangeRateService;
    @Autowired
    private SalesDeliveryDataService salesDeliveryDataService;

    @Autowired
    private PipelineApproveController pipelineApproveController;
    @Autowired
    private MyBusinessSelfService myBusinessSelfService;
    @Autowired
    private PipelineAmountRecordService pipelineAmountRecordService;
    @Autowired
    private  SalesBusinessService salesBusinessService;
    @Autowired
    private CacheManager cacheManager;


    @Autowired
    private LiteDatasetController liteDatasetController;

    /**
     * update Pipeline from http
     */
    @Override
    public ResponseDTO<Void> updateById(PipelineUpdateByIdDTO dto) {
        String checks = service.checkPipelineAmount(dto);
        if(EmptyUtils.isNotEmpty(checks)){
            throw  new EyTaxBusinessException("-1",checks);
        }
        Pipeline entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        String approveID = UUID.randomUUID().toString();
        boolean needApprove = true;
        //获取修改人，如果是合伙人，不需要审批
        String userId = EyUserContextHolder.get().getAuthUserId();
        UserAttribute userAttribute = userAttributeService.queryCurrentUser();
        if(userId.equals(entity.getOpprPartnerId())||CommonConstants.BOSS_NAME_LIST.contains(userAttribute.getUserEmail())){
            needApprove = false;
        }
        //如果状态是returned或者Amendment Submitted则调用审批表方法
        if(PipelineConstants.APPROVE_STATUS_RETURNED.equals(entity.getConfirmStatus())||PipelineConstants.APPROVE_STATUS_AMENDMENT_SUBMITTED.equals(entity.getConfirmStatus())){
            if(needApprove){
                pipelineApproveService.updateByIdForEm(dto);
                ResponseDTO responseDTO = ResponseDTO.success();
                responseDTO.setMessage("成功");
                return responseDTO;
            }else {
                pipelineApproveService.updateByIdForEp(dto);
                ResponseDTO responseDTO = ResponseDTO.success();
                responseDTO.setMessage("成功");
                return responseDTO;
            }
        }
        PipelineConfig config = new PipelineConfig();
        config.setTableCode("targetPipelineDetail");
        config.setIsEdit(BooleanConstants.BOOLEAN_1);
        config.setFiscalYear(String.valueOf(dto.getPursuitFy()));
        List<PipelineConfig> configs = pipelineConfigService.queryByPara(config);
        List<String> columnNames = configs.stream().map(PipelineConfig::getColumnName).collect(Collectors.toList());
        //查询原始数据，比较对象获取改动的字段
        Pipeline query = new Pipeline();
        query.setPipelineCode(dto.getPipelineCode());
        Pipeline oldEntity = getService().queryOneByPara(query);
        Pipeline oldENtity2 = new Pipeline();
        BeanUtils.copyProperties(oldEntity,oldENtity2);
        Map<String, Object> map = CommonUtils.getDifferences(oldEntity,entity,columnNames);
        if(map.containsKey("eicOwnEffort")){
            //➢ Edit时，如果只改了 “EIC‘s own effort” 这一个字段，需要EP在 “EIC‘s own effort” 列表下确认。（审批状态不变）
            //如果“EIC‘s own effort”从Y改成N，则直接生效不审批。
            if(EmptyUtils.isEmpty(oldEntity.getEicOwnEffort())){
                oldEntity.setEicOwnEffort(0);
                oldEntity.setEffortsApprove("NO");
            }
            if(((1==oldEntity.getEicOwnEffort()||2==oldEntity.getEicOwnEffort())&&0==entity.getEicOwnEffort())||!needApprove){
                oldEntity.setEicOwnEffort(entity.getEicOwnEffort());
                //如果改为1、2则为Confirmed 0则为No
                if(1==entity.getEicOwnEffort()||2==entity.getEicOwnEffort()) {
                    oldEntity.setEffortsApprove("Confirmed");
                }else {
                    oldEntity.setEffortsApprove("NO");
                }
                getService().update(oldEntity);
            }else{
                oldEntity.setEffortsApprove("Pending Approval");
                oldEntity.setEicOwnEffort(entity.getEicOwnEffort());
                getService().update(oldEntity);
            }
        }
        Map<String, Object> approveMap = new HashMap<>();
        Map<String, Object> editMap = new HashMap<>();
        BigDecimal kpiAmount = null;
        //查询配置信息获取字段列表
        if(map.containsKey("amountCurrency")||map.containsKey("currency")){
            //根据币种汇率计算amount
            map.put("amountCurrency",entity.getAmountCurrency());
            //获取汇率
            ExchangeRate exchangeRate2 = new ExchangeRate();
            exchangeRate2.setFiscalYear(String.valueOf(fiscalCalenderService.getCurrentFiscalCalender()));
            exchangeRate2.setExchangeCurrency(entity.getCurrency());
            List<ExchangeRate> exchangeRateList = exchangeRateService.queryByPara(exchangeRate2);
            if(EmptyUtils.isNotEmpty(exchangeRateList)){
                exchangeRate2 = exchangeRateList.get(0);
                BigDecimal amount = entity.getAmountCurrency().multiply(exchangeRate2.getExchangeRate());
                entity.setAmount(amount);
                map.put("amount",amount);
                //计算amountUsd
                //获取汇率
                ExchangeRate exchangeRate = new ExchangeRate();
                exchangeRate.setFiscalYear(String.valueOf(fiscalCalenderService.getCurrentFiscalCalender()));
                exchangeRate.setExchangeCurrency("USD");
                List<ExchangeRate> exchangeRate1 = exchangeRateService.queryByPara(exchangeRate);
                if(EmptyUtils.isNotEmpty(exchangeRate1)){
                    exchangeRate = exchangeRate1.get(0);
                }
                BigDecimal amountUsd = entity.getAmount().divide(exchangeRate.getExchangeRate(),PipelineConstants.AMOUNT_DECIMAL,BigDecimal.ROUND_HALF_UP);
                entity.setAmountUsd(amountUsd);
                map.put("amountUsd",amountUsd);
            }


        }
        for(PipelineConfig pipelineConfig:configs) {
            String columnName = pipelineConfig.getColumnName();
            if (map.containsKey(columnName)) {
                if (pipelineConfig.getIsEditApprove().equals(BooleanConstants.BOOLEAN_1) && needApprove) {
                    //说明需要该字段修改需要审批
                    approveMap.put(columnName, map.get(columnName));
                } else {
                    editMap.put(columnName, map.get(columnName));
                }
            }
        }
        if(EmptyUtils.isNotEmpty(editMap)){
            CommonUtils.setValues(oldEntity,editMap);

        }
        boolean kpiEdit = false;
        //比较salesDelivery
        SalesDelivery oldSalesDelivery = new SalesDelivery();
        oldSalesDelivery.setPipelineId(oldEntity.getId());
        List<SalesDelivery> salesDeliveries1 = salesDeliveryService.queryByPara(oldSalesDelivery);
        //salesDeliveries和salesDeliveries1通过salesDeliveryCredit比较，如果salesDeliveryCredit不存在，则类型为name，如果salesDeliveryCredit存在比较salesDeliveryCreditRatio，如果不一致则类型为ratio
        Map<String,String> salesDeliveryMap = new HashMap<>();//key为salesDeliveryCredit value为name或ratio
        for (SalesDelivery delivery : ConvertUtils.convertDTOList2EntityList(dto.getKpiList(), SalesDelivery.class)) {
            boolean has = false;
            for (SalesDelivery salesDelivery2 : salesDeliveries1) {
                if(delivery.getSalesDeliveryCredit().equals(salesDelivery2.getSalesDeliveryCredit())){
                    has = true;
                    //比较theAdvisorSContuibutionToThisCase、resourcesProvinceCity、resourcesTaxAuthority
                    boolean same = true;
                    boolean tpcSame = true;
                    if(delivery.getSalesDeliveryCreditType()==3||delivery.getSalesDeliveryCreditType()==8){
                        if(EmptyUtils.isNotEmpty(delivery.getTheAdvisorSContuibutionToThisCase())&&!delivery.getTheAdvisorSContuibutionToThisCase().equals(salesDelivery2.getTheAdvisorSContuibutionToThisCase())){
                            same = false;
                        }
                        if(EmptyUtils.isNotEmpty(delivery.getResourcesProvinceCity())&&!delivery.getResourcesProvinceCity().equals(salesDelivery2.getResourcesProvinceCity())){
                            same = false;
                        }
                        if(EmptyUtils.isNotEmpty(delivery.getResourcesTaxAuthority())&&!delivery.getResourcesTaxAuthority().equals(salesDelivery2.getResourcesTaxAuthority())){
                            same = false;
                        }
                    }
                    /*                  */
                    if(EmptyUtils.isNotEmpty(delivery.getUseAmount())&&delivery.getUseAmount()==1){
                        if(delivery.getSalesDeliveryCreditAmount().compareTo(salesDelivery2.getSalesDeliveryCreditAmount())!=0||!same){
                            salesDeliveryMap.put(delivery.getId(),"salesDeliveryCreditAmount");
                        }
                    }else {
                        if(delivery.getSalesDeliveryCreditRatio().compareTo(salesDelivery2.getSalesDeliveryCreditRatio())!=0||!same){
                            if(delivery.getSalesDeliveryCreditType()==PipelineConstants.SALES_DELIVERY_CREDIT_TYPE_COEEP||delivery.getSalesDeliveryCreditType()==PipelineConstants.SALES_DELIVERY_CREDIT_TYPE_COEEM) {
                                oldEntity.setCoeApprove(PipelineConstants.APPROVE_STATUS_AMENDMENT_SUBMITTED);
                            }
                            if(delivery.getSalesDeliveryCreditType()==PipelineConstants.SALES_DELIVERY_CREDIT_TYPE_TPC||delivery.getSalesDeliveryCreditType()==PipelineConstants.SALES_DELIVERY_CREDIT_TYPE_TPC_EM){
                                oldEntity.setTpcApprove(PipelineConstants.APPROVE_STATUS_AMENDMENT_SUBMITTED);
                            }
                            salesDeliveryMap.put(delivery.getId(),"salesDeliveryCreditRatio");
                        }
                    }
                }
            }
            if(!has) {
                if(delivery.getSalesDeliveryCreditType()==PipelineConstants.SALES_DELIVERY_CREDIT_TYPE_COEEP||delivery.getSalesDeliveryCreditType()==PipelineConstants.SALES_DELIVERY_CREDIT_TYPE_COEEM) {
                    oldEntity.setCoeApprove(PipelineConstants.APPROVE_STATUS_AMENDMENT_SUBMITTED);
                }
                if(delivery.getSalesDeliveryCreditType()==PipelineConstants.SALES_DELIVERY_CREDIT_TYPE_TPC||delivery.getSalesDeliveryCreditType()==PipelineConstants.SALES_DELIVERY_CREDIT_TYPE_TPC_EM){
                    oldEntity.setTpcApprove(PipelineConstants.APPROVE_STATUS_AMENDMENT_SUBMITTED);
                }
                if(EmptyUtils.isNotEmpty(delivery.getUseAmount())&&delivery.getUseAmount()==1) {
                    salesDeliveryMap.put(delivery.getId(), "salesDeliveryCredit,salesDeliveryCreditAmount");
                }else {
                    salesDeliveryMap.put(delivery.getId(), "salesDeliveryCredit,salesDeliveryCreditRatio");
                }

            }
        }
        if(EmptyUtils.isNotEmpty(salesDeliveryMap.keySet())&&needApprove){
            kpiEdit = true;
            needApprove = true;
        }
        if(EmptyUtils.isNotEmpty(approveMap)||kpiEdit){
            PipelineApprove pipelineApprove = new PipelineApprove();
            BeanUtils.copyProperties(oldEntity,pipelineApprove);
            CommonUtils.setValues(pipelineApprove,approveMap);
            pipelineApprove.setApproveStatus(oldEntity.getConfirmStatus());
            pipelineApprove.setConfirmStatus(PipelineConstants.APPROVE_STATUS_AMENDMENT_SUBMITTED);
            oldEntity.setConfirmStatus(PipelineConstants.APPROVE_STATUS_AMENDMENT_SUBMITTED);
            pipelineApprove.setId(approveID);
            //pipelineApprove.setCompanyType(null);
            //根据pipelineCode删除approve表数据
            PipelineApprove qPipelineApprove = new PipelineApprove();
            qPipelineApprove.setPipelineCode(oldEntity.getPipelineCode());
            pipelineApproveService.deleteByPara(qPipelineApprove);
            approveService.save(pipelineApprove);
        }else {
            needApprove = false;
        }
        oldEntity.setCompanyType(null);
        oldEntity.setStockExchange(null);
        oldEntity.setIsBlank(0);
        if(!needApprove){
            if(map.containsKey("amountCurrency")||map.containsKey("currency")) {
                //如果当前财年不等于wonfy
                String fy = fiscalCalenderService.getCurrentFiscalCalender();
                //如果updateFy为空，且wonFy不等于当前财年
                if (EmptyUtils.isEmpty(oldENtity2.getUpdateFy())) {
                        PipelineAmountRecord pipelineAmountRecord = new PipelineAmountRecord();
                        pipelineAmountRecord.setPipelineId(entity.getId());
                        pipelineAmountRecord.setPipelineAmountLast(oldENtity2.getAmount());
                        pipelineAmountRecord.setPipelineAmount(entity.getAmount());
                        pipelineAmountRecord.setKpiAmount(entity.getAmount().subtract(oldENtity2.getAmount()));
                        pipelineAmountRecord.setFiscalYear(fy);
                        kpiAmount = entity.getAmount().subtract(oldENtity2.getAmount());
                        pipelineAmountRecordService.save(pipelineAmountRecord);
                } else if (EmptyUtils.isNotEmpty(oldENtity2.getUpdateFy()) && !oldENtity2.getUpdateFy().equals(fy)) {
                    //查询历史财年数据
                    PipelineAmountRecord pipelineAmountRecord = new PipelineAmountRecord();
                    pipelineAmountRecord.setPipelineId(entity.getId());
                    pipelineAmountRecord.setFiscalYear(oldENtity2.getUpdateFy());
                    List<PipelineAmountRecord> pipelineAmountRecords = pipelineAmountRecordService.queryByPara(pipelineAmountRecord);
                    if (EmptyUtils.isNotEmpty(pipelineAmountRecords)) {
                        pipelineAmountRecord = pipelineAmountRecords.get(0);
                        PipelineAmountRecord pipelineAmountRecordNew = new PipelineAmountRecord();
                        pipelineAmountRecordNew.setPipelineId(entity.getId());
                        pipelineAmountRecordNew.setPipelineAmount(entity.getAmount());
                        pipelineAmountRecordNew.setKpiAmount(entity.getAmount().subtract(pipelineAmountRecord.getPipelineAmount()));
                        pipelineAmountRecordNew.setPipelineAmountLast(pipelineAmountRecord.getPipelineAmount());
                        pipelineAmountRecordNew.setFiscalYear(fy);
                        kpiAmount = entity.getAmount().subtract(pipelineAmountRecord.getPipelineAmountLast());
                        pipelineAmountRecordService.save(pipelineAmountRecordNew);
                    } else {
                        //说明上年没修改过金额
                        PipelineAmountRecord pipelineAmountRecord2 = new PipelineAmountRecord();
                        pipelineAmountRecord2.setPipelineId(entity.getId());
                        pipelineAmountRecord2.setPipelineAmountLast(oldENtity2.getAmount());
                        pipelineAmountRecord2.setPipelineAmount(entity.getAmount());
                        pipelineAmountRecord2.setKpiAmount(entity.getAmount().subtract(oldENtity2.getAmount()));
                        pipelineAmountRecord2.setFiscalYear(fy);
                        kpiAmount = entity.getAmount().subtract(oldENtity2.getAmount());
                        pipelineAmountRecordService.save(pipelineAmountRecord2);
                    }
                }else if(oldENtity2.getUpdateFy().equals(fy)){
                    //删除当前财年的数据
                    PipelineAmountRecord pipelineAmountRecord = new PipelineAmountRecord();
                    pipelineAmountRecord.setPipelineId(entity.getId());
                    pipelineAmountRecord.setFiscalYear(fy);
                    List<PipelineAmountRecord> pipelineAmountRecords = pipelineAmountRecordService.queryByPara(pipelineAmountRecord);
                    if(EmptyUtils.isEmpty(pipelineAmountRecords)){
                        pipelineAmountRecord = new PipelineAmountRecord();
                        pipelineAmountRecord.setPipelineId(entity.getId());
                        pipelineAmountRecord.setPipelineAmountLast(BigDecimal.ZERO);
                        pipelineAmountRecord.setPipelineAmount(entity.getAmount());
                        pipelineAmountRecord.setKpiAmount(entity.getAmount());
                        pipelineAmountRecord.setFiscalYear(fy);
                        kpiAmount = entity.getAmount().subtract(oldENtity2.getAmount());
                        pipelineAmountRecordService.save(pipelineAmountRecord);
                    }else {
                        pipelineAmountRecord = pipelineAmountRecords.get(0);
                        PipelineAmountRecord pipelineAmountRecordNew = new PipelineAmountRecord();
                        pipelineAmountRecordNew.setPipelineId(entity.getId());
                        pipelineAmountRecordNew.setPipelineAmount(entity.getAmount());
                        pipelineAmountRecordNew.setKpiAmount(entity.getAmount().subtract(pipelineAmountRecord.getPipelineAmountLast()));
                        pipelineAmountRecordNew.setPipelineAmountLast(pipelineAmountRecord.getPipelineAmountLast());
                        pipelineAmountRecordNew.setFiscalYear(fy);
                        kpiAmount = entity.getAmount().subtract(pipelineAmountRecord.getPipelineAmountLast());
                        pipelineAmountRecordService.save(pipelineAmountRecordNew);
                        //删除pipelineAmountRecords
                        pipelineAmountRecordService.deleteByIds(pipelineAmountRecords.stream().map(PipelineAmountRecord::getId).collect(Collectors.toList()));
                    }

                }
                oldEntity.setKpiAmount(kpiAmount);
            }else{
                String fy = fiscalCalenderService.getCurrentFiscalCalender();
                //如果当前财年不等于wonfy
                if (!fy.equals(oldENtity2.getWonFy())){
                    if(EmptyUtils.isNotEmpty(oldENtity2.getUpdateFy())&&!fy.equals(oldENtity2.getUpdateFy())){
                        oldEntity.setKpiAmount(BigDecimal.ZERO);
                    }else if(EmptyUtils.isEmpty(oldENtity2.getUpdateFy())){
                        oldEntity.setKpiAmount(BigDecimal.ZERO);
                    }
                }
            }

            oldEntity.setUpdateFy(fiscalCalenderService.getCurrentFiscalCalender());

        }
        getService().update(oldEntity);
        //处理kpi
        if(EmptyUtils.isNotEmpty(dto.getKpiList())){
            if(needApprove){
                List<SalesDelivery> list = salesDeliveryService.submit(entity,dto.getId(), String.valueOf(dto.getWonFy()),dto.getUpdateFy(), ConvertUtils.convertDTOList2EntityList(dto.getKpiList(), SalesDelivery.class),false);
                //设置kpi的pipelineId
                list.forEach(salesDelivery -> salesDelivery.setPipelineId(approveID));
                salesDeliveryService.apporve(list);
                   /* list.forEach(salesDelivery1 -> salesDelivery1.setPipelineId(dto.getId()));
                    salesDeliveryService.save(list);*/
            }else {
                //合伙人不需要审批
                List<SalesDelivery> list = salesDeliveryService.submit(entity,dto.getId(), String.valueOf(dto.getWonFy()),dto.getUpdateFy(), ConvertUtils.convertDTOList2EntityList(dto.getKpiList(), SalesDelivery.class),true);
                list.forEach(salesDelivery1 -> salesDelivery1.setPipelineId(dto.getId()));
                salesDeliveryService.save(list);

                    //更新pipeline表
                    SalesBusiness salesBusiness = new SalesBusiness();
                    salesBusiness.setPipelineId(dto.getId());
                    salesBusiness.setFiscalYear(fiscalCalenderService.getCurrentFiscalCalender());
                    salesBusinessService.realDeleteByPara(salesBusiness);
                    LiteDatasetExecutionDTO liteDatasetExecutionDTO = new LiteDatasetExecutionDTO();
                    liteDatasetExecutionDTO.setCode("SYNC_BUSINESS_SALES_2");
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("fiscalYear",fiscalCalenderService.getCurrentFiscalCalender());
                    //pipelineIdF
                    jsonObject.put("pipelineId",dto.getId());
                    //lastFiscalYear
                    jsonObject.put("fiscalYearLast",fiscalCalenderService.getLastFiscalCalender());
                    liteDatasetExecutionDTO.setArgs(jsonObject);
                    liteDatasetController.execute(liteDatasetExecutionDTO);

            }
        }
        ResponseDTO responseDTO = ResponseDTO.success();
        responseDTO.setMessage("成功");
        salesDeliveryDataService.syncDataByPipeline(oldEntity.getId());
        return responseDTO;
    }


    @Override
    public ResponseDTO<Void> confirmForEicOwnEffort(PipelineQueryByIdListDTO dto) {
        for (String id : dto.getIds()) {
            Pipeline entity = getService().queryById(id);
            if(EmptyUtils.isEmpty(entity)){
                //查询approve表
                PipelineApprove pipelineApprove = pipelineApproveService.queryById(id);
                if(EmptyUtils.isNotEmpty(pipelineApprove)){
                    entity = new Pipeline();
                    BeanUtils.copyProperties(pipelineApprove,entity);
                }
            }
            if(EmptyUtils.isNotEmpty(entity.getConfirmStatus())&&(entity.getConfirmStatus().equals(PipelineConstants.APPROVE_STATUS_AMENDMENT_SUBMITTED)||entity.getConfirmStatus().equals(PipelineConstants.APPROVE_STATUS_RETURNED))){
                //查询approve表
                PipelineApprove pipelineApprove = new PipelineApprove();
                pipelineApprove.setPipelineCode(entity.getPipelineCode());
                PipelineApprove pipelineApprove1 = pipelineApproveService.queryOneByPara(pipelineApprove);
                if(EmptyUtils.isNotEmpty(pipelineApprove1)){
                    pipelineApprove1.setEffortsApprove("Confirmed");
                    pipelineApproveService.update(pipelineApprove1);
                }
                //更新pipeline表
                Pipeline pipeline = new Pipeline();
                pipeline.setPipelineCode(entity.getPipelineCode());
                pipeline = getService().queryOneByPara(pipeline);
                pipeline.setEffortsApprove("Confirmed");
                getService().update(pipeline);
            }else {
                entity.setEffortsApprove("Confirmed");
                getService().update(entity);
            }

        }
        ResponseDTO responseDTO = ResponseDTO.success();
        responseDTO.setMessage("成功");
        return responseDTO;
    }



    /**
     * query Pipeline from http
     */
    @Override
    public ResponseDTO<PipelineRepDTO> queryById(PipelineQueryByIdDTO dto) {
        Pipeline entity = getService().queryById(dto.getId());
        if(EmptyUtils.isEmpty(entity)){
            //查询approve表
            PipelineApprove pipelineApprove = pipelineApproveService.queryById(dto.getId());
            if(EmptyUtils.isNotEmpty(pipelineApprove)){
                //如果审批状态是processed，则报错PIPELINE_STATUS_ERROR
                if(PipelineConstants.APPROVE_STATUS_PROCESSED.equals(pipelineApprove.getConfirmStatus())){
                    throwBuildBusinessException(ErrorCodeConstans.PIPELINE_STATUS_ERROR);
                }
                entity = new Pipeline();
                BeanUtils.copyProperties(pipelineApprove,entity);
            }
        }
        PipelineRepDTO rDTO = ConvertUtils.convertEntity2DTO(entity, PipelineRepDTO.class);

        //处理companyType 将list转为String.[1,2]变为“1，2”
        if(EmptyUtils.isNotEmpty(entity.getCompanyType())){
            String cleanedString = entity.getCompanyType().toString()
                    .replace("[", "")
                    .replace("]", "")
                    .replace("\"", "");
            //根据逗号分割，根据clientcompanytype字典转换
            String[] split = cleanedString.split(",");
            List<String> list = new ArrayList<>();
            for (String s : split) {
                list.add(dictionaryService.getDictName("clientType",s));
            }
            rDTO.setCompanyType(String.join(",", list));
        }
        //赋值、人员名称、产品名称
        if(EmptyUtils.isNotEmpty(entity)){
            rDTO.setProductName(productService.queryNameById(entity.getProductId(),EmptyUtils.isNotEmpty(entity.getWonFy())?entity.getWonFy():entity.getPursuitFy()));
        }
        UserAttribute userAttribute = userAttributeService.queryCurrentUser();
        //查询配置表
        PipelineConfig config = new PipelineConfig();
                    config.setTableCode(PipelineConstants.PIPELINE_TABLE_CODE);
            config.setFiscalYear(String.valueOf(entity.getPursuitFy()));
            List<PipelineConfig> configs = pipelineConfigService.queryByPara(config);
            for (PipelineConfig pipelineConfig : configs) {
                pipelineConfig.setEdit(Integer.valueOf(pipelineConfig.getIsEdit()));
                if(pipelineConfig.getColumnName().equals("opprPartnerId")&&userAttribute.getUserId().equals(entity.getEngManagerId())){
                    //如果当前用户是engManagerId，则opprPartnerId不可编辑
                    pipelineConfig.setIsEdit(BooleanConstants.BOOLEAN_0);
                }
        }

        if(EmptyUtils.isNotEmpty(rDTO)){
            rDTO.setPipelineConfigList(configs);
            List<String> columnNames = configs.stream().map(PipelineConfig::getColumnName).collect(Collectors.toList());
            //根据id和颜色生成map

            Map<String, String> map1 = new HashMap<>();
            for (PipelineConfig pipelineConfig : configs) {
                if(EmptyUtils.isNotEmpty(pipelineConfig.getColor())){
                    map1.put(pipelineConfig.getColumnName(),pipelineConfig.getColor());
                }
            }

            PipelineApprove qPipelineApprove = new PipelineApprove();
            qPipelineApprove.setPipelineCode(entity.getPipelineCode());
            qPipelineApprove.setConfirmStatus(PipelineConstants.APPROVE_STATUS_AMENDMENT_SUBMITTED);
            PipelineApprove pipelineApprove = pipelineApproveService.queryOneByPara(qPipelineApprove);
            if(EmptyUtils.isNotEmpty(pipelineApprove)){
                Pipeline pipeline2 = new Pipeline();
                BeanUtils.copyProperties(pipelineApprove,pipeline2);
                Map<String, Object> map = CommonUtils.getDifferences(entity,pipeline2,columnNames);
                Map<String,String> colorMap = new HashMap<>();
                for (Map.Entry<String, Object> entry : map.entrySet()) {
                    String key = entry.getKey();
                    Object value = entry.getValue();
                    if(EmptyUtils.isNotEmpty(value)){
                        colorMap.put(key,map1.get(key));
                    }
                }
                rDTO.setEditColumnMap(colorMap);
            }
        }

        //查询kpi如果审批状态为Amendment Submitted 数据替换为approve表数据
        if(PipelineConstants.APPROVE_STATUS_AMENDMENT_SUBMITTED.equals(entity.getConfirmStatus())||PipelineConstants.APPROVE_STATUS_RETURNED.equals(entity.getConfirmStatus())) {
            SalesDeliveryApprove salesDelivery = new SalesDeliveryApprove();
            salesDelivery.setPipelineId(entity.getId());
            List<SalesDeliveryApprove> salesDeliveries = salesDeliveryServiceApprove.queryByPara(salesDelivery);
            //如果当前用户不是boss
            if(!(CommonConstants.BOSS_NAME_LIST.contains(userAttribute.getUserEmail()) || CommonConstants.Pipe_Jesse.equals(userAttribute.getGpn()) || CommonConstants.Pipe_Paul.contains(userAttribute.getGpn()))) {
                //过滤如果当前用户是epem，则看全部数据，否则只看自己的数据
                if (!(EyUserContextHolder.get().getAuthUserId().equals(entity.getOpprPartnerId())||EyUserContextHolder.get().getAuthUserId().equals(entity.getEngManagerId()) )) {
                    //过滤数据
                    salesDeliveries = salesDeliveries.stream().filter(salesDeliveryApprove -> EyUserContextHolder.get().getAuthUserId().equals(salesDeliveryApprove.getSalesDeliveryCredit())).collect(Collectors.toList());
                }
            }
            if(EmptyUtils.isNotEmpty(salesDeliveries)){
                rDTO.setKpiList(ConvertUtils.convertEntityList2DTOList(salesDeliveries, SalesDeliveryRepDTO.class));
            }
        }else{
            SalesDelivery salesDelivery = new SalesDelivery();
            salesDelivery.setPipelineId(dto.getId());
            List<SalesDelivery> salesDeliveries = salesDeliveryService.queryByPara(salesDelivery);
            //如果当前用户不是boss
            if(!(CommonConstants.BOSS_NAME_LIST.contains(userAttribute.getUserEmail()) || CommonConstants.Pipe_Jesse.equals(userAttribute.getGpn()) || CommonConstants.Pipe_Paul.contains(userAttribute.getGpn()))) {
                //过滤如果当前用户是epem，则看全部数据，否则只看自己的数据
                if (!(EyUserContextHolder.get().getAuthUserId().equals(entity.getOpprPartnerId()) || EyUserContextHolder.get().getAuthUserId().equals(entity.getEngManagerId())|| EyUserContextHolder.get().getAuthUserId().equals(entity.getEngManagerId())||CommonConstants.BOSS_NAME_LIST.contains(userAttribute.getUserEmail()))) {
                    //过滤数据
                    salesDeliveries = salesDeliveries.stream().filter(salesDeliveryApprove -> EyUserContextHolder.get().getAuthUserId().equals(salesDeliveryApprove.getSalesDeliveryCredit())).collect(Collectors.toList());
                }
            }
            if(EmptyUtils.isNotEmpty(salesDeliveries)){
                rDTO.setKpiList(ConvertUtils.convertEntityList2DTOList(salesDeliveries, SalesDeliveryRepDTO.class));
            }
        }
        List<LocalDateTime> deliveryDateRange = new ArrayList<>();
        deliveryDateRange.add(rDTO.getKickoffWeek());
        deliveryDateRange.add(rDTO.getClosingWeek());
        rDTO.setDeliveryDateRange(deliveryDateRange);
        //填充user名字和状态
        List<String> ids = new ArrayList<>();
        ids.add(entity.getEngManagerId());
        ids.add(entity.getPursuitLeaderId());
        ids.add(entity.getOpprPartnerId());
        List<SalesDeliveryRepDTO> salesDeliveryRepDTOs = rDTO.getKpiList();
        if(EmptyUtils.isNotEmpty(salesDeliveryRepDTOs)){
            for(SalesDeliveryRepDTO salesDeliveryRepDTO:salesDeliveryRepDTOs){
                ids.add(salesDeliveryRepDTO.getSalesDeliveryCredit());
            }
        }
        Map<String,UserAttribute> userAttributeMap = userAttributeService.queryUserAttributeByIds(ids,fiscalCalenderService.getCurrentFiscalCalender());
        UserAttribute opprPartnerUserAttribute = userAttributeMap.get(entity.getOpprPartnerId());
        if(EmptyUtils.isNotEmpty(opprPartnerUserAttribute)){
            rDTO.setOpprPartnerName(opprPartnerUserAttribute.getUserName());
            rDTO.setOpprPartnerStatus(opprPartnerUserAttribute.getStatus()+"");
        }

        UserAttribute engManagerAttribute = userAttributeMap.get(entity.getEngManagerId());
        if(EmptyUtils.isNotEmpty(engManagerAttribute)){
            rDTO.setEngManagerName(engManagerAttribute.getUserName());
            rDTO.setEngManagerStatus(engManagerAttribute.getStatus()+"");
        }

        UserAttribute pursuitLeaderAttribute = userAttributeMap.get(entity.getPursuitLeaderId());
        if(EmptyUtils.isNotEmpty(pursuitLeaderAttribute)){
            rDTO.setPursuitLeaderName(pursuitLeaderAttribute.getUserName());
            rDTO.setPursuitLeaderStatus(pursuitLeaderAttribute.getStatus()+"");
        }
        if(EmptyUtils.isNotEmpty(salesDeliveryRepDTOs)){
            for(SalesDeliveryRepDTO salesDeliveryRepDTO:salesDeliveryRepDTOs){
                UserAttribute salesDeliveryUserAttribute = userAttributeMap.get(salesDeliveryRepDTO.getSalesDeliveryCredit());
                if(EmptyUtils.isNotEmpty(salesDeliveryUserAttribute)){
                    salesDeliveryRepDTO.setSalesDeliveryCreditStatus(salesDeliveryUserAttribute.getStatus()+"");
                }
            }
        }

        return ResponseDTO.success(rDTO);
    }

    @Override
    public ResponseDTO<PipelineRepDTO> queryByIdForEic(PipelineQueryByIdDTO dto) {
        Pipeline entity = getService().queryById(dto.getId());
        if(EmptyUtils.isEmpty(entity)){
            //查询approve表
            PipelineApprove pipelineApprove = pipelineApproveService.queryById(dto.getId());
            if(EmptyUtils.isNotEmpty(pipelineApprove)){
                entity = new Pipeline();
                BeanUtils.copyProperties(pipelineApprove,entity);
            }
        }
        PipelineRepDTO rDTO = ConvertUtils.convertEntity2DTO(entity, PipelineRepDTO.class);

        //处理companyType 将list转为String.[1,2]变为“1，2”
        if(EmptyUtils.isNotEmpty(entity.getCompanyType())){
            String cleanedString = entity.getCompanyType().toString()
                    .replace("[", "")
                    .replace("]", "")
                    .replace("\"", "");
            String[] split = cleanedString.split(",");
            List<String> list = new ArrayList<>();
            for (String s : split) {
                list.add(dictionaryService.getDictName("clientType",s));
            }
            rDTO.setCompanyType(String.join(",", list));
        }if(EmptyUtils.isNotEmpty(entity.getStockExchange())){
            String cleanedString = entity.getStockExchange().toString()
                    .replace("[", "")
                    .replace("]", "")
                    .replace("\"", "");
            rDTO.setStockExchange(cleanedString);
        }
        //赋值、人员名称、产品名称
        if(EmptyUtils.isNotEmpty(entity)){
            rDTO.setProductName(productService.queryNameById(entity.getProductId(),EmptyUtils.isNotEmpty(entity.getWonFy())?entity.getWonFy():entity.getPursuitFy()));
        }
        //查询配置表
        PipelineConfig config = new PipelineConfig();
        config.setTableCode(PipelineConstants.PIPELINE_TABLE_CODE);
        config.setFiscalYear(String.valueOf(entity.getPursuitFy()));
        List<PipelineConfig> configs = pipelineConfigService.queryByPara(config);
        for (PipelineConfig pipelineConfig : configs) {
            pipelineConfig.setEdit(Integer.valueOf(pipelineConfig.getIsEdit()));
        }
        if(EmptyUtils.isNotEmpty(rDTO)){
            rDTO.setPipelineConfigList(configs);
            List<String> columnNames = configs.stream().map(PipelineConfig::getColumnName).collect(Collectors.toList());
            //根据id和颜色生成map

            Map<String, String> map1 = new HashMap<>();
            for (PipelineConfig pipelineConfig : configs) {
                if(EmptyUtils.isNotEmpty(pipelineConfig.getColor())){
                    map1.put(pipelineConfig.getColumnName(),pipelineConfig.getColor());
                }
            }
            PipelineApprove qPipelineApprove = new PipelineApprove();
            qPipelineApprove.setPipelineCode(entity.getPipelineCode());
            qPipelineApprove.setConfirmStatus(PipelineConstants.APPROVE_STATUS_AMENDMENT_SUBMITTED);
            PipelineApprove pipelineApprove = pipelineApproveService.queryOneByPara(qPipelineApprove);
            if(EmptyUtils.isNotEmpty(pipelineApprove)){
                Pipeline pipeline2 = new Pipeline();
                BeanUtils.copyProperties(pipelineApprove,pipeline2);
                Map<String, Object> map = CommonUtils.getDifferences(entity,pipeline2,columnNames);
                Map<String,String> colorMap = new HashMap<>();
                for (Map.Entry<String, Object> entry : map.entrySet()) {
                    String key = entry.getKey();
                    Object value = entry.getValue();
                    if(EmptyUtils.isNotEmpty(value)){
                        colorMap.put(key,map1.get(key));
                    }
                }
                rDTO.setEditColumnMap(colorMap);
            }
        }
        //查询kpi如果审批状态为Amendment Submitted 数据替换为approve表数据
        SalesDelivery salesDelivery = new SalesDelivery();
        salesDelivery.setPipelineId(dto.getId());
        List<SalesDelivery> salesDeliveries = salesDeliveryService.queryByPara(salesDelivery);
        if(EmptyUtils.isNotEmpty(salesDeliveries)){
            rDTO.setKpiList(ConvertUtils.convertEntityList2DTOList(salesDeliveries, SalesDeliveryRepDTO.class));
        }
        List<LocalDateTime> deliveryDateRange = new ArrayList<>();
        deliveryDateRange.add(rDTO.getKickoffWeek());
        deliveryDateRange.add(rDTO.getClosingWeek());
        rDTO.setDeliveryDateRange(deliveryDateRange);
        //填充user名字和状态
        List<String> ids = new ArrayList<>();
        ids.add(entity.getEngManagerId());
        ids.add(entity.getPursuitLeaderId());
        ids.add(entity.getOpprPartnerId());
        List<SalesDeliveryRepDTO> salesDeliveryRepDTOs = rDTO.getKpiList();
        if(EmptyUtils.isNotEmpty(salesDeliveryRepDTOs)){
            for(SalesDeliveryRepDTO salesDeliveryRepDTO:salesDeliveryRepDTOs){
                ids.add(salesDeliveryRepDTO.getSalesDeliveryCredit());
            }
        }
        Map<String,UserAttribute> userAttributeMap = userAttributeService.queryUserAttributeByIds(ids,fiscalCalenderService.getCurrentFiscalCalender());
        UserAttribute opprPartnerUserAttribute = userAttributeMap.get(entity.getOpprPartnerId());
        if(EmptyUtils.isNotEmpty(opprPartnerUserAttribute)){
            rDTO.setOpprPartnerName(opprPartnerUserAttribute.getUserName());
            rDTO.setOpprPartnerStatus(opprPartnerUserAttribute.getStatus()+"");
        }

        UserAttribute engManagerAttribute = userAttributeMap.get(entity.getEngManagerId());
        if(EmptyUtils.isNotEmpty(engManagerAttribute)){
            rDTO.setEngManagerName(engManagerAttribute.getUserName());
            rDTO.setEngManagerStatus(engManagerAttribute.getStatus()+"");
        }

        UserAttribute pursuitLeaderAttribute = userAttributeMap.get(entity.getPursuitLeaderId());
        if(EmptyUtils.isNotEmpty(pursuitLeaderAttribute)){
            rDTO.setPursuitLeaderName(pursuitLeaderAttribute.getUserName());
            rDTO.setPursuitLeaderStatus(pursuitLeaderAttribute.getStatus()+"");
        }
        if(EmptyUtils.isNotEmpty(salesDeliveryRepDTOs)){
            for(SalesDeliveryRepDTO salesDeliveryRepDTO:salesDeliveryRepDTOs){
                UserAttribute salesDeliveryUserAttribute = userAttributeMap.get(salesDeliveryRepDTO.getSalesDeliveryCredit());
                if(EmptyUtils.isNotEmpty(salesDeliveryUserAttribute)){
                    salesDeliveryRepDTO.setSalesDeliveryCreditStatus(salesDeliveryUserAttribute.getStatus()+"");
                }
            }
        }

        return ResponseDTO.success(rDTO);
    }


    /**
     * queryList Pipeline from http
     */
    @Override
    public ResponseDTO<List<PipelineRepDTO>> queryList(PipelineQueryDTO dto) {
        Pipeline entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        if(EmptyUtils.isNotEmpty(entity.getPursuitFy())){
            EyCommonContextHolder.put(TargetAuthConstants.KEY_YEAR, entity.getPursuitFy());
        }
        List<Pipeline> entities = new ArrayList<>();
        if(EmptyUtils.isNotEmpty(dto.getIsRepeat())&&dto.getIsRepeat()==1){
            entities = getService().queryRepeatPipeline(entity);
        }else{
            entities = getService().queryByPara(entity);
        }
        List<PipelineRepDTO> pipelineRepDTOS = ConvertUtils.convertEntityList2DTOList(entities, PipelineRepDTO.class);
        for (PipelineRepDTO pipeline : pipelineRepDTOS) {
            pipeline.set_auto_idx(Long.valueOf(pipelineRepDTOS.indexOf(pipeline) + 1));
            pipeline.setOpprPartnerName(userAttributeService.queryNameByIdFromThread(pipeline.getOpprPartnerId(),pipeline.getPursuitFy()));
            pipeline.setEngManagerName(userAttributeService.queryNameByIdFromThread(pipeline.getEngManagerId(),pipeline.getPursuitFy()));
            pipeline.setPursuitLeaderName(userAttributeService.queryNameByIdFromThread(pipeline.getPursuitLeaderId(),pipeline.getPursuitFy() ));
            pipeline.setProductName(productService.queryNameByIdFromThreadLocal(pipeline.getProductId(),EmptyUtils.isNotEmpty(pipeline.getWonFy())?pipeline.getWonFy():pipeline.getPursuitFy() ));
        }

        return ResponseDTO.success(pipelineRepDTOS);
    }

    /**
     * Page Pipeline from http
     */
    @Override
    public ResponseDTO<SearchDTO<PipelineRepDTO>> queryPage(SearchDTO<PipelineQueryPageDTO> searchDTO) {
        Search<Pipeline> search = ConvertUtils.convertSearchDTO2Search(searchDTO, entityClass);
        if(EmptyUtils.isNotEmpty(search.getQueryParams().getPursuitFy())){
            EyCommonContextHolder.put(TargetAuthConstants.KEY_YEAR, search.getQueryParams().getPursuitFy());
        }
        UserAttribute userAttribute = userAttributeService.queryCurrentUser();
        if (CommonConstants.BOSS_NAME_LIST.contains(userAttribute.getUserEmail()) || CommonConstants.Pipe_Jesse.equals(userAttribute.getGpn()) || CommonConstants.Pipe_Paul.contains(userAttribute.getGpn())) {
            if(CommonConstants.Pipe_Jesse.equals(userAttribute.getGpn())){
                //Jesse Lv （CN010782297）权限可以看TT的（SL3 Leader）
                search.getQueryParams().setSsl3("1028");
            }else if(CommonConstants.Pipe_Paul.contains(userAttribute.getGpn())){
                search.getQueryParams().setSsl2("1229");
            }
        }else {
            search.getQueryParams().setPipelineUserId(userAttribute.getUserId());
        }
        //处理条件tpc
        if(EmptyUtils.isNotEmpty(search.getQueryParams().getTpcList())&&search.getQueryParams().getTpcList().size()==1){
            if(search.getQueryParams().getTpcList().get(0).equals(BooleanConstants.BOOLEAN_1)){
                search.getQueryParams().setIsTpc(BooleanConstants.BOOLEAN_1);
            }else {
                search.getQueryParams().setIsTpc("0");
            }
        }
        getService().queryPageByParaFromMainAndApprove(search);
        SearchDTO<PipelineRepDTO> searchDTOResponseDTO = ConvertUtils.convertSearch2SearchDTO(search, PipelineRepDTO.class);
        String authUserId = EyUserContextHolder.get().getAuthUserId();
        PipelineConfig pipelineConfig = new PipelineConfig();
        pipelineConfig.setTableCode(PipelineConstants.PIPELINE_TABLE_CODE);
        pipelineConfig.setFiscalYear(String.valueOf(EyCommonContextHolder.get(TargetAuthConstants.KEY_YEAR).get()));
        List<PipelineConfig> configs = pipelineConfigService.queryByPara(pipelineConfig);
        //configs取isBlank为1的字段
        List<String> columnNames = configs.stream().filter(c->EmptyUtils.isNotEmpty(c.getIsBlank())).filter(pipelineConfig1 -> pipelineConfig1.getIsBlank().equals(BooleanConstants.BOOLEAN_1)).map(PipelineConfig::getColumnName).collect(Collectors.toList());
        //设置是否可以编辑。outcome为won时，可以编辑，否则不可以编辑、审批状态不为1时，可以编辑
        //赋值、人员名称、产品名称
        searchDTOResponseDTO.getRecords().forEach(pipeline -> {
            //status=won
            //Confirm  Status = Processed或Returned
            //主EP、EM
            if(PipelineConstants.PIPELINE_STATUS_WON.equals(pipeline.getStatus()) && EmptyUtils.isNotEmpty(pipeline.getConfirmStatus()) &&
                    (pipeline.getEngManagerId().equals(authUserId) || pipeline.getOpprPartnerId().equals(authUserId))
            ){
                pipeline.setEditable(true);
                //getNullProperties 判断是否有必填字段为空的
                List<String> nullProperties = CommonUtils.getNullProperties(pipeline, columnNames);
                if(EmptyUtils.isNotEmpty(nullProperties)){
                    pipeline.setConfirmStatus("Processed - missing data");
                }
            }else {
                pipeline.setEditable(false);
            }
            pipeline.setOpprPartnerName(userAttributeService.queryNameByIdFromThread(pipeline.getOpprPartnerId(),String.valueOf(EyCommonContextHolder.get(TargetAuthConstants.KEY_YEAR).get()) ));
            pipeline.setEngManagerName(userAttributeService.queryNameByIdFromThread(pipeline.getEngManagerId(),String.valueOf(EyCommonContextHolder.get(TargetAuthConstants.KEY_YEAR).get())));
            pipeline.setPursuitLeaderName(userAttributeService.queryNameByIdFromThread(pipeline.getPursuitLeaderId(),String.valueOf(EyCommonContextHolder.get(TargetAuthConstants.KEY_YEAR).get()) ));
            pipeline.setProductName(productService.queryNameByIdFromThreadLocal(pipeline.getProductId(),EmptyUtils.isNotEmpty(pipeline.getWonFy())?pipeline.getWonFy():pipeline.getPursuitFy() ));
            List<LocalDateTime> deliveryDateRange = new ArrayList<>();
            deliveryDateRange.add(pipeline.getForecastStartDate());
            deliveryDateRange.add(pipeline.getForecastEndDate());
            pipeline.setDeliveryDateRange(deliveryDateRange);
            //查询businesssales
            SalesBusiness salesBusiness = new SalesBusiness();
            salesBusiness.setPipelineId(pipeline.getId());
            List<SalesBusiness> salesBusinessList = salesBusinessService.queryByPara(salesBusiness);
            List<SalesBusinessDTO> salesBusinessDTOS = new ArrayList<>();

            if(EmptyUtils.isNotEmpty(salesBusinessList)){
                for (SalesBusiness business : salesBusinessList) {
                    SalesBusinessDTO salesBusinessDTO = new SalesBusinessDTO();
                    salesBusinessDTO.setFiscalYear(business.getFiscalYear());
                    salesBusinessDTO.setSalesDeliveryCredit(business.getSalesDeliveryCredit());
                    salesBusinessDTO.setNer(business.getNer());
                    salesBusinessDTO.setSalesDeliveryCreditType(dictionaryService.getDictName( "salesCreditType", String.valueOf(business.getSalesDeliveryCreditType())));
                    salesBusinessDTOS.add(salesBusinessDTO);
                }
                //按年分组
                Map<String, List<SalesBusinessDTO>> map = salesBusinessDTOS.stream().collect(Collectors.groupingBy(SalesBusinessDTO::getFiscalYear));
                pipeline.setMemos(map);
            }

        });
        return ResponseDTO.success(searchDTOResponseDTO);
    }

    @Override
    public ResponseDTO<SearchDTO<PipelineRepDTO>> queryPageEm(SearchDTO<PipelineQueryPageDTO> searchDTO) {
        return queryPageWithOutAuth(searchDTO);
    }

    @Override
    public ResponseDTO<SearchDTO<PipelineRepDTO>> queryPageEp(SearchDTO<PipelineQueryPageDTO> searchDTO) {
        return queryPageWithOutAuth(searchDTO);
    }

    @Override
    public ResponseDTO<SearchDTO<PipelineRepDTO>> queryPageForReport(SearchDTO<PipelineReportQueryPageDTO> searchDTO) {
        try {
            if(EmptyUtils.isNotEmpty(searchDTO.getQueryParams().getSearchType())){
                EyCommonContextHolder.put(TargetAuthConstants.KEY_TARGET_AUTH,searchDTO.getQueryParams().getSearchType());
            }
            Boolean self = false;//查询单人
            if (EmptyUtils.isNotEmpty(searchDTO.getQueryParams().getUserId())) {
                self = true;
            }
            if (EmptyUtils.isNotEmpty(searchDTO.getQueryParams().getPursuitFy())) {
                EyCommonContextHolder.put(TargetAuthConstants.KEY_YEAR, searchDTO.getQueryParams().getPursuitFy());
            }else if(EmptyUtils.isNotEmpty(searchDTO.getQueryParams().getWonFy())){
                EyCommonContextHolder.put(TargetAuthConstants.KEY_YEAR, searchDTO.getQueryParams().getWonFy());
            } else if (EmptyUtils.isNotEmpty(searchDTO.getQueryParams().getSearchFy())) {
                EyCommonContextHolder.put(TargetAuthConstants.KEY_YEAR, searchDTO.getQueryParams().getSearchFy());
            }
            SearchDTO<PipelineRepDTO> searchDTOResponseDTO = null;
            if (self) {
                List<String> pipelineIds = new ArrayList<>();
                SalesDelivery salesDelivery = new SalesDelivery();
                //salesDelivery.setFiscalYear(String.valueOf(searchDTO.getQueryParams().getPursuitFy()));
                salesDelivery.setSalesDeliveryCredit(searchDTO.getQueryParams().getUserId());
                //比例大于50
                salesDelivery.setSalesDeliveryCreditRatiomin(new BigDecimal("50"));
                List<SalesDelivery> salesDeliveries = salesDeliveryService.queryByPara(salesDelivery);
                pipelineIds = new ArrayList<>(salesDeliveries.stream().map(SalesDelivery::getPipelineId).collect(Collectors.toSet()));
                if (EmptyUtils.isEmpty(pipelineIds)) {
                    pipelineIds.add("0");//如果没有数据，查询不到数据
                }
                Search<Pipeline> search = ConvertUtils.convertSearchDTO2Search(searchDTO, entityClass);
                if (EmptyUtils.isNotEmpty(searchDTO.getQueryParams().getCompetencyList())) {
                    search.getQueryParams().setLocalServiceCodeList(searchDTO.getQueryParams().getCompetencyList());
                }
                search.getQueryParams().setPipelineIdList(pipelineIds);
                getService().queryPageByPara(search);
                searchDTOResponseDTO = ConvertUtils.convertSearch2SearchDTO(search, PipelineRepDTO.class);
            } else {
                Search<Pipeline> search = ConvertUtils.convertSearchDTO2Search(searchDTO, entityClass);
                if (EmptyUtils.isNotEmpty(searchDTO.getQueryParams().getMarketList()) || EmptyUtils.isNotEmpty(searchDTO.getQueryParams().getCityList()) || EmptyUtils.isNotEmpty(searchDTO.getQueryParams().getSubServiceLineList()) || EmptyUtils.isNotEmpty(searchDTO.getQueryParams().getCompetencyList()) || searchDTO.getQueryParams().getType() == 1) {
                    //查询当前用户为buddyPartner的用户
                    String userId = EyUserContextHolder.get().getAuthUserId();
                    UserAttribute userAttribute = new UserAttribute();
                    userAttribute.setUserId(userId);
                    userAttribute.setFiscalYear(String.valueOf(EyCommonContextHolder.get(TargetAuthConstants.KEY_YEAR).get()));
                    List<UserAttribute> list = userAttributeService.queryByPara(userAttribute);
                    if (EmptyUtils.isNotEmpty(list)) {
                        userAttribute = list.get(0);
                        if ((searchDTO.getQueryParams().getType() == 1) && (!CommonConstants.BOSS_NAME_LIST.contains(userAttribute.getUserEmail())&& !CommonConstants.BOSS_NAME_LIST_25.contains(userAttribute.getUserName()))) {
                            search.getQueryParams().setBuddyPartnerEmail(userAttribute.getUserEmail());
                        }
                    }else {
                        search.getQueryParams().setBuddyPartnerEmail("0");
                    }
                    if(searchDTO.getQueryParams().getType()==1){
                        search.getQueryParams().setIsBuddyPartner(0);
                        //type查询3、8
                        List<Integer> salesDeliveryCreditTypes = new ArrayList<>();
                        salesDeliveryCreditTypes.add(3);
                        salesDeliveryCreditTypes.add(8);
                        search.getQueryParams().setSalesDeliveryCreditTypes(salesDeliveryCreditTypes);
                    }else {
                        List<Integer> salesDeliveryCreditTypes = new ArrayList<>();
                        salesDeliveryCreditTypes.add(1);
                        salesDeliveryCreditTypes.add(2);
                        search.getQueryParams().setSalesDeliveryCreditTypes(salesDeliveryCreditTypes);
                    }

                    if (EmptyUtils.isNotEmpty(searchDTO.getQueryParams().getMarketList())) {
                        search.getQueryParams().setRegions(searchDTO.getQueryParams().getMarketList());
                    }
                    if (EmptyUtils.isNotEmpty(searchDTO.getQueryParams().getCityList())) {
                        search.getQueryParams().setLocations(searchDTO.getQueryParams().getCityList());
                    }


                    if (EmptyUtils.isNotEmpty(searchDTO.getQueryParams().getCompetencyList())) {
                        search.getQueryParams().setSsl3s(searchDTO.getQueryParams().getCompetencyList());
                    }
                    if((CommonConstants.BOSS_NAME_LIST.contains(userAttribute.getUserEmail())|| ( CommonConstants.BOSS_NAME_LIST_25.contains(userAttribute.getUserName())))&& searchDTO.getQueryParams().getType() == 1){
                        search.getQueryParams().setIsBoss(1);
                    }
                }
                if (EmptyUtils.isNotEmpty(search.getQueryParams().getPursuitFy())) {
                    EyCommonContextHolder.put(TargetAuthConstants.KEY_YEAR, search.getQueryParams().getPursuitFy());
                }
                if (EmptyUtils.isNotEmpty(searchDTO.getQueryParams().getCompetencyList())) {
                    search.getQueryParams().setLocalServiceCodeList(searchDTO.getQueryParams().getCompetencyList());
                }
                //如果当前用户是boss
                UserAttribute user = userAttributeService.queryCurrentUser();
                Search<PipelineForMemo> search1 = ConvertUtils.convertSearchDTO2Search(searchDTO, PipelineForMemo.class);
                if(EmptyUtils.isNotEmpty(searchDTO.getQueryParams().getSearchType())){
                    getService().queryPageByParaForAuthMemo(search1);

                }else if((CommonConstants.BOSS_NAME_LIST.contains(user.getUserEmail())||CommonConstants.BOSS_NAME_LIST_25.contains(user.getUserEmail()) )&& searchDTO.getQueryParams().getType() != 1&&EmptyUtils.isEmpty(searchDTO.getQueryParams().getSearchType())) {
                    getService().queryPageByPara(search);
                }else {
                    getService().queryPageByParaForAuth(search);
                }
                if(EmptyUtils.isNotEmpty(searchDTO.getQueryParams().getSearchType())){
                    searchDTOResponseDTO = ConvertUtils.convertSearch2SearchDTO(search1, PipelineRepDTO.class);
                }else {
                    searchDTOResponseDTO = ConvertUtils.convertSearch2SearchDTO(search, PipelineRepDTO.class);
                }

            }
            String authUserId = EyUserContextHolder.get().getAuthUserId();
            List<PipelineRepDTO> result = new ArrayList<>();
            //赋值、人员名称、产品名称
            int index = 1;
            searchDTOResponseDTO.getRecords().forEach(pipeline -> {
                pipeline.setOpprPartnerName(userAttributeService.queryNameByIdFromThread(pipeline.getOpprPartnerId(),String.valueOf(EyCommonContextHolder.get(TargetAuthConstants.KEY_YEAR).get())));
                pipeline.setEngManagerName(userAttributeService.queryNameByIdFromThread(pipeline.getEngManagerId(),String.valueOf(EyCommonContextHolder.get(TargetAuthConstants.KEY_YEAR).get())));
                pipeline.setPursuitLeaderName(userAttributeService.queryNameByIdFromThread(pipeline.getPursuitLeaderId(),String.valueOf(EyCommonContextHolder.get(TargetAuthConstants.KEY_YEAR).get())));
                pipeline.setProductName(productService.queryNameByIdFromThreadLocal(pipeline.getProductId(),EmptyUtils.isNotEmpty(pipeline.getWonFy())?pipeline.getWonFy():pipeline.getPursuitFy()));

                List<LocalDateTime> deliveryDateRange = new ArrayList<>();
                deliveryDateRange.add(pipeline.getKickoffWeek());
                deliveryDateRange.add(pipeline.getClosingWeek());
                pipeline.setDeliveryDateRange(deliveryDateRange);
                //赋值 market
                UserAttribute u = userAttributeService.queryByIdFromThread(pipeline.getOpprPartnerId(),String.valueOf(EyCommonContextHolder.get(TargetAuthConstants.KEY_YEAR).get()));
                //pipeline.setLocalServiceCode(dictionaryService.getDictName("localServiceCode", pipeline.getLocalServiceCode()));
                if(EmptyUtils.isNotEmpty(u)){
                    pipeline.setMarket(dictionaryService.getParentValue("location", u.getCity()));
                    pipeline.setCity(dictionaryService.getDictName("location", u.getCity()));
                }
                pipeline.setIndustrySector(dictionaryService.getDictName("sector", pipeline.getIndustrySector()));
                //判断pipeline下是否有其他tpc
                if (searchDTO.getQueryParams().getType() == 1) {
                    pipeline.setAdviceName(userAttributeService.queryNameByIdFromThread(pipeline.getAdviceName(),String.valueOf(EyCommonContextHolder.get(TargetAuthConstants.KEY_YEAR).get())));
                    pipeline.setPipelineId(pipeline.getId());
                    //赋值随机id
                    pipeline.setId(index + "");
                }
                if(EmptyUtils.isNotEmpty(searchDTO.getQueryParams().getSearchType())){
                    pipeline.setMemoSales(pipeline.getSalesDeliveryCreditAmount());
                    if(EmptyUtils.isNotEmpty(pipeline.getUseAmount())&&pipeline.getUseAmount()==1){
                        pipeline.setMemoNer(pipeline.getFNer());
                        pipeline.setMemoSales(BigDecimal.ZERO);
                    }else {
                        pipeline.setMemoNer(pipeline.getFNer());
                    }
                    pipeline.setSalesDeliveryCredit(userAttributeService.queryNameByIdFromThread(pipeline.getSalesDeliveryCredit(),String.valueOf(EyCommonContextHolder.get(TargetAuthConstants.KEY_YEAR).get())));

                }
                result.add(pipeline);
            });
            searchDTOResponseDTO.setRecords(result);
            return ResponseDTO.success(searchDTOResponseDTO);
        }catch (Exception e){
            logger.error("queryPageForReport error",e);
            SearchDTO<PipelineRepDTO> searchDTOResponseDTO = new SearchDTO<>();
            return ResponseDTO.success(searchDTOResponseDTO);
        }
    }

    @Override
    public ResponseDTO<SearchDTO<PipelineRepDTO>> queryPageForReportPipeline(SearchDTO<PipelineReportQueryPageDTO> searchDTO) {
        return this.queryPageForReport(searchDTO);
    }

    @Override
    public ResponseDTO<SearchDTO<PipelineRepDTO>> queryPageForReportSales(SearchDTO<PipelineReportQueryPageDTO> searchDTO) {
        return this.queryPageForReport(searchDTO);
    }
    public ResponseDTO<SearchDTO<PipelineRepDTO>> queryPageForReportUser(SearchDTO<PipelineReportQueryPageDTO> searchDTO) {
        if(EmptyUtils.isEmpty(searchDTO.getQueryParams().getStatus())){
            //将Won、Pursuit添加到状态查询
            searchDTO.getQueryParams().setStatusList(Arrays.asList(PipelineConstants.PIPELINE_STATUS_WON,PipelineConstants.PIPELINE_STATUS_PURSUIT));
        }
        return this.queryPageForReport(searchDTO);
    }

    @Override
    public ResponseDTO<SearchDTO<PipelineRepDTO>> queryPageForReportTpc(SearchDTO<PipelineReportQueryPageDTO> searchDTO) {
        return this.queryPageForReport(searchDTO);
    }

    /**
     * Page Pipeline from http
     */
    @Override
    public ResponseDTO<SearchDTO<PipelineRepDTO>> queryPageWithOutAuth(SearchDTO<PipelineQueryPageDTO> searchDTO) {
        Search<Pipeline> search = ConvertUtils.convertSearchDTO2Search(searchDTO, entityClass);
        if(EmptyUtils.isNotEmpty(search.getQueryParams().getPursuitFy())){
            EyCommonContextHolder.put(TargetAuthConstants.KEY_YEAR, search.getQueryParams().getPursuitFy());
            search.getQueryParams().setSearchYear(String.valueOf(EyCommonContextHolder.get(TargetAuthConstants.KEY_YEAR).get()));
            search.getQueryParams().setPursuitFy(null);
        }
        String currentFiscalYear = fiscalCalenderService.getCurrentFiscalCalender();
        UserAttribute userAttribute = userAttributeService.queryCurrentUser();
        if (CommonConstants.BOSS_NAME_LIST.contains(userAttribute.getUserEmail()) || CommonConstants.Pipe_Jesse.equals(userAttribute.getGpn()) || CommonConstants.Pipe_Paul.contains(userAttribute.getGpn())) {
            if(CommonConstants.Pipe_Jesse.equals(userAttribute.getGpn())){
                //Jesse Lv （CN010782297）权限可以看TT的（SL3 Leader）
                search.getQueryParams().setSsl3("1028");
            }else if(CommonConstants.Pipe_Paul.contains(userAttribute.getGpn())){
                search.getQueryParams().setSsl2("1229");
            }
        }else {
            search.getQueryParams().setPipelineUserId(userAttribute.getUserId());
        }
        String fiscalYear = String.valueOf(EyCommonContextHolder.get(TargetAuthConstants.KEY_YEAR).get());
        //处理条件tpc
        if(EmptyUtils.isNotEmpty(search.getQueryParams().getTpcList())&&search.getQueryParams().getTpcList().size()==1){
            if(search.getQueryParams().getTpcList().get(0).equals(BooleanConstants.BOOLEAN_1)){
                search.getQueryParams().setIsTpc(BooleanConstants.BOOLEAN_1);
            }else {
                search.getQueryParams().setIsTpc("0");
            }
        }
        getService().queryPageByParaFromMainAndApprove(search);
        SearchDTO<PipelineRepDTO> searchDTOResponseDTO = ConvertUtils.convertSearch2SearchDTO(search, PipelineRepDTO.class);
        String authUserId = EyUserContextHolder.get().getAuthUserId();
        //设置是否可以编辑。outcome为won时，可以编辑，否则不可以编辑、审批状态不为1时，可以编辑
        //赋值、人员名称、产品名称
        // 提前收集所有需要的用户ID和产品ID，减少循环内查询
        List<String> userIds = new ArrayList<>();
        Map<String, String> fiscalYearByPipelineId = new HashMap<>();
        List<String> pipelineIds = new ArrayList<>();

        for (PipelineRepDTO pipeline : searchDTOResponseDTO.getRecords()) {
            if (EmptyUtils.isNotEmpty(pipeline.getOpprPartnerId())) {
                userIds.add(pipeline.getOpprPartnerId());
            }
            if (EmptyUtils.isNotEmpty(pipeline.getEngManagerId())) {
                userIds.add(pipeline.getEngManagerId());
            }
            if (EmptyUtils.isNotEmpty(pipeline.getPursuitLeaderId())) {
                userIds.add(pipeline.getPursuitLeaderId());
            }


            String pipelineFiscalYear = EmptyUtils.isNotEmpty(pipeline.getWonFy()) ? pipeline.getWonFy() : pipeline.getPursuitFy();
            fiscalYearByPipelineId.put(pipeline.getId(), pipelineFiscalYear);
            pipelineIds.add(pipeline.getId());
        }

        // 批量查询用户信息
        Map<String, String> userNameMap = new HashMap<>();
        Map<String, UserAttribute> userAttributeMap = userAttributeService.queryUserAttributeByIds( userIds, fiscalYear);
        userAttributeMap.forEach((userId, userAttribute1) -> {
            if (userAttribute1 != null) {
                userNameMap.put(userId, userAttribute1.getUserName());
            }
        });


        // 批量查询SalesBusiness信息
        Map<String, List<SalesBusiness>> salesBusinessByPipelineId = Collections.emptyMap();
        if (!pipelineIds.isEmpty()) {
            List<SalesBusiness> allSalesBusiness = salesBusinessService.queryByPipelineIdsAndFiscalYear(pipelineIds, fiscalYear);
            salesBusinessByPipelineId = allSalesBusiness.stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.groupingBy(SalesBusiness::getPipelineId));
        }


        for (PipelineRepDTO pipeline : searchDTOResponseDTO.getRecords()) {


            if(EmptyUtils.isNotEmpty(pipeline.getIsSame()) && pipeline.getIsSame()==1){
                pipeline.setStatus(pipeline.getStatus()+"-Repeat");
            }

            // 设置编辑权限
            boolean isWon = PipelineConstants.PIPELINE_STATUS_WON.equals(pipeline.getStatus());
            boolean hasConfirmStatus = EmptyUtils.isNotEmpty(pipeline.getConfirmStatus());
            boolean isManager = authUserId.equals(pipeline.getEngManagerId()) || authUserId.equals(pipeline.getOpprPartnerId());

            pipeline.setEditable(isWon && hasConfirmStatus && isManager);

            if(CommonConstants.BOSS_NAME_LIST.contains(userAttribute.getUserEmail())&& EmptyUtils.isNotEmpty(pipeline.getConfirmStatus())&&PipelineConstants.APPROVE_STATUS_PROCESSED.equals(pipeline.getConfirmStatus())){
                pipeline.setEditable(true);
            }

            if(pipeline.isEditable() && EmptyUtils.isNotEmpty(pipeline.getIsBlank()) && pipeline.getIsBlank()==1){
                pipeline.setConfirmStatus("Processed - missing data");
            }

            // 设置用户名称（从预加载的Map中获取）
            pipeline.setOpprPartnerName(userNameMap.getOrDefault(pipeline.getOpprPartnerId(), ""));
            pipeline.setEngManagerName(userNameMap.getOrDefault(pipeline.getEngManagerId(), ""));
            pipeline.setPursuitLeaderName(userNameMap.getOrDefault(pipeline.getPursuitLeaderId(), ""));

            List<LocalDateTime> deliveryDateRange = new ArrayList<>();
            deliveryDateRange.add(pipeline.getForecastStartDate());
            deliveryDateRange.add(pipeline.getForecastEndDate());
            pipeline.setDeliveryDateRange(deliveryDateRange);
            // 设置产品名称
            pipeline.setProductName(productService.queryNameByIdFromThreadLocal(pipeline.getProductId(),EmptyUtils.isNotEmpty(pipeline.getWonFy())?pipeline.getWonFy():pipeline.getPursuitFy()));


            // 处理SalesBusiness数据
            List<SalesBusiness> salesBusinessList = salesBusinessByPipelineId.getOrDefault(pipeline.getId(), Collections.emptyList());
            if (!salesBusinessList.isEmpty()) {
                List<SalesBusinessDTO> salesBusinessDTOs = new ArrayList<>(salesBusinessList.size());
                BigDecimal currentYearNer = BigDecimal.ZERO;

                for (SalesBusiness business : salesBusinessList) {
                    SalesBusinessDTO dto = new SalesBusinessDTO();
                    dto.setFiscalYear(business.getFiscalYear());

                    try {
                        String salesUserName = userAttributeService.queryNameByIdFromThread(business.getSalesDeliveryCredit(), business.getFiscalYear());
                        dto.setSalesDeliveryCredit(salesUserName != null ? salesUserName : "");
                    } catch (Exception e) {
                        logger.error("Failed to get user name for ID: " + business.getSalesDeliveryCredit(), e);
                        dto.setSalesDeliveryCredit("");
                    }

                    BigDecimal ner = BigDecimal.ZERO;

                    //如果小于0，则设置为0
                    if (EmptyUtils.isNotEmpty(business.getKpiAmount())&&business.getKpiAmount().compareTo(BigDecimal.ZERO) > 0) {
                        ner = business.getKpiAmount().setScale(2,RoundingMode.HALF_UP);
                    }
                    dto.setNer(ner);
                     dto.setSalesDeliveryCreditType(dictionaryService.getDictName( "salesCreditType", String.valueOf(business.getSalesDeliveryCreditType())));


                    salesBusinessDTOs.add(dto);

                    // 计算当前财年NER
                    if (business.getFiscalYear().equals(currentFiscalYear)) {
                        currentYearNer = currentYearNer.add(ner);
                    }
                }

                // 按年分组
                Map<String, List<SalesBusinessDTO>> memosByYear = salesBusinessDTOs.stream()
                        .collect(Collectors.groupingBy(SalesBusinessDTO::getFiscalYear));

                pipeline.setRecognized(currentYearNer);
                pipeline.setMemos(memosByYear);
            }
        }
        return ResponseDTO.success(searchDTOResponseDTO);
    }

    @Override
    /**
     * 查询汇总信息
     *
     * @param dto PipelineQueryPageDTO对象，包含查询参数
     * @return ResponseDTO<PipelineSumDTO> 返回包含PipelineSumDTO的ResponseDTO对象
     *
     *     - 如果Pipeline对象的状态为"PURSUIT"，将其金额添加到PipelineSumDTO对象的pursuit字段。
     *     - 如果Pipeline对象的状态为"WON"，并且其确认状态为"PROCESSED"或"AMENDMENT_SUBMITTED"，将其金额添加到PipelineSumDTO对象的sales字段。
     *     - 如果Pipeline对象的状态为"WON"，"LOST"或"PURSUIT"，将其金额添加到PipelineSumDTO对象的pipeline字段。
     *     - 如果Pipeline对象的effortsApprove字段为"APPROVE"，将其金额添加到PipelineSumDTO对象的eicEfforts字段。
     */
    public ResponseDTO<PipelineSumDTO> querySum(PipelineQueryPageDTO dto) {
        if(EmptyUtils.isNotEmpty(dto.getPursuitFy())){
            EyCommonContextHolder.put(TargetAuthConstants.KEY_YEAR, dto.getPursuitFy());
        }
        Pipeline p = ConvertUtils.convertDTO2Entity(dto, entityClass);
        List<String> pipelineIds = new ArrayList<>();
        if(EmptyUtils.isNotEmpty(dto.getCompetencyList())){
            UserAttribute userAttributes = new UserAttribute();
            userAttributes.setFiscalYear(String.valueOf(dto.getPursuitFy()));
            userAttributes.setSsl3s(dto.getCompetencyList());
            List<UserAttribute> userAttributeList = userAttributeService.queryByPara(userAttributes);
            List<String> userIds = userAttributeList.stream().map(UserAttribute::getUserId).collect(Collectors.toList());
            if(EmptyUtils.isNotEmpty(userIds)){
                //根据salesDeliveryCredit查询pipeline
                SalesDelivery salesDelivery = new SalesDelivery();
                //salesDelivery.setFiscalYear(String.valueOf(dto.getPursuitFy()));
                salesDelivery.setSalesDeliveryCreditList(userIds);
                List<SalesDelivery> salesDeliveries = salesDeliveryService.queryByPara(salesDelivery);
                pipelineIds = new ArrayList<>(salesDeliveries.stream().map(SalesDelivery::getPipelineId).collect(Collectors.toSet()));
            }
        }
        //处理条件tpc
        if(EmptyUtils.isNotEmpty(dto.getTpcList())&&dto.getTpcList().size()==1){
            //查询SalesDeliveryService type为2的数据
            SalesDelivery salesDelivery = new SalesDelivery();
            salesDelivery.setSalesDeliveryCreditType(3);
            List<SalesDelivery> list = salesDeliveryService.queryByPara(salesDelivery);
            List<String> ids = new ArrayList<>();
            if(EmptyUtils.isNotEmpty(list)){
                ids = list.stream().filter(s->EmptyUtils.isNotEmpty(s.getPipelineId()) ).map(SalesDelivery::getPipelineId).toList();
            }
            if(dto.getTpcList().get(0).equals(BooleanConstants.BOOLEAN_1)){
                if(EmptyUtils.isEmpty(ids)|| ids.isEmpty()||pipelineIds.isEmpty()){
                    pipelineIds.add("0");//如果没有数据，查询不到数据
                }else {
                    //取交集
                    pipelineIds.retainAll(ids);
                }
            }else {
                if(EmptyUtils.isNotEmpty(ids)&&!ids.isEmpty()){
                    p.setPipelineIdNotInList(ids  );
                }

            }
        }
        if(EmptyUtils.isNotEmpty(pipelineIds)){
            p.setPipelineIdList(pipelineIds);
        }
        List<String> list = new ArrayList<>();
        list.add("amount");
        list.add("status");
        list.add("confirm_status");
        list.add("efforts_Approve");
        List<Pipeline> pipelines = getService().queryByParaByAuth(p,list);
        PipelineSumDTO rDTO = new PipelineSumDTO();
        rDTO.setPipeline(BigDecimal.ZERO);
        rDTO.setSales(BigDecimal.ZERO);
        rDTO.setPursuit(BigDecimal.ZERO);
        if(EmptyUtils.isNotEmpty(pipelines)){
            for(Pipeline pipeline:pipelines){
                if(PipelineConstants.PIPELINE_STATUS_PURSUIT.equals(pipeline.getStatus())){
                    rDTO.setPursuit(rDTO.getPursuit().add(pipeline.getAmount()));
                }
                if(PipelineConstants.PIPELINE_STATUS_WON.equals(pipeline.getStatus())&&
                        (PipelineConstants.APPROVE_STATUS_PROCESSED.equals(pipeline.getConfirmStatus())||
                                PipelineConstants.APPROVE_STATUS_AMENDMENT_SUBMITTED.equals(pipeline.getConfirmStatus()))){
                    rDTO.setSales(rDTO.getSales().add(pipeline.getAmount()));
                }
                if(PipelineConstants.PIPELINE_STATUS_WON.equals(pipeline.getStatus())||
                        PipelineConstants.PIPELINE_STATUS_LOST.equals(pipeline.getStatus())||
                        PipelineConstants.PIPELINE_STATUS_PURSUIT.equals(pipeline.getStatus())){
                    rDTO.setPipeline(rDTO.getPipeline().add(pipeline.getAmount()));
                }
                if(PipelineConstants.EFFORTS_APPROVE_APPROVE.equals(pipeline.getEffortsApprove())){
                    rDTO.setEicEfforts(rDTO.getEicEfforts().add(pipeline.getAmount()));
                }
            }
        }
        return ResponseDTO.success(rDTO);
    }

    private ResponseDTO<PipelineSumDTO> querySumWithOutAuth(PipelineQueryPageDTO dto) {
        try {
            Pipeline p = ConvertUtils.convertDTO2Entity(dto, entityClass);
            if (EmptyUtils.isNotEmpty(dto.getPursuitFy())) {
                EyCommonContextHolder.put(TargetAuthConstants.KEY_YEAR, dto.getPursuitFy());
            }
            UserAttribute userAttribute = userAttributeService.queryCurrentUser();
            if (CommonConstants.BOSS_NAME_LIST.contains(userAttribute.getUserEmail()) || CommonConstants.Pipe_Jesse.equals(userAttribute.getGpn()) || CommonConstants.Pipe_Paul.contains(userAttribute.getGpn())) {
                if(CommonConstants.Pipe_Jesse.equals(userAttribute.getGpn())){
                    //Jesse Lv （CN010782297）权限可以看TT的（SL3 Leader）
                    p.setSsl3("1028");
                }else if(CommonConstants.Pipe_Paul.contains(userAttribute.getGpn())){
                    p.setSsl2("1229");
                }
            }else {
                p.setPipelineUserId(userAttribute.getUserId());
            }
            //处理条件tpc
            if(EmptyUtils.isNotEmpty(p.getTpcList())&&p.getTpcList().size()==1){
                if(p.getTpcList().get(0).equals(BooleanConstants.BOOLEAN_1)){
                    p.setIsTpc("1");
                }else {
                    p.setIsTpc("0");
                }
            }
            List<String> list = new ArrayList<>();
            list.add("amount");
            list.add("status");
            list.add("confirm_status");
            list.add("efforts_Approve");
            List<Pipeline> pipelines = getService().queryByParaFromMainAndApprove(p,list);
            p.setWonFy(p.getPursuitFy());
            p.setPursuitFy(null);
            List<Pipeline> pipelines2 = getService().queryByParaFromMainAndApprove(p,list);
            PipelineSumDTO rDTO = new PipelineSumDTO();
            rDTO.setPipeline(BigDecimal.ZERO);
            rDTO.setSales(BigDecimal.ZERO);
            rDTO.setPursuit(BigDecimal.ZERO);
            rDTO.setEicEfforts(BigDecimal.ZERO);
            if (EmptyUtils.isNotEmpty(pipelines)&&!pipelines.isEmpty()) {
                pipelines.parallelStream().forEach(pipeline -> {
                    synchronized (rDTO) {
                        if (PipelineConstants.PIPELINE_STATUS_PURSUIT.equals(pipeline.getStatus())) {
                            rDTO.setPursuit(rDTO.getPursuit().add(pipeline.getAmount()));
                        }
                        if (PipelineConstants.PIPELINE_STATUS_WON.equals(pipeline.getStatus()) ||
                                PipelineConstants.PIPELINE_STATUS_LOST.equals(pipeline.getStatus()) ||
                                PipelineConstants.PIPELINE_STATUS_PURSUIT.equals(pipeline.getStatus())) {
                            rDTO.setPipeline(rDTO.getPipeline().add(pipeline.getAmount()));
                        }
                    }
                });
            }

            if (EmptyUtils.isNotEmpty(pipelines2)&&!pipelines2.isEmpty()) {
                pipelines2.parallelStream().forEach(pipeline -> {
                    synchronized (rDTO) {
                        if (PipelineConstants.PIPELINE_STATUS_WON.equals(pipeline.getStatus()) &&
                                (PipelineConstants.APPROVE_STATUS_PROCESSED.equals(pipeline.getConfirmStatus()) ||
                                        PipelineConstants.APPROVE_STATUS_AMENDMENT_SUBMITTED.equals(pipeline.getConfirmStatus()))) {
                            rDTO.setSales(rDTO.getSales().add(pipeline.getAmount()));
                        }
                        if (PipelineConstants.EFFORTS_APPROVE_APPROVE.equals(pipeline.getEffortsApprove())) {
                            //查询"Processed", "Amendment Submitted"
                            if(PipelineConstants.APPROVE_STATUS_PROCESSED.equals(pipeline.getConfirmStatus()) ||
                                    PipelineConstants.APPROVE_STATUS_AMENDMENT_SUBMITTED.equals(pipeline.getConfirmStatus())){
                                rDTO.setEicEfforts(rDTO.getEicEfforts().add(pipeline.getAmount()));
                            }
                        }
                    }
                });
            }
            //保留整数
            rDTO.setPipeline(rDTO.getPipeline().setScale(0, RoundingMode.HALF_UP));
            rDTO.setSales(rDTO.getSales().setScale(0, RoundingMode.HALF_UP));
            rDTO.setPursuit(rDTO.getPursuit().setScale(0, RoundingMode.HALF_UP));
            rDTO.setEicEfforts(rDTO.getEicEfforts().setScale(0, RoundingMode.HALF_UP));
            return ResponseDTO.success(rDTO);
        }catch (Exception e){
            logger.error("querySumWithOutAuth error",e);
            PipelineSumDTO rDTO = new PipelineSumDTO();
            rDTO.setPipeline(BigDecimal.ZERO);
            rDTO.setSales(BigDecimal.ZERO);
            rDTO.setPursuit(BigDecimal.ZERO);
            rDTO.setEicEfforts(BigDecimal.ZERO);
            return ResponseDTO.success(rDTO);
        }
    }

    @Override
    public ResponseDTO<PipelineSumReportDTO> querySumForReport(PipelineReportQueryPageDTO dto) {
        PipelineSumReportDTO rDTO = new PipelineSumReportDTO();
        rDTO.setTal(BigDecimal.ZERO);
        try {
            List<Pipeline> pipelines = new ArrayList<>();
            Boolean self = false;//查询单人
            if (EmptyUtils.isNotEmpty(dto.getUserId())) {
                self = true;
            }
            if (EmptyUtils.isNotEmpty(dto.getPursuitFy())) {
                EyCommonContextHolder.put(TargetAuthConstants.KEY_YEAR, dto.getPursuitFy());
            }else if(EmptyUtils.isNotEmpty(dto.getWonFy())){
                EyCommonContextHolder.put(TargetAuthConstants.KEY_YEAR, dto.getWonFy());
            }
            SearchDTO<PipelineRepDTO> searchDTOResponseDTO = null;
            if (self) {
                List<String> pipelineIds = new ArrayList<>();
                SalesDelivery salesDelivery = new SalesDelivery();
                //salesDelivery.setFiscalYear(String.valueOf(dto.getPursuitFy()));
                salesDelivery.setSalesDeliveryCredit(dto.getUserId());
                salesDelivery.setSalesDeliveryCreditRatiomin(new BigDecimal("50"));
                List<SalesDelivery> salesDeliveries = salesDeliveryService.queryByPara(salesDelivery);
                pipelineIds = new ArrayList<>(salesDeliveries.stream().map(SalesDelivery::getPipelineId).collect(Collectors.toSet()));
                if (EmptyUtils.isEmpty(pipelineIds)) {
                    pipelineIds.add("0");//如果没有数据，查询不到数据
                }
                Pipeline p = ConvertUtils.convertDTO2Entity(dto, entityClass);
                if (EmptyUtils.isNotEmpty(dto.getPursuitFy())) {
                    EyCommonContextHolder.put(TargetAuthConstants.KEY_YEAR, p.getPursuitFy());
                }
                if (EmptyUtils.isNotEmpty(dto.getCompetencyList())) {
                    p.setLocalServiceCodeList(dto.getCompetencyList());
                }
                p.setPipelineIdList(pipelineIds);
                pipelines = getService().queryByPara(p);
            } else {
                Pipeline p = ConvertUtils.convertDTO2Entity(dto, entityClass);
                if (EmptyUtils.isNotEmpty(dto.getMarketList()) || EmptyUtils.isNotEmpty(dto.getCityList()) || EmptyUtils.isNotEmpty(dto.getSubServiceLineList()) || EmptyUtils.isNotEmpty(dto.getCompetencyList()) || dto.getType() == 1) {
                    //查询当前用户为buddyPartner的用户
                    String userId = EyUserContextHolder.get().getAuthUserId();
                    UserAttribute userAttribute = new UserAttribute();
                    userAttribute.setUserId(userId);
                    userAttribute.setFiscalYear(String.valueOf(EyCommonContextHolder.get(TargetAuthConstants.KEY_YEAR).get()));
                    List<UserAttribute> list = userAttributeService.queryByPara(userAttribute);
                    if (EmptyUtils.isNotEmpty(list)) {
                        userAttribute = list.get(0);
                        if ((dto.getType() == 1) && (!CommonConstants.BOSS_NAME_LIST.contains(userAttribute.getUserEmail())&& !CommonConstants.BOSS_NAME_LIST_25.contains(userAttribute.getUserName()))) {
                            p.setBuddyPartnerEmail(userAttribute.getUserEmail());
                        }
                    }else {
                        p.setBuddyPartnerEmail("0");
                    }
                    if(dto.getType()==1){
                        p.setIsBuddyPartner(0);
                        //3、8
                        p.setSalesDeliveryCreditTypes(Arrays.asList(3, 8));
                    }else {
                        List<Integer> salesDeliveryCreditTypes = new ArrayList<>();
                        salesDeliveryCreditTypes.add(1);
                        salesDeliveryCreditTypes.add(2);
                        p.setSalesDeliveryCreditTypes(salesDeliveryCreditTypes);
                    }

                    if (EmptyUtils.isNotEmpty(dto.getMarketList())) {
                        p.setRegions(dto.getMarketList());
                    }
                    if (EmptyUtils.isNotEmpty(dto.getCityList())) {
                        p.setLocations(dto.getCityList());
                    }


                    if (EmptyUtils.isNotEmpty(dto.getCompetencyList())) {
                        p.setSsl3s(dto.getCompetencyList());
                    }
                    if((CommonConstants.BOSS_NAME_LIST.contains(userAttribute.getUserEmail())|| ( CommonConstants.BOSS_NAME_LIST_25.contains(userAttribute.getUserName())))&& dto.getType() == 1){
                        p.setIsBoss(1);
                    }
                }

                if (EmptyUtils.isNotEmpty(p.getPursuitFy())) {
                    EyCommonContextHolder.put(TargetAuthConstants.KEY_YEAR, p.getPursuitFy());
                }
                if (EmptyUtils.isNotEmpty(dto.getCompetencyList())) {
                    p.setLocalServiceCodeList(dto.getCompetencyList());
                }
                //如果当前用户是boss
                UserAttribute user = userAttributeService.queryCurrentUser();
                if((CommonConstants.BOSS_NAME_LIST.contains(user.getUserEmail())||CommonConstants.BOSS_NAME_LIST_25.contains(user.getUserEmail()))&& p.getType() != 1) {
                    pipelines = getService().queryByPara(p);
                }else {
                    List<String> list = new ArrayList<>();
                    list.add("amount");
                    pipelines = getService().queryByParaByAuth(p,list);
                }

            }
            //汇总amount
            if (EmptyUtils.isNotEmpty(pipelines)) {
                for (Pipeline pipeline : pipelines) {
                    rDTO.setTal(rDTO.getTal().add(pipeline.getAmount()));
                }
            }
            //保留整数
            rDTO.setTal(rDTO.getTal().setScale(0, BigDecimal.ROUND_HALF_UP));
            return ResponseDTO.success(rDTO);
        }catch (Exception e){
            logger.error("querySumForReport error",e);
            PipelineSumReportDTO rDTO2 = new PipelineSumReportDTO();
            rDTO2.setTal(BigDecimal.ZERO);
            return ResponseDTO.success(rDTO2);
        }
    }

    public ResponseDTO<PipelineSumReportDTO> querySumForReportUser(PipelineReportQueryPageDTO dto) {
        PipelineSumReportDTO rDTO = new PipelineSumReportDTO();
        rDTO.setTal(BigDecimal.ZERO);
        rDTO.setPipelineTal(BigDecimal.ZERO);
        rDTO.setSalesTal(BigDecimal.ZERO);
        try {
            List<Pipeline> pipelines = new ArrayList<>();
            Boolean self = false;//查询单人
            if (EmptyUtils.isNotEmpty(dto.getUserId())) {
                self = true;
            }
            if (EmptyUtils.isNotEmpty(dto.getPursuitFy())) {
                EyCommonContextHolder.put(TargetAuthConstants.KEY_YEAR, dto.getPursuitFy());
            }else if(EmptyUtils.isNotEmpty(dto.getWonFy())){
                EyCommonContextHolder.put(TargetAuthConstants.KEY_YEAR, dto.getWonFy());
            }
            SearchDTO<PipelineRepDTO> searchDTOResponseDTO = null;
            if (self) {
                List<String> pipelineIds = new ArrayList<>();
                SalesDelivery salesDelivery = new SalesDelivery();
                //salesDelivery.setFiscalYear(String.valueOf(dto.getPursuitFy()));
                salesDelivery.setSalesDeliveryCredit(dto.getUserId());
                salesDelivery.setSalesDeliveryCreditRatiomin(new BigDecimal("50"));
                List<SalesDelivery> salesDeliveries = salesDeliveryService.queryByPara(salesDelivery);
                pipelineIds = new ArrayList<>(salesDeliveries.stream().map(SalesDelivery::getPipelineId).collect(Collectors.toSet()));
                if (EmptyUtils.isEmpty(pipelineIds)) {
                    pipelineIds.add("0");//如果没有数据，查询不到数据
                }
                Pipeline p = ConvertUtils.convertDTO2Entity(dto, entityClass);
                if (EmptyUtils.isNotEmpty(dto.getPursuitFy())) {
                    EyCommonContextHolder.put(TargetAuthConstants.KEY_YEAR, p.getPursuitFy());
                }
                if (EmptyUtils.isNotEmpty(dto.getCompetencyList())) {
                    p.setLocalServiceCodeList(dto.getCompetencyList());
                }
                p.setPipelineIdList(pipelineIds);
                pipelines = getService().queryByPara(p);
            } else {
                Pipeline p = ConvertUtils.convertDTO2Entity(dto, entityClass);
                if (EmptyUtils.isNotEmpty(dto.getMarketList()) || EmptyUtils.isNotEmpty(dto.getCityList()) || EmptyUtils.isNotEmpty(dto.getSubServiceLineList()) || EmptyUtils.isNotEmpty(dto.getCompetencyList()) || dto.getType() == 1) {
                    //查询当前用户为buddyPartner的用户
                    String userId = EyUserContextHolder.get().getAuthUserId();
                    UserAttribute userAttribute = new UserAttribute();
                    userAttribute.setUserId(userId);
                    userAttribute.setFiscalYear(String.valueOf(EyCommonContextHolder.get(TargetAuthConstants.KEY_YEAR).get()));
                    List<UserAttribute> list = userAttributeService.queryByPara(userAttribute);
                    if (EmptyUtils.isNotEmpty(list)) {
                        userAttribute = list.get(0);
                        if ((dto.getType() == 1) && (!CommonConstants.BOSS_NAME_LIST.contains(userAttribute.getUserEmail())&& !CommonConstants.BOSS_NAME_LIST_25.contains(userAttribute.getUserName()))) {
                            p.setBuddyPartnerEmail(userAttribute.getUserEmail());
                        }
                    }else {
                        p.setBuddyPartnerEmail("0");
                    }
                    if(dto.getType()==1){
                        p.setIsBuddyPartner(0);
                        p.setSalesDeliveryCreditTypes(Arrays.asList(3, 8));
                    }else {
                        List<Integer> salesDeliveryCreditTypes = new ArrayList<>();
                        salesDeliveryCreditTypes.add(1);
                        salesDeliveryCreditTypes.add(2);
                        p.setSalesDeliveryCreditTypes(salesDeliveryCreditTypes);
                    }

                    if (EmptyUtils.isNotEmpty(dto.getMarketList())) {
                        p.setRegions(dto.getMarketList());
                    }
                    if (EmptyUtils.isNotEmpty(dto.getCityList())) {
                        p.setLocations(dto.getCityList());
                    }


                    if (EmptyUtils.isNotEmpty(dto.getCompetencyList())) {
                        p.setSsl3s(dto.getCompetencyList());
                    }
                    if((CommonConstants.BOSS_NAME_LIST.contains(userAttribute.getUserEmail())|| ( CommonConstants.BOSS_NAME_LIST_25.contains(userAttribute.getUserName())))&& dto.getType() == 1){
                        p.setIsBoss(1);
                    }
                }

                if (EmptyUtils.isNotEmpty(p.getPursuitFy())) {
                    EyCommonContextHolder.put(TargetAuthConstants.KEY_YEAR, p.getPursuitFy());
                }
                if (EmptyUtils.isNotEmpty(dto.getCompetencyList())) {
                    p.setLocalServiceCodeList(dto.getCompetencyList());
                }
                //如果当前用户是boss
                UserAttribute user = userAttributeService.queryCurrentUser();
                if((CommonConstants.BOSS_NAME_LIST.contains(user.getUserEmail())||CommonConstants.BOSS_NAME_LIST_25.contains(user.getUserEmail()))&& p.getType() != 1) {
                    pipelines = getService().queryByPara(p);
                }else {
                    List<String> list = new ArrayList<>();
                    list.add("amount");
                    list.add("status");
                    list.add("confirm_status");
                    list.add("efforts_Approve");
                    pipelines = getService().queryByParaByAuth(p,list);
                }

            }
            //汇总amount
            if (EmptyUtils.isNotEmpty(pipelines)) {
                for (Pipeline pipeline : pipelines) {
                    //根据pipeline状态汇总、Won\Pursuit
                    if(PipelineConstants.PIPELINE_STATUS_WON.equals(pipeline.getStatus())){
                        rDTO.setSalesTal(rDTO.getSalesTal().add(pipeline.getAmountUsd()));
                    }else if(PipelineConstants.PIPELINE_STATUS_PURSUIT.equals(pipeline.getStatus())){
                        rDTO.setPipelineTal(rDTO.getPipelineTal().add(pipeline.getAmountUsd()));
                    }
                }
            }
            //保留整数
            rDTO.setTal(rDTO.getTal().setScale(0, BigDecimal.ROUND_HALF_UP));
            rDTO.setPipelineTal(rDTO.getPipelineTal().setScale(0, BigDecimal.ROUND_HALF_UP));
            rDTO.setSalesTal(rDTO.getSalesTal().setScale(0, BigDecimal.ROUND_HALF_UP));
            return ResponseDTO.success(rDTO);
        }catch (Exception e){
            logger.error("querySumForReport error",e);
            PipelineSumReportDTO rDTO2 = new PipelineSumReportDTO();
            rDTO2.setTal(BigDecimal.ZERO);
            return ResponseDTO.success(rDTO2);
        }
    }

    @Override
    public ResponseDTO<PipelineSumDTO> querySumEm(PipelineQueryPageDTO dto) {
        return this.querySumWithOutAuth(dto);
    }

    @Override
    public ResponseDTO<PipelineSumReportDTO> querySumForReportPipeline(PipelineReportQueryPageDTO dto) {
        return this.querySumForReport(dto);
    }
    @Override
    public ResponseDTO<PipelineSumReportDTO> querySumForReportPipelineUser(PipelineReportQueryPageDTO dto) {
        if(EmptyUtils.isEmpty(dto.getStatus())){
            dto.setStatusList(Arrays.asList(PipelineConstants.PIPELINE_STATUS_WON,PipelineConstants.PIPELINE_STATUS_PURSUIT));
        }
        return this.querySumForReportUser(dto);
    }

    @Override
    public ResponseDTO<PipelineSumDTO> querySumEp(PipelineQueryPageDTO dto) {
        return this.querySumWithOutAuth(dto);
    }

    @Override
    public ResponseDTO<PipelineSumReportDTO> querySumForReportSales(PipelineReportQueryPageDTO dto) {
        return this.querySumForReport(dto);
    }

    @Override
    public ResponseDTO<PipelineSumReportDTO> querySumForReportTpc(PipelineReportQueryPageDTO dto) {
        return this.querySumForReport(dto);
    }


    @Override
    public ResponseDTO<SearchDTO<PipelineRepDTO>> queryPageForEicOwnEffort(SearchDTO<PipelineQueryPageDTO> searchDTO) {
        Search<Pipeline> search = ConvertUtils.convertSearchDTO2Search(searchDTO, entityClass);
        if(EmptyUtils.isNotEmpty(search.getQueryParams().getPursuitFy())){
            EyCommonContextHolder.put(TargetAuthConstants.KEY_YEAR, search.getQueryParams().getPursuitFy());
        }
        String userId = EyUserContextHolder.get().getAuthUserId();
        search.getQueryParams().setOpprPartnerId(userId);
        search.getQueryParams().setEffortsApprove(PipelineConstants.EFFORTS_APPROVE_PENDING_APPROVAL);
        search.getQueryParams().setConfirmStatusNotNull("1");
        getService().queryPageByPara(search);
        SearchDTO<PipelineRepDTO> rSearchDTO = ConvertUtils.convertSearch2SearchDTO(search, PipelineRepDTO.class);
        List<PipelineRepDTO> pipelines = (List<PipelineRepDTO>) rSearchDTO.getRecords();
        for (PipelineRepDTO pipeline : pipelines) {
            //判断审批状态，如果是Amendment Submitted 数据替换为approve表数据//如果时return也替换
            if(PipelineConstants.APPROVE_STATUS_AMENDMENT_SUBMITTED.equals(pipeline.getConfirmStatus())||PipelineConstants.APPROVE_STATUS_RETURNED.equals(pipeline.getConfirmStatus())) {
                PipelineApprove pipelineApprove = new PipelineApprove();
                pipelineApprove.setPipelineCode(pipeline.getPipelineCode());
                pipelineApprove.setConfirmStatus(pipeline.getConfirmStatus());
                pipelineApprove = pipelineApproveService.queryOneByPara(pipelineApprove);
                if (EmptyUtils.isNotEmpty(pipelineApprove)) {
                    BeanUtils.copyProperties(pipelineApprove, pipeline);
                }
            }
        }

        //填充user名字和状态
        List<String> ids = new ArrayList<>();
        if(EmptyUtils.isNotEmpty(pipelines)){
            for(PipelineRepDTO pipeline:pipelines){
                ids.add(pipeline.getEngManagerId());
                ids.add(pipeline.getPursuitLeaderId());
                ids.add(pipeline.getOpprPartnerId());
            }
        }
        if(EmptyUtils.isNotEmpty(pipelines)) {
            for (PipelineRepDTO pipeline : pipelines) {
                Map<String,UserAttribute> userAttributeMap = userAttributeService.queryUserAttributeByIds(ids, fiscalCalenderService.getCurrentFiscalCalender());
                UserAttribute opprPartnerUserAttribute = userAttributeMap.get(pipeline.getOpprPartnerId());
                if(EmptyUtils.isNotEmpty(opprPartnerUserAttribute)){
                    pipeline.setOpprPartnerName(opprPartnerUserAttribute.getUserName());
                }

                UserAttribute engManagerAttribute = userAttributeMap.get(pipeline.getEngManagerId());
                if(EmptyUtils.isNotEmpty(engManagerAttribute)){
                    pipeline.setEngManagerName(engManagerAttribute.getUserName());
                }

                UserAttribute pursuitLeaderAttribute = userAttributeMap.get(pipeline.getPursuitLeaderId());
                if(EmptyUtils.isNotEmpty(pursuitLeaderAttribute)){
                    pipeline.setPursuitLeaderName(pursuitLeaderAttribute.getUserName());
                }

                pipeline.setProductName(productService.queryNameByIdFromThreadLocal(pipeline.getProductId(),EmptyUtils.isNotEmpty(pipeline.getWonFy())?pipeline.getWonFy():pipeline.getPursuitFy() ));
            }
        }

        return ResponseDTO.success(rSearchDTO);
    }

    @Override
    public ResponseDTO<Long> queryPageForEicOwnEffortCount(PipelineQueryPageDTO dto) {
        Pipeline entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        entity.setOpprPartnerId(EyUserContextHolder.get().getAuthUserId());
        entity.setConfirmStatusNotNull("1");
        //查询"Processed", "Amendment Submitted"
        entity.setEffortsApprove(PipelineConstants.EFFORTS_APPROVE_PENDING_APPROVAL);
        List<Pipeline> pipelines = service.queryByPara(entity);
        Long count = pipelines.stream().count();
        return ResponseDTO.success(count);
    }

    /**
     * Count Pipeline from http
     */
    @Override
    public ResponseDTO<Long> queryCount(PipelineQueryPageDTO dto) {
        try {
            Pipeline p = ConvertUtils.convertDTO2Entity(dto, entityClass);
            if (EmptyUtils.isNotEmpty(dto.getPursuitFy())) {
                EyCommonContextHolder.put(TargetAuthConstants.KEY_YEAR, dto.getPursuitFy());
            }
            List<String> pipelineIds = new ArrayList<>();
            if (EmptyUtils.isNotEmpty(dto.getLocalServiceCodeList())) {
                UserAttribute userAttributes = new UserAttribute();
                userAttributes.setFiscalYear(String.valueOf(dto.getPursuitFy()));
                userAttributes.setSsl3s(dto.getLocalServiceCodeList());
                List<UserAttribute> userAttributeList = userAttributeService.queryByPara(userAttributes);
                List<String> userIds = userAttributeList.stream().map(UserAttribute::getUserId).collect(Collectors.toList());
                if (EmptyUtils.isNotEmpty(userIds)) {
                    //根据salesDeliveryCredit查询pipeline
                    SalesDelivery salesDelivery = new SalesDelivery();
                    // salesDelivery.setFiscalYear(String.valueOf(dto.getPursuitFy()));
                    salesDelivery.setSalesDeliveryCreditList(userIds);
                    List<SalesDelivery> salesDeliveries = salesDeliveryService.queryByPara(salesDelivery);
                    pipelineIds = new ArrayList<>(salesDeliveries.stream().map(SalesDelivery::getPipelineId).collect(Collectors.toSet()));
                }
            } else {
                SalesDelivery salesDelivery = new SalesDelivery();
                //salesDelivery.setFiscalYear(String.valueOf(dto.getPursuitFy()));
                salesDelivery.setSalesDeliveryCredit(EyUserContextHolder.get().getAuthUserId());
                List<SalesDelivery> salesDeliveries = salesDeliveryService.queryByPara(salesDelivery);
                pipelineIds = new ArrayList<>(salesDeliveries.stream().map(SalesDelivery::getPipelineId).collect(Collectors.toSet()));
            }
            //处理条件tpc
            if (EmptyUtils.isNotEmpty(dto.getTpcList()) && dto.getTpcList().size() == 1) {
                //查询SalesDeliveryService type为2的数据
                SalesDelivery salesDelivery = new SalesDelivery();
                salesDelivery.setSalesDeliveryCreditType(3);
                List<SalesDelivery> list = salesDeliveryService.queryByPara(salesDelivery);
                List<String> ids = new ArrayList<>();
                if (EmptyUtils.isNotEmpty(list)) {
                    ids = list.stream().filter(s -> EmptyUtils.isNotEmpty(s.getPipelineId())).map(SalesDelivery::getPipelineId).toList();
                }
                if (dto.getTpcList().get(0).equals(BooleanConstants.BOOLEAN_1)) {
                    if(EmptyUtils.isEmpty(ids)|| ids.isEmpty()||pipelineIds.isEmpty()){
                        pipelineIds.add("0");//如果没有数据，查询不到数据
                    }else {
                        //取交集
                        pipelineIds.retainAll(ids);
                    }
                } else {
                    if (EmptyUtils.isNotEmpty(ids) && !ids.isEmpty()) {
                        p.setPipelineIdNotInList(ids);
                    }

                }
            }
            if (EmptyUtils.isNotEmpty(pipelineIds)) {
                p.setPipelineIdList(pipelineIds);
            }

            List<Pipeline> pipelines = getService().queryByPara(p);
            if(EmptyUtils.isNotEmpty(pipelines)){
                return ResponseDTO.success(Long.valueOf(pipelines.size()));
            }else {
                return ResponseDTO.success(0L);

            }
        }catch (Exception e){
            logger.error("queryCount error",e);
        }
        return ResponseDTO.success();

    }

}
