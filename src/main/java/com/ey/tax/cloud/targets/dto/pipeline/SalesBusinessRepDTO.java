package com.ey.tax.cloud.targets.dto.pipeline;

import com.ey.cn.tax.framework.dto.BaseRepDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2025-04-07 14:45:16
 * 
 */
@Data
@Schema(description = "查询返回实体")
public class SalesBusinessRepDTO extends BaseRepDTO {
    /**
     * pipelineid COLUMN:pipeline_id
     */
    @Schema(description = "pipelineid")
    private String pipelineId;

    /**
     * 销售交付人员id COLUMN:sales_delivery_credit
     */
    @Schema(description = "销售交付人员id")
    private String salesDeliveryCredit;

    /**
     * 销售交付人员类型（p1、m2、tpc3） COLUMN:sales_delivery_credit_type
     */
    @Schema(description = "销售交付人员类型（p1、m2、tpc3）")
    private Integer salesDeliveryCreditType;

    /**
     * COLUMN:sales_delivery_credit_ratio
     */
    @Schema(description = "null")
    private Short salesDeliveryCreditRatio;

    /**
     * ner COLUMN:ner
     */
    @Schema(description = "ner")
    private BigDecimal ner;

    /**
     * 财年 COLUMN:fiscal_year
     */
    @Schema(description = "财年")
    private String fiscalYear;

    /**
     * COLUMN:business_id
     */
    @Schema(description = "null")
    private String businessId;

    /**
     * 按照金额而非比例分费 COLUMN:use_amount
     */
    @Schema(description = "按照金额而非比例分费")
    private Integer useAmount;
}