package com.ey.tax.cloud.targets.dto.transfer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 公共独立字典表合并获取接口请求DTO
 */
@Data
@Schema(description = "公共独立字典表合并获取接口请求参数")
public class DictMergeRequestDTO {

    /**
     * 字典数据状态
     */
    @Schema(description = "字典数据状态", defaultValue = "active", allowableValues = {"all", "active", "inactive"})
    private String status = "active";

    /**
     * 分类编码列表
     */
    @Schema(description = "分类编码列表", required = true)
    private List<String> categoryCodes;

}
