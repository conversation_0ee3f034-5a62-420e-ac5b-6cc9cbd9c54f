package com.ey.tax.cloud.targets.dto.user;

import com.ey.cn.tax.framework.validator.QueryByIdGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-01-09 10:02:34
 * 
 */
@Data
@Schema(description = "根据id查询实体")
public class UserAttributeQueryByIdDTO {
    
    /**
     * 数据id
     */
    @Schema(description = "数据id")
    @NotBlank(message = "{UserAttributeDTO.id.NotBlank}", groups = {QueryByIdGroup.class})
    private String id;
}