package com.ey.tax.cloud.targets.service.pipeline.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ey.cn.tax.framework.context.EyCommonContextHolder;
import com.ey.cn.tax.framework.context.EyUserContextHolder;
import com.ey.cn.tax.framework.entity.ICustomBaseEntity;
import com.ey.cn.tax.framework.entity.Search;
import com.ey.cn.tax.framework.mybatisplus.core.mapper.IEyBaseMapper;
import com.ey.cn.tax.framework.mybatisplus.core.query.EyQueryWrapper;
import com.ey.cn.tax.framework.mybatisplus.core.toolkit.EyWrappers;
import com.ey.cn.tax.framework.mybatisplus.extension.service.AbstractService;
import com.ey.cn.tax.framework.mybatisplus.utils.PageUtils;
import com.ey.cn.tax.framework.query.Direction;
import com.ey.cn.tax.framework.query.StringSort;
import com.ey.cn.tax.framework.utils.EmptyUtils;
import com.ey.cn.tax.framework.utils.GroovyUtils;
import com.ey.tax.cloud.targets.constant.CommonConstants;
import com.ey.tax.cloud.targets.constant.PipelineConstants;
import com.ey.tax.cloud.targets.constant.TargetAuthConstants;
import com.ey.tax.cloud.targets.constant.UserConstants;
import com.ey.tax.cloud.targets.dto.pipeline.PipelineApproveUpdateByIdDTO;
import com.ey.tax.cloud.targets.dto.pipeline.PipelineUpdateByIdDTO;
import com.ey.tax.cloud.targets.dto.pipeline.SalesDeliveryDTO;
import com.ey.tax.cloud.targets.entity.auth.AuthGroup;
import com.ey.tax.cloud.targets.entity.auth.AuthGroupDetail;
import com.ey.tax.cloud.targets.entity.business.MyBusiness;
import com.ey.tax.cloud.targets.entity.pipeline.*;
import com.ey.tax.cloud.targets.entity.user.UserAttribute;
import com.ey.tax.cloud.targets.mapper.pipeline.PipelineMapper;
import com.ey.tax.cloud.targets.service.auth.AuthGroupService;
import com.ey.tax.cloud.targets.service.business.MyBusinessService;
import com.ey.tax.cloud.targets.service.data.FiscalCalenderService;
import com.ey.tax.cloud.targets.service.forecast.ForecastConfigService;
import com.ey.tax.cloud.targets.service.pipeline.PipelineForTransferService;
import com.ey.tax.cloud.targets.service.pipeline.PipelineService;
import com.ey.tax.cloud.targets.service.pipeline.SalesDeliveryDataService;
import com.ey.tax.cloud.targets.service.pipeline.SalesDeliveryService;
import com.ey.tax.cloud.targets.service.user.UserAttributeService;
import com.ey.tax.cloud.targets.utils.CommonUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.ey.tax.cloud.targets.constant.PipelineConstants.SALES_DELIVERY_CREDIT_TYPE_COEEM;
import static com.ey.tax.cloud.targets.constant.PipelineConstants.SALES_DELIVERY_CREDIT_TYPE_COEEP;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2023-12-25 11:19:56
 *
 */
@Service
public class PipelineServiceImpl extends AbstractService<PipelineMapper, Pipeline> implements PipelineService {

    @Lazy
    @Autowired
    private ForecastConfigService forecastConfigService;

    @Lazy
    @Autowired
    private UserAttributeService userAttributeService;

    @Lazy
    @Autowired
    private SalesDeliveryService salesDeliveryService;

    @Lazy
    @Autowired
    private SalesDeliveryDataService salesDeliveryDataService;
    @Autowired
    private FiscalCalenderService fiscalCalenderService;

    @Autowired
    private AuthGroupService authGroupService;
    @Autowired
    private MyBusinessService myBusinessService;

    @Lazy
    @Autowired
    private PipelineForTransferService pipelineForTransferService;


    public Search<Pipeline> queryPageByParaForTransfer(Search<Pipeline> search) {
        return super.queryPageByPara(search);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Pipeline> queryByParaByAuth(Pipeline entity, List<String> selectColumns) { // Added selectColumns
        logger.debug("queryByParaByAuth {}:{} with selectColumns: {}", getEntitySimpleName(), entity, selectColumns);
        String usrId = EyUserContextHolder.get().getAuthUserId();
        List<List<AuthGroupDetail>> list  = null;
        //范围内权限List
        if(EyCommonContextHolder.get(TargetAuthConstants.KEY_TARGET_AUTH_RANGE_LIST).isPresent()){
            list = (List<List<AuthGroupDetail>>) EyCommonContextHolder.get(TargetAuthConstants.KEY_TARGET_AUTH_RANGE_LIST).get();
        }
        QueryWrapper<Pipeline> queryWrapperForUser = new QueryWrapper<>();
        //1=1
        queryWrapperForUser.apply("1=1");

        // Apply selectColumns if provided
        if (EmptyUtils.isNotEmpty(selectColumns)) {
            queryWrapperForUser.select(selectColumns.toArray(new String[0]));
        }

        //locations
        if(EmptyUtils.isNotEmpty(entity.getLocations())){
            //使用apply
            List<String> region = entity.getLocations();
            String ssl3sStr = region.stream().map(s -> "'" + s + "'").collect(Collectors.joining(","));
            queryWrapperForUser.apply("p.city IN (" + ssl3sStr + ")");        }
        //regions
        if(EmptyUtils.isNotEmpty(entity.getRegions())){
            List<String> region = entity.getRegions();
            String ssl3sStr = region.stream().map(s -> "'" + s + "'").collect(Collectors.joining(","));
            queryWrapperForUser.apply("p.region IN (" + ssl3sStr + ")");
        }
        //ssl3s
        if (EmptyUtils.isNotEmpty(entity.getSsl3s())) {
            List<String> ssl3s = entity.getSsl3s();
            String ssl3sStr = ssl3s.stream().map(s -> "'" + s + "'").collect(Collectors.joining(","));
            queryWrapperForUser.apply("p.ssl3 IN (" + ssl3sStr + ")");
        }
        //salesDeliveryCreditTypes
        if(EmptyUtils.isNotEmpty(entity.getSalesDeliveryCreditTypes())){
            List<Integer> ssl3s = entity.getSalesDeliveryCreditTypes();
            queryWrapperForUser.apply("p.sales_delivery_credit_type IN (" + ssl3s.stream().map(String::valueOf).collect(Collectors.joining(",")) +") ");
        }
        //isBuddyPartner
        if (EmptyUtils.isNotEmpty(entity.getIsBuddyPartner())) {
            queryWrapperForUser.apply("a.is_buddy_partner = "+entity.getIsBuddyPartner()+" ");
        }
        //buddyPartnerEmail
        if(EmptyUtils.isNotEmpty(entity.getBuddyPartnerEmail())){
            queryWrapperForUser.apply("a.buddy_partner_email = '"+entity.getBuddyPartnerEmail()+"' ");
        }
        //是否仅自己
        boolean isSelf = false;
        if(EyCommonContextHolder.get(TargetAuthConstants.KEY_TARGET_AUTH_IS_SLEF).isPresent()){
            isSelf = ((Boolean) EyCommonContextHolder.get(TargetAuthConstants.KEY_TARGET_AUTH_IS_SLEF).get());
        }
        QueryWrapper<Pipeline> queryWrapper = Wrappers.query(entity);
        queryWrapper = processSearchConditon(entity,queryWrapper);

        // Apply selectColumns to the main queryWrapper as well if it's the one used for selection
        // This depends on how selectListForTPCVK, selectListForTPCAuth, selectListForAuth handle ew.sqlSelect
        // If queryWrapperForUser is the primary driver for select, the above select on queryWrapperForUser is key.
        // If the main queryWrapper's select is used by the mapper methods, then apply it here too.
        if (EmptyUtils.isNotEmpty(selectColumns)) {
            queryWrapper.select(selectColumns.toArray(new String[0]));
        }

        QueryWrapper<Pipeline> order = Wrappers.query(entity);
        order.orderByDesc("create_time");
        List<Pipeline> entities = null;
        if(EmptyUtils.isNotEmpty(entity.getType())&&entity.getType()==1&&EmptyUtils.isNotEmpty(entity.getIsBoss())&&entity.getIsBoss()==1) {
            entities = baseMapper.selectListForTPCVK(null, queryWrapper, usrId, String.valueOf(EyCommonContextHolder.get(TargetAuthConstants.KEY_YEAR).get()), list, isSelf, order,queryWrapperForUser);
        }else if(EmptyUtils.isNotEmpty(entity.getType())&&entity.getType()==1){
            entities = baseMapper.selectListForTPCAuth(null, queryWrapper,usrId,String.valueOf(EyCommonContextHolder.get(TargetAuthConstants.KEY_YEAR).get()),list,isSelf,order,queryWrapperForUser);
        }else {
            entities = baseMapper.selectListForAuth(null,queryWrapper, usrId, String.valueOf(EyCommonContextHolder.get(TargetAuthConstants.KEY_YEAR).get()),list,isSelf,order,queryWrapperForUser);
        }
        processUserName(entities);
        return entities;
    }

    // Implementation for the new queryByPara overload
    // This is a general implementation. If the actual queryByPara used by querySumForReport
    // has more specific logic (like queryByParaByAuth), that specific method should be updated instead.
    @Override
    @Transactional(readOnly = true)
    public List<Pipeline> queryByPara(Pipeline entity, List<String> selectColumns) {
        logger.debug("queryByPara {}:{} with selectColumns: {}", getEntitySimpleName(), entity, selectColumns);
        EyQueryWrapper<Pipeline> queryWrapper = EyWrappers.query(entity);

        if (EmptyUtils.isNotEmpty(selectColumns)) {
            queryWrapper.select(selectColumns.toArray(new String[0]));
        }
        // Assuming a generic selectList method in the mapper or that the default selectList handles ew.sqlSelect
        return baseMapper.selectList(queryWrapper);
    }


    @Transactional(
            rollbackFor = {Throwable.class}
    )
    @Override
    public boolean update(Pipeline entity) {
        this.logger.debug("update {}:{}", this.getEntitySimpleName(), entity);
        if (entity == null) {
            return false;
        } else if (this.getId(entity) == null) {
            return false;
        } else {
            entity.setCompanyType(null);
            entity.setStockExchange(null);
            return ((IEyBaseMapper)this.baseMapper).updateById(entity) > 0;
        }
    }

    @Transactional(readOnly = true)
    public Search<Pipeline> queryPageForForecast(Search<Pipeline> search) {
        this.logger.debug("queryPageByPara {}:{}", this.getEntitySimpleName(), search);
        Pipeline queryParams = search.getQueryParams();
        EyQueryWrapper<Pipeline> queryWrapper = EyWrappers.query(queryParams);
        if (queryParams.getQueryExtension() == null) {
            queryWrapper.extension(search.getQueryExtension());
        }

        if (!queryWrapper.isOrdered()) {
            queryWrapper.orderByDesc("create_time");
        }

        if(EmptyUtils.isNotEmpty(queryParams.getConfirmStatuss())){
            queryWrapper.in("confirm_status", queryParams.getConfirmStatuss());
        }
        if(EmptyUtils.isNotEmpty(queryParams.getRecurrings())){
            queryWrapper.in("recurring", queryParams.getRecurrings());
        }
        if(EmptyUtils.isNotEmpty(queryParams.getWinRate())){
            queryWrapper.ge("win_rate", queryParams.getWinRate());
        }
        if(EmptyUtils.isNotEmpty(queryParams.getFirstConfirmDateForecast())){
            queryWrapper.ge("first_confirm_date", queryParams.getWinRate());
        }

        IPage<Pipeline> iPage = this.baseMapper.selectPage(GroovyUtils.convertTo(Page.class, search.getPage(), true, new String[0]), queryWrapper);
        List<Pipeline> records = iPage.getRecords();
        search.setEntities(records);
        //循环赋值
        for (Pipeline record : records) {
            record.setForcastPipelineId(record.getId());
        }
        forecastConfigService.queryByPipelineCodes(records, search.getQueryParams().getPursuitFy());
        if (CollectionUtils.isEmpty(records)) {
            search.getPage().setTotalCount(0L);
        } else {
            this.processUserName(records);
            search.getPage().setTotalCount(iPage.getTotal());
        }
        return search;
    }


    public Search<Pipeline> queryPageByPara(Search<Pipeline> search) {
        this.logger.debug("queryPageByPara {}:{}", this.getEntitySimpleName(), search);
        Pipeline queryParams = (Pipeline)search.getQueryParams();
        EyQueryWrapper<Pipeline> queryWrapper = new EyQueryWrapper<>(new Pipeline());
        if (queryParams.getQueryExtension() == null) {
            queryWrapper.extension(search.getQueryExtension());
        }
        queryWrapper = processPageSearchConditon(search.getQueryParams(),queryWrapper);

        if (!queryWrapper.isOrdered()) {
            queryWrapper.orderByDesc("create_time");
        }

        if(EmptyUtils.isNotEmpty(search.getQueryParams().getLocations())||EmptyUtils.isNotEmpty(search.getQueryParams().getRegions())||EmptyUtils.isNotEmpty(search.getQueryParams().getSsl3s())){
            List<String> pipelines = salesDeliveryDataService.selectPipelineIdBySalesDeliveryCreditList(String.valueOf(EyCommonContextHolder.get(TargetAuthConstants.KEY_YEAR).get()),search.getQueryParams().getSsl3s(),search.getQueryParams().getLocations(),search.getQueryParams().getRegions());
                if(CollectionUtils.isNotEmpty(pipelines)){
                    queryWrapper.in("id",pipelines);
                }else {
                    List<String> pipelineIds = new ArrayList<>();
                    pipelineIds.add("0");
                    queryWrapper.in("id",pipelineIds);
                }
        }

        IPage<Pipeline> iPage = ((IEyBaseMapper)this.baseMapper).selectPage((Page)GroovyUtils.convertTo(Page.class, search.getPage(), true, new String[0]), queryWrapper);
        List<Pipeline> records = iPage.getRecords();
        search.setEntities(records);
        //循环赋值
        /*for (Pipeline record : records) {
            record.setForcastPipelineId(record.getId());
        }
        forecastConfigService.queryByPipelineCodes(records,String.valueOf(EyCommonContextHolder.get(TargetAuthConstants.KEY_YEAR).get()));*/
        if (CollectionUtils.isEmpty(records)) {
            search.getPage().setTotalCount(0L);
        } else {
            this.processUserName((Collection)records);
            search.getPage().setTotalCount(iPage.getTotal());
        }

        return search;
    }

    public List<PipelineSales> queryPipelineSales(PipelineSales entity) {
        this.logger.debug("queryPipelineSales {}:{}", this.getEntitySimpleName(), entity);
        EyQueryWrapper<PipelineSales> queryWrapper = EyWrappers.query(entity);
        //__t.is_del = 0
        queryWrapper.eq("__t.is_del",0);
        //searchFy = wonFy or pursuitFy
        if(EmptyUtils.isNotEmpty(entity.getSearchFy())){
            queryWrapper.and(i->i.eq("pursuit_Fy",entity.getSearchFy()).or().eq("won_Fy",entity.getSearchFy()));

        }
        //u.is_del = 0
queryWrapper.eq("u.is_del",0);
        List<PipelineSales> entities = this.baseMapper.selectListNew(queryWrapper,entity.getSearchFy());
        return entities;
    }

    public IPage<PipelineSales> queryPipelineSales(PipelineSales entity, long current, long size) {
        this.logger.debug("queryPipelineSales {}:{}, page:{}, size:{}", this.getEntitySimpleName(), entity, current, size);

        // 创建分页对象
        Page<PipelineSales> page = new Page<>(current, size);

        // 构建查询条件
        EyQueryWrapper<PipelineSales> queryWrapper = EyWrappers.query(entity);
        //__t.is_del = 0
        queryWrapper.eq("__t.is_del", 0);
        //searchFy = wonFy or pursuitFy
        if(EmptyUtils.isNotEmpty(entity.getSearchFy())){
            queryWrapper.and(i->i.eq("pursuit_Fy", entity.getSearchFy()).or().eq("won_Fy", entity.getSearchFy()));
        }
        //u.is_del = 0
        queryWrapper.eq("u.is_del", 0);

        // 调用分页查询方法（需要创建对应的XML方法）
        IPage<PipelineSales> pageResult = this.baseMapper.selectPageNew(page, queryWrapper, entity.getSearchFy());

        return pageResult;
    }



    public List<PipelineSales> queryPipelineBusiness(PipelineSales entity) {
        this.logger.debug("queryPipelineSales {}:{}", this.getEntitySimpleName(), entity);
        EyQueryWrapper<PipelineSales> queryWrapper = EyWrappers.query(entity);
        //__t.is_del = 0
        queryWrapper.eq("__t.is_del",0);
        //searchFy = wonFy or pursuitFy
        if(EmptyUtils.isNotEmpty(entity.getSearchFy())){
            queryWrapper.and(i->i.eq("busyear",entity.getSearchFy()));
        }
        List<PipelineSales> entities = this.baseMapper.selectListBusiness(queryWrapper);
        return entities;
    }


    public List<PipelineSales> queryPipelineEngCode(PipelineSales entity) {
        this.logger.debug("queryPipelineSales {}:{}", this.getEntitySimpleName(), entity);
        EyQueryWrapper<PipelineSales> queryWrapper = EyWrappers.query(entity);
        //__t.is_del = 0
        queryWrapper.eq("__t.is_del",0);
        //searchFy = wonFy or pursuitFy
        if(EmptyUtils.isNotEmpty(entity.getSearchFy())){
            queryWrapper.and(i->i.eq("busyear",entity.getSearchFy()));
        }
        List<PipelineSales> entities = this.baseMapper.selectListEngCode(queryWrapper);
        return entities;
    }

    public IPage<PipelineSales> queryPipelineEngCodePage(PipelineSales entity, long current, long size) {
        this.logger.debug("queryPipelineEngCode {}:{}, page:{}, size:{}", this.getEntitySimpleName(), entity, current, size);

        // 创建分页对象
        Page<PipelineSales> page = new Page<>(current, size);

        // 构建查询条件
        EyQueryWrapper<PipelineSales> queryWrapper = EyWrappers.query(entity);
        //__t.is_del = 0
        queryWrapper.eq("__t.is_del", 0);
        //searchFy = wonFy or pursuitFy
        if(EmptyUtils.isNotEmpty(entity.getSearchFy())){
            queryWrapper.and(i->i.eq("busyear", entity.getSearchFy()));
        }

        // 调用分页查询方法
        IPage<PipelineSales> pageResult = this.baseMapper.selectPageEngCode(page, queryWrapper);

        return pageResult;
    }


    public Search<Pipeline> queryPageByParaFromMainAndApprove(Search<Pipeline> search) {
        this.logger.debug("queryPageByPara {}:{}", this.getEntitySimpleName(), search);
        Pipeline queryParams = (Pipeline)search.getQueryParams();
        EyQueryWrapper<Pipeline> queryWrapper = EyWrappers.query(search.getQueryParams());
        queryWrapper = processPageSearchConditonApprove(search.getQueryParams(),queryWrapper);
        //排序再加一个QueryWrapper
        EyQueryWrapper<Pipeline> order = new EyQueryWrapper<>();
        if(EmptyUtils.isNotEmpty(search.getQueryExtension())&&EmptyUtils.isNotEmpty(search.getQueryExtension().getSorts())){
            search.getQueryExtension().getSorts().forEach(orders->{
                if(orders.getProperty().equals("engManagerName")){
                    orders.setProperty("eng_manager_id");
                }
                else if(orders.getProperty().equals("pursuitLeaderName")){
                    orders.setProperty("pursuit_leader_id");
                }
                else if(orders.getProperty().equals("opprPartnerName")){
                    orders.setProperty("oppr_partner_id");
                }else {
                    orders.setProperty(CommonUtils.toSnakeCase(orders.getProperty()));
                }

            });
            order.extension(search.getQueryExtension());
        }else {
            order.orderByDesc("create_time");
        }

        IPage<Pipeline> iPage = baseMapper.selectPageFromMainAndApprove(PageUtils.convertPageToIPage(search.getPage()), queryWrapper, EyUserContextHolder.get().getAuthUserId(), String.valueOf(EyCommonContextHolder.get(TargetAuthConstants.KEY_YEAR).get()), null, false, order);
        List<Pipeline> records = iPage.getRecords();
        search.setEntities(records);
        //循环赋值
        for (Pipeline record : records) {
            record.setForcastPipelineId(record.getId());
            //如果是审批中或者退回状态，通过审批表pipelinecode查询
            if(EmptyUtils.isNotEmpty(record.getConfirmStatus())){
                if(record.getConfirmStatus().equals(PipelineConstants.APPROVE_STATUS_AMENDMENT_SUBMITTED)||record.getConfirmStatus().equals(PipelineConstants.APPROVE_STATUS_RETURNED)){
                    Pipeline pipeline = new Pipeline();
                    pipeline.setPipelineCode(record.getPipelineCode());
                    List<Pipeline> pipelines = baseMapper.selectList(Wrappers.query(pipeline));
                    if(CollectionUtils.isNotEmpty(pipelines)){
                        record.setForcastPipelineId(pipelines.get(0).getId());
                    }
                }
            }
        }
        forecastConfigService.queryByPipelineCodes(records,String.valueOf(EyCommonContextHolder.get(TargetAuthConstants.KEY_YEAR).get()));
        if (CollectionUtils.isEmpty(records)) {
            search.getPage().setTotalCount(0L);
        } else {
            this.processUserName((Collection)records);
            search.getPage().setTotalCount(iPage.getTotal());
        }

        return search;
    }


    @Transactional(
            readOnly = true
    )
    public List<Pipeline> queryByPara(Pipeline entity) {
        this.logger.debug("queryByPara {}:{}", this.getEntitySimpleName(), entity);
        EyQueryWrapper<Pipeline> queryWrapper = EyWrappers.query(entity);
        queryWrapper = processSearchConditon(entity,queryWrapper);

        if(EmptyUtils.isNotEmpty(entity.getLocations())||EmptyUtils.isNotEmpty(entity.getRegions())||EmptyUtils.isNotEmpty(entity.getSsl3s())){
                List<String> pipelines = salesDeliveryDataService.selectPipelineIdBySalesDeliveryCreditList(String.valueOf(EyCommonContextHolder.get(TargetAuthConstants.KEY_YEAR).get()),entity.getSsl3s(),entity.getLocations(),entity.getRegions());
                if(CollectionUtils.isNotEmpty(pipelines)){
                    queryWrapper.in("id",pipelines);
                } else {
                List<String> pipelineIds = new ArrayList<>();
                pipelineIds.add("0");
                queryWrapper.in("id",pipelineIds);
            }
        }

        if (!queryWrapper.isOrdered()) {
            queryWrapper.orderByDesc("create_time");
        }
        List<Pipeline> entities = ((IEyBaseMapper)this.baseMapper).selectList(queryWrapper);
        this.processUserName((Collection)entities);
        return entities;
    }
    @Transactional(
            readOnly = true
    )
    public List<Pipeline> queryByParaFromMainAndApprove(Pipeline entity,List<String> selectColumns) {
        this.logger.debug("queryByPara {}:{}", this.getEntitySimpleName(), entity);
        EyQueryWrapper<Pipeline> queryWrapper = EyWrappers.query(entity);
        if (selectColumns != null && !selectColumns.isEmpty()) {
            queryWrapper.select(selectColumns.toArray(new String[0]));
        }

        queryWrapper = processPageSearchConditonApprove(entity,queryWrapper);
        List<Pipeline> entities = baseMapper.selectListFromMainAndApprove(null,queryWrapper,EyUserContextHolder.get().getAuthUserId(),String.valueOf(EyCommonContextHolder.get(TargetAuthConstants.KEY_YEAR).get()),null,false,null);
        this.processUserName((Collection)entities);
        return entities;
    }
    @Override
    public Search<Pipeline> queryPageByParaForAuth(Search<Pipeline> search) {
        logger.debug("queryPageByPara {}:{}", getEntitySimpleName(), search);
        String usrId = EyUserContextHolder.get().getAuthUserId();
        List<List<AuthGroupDetail>> list  = null;
        //范围内权限List
        if(EyCommonContextHolder.get(TargetAuthConstants.KEY_TARGET_AUTH_RANGE_LIST).isPresent()){
            list = (List<List<AuthGroupDetail>>) EyCommonContextHolder.get(TargetAuthConstants.KEY_TARGET_AUTH_RANGE_LIST).get();
        }
        //是否仅自己
        boolean isSelf = false;
        if(EyCommonContextHolder.get(TargetAuthConstants.KEY_TARGET_AUTH_IS_SLEF).isPresent()){
            isSelf = ((Boolean) EyCommonContextHolder.get(TargetAuthConstants.KEY_TARGET_AUTH_IS_SLEF).get());
        }
        QueryWrapper<Pipeline> queryWrapper = Wrappers.query(search.getQueryParams());
        queryWrapper = processSearchConditon(search.getQueryParams(),queryWrapper);
        QueryWrapper<Pipeline> queryWrapperForUser = new QueryWrapper<>();
        //1=1
        queryWrapperForUser.apply("1=1");



        //locations
        if(EmptyUtils.isNotEmpty(search.getQueryParams().getLocations())){
            //使用apply
            List<String> region = search.getQueryParams().getLocations();
            String ssl3sStr = region.stream().map(s -> "'" + s + "'").collect(Collectors.joining(","));
            queryWrapperForUser.apply("p.city IN (" + ssl3sStr + ")");        }
        //regions
        if(EmptyUtils.isNotEmpty(search.getQueryParams().getRegions())){
            List<String> region = search.getQueryParams().getRegions();
            String ssl3sStr = region.stream().map(s -> "'" + s + "'").collect(Collectors.joining(","));
            queryWrapperForUser.apply("p.region IN (" + ssl3sStr + ")");
        }
        //ssl3s
        if (EmptyUtils.isNotEmpty(search.getQueryParams().getSsl3s())) {
            List<String> ssl3s = search.getQueryParams().getSsl3s();
            String ssl3sStr = ssl3s.stream().map(s -> "'" + s + "'").collect(Collectors.joining(","));
            queryWrapperForUser.apply("p.ssl3 IN (" + ssl3sStr + ")");
        }
        //salesDeliveryCreditTypes
        if(EmptyUtils.isNotEmpty(search.getQueryParams().getSalesDeliveryCreditTypes())){
            List<Integer> ssl3s = search.getQueryParams().getSalesDeliveryCreditTypes();
            queryWrapperForUser.apply("p.sales_delivery_credit_type IN (" + ssl3s.stream().map(String::valueOf).collect(Collectors.joining(",")) +") ");
        }
        //isBuddyPartner
        if (EmptyUtils.isNotEmpty(search.getQueryParams().getIsBuddyPartner())) {
            queryWrapperForUser.apply("a.user_tag = '"+ UserConstants.USER_TAG_TPC_ADVISOR +"' ");
        }
        //buddyPartnerEmail
        if(EmptyUtils.isNotEmpty(search.getQueryParams().getBuddyPartnerEmail())){
            queryWrapperForUser.apply("a.buddy_partner_email = '"+search.getQueryParams().getBuddyPartnerEmail()+"' ");
        }
        //searchType
        if(EmptyUtils.isNotEmpty(search.getQueryParams().getSearchType())){
            //targetMemoOutboundCoe
            if(search.getQueryParams().getSearchType().equals("targetMemoOutboundCoe")){
              //sales_delivery_credit_type in 4 5
                 queryWrapperForUser.apply("p.sales_delivery_credit_type IN (4,5)");
                queryWrapper.apply("u.sales_delivery_credit_type IN (4,5)");
            }else if(search.getQueryParams().getSearchType().equals("targetMemoTTT")){
                //sales_delivery_credit_type in 6 7
                queryWrapperForUser.apply("p.sales_delivery_credit_type IN (6,7)");
                queryWrapper.apply("u.sales_delivery_credit_type IN (6,7)");
            }
        }
        //排序再加一个QueryWrapper
        EyQueryWrapper<Pipeline> order = new EyQueryWrapper<>();
        if(EmptyUtils.isNotEmpty(search.getQueryExtension())&&EmptyUtils.isNotEmpty(search.getQueryExtension().getSorts())){
            search.getQueryExtension().getSorts().forEach(orders->{
                if(orders.getProperty().equals("engManagerName")){
                    orders.setProperty("eng_manager_id");
                }
                else if(orders.getProperty().equals("pursuitLeaderName")){
                    orders.setProperty("pursuit_leader_id");
                }
                else if(orders.getProperty().equals("opprPartnerName")){
                    orders.setProperty("oppr_partner_id");
                }else {
                    orders.setProperty(CommonUtils.toSnakeCase(orders.getProperty()));
                }

            });
            order.extension(search.getQueryExtension());
        }else {
            order.orderByDesc("create_time");
        }


        IPage<Pipeline> iPage = null;
        if(EmptyUtils.isNotEmpty(search.getQueryParams().getSearchType())){
            logger.info("searchType:{}",search.getQueryParams().getSearchType());
            logger.info("authList" + JSON.toJSON(list));
        }else if(EmptyUtils.isNotEmpty(search.getQueryParams().getType())&&search.getQueryParams().getType()==1&&EmptyUtils.isNotEmpty(search.getQueryParams().getIsBoss())&&search.getQueryParams().getIsBoss()==1) {
            iPage = baseMapper.selectPageForTPCVK(PageUtils.convertPageToIPage(search.getPage()), queryWrapper, usrId, String.valueOf(EyCommonContextHolder.get(TargetAuthConstants.KEY_YEAR).get()), list, isSelf, order,queryWrapperForUser);
        }else if(EmptyUtils.isNotEmpty(search.getQueryParams().getType())&&search.getQueryParams().getType()==1){
            iPage = baseMapper.selectPageForTPCAuth(PageUtils.convertPageToIPage(search.getPage()), queryWrapper,usrId,String.valueOf(EyCommonContextHolder.get(TargetAuthConstants.KEY_YEAR).get()),list,isSelf,order,queryWrapperForUser);
        }else {
           iPage = baseMapper.selectPageForAuth(PageUtils.convertPageToIPage(search.getPage()), queryWrapper,usrId,String.valueOf(EyCommonContextHolder.get(TargetAuthConstants.KEY_YEAR).get()),list,isSelf,order,queryWrapperForUser);
        }
        List<Pipeline> records = iPage.getRecords();
        search.setEntities(records);
        if (CollectionUtils.isEmpty(records)) {
            search.getPage().setTotalCount(0L);
        } else {
            processUserName(records);
            search.getPage().setTotalCount(iPage.getTotal());
        }
        return search;
    }
    public Search<PipelineForMemo> queryPageByParaForAuthMemo(Search<PipelineForMemo> search) {
        logger.debug("queryPageByPara {}:{}", getEntitySimpleName(), search);
        String usrId = EyUserContextHolder.get().getAuthUserId();
        List<List<AuthGroupDetail>> list  = null;
        //范围内权限List
        if(EyCommonContextHolder.get(TargetAuthConstants.KEY_TARGET_AUTH_RANGE_LIST).isPresent()){
            list = (List<List<AuthGroupDetail>>) EyCommonContextHolder.get(TargetAuthConstants.KEY_TARGET_AUTH_RANGE_LIST).get();
        }
        //是否仅自己
        boolean isSelf = false;
        if(EyCommonContextHolder.get(TargetAuthConstants.KEY_TARGET_AUTH_IS_SLEF).isPresent()){
            isSelf = ((Boolean) EyCommonContextHolder.get(TargetAuthConstants.KEY_TARGET_AUTH_IS_SLEF).get());
        }
        QueryWrapper<PipelineForMemo> queryWrapper = Wrappers.query(search.getQueryParams());
        queryWrapper = processSearchConditonMemo(search.getQueryParams(),queryWrapper);
        QueryWrapper<PipelineForMemo> queryWrapperForUser = new QueryWrapper<>();
        //1=1
        queryWrapperForUser.apply("1=1");
        //locations
        if(EmptyUtils.isNotEmpty(search.getQueryParams().getLocations())){
            //使用apply
            List<String> region = search.getQueryParams().getLocations();
            String ssl3sStr = region.stream().map(s -> "'" + s + "'").collect(Collectors.joining(","));
            queryWrapperForUser.apply("p.city IN (" + ssl3sStr + ")");        }
        //regions
        if(EmptyUtils.isNotEmpty(search.getQueryParams().getRegions())){
            List<String> region = search.getQueryParams().getRegions();
            String ssl3sStr = region.stream().map(s -> "'" + s + "'").collect(Collectors.joining(","));
            queryWrapperForUser.apply("p.region IN (" + ssl3sStr + ")");
        }
        //ssl3s
        if (EmptyUtils.isNotEmpty(search.getQueryParams().getSsl3s())) {
            List<String> ssl3s = search.getQueryParams().getSsl3s();
            String ssl3sStr = ssl3s.stream().map(s -> "'" + s + "'").collect(Collectors.joining(","));
            queryWrapperForUser.apply("p.ssl3 IN (" + ssl3sStr + ")");
        }
        //salesDeliveryCreditTypes
        if(EmptyUtils.isNotEmpty(search.getQueryParams().getSalesDeliveryCreditTypes())){
            List<Integer> ssl3s = search.getQueryParams().getSalesDeliveryCreditTypes();
            queryWrapperForUser.apply("p.sales_delivery_credit_type IN (" + ssl3s.stream().map(String::valueOf).collect(Collectors.joining(",")) +") ");
        }
        //isBuddyPartner
        if (EmptyUtils.isNotEmpty(search.getQueryParams().getIsBuddyPartner())) {
            queryWrapperForUser.apply("a.user_tag = '"+ UserConstants.USER_TAG_TPC_ADVISOR +"' ");
        }
        //buddyPartnerEmail
        if(EmptyUtils.isNotEmpty(search.getQueryParams().getBuddyPartnerEmail())){
            queryWrapperForUser.apply("a.buddy_partner_email = '"+search.getQueryParams().getBuddyPartnerEmail()+"' ");
        }

        //ConfirmStatus is not null
        queryWrapperForUser.isNotNull("p.confirm_status");
        //searchType
        if(EmptyUtils.isNotEmpty(search.getQueryParams().getSearchType())){
            //targetMemoOutboundCoe
            if(search.getQueryParams().getSearchType().equals("targetMemoOutboundCoe")){
              //sales_delivery_credit_type in 4 5
                 queryWrapperForUser.apply("p.sales_delivery_credit_type IN (4,5)");
                queryWrapper.apply("u.sales_delivery_credit_type IN (4,5)");
            }else if(search.getQueryParams().getSearchType().equals("targetMemoTTT")){
                //sales_delivery_credit_type in 6 7
                queryWrapperForUser.apply("p.sales_delivery_credit_type IN (6,7)");
                queryWrapper.apply("u.sales_delivery_credit_type IN (6,7)");
            }
            //targetMemoTpc
            else if(search.getQueryParams().getSearchType().equals("targetMemoTpc")){
                //sales_delivery_credit_type in 1 2
                queryWrapperForUser.apply("p.sales_delivery_credit_type IN (3,8)");
                queryWrapper.apply("u.sales_delivery_credit_type IN (3,8)");
            }
            //targetMemoProduct
            else if(search.getQueryParams().getSearchType().equalsIgnoreCase("memoProduct")){
                //sales_delivery_credit_type in 9
                queryWrapperForUser.apply("p.sales_delivery_credit_type IN (9,10)");
                queryWrapper.apply("u.sales_delivery_credit_type IN (9,10)");
            }
        }
        //排序再加一个QueryWrapper
        EyQueryWrapper<Pipeline> order = new EyQueryWrapper<>();
        if(EmptyUtils.isNotEmpty(search.getQueryExtension())&&EmptyUtils.isNotEmpty(search.getQueryExtension().getSorts())){
            search.getQueryExtension().getSorts().forEach(orders->{
                if(orders.getProperty().equals("engManagerName")){
                    orders.setProperty("eng_manager_id");
                }
                else if(orders.getProperty().equals("pursuitLeaderName")){
                    orders.setProperty("pursuit_leader_id");
                }
                else if(orders.getProperty().equals("opprPartnerName")){
                    orders.setProperty("oppr_partner_id");
                }else {
                    orders.setProperty(CommonUtils.toSnakeCase(orders.getProperty()));
                }

            });
            order.extension(search.getQueryExtension());
        }else {
            order.orderByDesc("create_time");
        }


        IPage<PipelineForMemo> iPage = null;
        if(EmptyUtils.isNotEmpty(search.getQueryParams().getSearchType())){

            iPage = baseMapper.selectPageForAuthMemo(PageUtils.convertPageToIPage(search.getPage()), queryWrapper, usrId, String.valueOf(EyCommonContextHolder.get(TargetAuthConstants.KEY_YEAR).get()), list, isSelf, order,queryWrapperForUser);
        }
        List<PipelineForMemo> records = iPage.getRecords();
        search.setEntities(records);
        if (CollectionUtils.isEmpty(records)) {
            search.getPage().setTotalCount(0L);
        } else {
            search.getPage().setTotalCount(iPage.getTotal());
        }
        return search;
    }




    /**
     * 拼接查询条件
     */
    private EyQueryWrapper<Pipeline> processSearchConditon(Pipeline pipeline, EyQueryWrapper<Pipeline> queryWrapper) {
//所有查询条件都要在这拼接
        queryWrapper.eq("is_del",0);
        List<String> outcomeList = pipeline.getStatusList();
        if(CollectionUtils.isNotEmpty(outcomeList)){
            queryWrapper.in("status",outcomeList);
        }
        if(EmptyUtils.isNotEmpty(pipeline.getPursuitFy())){
            queryWrapper.eq("pursuit_Fy",pipeline.getPursuitFy());
        }
        //wonFy 如果wonFy为空则使用pursuitFy
        if(EmptyUtils.isNotEmpty(pipeline.getWonFy())){
            queryWrapper.eq("won_Fy",pipeline.getWonFy());
        }
        //clientName
        if(EmptyUtils.isNotEmpty(pipeline.getClientName())){
            //ilike
            queryWrapper.apply("client_name ilike '%"+pipeline.getClientName()+"%'");
        }
        if(EmptyUtils.isNotEmpty(pipeline.getSearchFy())){
            queryWrapper.and(i->i.eq("pursuit_Fy",pipeline.getSearchFy()).or().eq("won_Fy",pipeline.getSearchFy()));
        }
        //confirmStatus
        if(EmptyUtils.isNotEmpty(pipeline.getConfirmStatus())){
            queryWrapper.eq("confirm_status",pipeline.getConfirmStatus());
        }
        //locationList
        List<String> locationList = pipeline.getLocationList();
        if(CollectionUtils.isNotEmpty(locationList)){
            queryWrapper.in("location",locationList);
        }
        // locationCodeList;
        /*List<String> locationCodeList = pipeline.getLocalServiceCodeList();
        if(CollectionUtils.isNotEmpty(locationCodeList)){
            queryWrapper.in("local_service_code",locationCodeList);
        }*/
        // productList;
        List<String> productList = pipeline.getProductList();
        if(CollectionUtils.isNotEmpty(productList)){
            queryWrapper.in("product_id",productList);
        }
        //statusList
        List<String> statusList = pipeline.getStatusList();
        if(CollectionUtils.isNotEmpty(statusList)){
            queryWrapper.in("status",statusList);
        }
        //subSectorList
        List<String> subSectorList = pipeline.getSubSectorList();
        if(CollectionUtils.isNotEmpty(subSectorList)){
            queryWrapper.in("industry_sub_Sector",subSectorList);
        }
        //companyTypeList
        List<String> companyTypeList = pipeline.getCompanyTypeList();
        if(CollectionUtils.isNotEmpty(companyTypeList)){
            String joinedElements = String.join(", ", companyTypeList.stream().map(s -> "'" + s + "'").collect(Collectors.toList()));
            queryWrapper.apply(" jsonb_exists_any(company_type, ARRAY[" + joinedElements + "])");
        }
        //headOfficeLocationList
        List<String> headOfficeLocationList = pipeline.getHeadOfficeLocationList();
        if(CollectionUtils.isNotEmpty(headOfficeLocationList)){
            queryWrapper.in("head_office_location",headOfficeLocationList);
        }
        //confirmStatusList
        List<String> confirmStatusList = pipeline.getConfirmStatusList();
        if(CollectionUtils.isNotEmpty(confirmStatusList)){
            queryWrapper.in("confirm_status",confirmStatusList);
        }
        //approveStatusList
        List<String> approveStatusList = pipeline.getApproveStatusList();
        if(CollectionUtils.isNotEmpty(approveStatusList)){
            queryWrapper.in("efforts_approve",approveStatusList);
        }
        if(EmptyUtils.isNotEmpty(pipeline.getEffortsApprove())){
            queryWrapper.eq("efforts_approve",pipeline.getEffortsApprove());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getStatus())){
            queryWrapper.eq("status", pipeline.getStatus());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getPipelineCode())){
            queryWrapper.eq("pipeline_code",pipeline.getPipelineCode());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getPipelineName())){
            queryWrapper.like("pipeline_name",pipeline.getPipelineName());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getIndustrySector())){
            queryWrapper.eq("industry_sector",pipeline.getIndustrySector());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getIndustrySubSector())){
            queryWrapper.eq("industry_sub_sector",pipeline.getIndustrySubSector());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getAmountMin())&&pipeline.getAmountMin().compareTo(BigDecimal.ZERO)>0){
            queryWrapper.ge("amount",pipeline.getAmountMin());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getAmountMax())&&pipeline.getAmountMax().compareTo(BigDecimal.ZERO)>0){
            queryWrapper.le("amount",pipeline.getAmountMax());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getPipelineIdList())&&EmptyUtils.isEmpty(pipeline.getSearchUserId())){
            queryWrapper.in("id",pipeline.getPipelineIdList());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getPipelineIdList())&&EmptyUtils.isNotEmpty(pipeline.getSearchUserId())){
            //id在pipelineIdList中且 mangerId等于searchUserId或者partnerId等于searchUserId
            queryWrapper.in("id", pipeline.getPipelineIdList())
                    .and(i -> i.eq("eng_manager_id", pipeline.getSearchUserId())
                            .or().eq("oppr_partner_id", pipeline.getSearchUserId()));
        }
        if(EmptyUtils.isNotEmpty(pipeline.getSearchUserId())&&EmptyUtils.isEmpty(pipeline.getPipelineIdList())){
            //mangerId等于searchUserId或者partnerId等于searchUserId
            queryWrapper.and(i->i.eq("eng_manager_id",pipeline.getSearchUserId()).or().eq("oppr_partner_id",pipeline.getSearchUserId()));
        }
        if(EmptyUtils.isNotEmpty(pipeline.getPipelineIdNotInList())&&pipeline.getPipelineIdNotInList().size()>0){
            queryWrapper.notIn("id",pipeline.getPipelineIdNotInList());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getOpprPartnerId())){
            queryWrapper.eq("oppr_partner_id",pipeline.getOpprPartnerId());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getEngManagerId())){
            queryWrapper.eq("eng_manager_id",pipeline.getEngManagerId());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getPursuitLeaderId())){
            queryWrapper.eq("pursuit_leader_id",pipeline.getPursuitLeaderId());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getProductId())){
            queryWrapper.eq("product_id",pipeline.getProductId());
        }
        //pipelineCodeList
        if(EmptyUtils.isNotEmpty(pipeline.getPipelineCodeList())){
            queryWrapper.in("pipeline_code",pipeline.getPipelineCodeList());
        }
        //account_id
        if(EmptyUtils.isNotEmpty(pipeline.getAccountId())){
            queryWrapper.eq("account_id",pipeline.getAccountId());
        }
        //amount
        if(EmptyUtils.isNotEmpty(pipeline.getAmount())){
            queryWrapper.eq("amount",pipeline.getAmount());
        }
        return queryWrapper;
    }
    private EyQueryWrapper<Pipeline> processPageSearchConditon(Pipeline pipeline, EyQueryWrapper<Pipeline> queryWrapper) {
//所有查询条件都要在这拼接
        queryWrapper.eq("is_del",0);
        List<String> outcomeList = pipeline.getStatusList();
        if(CollectionUtils.isNotEmpty(outcomeList)){
            queryWrapper.in("status",outcomeList);
        }
        if(EmptyUtils.isNotEmpty(pipeline.getPursuitFy())){
            queryWrapper.eq("pursuit_Fy",pipeline.getPursuitFy());
        }
        //wonFy 如果wonFy为空则使用pursuitFy
        if(EmptyUtils.isNotEmpty(pipeline.getWonFy())){
            queryWrapper.eq("won_Fy",pipeline.getWonFy());
        }
        //searchFy (pursuitFy or wonFy)

        if(EmptyUtils.isNotEmpty(pipeline.getSearchFy())){
            queryWrapper.and(i->i.eq("pursuit_Fy",pipeline.getSearchFy()).or().eq("won_Fy",pipeline.getSearchFy()));
        }
        //clientName
        if(EmptyUtils.isNotEmpty(pipeline.getClientName())){
            //ilike
            queryWrapper.apply("client_name ilike '%"+pipeline.getClientName()+"%'");
        }

        //confirmStatus
        if(EmptyUtils.isNotEmpty(pipeline.getConfirmStatus())){
            queryWrapper.eq("confirm_status",pipeline.getConfirmStatus());
        }
        //locationList
        List<String> locationList = pipeline.getLocationList();
        if(CollectionUtils.isNotEmpty(locationList)){
            queryWrapper.in("location",locationList);
        }
        // locationCodeList;
       /* List<String> locationCodeList = pipeline.getLocalServiceCodeList();
        if(CollectionUtils.isNotEmpty(locationCodeList)){
            queryWrapper.in("local_service_code",locationCodeList);
        }*/
        // productList;
        List<String> productList = pipeline.getProductList();
        if(CollectionUtils.isNotEmpty(productList)){
            queryWrapper.in("product_id",productList);
        }
        //subSectorList
        List<String> subSectorList = pipeline.getSubSectorList();
        if(CollectionUtils.isNotEmpty(subSectorList)){
            queryWrapper.in("industry_sub_Sector",subSectorList);
        }
        //companyTypeList
        List<String> companyTypeList = pipeline.getCompanyTypeList();
        if(CollectionUtils.isNotEmpty(companyTypeList)){
            String joinedElements = String.join(", ", companyTypeList.stream().map(s -> "'" + s + "'").collect(Collectors.toList()));
            queryWrapper.apply(" jsonb_exists_any(company_type, ARRAY[" + joinedElements + "])");
        }
        //headOfficeLocationList
        List<String> headOfficeLocationList = pipeline.getHeadOfficeLocationList();
        if(CollectionUtils.isNotEmpty(headOfficeLocationList)){
            queryWrapper.in("head_office_location",headOfficeLocationList);
        }
        //confirmStatusList
        List<String> confirmStatusList = pipeline.getConfirmStatusList();
        if(CollectionUtils.isNotEmpty(confirmStatusList)){
            queryWrapper.in("confirm_status",confirmStatusList);
        }
        //approveStatusList
        List<String> approveStatusList = pipeline.getApproveStatusList();
        if(CollectionUtils.isNotEmpty(approveStatusList)){
            queryWrapper.in("efforts_approve",approveStatusList);
        }
        if(EmptyUtils.isNotEmpty(pipeline.getEffortsApprove())){
            queryWrapper.eq("efforts_approve",pipeline.getEffortsApprove());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getStatus())){
            queryWrapper.eq("status", pipeline.getStatus());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getPipelineCode())){
            queryWrapper.ilike("pipeline_code",pipeline.getPipelineCode());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getPipelineName())){
            queryWrapper.ilike("pipeline_name",pipeline.getPipelineName());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getIndustrySector())){
            queryWrapper.eq("industry_sector",pipeline.getIndustrySector());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getIndustrySubSector())){
            queryWrapper.eq("industry_sub_sector",pipeline.getIndustrySubSector());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getAmountMin())&&pipeline.getAmountMin().compareTo(BigDecimal.ZERO)>0){
            queryWrapper.ge("amount",pipeline.getAmountMin());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getAmountMax())&&pipeline.getAmountMax().compareTo(BigDecimal.ZERO)>0){
            queryWrapper.le("amount",pipeline.getAmountMax());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getPipelineIdList())&&EmptyUtils.isEmpty(pipeline.getSearchUserId())){
            queryWrapper.in("id",pipeline.getPipelineIdList());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getPipelineIdList())&&EmptyUtils.isNotEmpty(pipeline.getSearchUserId())){
            //id在pipelineIdList中且 mangerId等于searchUserId或者partnerId等于searchUserId
            queryWrapper.in("id", pipeline.getPipelineIdList())
                    .and(i -> i.eq("eng_manager_id", pipeline.getSearchUserId())
                            .or().eq("oppr_partner_id", pipeline.getSearchUserId()));
        }
        if(EmptyUtils.isNotEmpty(pipeline.getSearchUserId())&&EmptyUtils.isEmpty(pipeline.getPipelineIdList())){
            //mangerId等于searchUserId或者partnerId等于searchUserId
            queryWrapper.and(i->i.eq("eng_manager_id",pipeline.getSearchUserId()).or().eq("oppr_partner_id",pipeline.getSearchUserId()));
        }
        if(EmptyUtils.isNotEmpty(pipeline.getPipelineIdNotInList())&&pipeline.getPipelineIdNotInList().size()>0){
            queryWrapper.notIn("id",pipeline.getPipelineIdNotInList());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getOpprPartnerId())){
            queryWrapper.eq("oppr_partner_id",pipeline.getOpprPartnerId());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getEngManagerId())){
            queryWrapper.eq("eng_manager_id",pipeline.getEngManagerId());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getPursuitLeaderId())){
            queryWrapper.eq("pursuit_leader_id",pipeline.getPursuitLeaderId());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getProductId())){
            queryWrapper.eq("product_id",pipeline.getProductId());
        }
        //isTpc
        if(EmptyUtils.isNotEmpty(pipeline.getIsTpc())){
            if(pipeline.getIsTpc().equals("1")) {
                queryWrapper.eq("u.sales_delivery_credit_type", 3);
            }
            if(pipeline.getIsTpc().equals("0")) {
                queryWrapper.ne("u.sales_delivery_credit_type", 3);
            }
        }
        //ssl2
        if(EmptyUtils.isNotEmpty(pipeline.getSsl2())){
            queryWrapper.eq("a.ssl2", pipeline.getSsl2());
        }
        //pipelineUserId
        if(EmptyUtils.isNotEmpty(pipeline.getPipelineUserId())){
            queryWrapper.eq("u.sales_delivery_credit", pipeline.getPipelineUserId());
        }
        //ssl3
        if(EmptyUtils.isNotEmpty(pipeline.getSsl3())){
            queryWrapper.eq("a.ssl3", pipeline.getSsl3());
        }
        //confirmStatusNotNull
        if(EmptyUtils.isNotEmpty(pipeline.getConfirmStatusNotNull())){
            queryWrapper.isNotNull("confirm_status");
        }
        return queryWrapper;
    }
    private EyQueryWrapper<Pipeline> processPageSearchConditonApprove(Pipeline pipeline, EyQueryWrapper<Pipeline> queryWrapper) {
//所有查询条件都要在这拼接
        queryWrapper.eq("__t.is_del",0);
        queryWrapper.eq("a.is_del",0);
        queryWrapper.eq("u.is_del",0);
        List<String> outcomeList = pipeline.getStatusList();
        if(CollectionUtils.isNotEmpty(outcomeList)){
            queryWrapper.in("__t.status",outcomeList);
        }
        if(EmptyUtils.isNotEmpty(pipeline.getPursuitFy())){
            //purfuitFy or wonfy
            queryWrapper.eq("__t.pursuit_Fy",pipeline.getPursuitFy());
        }

        //clientName
        if(EmptyUtils.isNotEmpty(pipeline.getClientName())){
            //ilike
            queryWrapper.apply("client_name ilike '%"+pipeline.getClientName()+"%'");
        }


        if(EmptyUtils.isNotEmpty(pipeline.getSearchYear())){
            //and (purfuitFy or wonfy)
            queryWrapper.apply("(__t.pursuit_Fy = '"+pipeline.getSearchYear()+"' or __t.won_Fy = '"+pipeline.getSearchYear()+"')");
        }
        //wonFy 如果wonFy为空则使用pursuitFy
        if(EmptyUtils.isNotEmpty(pipeline.getWonFy())){
            queryWrapper.eq("won_Fy",pipeline.getWonFy());
        }
        //englishName 模糊查询
        //confirmStatus
        if(EmptyUtils.isNotEmpty(pipeline.getConfirmStatus())){
            queryWrapper.eq("__t.confirm_status",pipeline.getConfirmStatus());
        }
        //locationList
        List<String> locationList = pipeline.getLocationList();
        if(CollectionUtils.isNotEmpty(locationList)){
            queryWrapper.in("location",locationList);
        }
        //outboundRelated
        if(EmptyUtils.isNotEmpty(pipeline.getOutboundRelated())){
            queryWrapper.eq("outbound_related",pipeline.getOutboundRelated());
        }
        // locationCodeList;
       /* List<String> locationCodeList = pipeline.getLocalServiceCodeList();
        if(CollectionUtils.isNotEmpty(locationCodeList)){
            queryWrapper.in("local_service_code",locationCodeList);
        }*/
        // productList;
        List<String> productList = pipeline.getProductList();
        if(CollectionUtils.isNotEmpty(productList)){
            queryWrapper.in("product_id",productList);
        }
        //subSectorList
        List<String> subSectorList = pipeline.getSubSectorList();
        if(CollectionUtils.isNotEmpty(subSectorList)){
            queryWrapper.in("industry_sub_Sector",subSectorList);
        }
        //companyTypeList
        List<String> companyTypeList = pipeline.getCompanyTypeList();
        if(CollectionUtils.isNotEmpty(companyTypeList)){
            String joinedElements = String.join(", ", companyTypeList.stream().map(s -> "'" + s + "'").collect(Collectors.toList()));
            queryWrapper.apply(" jsonb_exists_any(company_type, ARRAY[" + joinedElements + "])");
        }
        //headOfficeLocationList
        List<String> headOfficeLocationList = pipeline.getHeadOfficeLocationList();
        if(CollectionUtils.isNotEmpty(headOfficeLocationList)){
            queryWrapper.in("head_office_location",headOfficeLocationList);
        }
        //confirmStatusList
        List<String> confirmStatusList = pipeline.getConfirmStatusList();
        if(CollectionUtils.isNotEmpty(confirmStatusList)){
            queryWrapper.in("__t.confirm_status",confirmStatusList);
        }
        //approveStatusList
        List<String> approveStatusList = pipeline.getApproveStatusList();
        if(CollectionUtils.isNotEmpty(approveStatusList)){
            queryWrapper.in("efforts_approve",approveStatusList);
        }
        if(EmptyUtils.isNotEmpty(pipeline.getEffortsApprove())){
            queryWrapper.eq("efforts_approve",pipeline.getEffortsApprove());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getStatus())){
            queryWrapper.eq("status", pipeline.getStatus());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getPipelineCode())){
            queryWrapper.ilike("pipeline_code",pipeline.getPipelineCode());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getPipelineName())){
            queryWrapper.ilike("pipeline_name",pipeline.getPipelineName());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getIndustrySector())){
            queryWrapper.eq("industry_sector",pipeline.getIndustrySector());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getIndustrySubSector())){
            queryWrapper.eq("industry_sub_sector",pipeline.getIndustrySubSector());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getAmountMin())&&pipeline.getAmountMin().compareTo(BigDecimal.ZERO)>0){
            queryWrapper.ge("amount",pipeline.getAmountMin());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getAmountMax())&&pipeline.getAmountMax().compareTo(BigDecimal.ZERO)>0){
            queryWrapper.le("amount",pipeline.getAmountMax());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getPipelineIdList())&&EmptyUtils.isEmpty(pipeline.getSearchUserId())){
            queryWrapper.in("__t.id",pipeline.getPipelineIdList());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getPipelineIdList())&&EmptyUtils.isNotEmpty(pipeline.getSearchUserId())){
            //id在pipelineIdList中且 mangerId等于searchUserId或者partnerId等于searchUserId
            queryWrapper.in("id", pipeline.getPipelineIdList())
                    .and(i -> i.eq("eng_manager_id", pipeline.getSearchUserId())
                            .or().eq("oppr_partner_id", pipeline.getSearchUserId()));
        }
        if(EmptyUtils.isNotEmpty(pipeline.getSearchUserId())&&EmptyUtils.isEmpty(pipeline.getPipelineIdList())){
            //mangerId等于searchUserId或者partnerId等于searchUserId
            queryWrapper.and(i->i.eq("eng_manager_id",pipeline.getSearchUserId()).or().eq("oppr_partner_id",pipeline.getSearchUserId()));
        }
        if(EmptyUtils.isNotEmpty(pipeline.getPipelineIdNotInList())&&pipeline.getPipelineIdNotInList().size()>0){
            queryWrapper.notIn("__t.id",pipeline.getPipelineIdNotInList());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getOpprPartnerId())){
            queryWrapper.eq("oppr_partner_id",pipeline.getOpprPartnerId());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getEngManagerId())){
            queryWrapper.eq("eng_manager_id",pipeline.getEngManagerId());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getPursuitLeaderId())){
            queryWrapper.eq("pursuit_leader_id",pipeline.getPursuitLeaderId());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getProductId())){
            queryWrapper.eq("product_id",pipeline.getProductId());
        }
        //isTpc
        if(EmptyUtils.isNotEmpty(pipeline.getIsTpc())){
            if(pipeline.getIsTpc().equals("0")) {
               //__t.id in (SELECT pipeline_id
                //                                                                                                   FROM tax_cloud_target.target_pipeline_sales_delivery
                //                                                                                                   GROUP BY pipeline_id
                //                                                                                                   HAVING SUM(CASE WHEN sales_delivery_credit_type = 3 THEN 1 ELSE 0 END) = 0
                //                                                                                                   union all
                //                                                                                                   SELECT pipeline_id
                //                                                                                                   FROM tax_cloud_target.target_pipeline_sales_delivery_approve
                //                                                                                                   GROUP BY pipeline_id
                //                                                                                                   HAVING SUM(CASE WHEN sales_delivery_credit_type = 3 THEN 1 ELSE 0 END) =0)
                queryWrapper.apply(" __t.id in (SELECT pipeline_id FROM tax_cloud_target.target_pipeline_sales_delivery GROUP BY pipeline_id HAVING SUM(CASE WHEN sales_delivery_credit_type = 3 THEN 1 ELSE 0 END) = 0 union all SELECT pipeline_id FROM tax_cloud_target.target_pipeline_sales_delivery_approve GROUP BY pipeline_id HAVING SUM(CASE WHEN sales_delivery_credit_type = 3 THEN 1 ELSE 0 END) =0)");
            }
            if(pipeline.getIsTpc().equals("1")) {
              //__t.id in (SELECT pipeline_id
                //                                                                                                   FROM tax_cloud_target.target_pipeline_sales_delivery
                //                                                                                                   GROUP BY pipeline_id
                //                                                                                                   HAVING SUM(CASE WHEN sales_delivery_credit_type = 3 THEN 1 ELSE 0 END) > 0
                //                                                                                                   union all
                //                                                                                                   SELECT pipeline_id
                //                                                                                                   FROM tax_cloud_target.target_pipeline_sales_delivery_approve
                //                                                                                                   GROUP BY pipeline_id
                //                                                                                                   HAVING SUM(CASE WHEN sales_delivery_credit_type = 3 THEN 1 ELSE 0 END) >0)
                queryWrapper.apply(" __t.id in (SELECT pipeline_id FROM tax_cloud_target.target_pipeline_sales_delivery GROUP BY pipeline_id HAVING SUM(CASE WHEN sales_delivery_credit_type = 3 THEN 1 ELSE 0 END) > 0 union all SELECT pipeline_id FROM tax_cloud_target.target_pipeline_sales_delivery_approve GROUP BY pipeline_id HAVING SUM(CASE WHEN sales_delivery_credit_type = 3 THEN 1 ELSE 0 END) >0)");
            }
        }
        //ssl2
        if(EmptyUtils.isNotEmpty(pipeline.getSsl2())){
            queryWrapper.eq("a.ssl2", pipeline.getSsl2());
        }
        //pipelineUserId
        if(EmptyUtils.isNotEmpty(pipeline.getPipelineUserId())){
            queryWrapper.eq("u.sales_delivery_credit", pipeline.getPipelineUserId());
        }
        //ssl3
        if(EmptyUtils.isNotEmpty(pipeline.getSsl3())){
            queryWrapper.eq("a.ssl3", pipeline.getSsl3());
        }
        //isBlank
        if(EmptyUtils.isNotEmpty(pipeline.getIsBlank())){
            if(pipeline.getIsBlank()==1){
                queryWrapper.eq("is_blank", pipeline.getIsBlank());
            }else {
                //is_blank !=1 or is_blank is null
                queryWrapper.apply(" (is_blank !=1 or is_blank is null )");

            }
        }
        //isSame
        if(EmptyUtils.isNotEmpty(pipeline.getIsSame())){
            if(pipeline.getIsSame()==1){
                queryWrapper.eq("is_same", pipeline.getIsSame());
            }else {
                //is_same !=1 or is_same is null
                queryWrapper.apply(" (is_same !=1 or is_same is null) ");
            }
        }
        return queryWrapper;
    }

private QueryWrapper<Pipeline> processSearchConditon(Pipeline pipeline, QueryWrapper<Pipeline> queryWrapper) {
//所有查询条件都要在这拼接
        queryWrapper.eq("is_del",0);
        List<String> outcomeList = pipeline.getStatusList();
        if(CollectionUtils.isNotEmpty(outcomeList)){
            queryWrapper.in("status",outcomeList);
        }
        if(EmptyUtils.isNotEmpty(pipeline.getPursuitFy())){
            queryWrapper.eq("pursuit_Fy",pipeline.getPursuitFy());
        }
        //wonFy 如果wonFy为空则使用pursuitFy
        if(EmptyUtils.isNotEmpty(pipeline.getWonFy())){
            queryWrapper.eq("won_Fy",pipeline.getWonFy());
        }
        //englishName 模糊查询

        //confirmStatus
        if(EmptyUtils.isNotEmpty(pipeline.getConfirmStatus())){
            queryWrapper.eq("confirm_status",pipeline.getConfirmStatus());
        }
        //locationList
        List<String> locationList = pipeline.getLocationList();
        if(CollectionUtils.isNotEmpty(locationList)){
            queryWrapper.in("location",locationList);
        }
        // locationCodeList;
       /* List<String> locationCodeList = pipeline.getLocalServiceCodeList();
        if(CollectionUtils.isNotEmpty(locationCodeList)){
            queryWrapper.in("local_service_code",locationCodeList);
        }*/
        // productList;
        List<String> productList = pipeline.getProductList();
        if(CollectionUtils.isNotEmpty(productList)){
            queryWrapper.in("product_id",productList);
        }
        //subSectorList
        List<String> subSectorList = pipeline.getSubSectorList();
        if(CollectionUtils.isNotEmpty(subSectorList)){
            queryWrapper.in("industry_sub_Sector",subSectorList);
        }
        //companyTypeList
        List<String> companyTypeList = pipeline.getCompanyTypeList();
        if(CollectionUtils.isNotEmpty(companyTypeList)){
            String joinedElements = String.join(", ", companyTypeList.stream().map(s -> "'" + s + "'").collect(Collectors.toList()));
            queryWrapper.apply(" jsonb_exists_any(company_type, ARRAY[" + joinedElements + "])");
        }
        //headOfficeLocationList
        List<String> headOfficeLocationList = pipeline.getHeadOfficeLocationList();
        if(CollectionUtils.isNotEmpty(headOfficeLocationList)){
            queryWrapper.in("head_office_location",headOfficeLocationList);
        }
        //confirmStatusList
        List<String> confirmStatusList = pipeline.getConfirmStatusList();
        if(CollectionUtils.isNotEmpty(confirmStatusList)){
            queryWrapper.in("confirm_status",confirmStatusList);
        }
        //approveStatusList
        List<String> approveStatusList = pipeline.getApproveStatusList();
        if(CollectionUtils.isNotEmpty(approveStatusList)){
            queryWrapper.in("efforts_approve",approveStatusList);
        }
        if(EmptyUtils.isNotEmpty(pipeline.getEffortsApprove())){
            queryWrapper.eq("efforts_approve",pipeline.getEffortsApprove());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getStatus())){
            queryWrapper.eq("status", pipeline.getStatus());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getPipelineCode())){
            queryWrapper.eq("pipeline_code",pipeline.getPipelineCode());
        } if(EmptyUtils.isNotEmpty(pipeline.getPipelineCode())){
            queryWrapper.eq("pipeline_code",pipeline.getPipelineCode());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getPipelineName())){
            queryWrapper.apply("pipeline_name ilike '%"+pipeline.getPipelineName()+"%'");
        }
        if(EmptyUtils.isNotEmpty(pipeline.getIndustrySector())){
            queryWrapper.eq("industry_sector",pipeline.getIndustrySector());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getIndustrySubSector())){
            queryWrapper.eq("industry_sub_sector",pipeline.getIndustrySubSector());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getAmountMin())&&pipeline.getAmountMin().compareTo(BigDecimal.ZERO)>0){
            queryWrapper.ge("amount",pipeline.getAmountMin());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getAmountMax())&&pipeline.getAmountMax().compareTo(BigDecimal.ZERO)>0){
            queryWrapper.le("amount",pipeline.getAmountMax());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getPipelineIdList())&&EmptyUtils.isEmpty(pipeline.getSearchUserId())){
            queryWrapper.in("id",pipeline.getPipelineIdList());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getPipelineIdList())&&EmptyUtils.isNotEmpty(pipeline.getSearchUserId())){
            //id在pipelineIdList中且 mangerId等于searchUserId或者partnerId等于searchUserId
            queryWrapper.in("id", pipeline.getPipelineIdList())
                    .and(i -> i.eq("eng_manager_id", pipeline.getSearchUserId())
                            .or().eq("oppr_partner_id", pipeline.getSearchUserId()));
        }
        if(EmptyUtils.isNotEmpty(pipeline.getSearchUserId())&&EmptyUtils.isEmpty(pipeline.getPipelineIdList())){
            //mangerId等于searchUserId或者partnerId等于searchUserId
            queryWrapper.and(i->i.eq("eng_manager_id",pipeline.getSearchUserId()).or().eq("oppr_partner_id",pipeline.getSearchUserId()));
        }
        if(EmptyUtils.isNotEmpty(pipeline.getPipelineIdNotInList())&&pipeline.getPipelineIdNotInList().size()>0){
            queryWrapper.notIn("id",pipeline.getPipelineIdNotInList());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getOpprPartnerId())){
            queryWrapper.eq("oppr_partner_id",pipeline.getOpprPartnerId());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getEngManagerId())){
            queryWrapper.eq("eng_manager_id",pipeline.getEngManagerId());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getPursuitLeaderId())){
            queryWrapper.eq("pursuit_leader_id",pipeline.getPursuitLeaderId());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getProductId())){
            queryWrapper.eq("product_id",pipeline.getProductId());
        }
        //clientName
        if(EmptyUtils.isNotEmpty(pipeline.getClientName())){
            //ilike
            queryWrapper.apply("client_name ilike '%"+pipeline.getClientName()+"%'");
        }
        //engCode
        if(EmptyUtils.isNotEmpty(pipeline.getEngCode())){
            queryWrapper.apply("eng_code ilike '%"+pipeline.getEngCode()+"%'");
        }
        return queryWrapper;
    }
private QueryWrapper<PipelineForMemo> processSearchConditonMemo(PipelineForMemo pipeline, QueryWrapper<PipelineForMemo> queryWrapper) {
//所有查询条件都要在这拼接
        queryWrapper.eq("is_del",0);
        //status = Won
        queryWrapper.eq("status","Won");
        //confirmStatus Processed
    queryWrapper.isNotNull("confirm_status");
        List<String> outcomeList = pipeline.getStatusList();
        if(CollectionUtils.isNotEmpty(outcomeList)){
            queryWrapper.in("status",outcomeList);
        }
/*        if(EmptyUtils.isNotEmpty(pipeline.getPursuitFy())){
            queryWrapper.eq("pursuit_Fy",pipeline.getPursuitFy());
        }
        //wonFy 如果wonFy为空则使用pursuitFy
        if(EmptyUtils.isNotEmpty(pipeline.getWonFy())){
            queryWrapper.eq("won_Fy",pipeline.getWonFy());
        }*/
        //englishName 模糊查询

        //confirmStatus
        if(EmptyUtils.isNotEmpty(pipeline.getConfirmStatus())){
            queryWrapper.eq("confirm_status",pipeline.getConfirmStatus());
        }
        //locationList
        List<String> locationList = pipeline.getLocationList();
        if(CollectionUtils.isNotEmpty(locationList)){
            queryWrapper.in("location",locationList);
        }
        // locationCodeList;
       /* List<String> locationCodeList = pipeline.getLocalServiceCodeList();
        if(CollectionUtils.isNotEmpty(locationCodeList)){
            queryWrapper.in("local_service_code",locationCodeList);
        }*/
        // productList;
        List<String> productList = pipeline.getProductList();
        if(CollectionUtils.isNotEmpty(productList)){
            queryWrapper.in("product_id",productList);
        }
        //subSectorList
        List<String> subSectorList = pipeline.getSubSectorList();
        if(CollectionUtils.isNotEmpty(subSectorList)){
            queryWrapper.in("industry_sub_Sector",subSectorList);
        }
        //companyTypeList
        List<String> companyTypeList = pipeline.getCompanyTypeList();
        if(CollectionUtils.isNotEmpty(companyTypeList)){
            String joinedElements = String.join(", ", companyTypeList.stream().map(s -> "'" + s + "'").collect(Collectors.toList()));
            queryWrapper.apply(" jsonb_exists_any(company_type, ARRAY[" + joinedElements + "])");
        }
        //headOfficeLocationList
        List<String> headOfficeLocationList = pipeline.getHeadOfficeLocationList();
        if(CollectionUtils.isNotEmpty(headOfficeLocationList)){
            queryWrapper.in("head_office_location",headOfficeLocationList);
        }
        //confirmStatusList
        List<String> confirmStatusList = pipeline.getConfirmStatusList();
        if(CollectionUtils.isNotEmpty(confirmStatusList)){
            queryWrapper.in("confirm_status",confirmStatusList);
        }
        //approveStatusList
        List<String> approveStatusList = pipeline.getApproveStatusList();
        if(CollectionUtils.isNotEmpty(approveStatusList)){
            queryWrapper.in("efforts_approve",approveStatusList);
        }
        if(EmptyUtils.isNotEmpty(pipeline.getEffortsApprove())){
            queryWrapper.eq("efforts_approve",pipeline.getEffortsApprove());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getStatus())){
            queryWrapper.eq("status", pipeline.getStatus());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getPipelineCode())){
            queryWrapper.eq("pipeline_code",pipeline.getPipelineCode());
        } if(EmptyUtils.isNotEmpty(pipeline.getPipelineCode())){
            queryWrapper.eq("pipeline_code",pipeline.getPipelineCode());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getPipelineName())){
            queryWrapper.apply("pipeline_name ilike '%"+pipeline.getPipelineName()+"%'");
        }
        if(EmptyUtils.isNotEmpty(pipeline.getIndustrySector())){
            queryWrapper.eq("industry_sector",pipeline.getIndustrySector());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getIndustrySubSector())){
            queryWrapper.eq("industry_sub_sector",pipeline.getIndustrySubSector());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getAmountMin())&&pipeline.getAmountMin().compareTo(BigDecimal.ZERO)>0){
            queryWrapper.ge("amount",pipeline.getAmountMin());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getAmountMax())&&pipeline.getAmountMax().compareTo(BigDecimal.ZERO)>0){
            queryWrapper.le("amount",pipeline.getAmountMax());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getPipelineIdList())&&EmptyUtils.isEmpty(pipeline.getSearchUserId())){
            queryWrapper.in("id",pipeline.getPipelineIdList());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getPipelineIdList())&&EmptyUtils.isNotEmpty(pipeline.getSearchUserId())){
            //id在pipelineIdList中且 mangerId等于searchUserId或者partnerId等于searchUserId
            queryWrapper.in("id", pipeline.getPipelineIdList())
                    .and(i -> i.eq("eng_manager_id", pipeline.getSearchUserId())
                            .or().eq("oppr_partner_id", pipeline.getSearchUserId()));
        }
        if(EmptyUtils.isNotEmpty(pipeline.getSearchUserId())&&EmptyUtils.isEmpty(pipeline.getPipelineIdList())){
            //mangerId等于searchUserId或者partnerId等于searchUserId
            queryWrapper.and(i->i.eq("eng_manager_id",pipeline.getSearchUserId()).or().eq("oppr_partner_id",pipeline.getSearchUserId()));
        }
        if(EmptyUtils.isNotEmpty(pipeline.getPipelineIdNotInList())&&pipeline.getPipelineIdNotInList().size()>0){
            queryWrapper.notIn("id",pipeline.getPipelineIdNotInList());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getOpprPartnerId())){
            queryWrapper.eq("oppr_partner_id",pipeline.getOpprPartnerId());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getEngManagerId())){
            queryWrapper.eq("eng_manager_id",pipeline.getEngManagerId());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getPursuitLeaderId())){
            queryWrapper.eq("pursuit_leader_id",pipeline.getPursuitLeaderId());
        }
        if(EmptyUtils.isNotEmpty(pipeline.getProductId())){
            queryWrapper.eq("product_id",pipeline.getProductId());
        }
        //clientName
        if(EmptyUtils.isNotEmpty(pipeline.getClientName())){
            //ilike
            queryWrapper.apply("client_name ilike '%"+pipeline.getClientName()+"%'");
        }
        //engCode
        if(EmptyUtils.isNotEmpty(pipeline.getEngCode())){
            queryWrapper.apply("eng_code ilike '%"+pipeline.getEngCode()+"%'");
        }
        return queryWrapper;
    }


    @Override
    public List<Pipeline> queryByEpOrEm(Pipeline entity) {
        QueryWrapper<Pipeline> queryWrapper = Wrappers.query();
        queryWrapper.eq("oppr_partner_id", entity.getOpprPartnerId());
        queryWrapper.or().eq("eng_manager_id", entity.getEngManagerId());
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<Pipeline> queryForForecast(Pipeline entity, List<StringSort> stringSortList) {
        Integer winRate = entity.getWinRate();
        entity.setWinRate(null);
        QueryWrapper<Pipeline> queryWrapper = Wrappers.query(entity);
        if(EmptyUtils.isNotEmpty(entity.getIds())){
            queryWrapper.in("id", entity.getIds());
        }
        if(EmptyUtils.isNotEmpty(entity.getConfirmStatuss())){
            queryWrapper.in("confirm_status", entity.getConfirmStatuss());
        }
        if(EmptyUtils.isNotEmpty(entity.getRecurrings())){
            queryWrapper.in("recurring", entity.getRecurrings());
        }
        if(EmptyUtils.isNotEmpty(winRate)){
            queryWrapper.ge("win_rate", winRate);
        }
        if(EmptyUtils.isNotEmpty(entity.getFirstConfirmDateForecast())){
            queryWrapper.ge("first_confirm_date", entity.getFirstConfirmDateForecast());
        }
        if(EmptyUtils.isNotEmpty(entity.getAnticipatedWinDateForecast())){
            queryWrapper.ge("anticipated_win_date", entity.getAnticipatedWinDateForecast());
        }
        if(EmptyUtils.isNotEmpty(stringSortList)){
            for(StringSort stringSort:stringSortList){
                String property = stringSort.getProperty();
                Direction direction = stringSort.getDirection();
                if("amount".equals(property)){
                    if(Direction.ASC.equals(direction)){
                        queryWrapper.orderByAsc("amount");
                    }else{
                        queryWrapper.orderByDesc("amount");
                    }
                }
            }
        }
        return baseMapper.selectList(queryWrapper);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void save(Collection<Pipeline> entities) {
        logger.debug("save {}:{}", getEntitySimpleName(), entities);

        if (CollectionUtils.isEmpty(entities)) {
            return;
        }
        for (List<Pipeline> partition : ListUtils.partition(entities.stream().toList(), 500)) {
            baseMapper.insertBatch(partition);
        }
    }



    @Override
    public List<String> checkUserInAuthRange(List<AuthGroup> authGroups,String fiscalYear) {
        List<List<AuthGroupDetail>> list = new ArrayList<>();
        for (AuthGroup authGroup : authGroups) {
            AuthGroupDetail authGroupDetail = new AuthGroupDetail();
            authGroupDetail.setGroupId(authGroup.getId());
            List<AuthGroupDetail> groupDetails = authGroupService.setDetailMap(authGroupDetail,fiscalYear);
            list.add(groupDetails);
        }
        return baseMapper.selectUsers(EyCommonContextHolder.get(TargetAuthConstants.KEY_YEAR).get().toString(),userAttributeService.queryCurrentUser().getUserId(), list);
    }

    @Override
    public void dealSame() {
        String currentFiscalCalender = fiscalCalenderService.getCurrentFiscalCalender();
        baseMapper.updateSame(currentFiscalCalender);
        List<PipelineForTransfer> pipelines =  baseMapper.selectSame(currentFiscalCalender);
               //过滤掉engCode不为空或者状态是Declined的数据
        pipelines = pipelines.stream().filter(pipeline -> !"Declined".equals(pipeline.getStatus())).collect(Collectors.toList());

        int sameIndex = 1;
        if(EmptyUtils.isNotEmpty(pipelines)){
            //处理相同的pipeline、查询每组相同的pipeline,判断firstConfirmDate最早的
            for (PipelineForTransfer pipeline : pipelines) {
                sameIndex++;
                List<PipelineForTransfer> samePipelines = pipelineForTransferService.queryByPara(pipeline);
                   //过滤掉engCode不为空或者状态是Declined的数据
                samePipelines = samePipelines.stream().filter(samePipeline -> !"Declined".equals(samePipeline.getStatus())).collect(Collectors.toList());
                //如果状态全部是Processed，则跳过
                if(EmptyUtils.isNotEmpty(samePipelines)){
                    boolean flag = samePipelines.stream().allMatch(samePipeline -> "Processed".equals(samePipeline.getConfirmStatus()));
                    if(flag){
                        continue;
                    }
                }
                if(EmptyUtils.isNotEmpty(samePipelines)){
                    //取当前财年

                    //currentFiscalCalender为 24、25这种数据，取其减一为年份，获取年份第一天
                   String currentFiscalCalenderYear ="20"+ String.valueOf(Integer.valueOf(currentFiscalCalender)-1);
                   //取currentFiscalCalenderYear年的第一天并则转为LocalDateTime
                    String firstDayOfYear = currentFiscalCalenderYear+"-01-01";
                    LocalDateTime firstDayOfYearLocalDateTime = LocalDate.parse(firstDayOfYear, DateTimeFormatter.ofPattern("yyyy-MM-dd")).atStartOfDay();
                    //取samePipelines的tigerCreateTime在firstDayOfYearLocalDateTime之后的数据，如果tigerCreateTime为空则取createTime
                    samePipelines = samePipelines.stream().filter(samePipeline -> {
                        if(EmptyUtils.isNotEmpty(samePipeline.getTigerCreateTime())){
                            return samePipeline.getTigerCreateTime().isAfter(firstDayOfYearLocalDateTime);
                        }else {
                            return samePipeline.getCreateTime().isAfter(firstDayOfYearLocalDateTime);
                        }
                    }).collect(Collectors.toList());
                    //如果状态全部是Processed，则跳过
                    if(EmptyUtils.isNotEmpty(samePipelines)){
                        boolean flag = samePipelines.stream().allMatch(samePipeline -> "Processed".equals(samePipeline.getConfirmStatus()));
                        if(flag){
                            continue;
                        }
                    }
                    if(EmptyUtils.isEmpty(samePipelines)||samePipelines.size()==1){
                        continue;
                    }


                    //如果samePipelines的engCode有且只有一个不为空的，则其他数据setIsSameKpi、setIsSame 它setIsSame
                    List<PipelineForTransfer> engCodeList = samePipelines.stream().filter(samePipeline -> EmptyUtils.isNotEmpty(samePipeline.getEngCode())).collect(Collectors.toList());
                    if(engCodeList.size()>=1){
                        for (PipelineForTransfer samePipeline : samePipelines) {
                            if(EmptyUtils.isNotEmpty(samePipeline.getEngCode())){
                                Pipeline samePipeline1 = new Pipeline();
                                samePipeline1.setId(samePipeline.getId());
                                samePipeline1.setIsSame(sameIndex);
                                baseMapper.updateById(samePipeline1);
                            }else {
                                //将其他的pipeline的isSame设置为1
                                Pipeline samePipeline1 = new Pipeline();
                                samePipeline1.setId(samePipeline.getId());
                                samePipeline1.setIsSameKpi(1);
                                samePipeline1.setIsSame(sameIndex);
                                baseMapper.updateById(samePipeline1);
                            }
                        }
                        continue;
                    }
                    if(EmptyUtils.isEmpty(engCodeList)) {
                        PipelineForTransfer minPipeline = samePipelines.stream()
                                .min(Comparator.comparing(PipelineForTransfer::getFirstConfirmDate, Comparator.nullsFirst(Comparator.naturalOrder()))
                                        .thenComparing(PipelineForTransfer::getTigerCreateTime, Comparator.nullsFirst(Comparator.naturalOrder()))
                                        .thenComparing(PipelineForTransfer::getCreateTime, Comparator.nullsFirst(Comparator.naturalOrder())))
                                .orElse(null);
                        for (PipelineForTransfer samePipeline : samePipelines) {
                            if (!samePipeline.getId().equals(minPipeline.getId())) {
                                //将其他的pipeline的isSame设置为1
                                Pipeline samePipeline1 = new Pipeline();
                                samePipeline1.setId(samePipeline.getId());
                                samePipeline1.setIsSameKpi(1);
                                samePipeline1.setIsSame(sameIndex);
                                baseMapper.updateById(samePipeline1);
                            } else {
                                Pipeline samePipeline1 = new Pipeline();
                                samePipeline1.setId(samePipeline.getId());
                                samePipeline1.setIsSame(sameIndex);
                                baseMapper.updateById(samePipeline1);
                            }
                        }
                    }

                }

            }
        }
    }

    @Override
    public List<Pipeline> queryRepeatPipeline(Pipeline entity) {
        QueryWrapper<Pipeline> queryWrapper = Wrappers.query();
        //查询accountId一致，amount或者pipelineName相同的数据
        queryWrapper.eq("account_id", entity.getAccountId());
        queryWrapper.and(i->i.eq("amount",entity.getAmount()).or().eq("pipeline_name",entity.getPipelineName()));
        //isDel=0
        queryWrapper.eq("is_del",0);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public String checkPipelineAmount(PipelineUpdateByIdDTO dto) {
        //根据pipeline查询Mybusiness的ner
        List<SalesDeliveryDTO> kpiList = dto.getKpiList();
        //取出useAmount是1的
        List<SalesDeliveryDTO> userAmountList = kpiList.stream().filter(salesDeliveryDTO -> EmptyUtils.isNotEmpty(salesDeliveryDTO.getUseAmount())&& salesDeliveryDTO.getUseAmount().equals(1)).collect(Collectors.toList());
        String fiscal = fiscalCalenderService.getCurrentFiscalCalender();
        MyBusiness myBusiness = new MyBusiness();
        myBusiness.setOpportunityId(dto.getPipelineCode());
        List<MyBusiness> myBusinesses = myBusinessService.queryAll(myBusiness);
        //如果有数据，按财年分组取最新财年的数据
        if(EmptyUtils.isNotEmpty(myBusinesses)){
            Map<String, List<MyBusiness>> map = myBusinesses.stream().collect(Collectors.groupingBy(MyBusiness::getFiscalYear));
                //取财年最大的
                myBusinesses = map.get(map.keySet().stream().max(Comparator.naturalOrder()).orElse(null));
        }
        //判断是否有type为345678的数据
        Boolean flags = false;
        flags = kpiList.stream().anyMatch(salesDeliveryDTO -> salesDeliveryDTO.getSalesDeliveryCreditType()==3||salesDeliveryDTO.getSalesDeliveryCreditType()==4||salesDeliveryDTO.getSalesDeliveryCreditType()==5||salesDeliveryDTO.getSalesDeliveryCreditType()==6
                ||salesDeliveryDTO.getSalesDeliveryCreditType()==7||salesDeliveryDTO.getSalesDeliveryCreditType()==8
        ||salesDeliveryDTO.getSalesDeliveryCreditType()==9||salesDeliveryDTO.getSalesDeliveryCreditType()==10);
        if(!flags)
        {
            return null;
        }
        if(EmptyUtils.isEmpty(myBusinesses)&&EmptyUtils.isNotEmpty(userAmountList)){
            return "The pipeline does not have NER，please Input the percentage";
        }
        //如果userAmountList为空跳过
        if(EmptyUtils.isEmpty(userAmountList)){
            //kpiList汇总type为 (3、4、6)(5\7\8) 的ratio,如何总计超过100，则提示The total ratio of Memo revenue EP cannot exceed 100%
            BigDecimal sumRatio = kpiList.stream().filter(salesDeliveryDTO -> salesDeliveryDTO.getSalesDeliveryCreditType()==3||salesDeliveryDTO.getSalesDeliveryCreditType()==4
                    ||salesDeliveryDTO.getSalesDeliveryCreditType()==6||salesDeliveryDTO.getSalesDeliveryCreditType()==9).map(SalesDeliveryDTO::getSalesDeliveryCreditRatio).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal sumRatio2 = kpiList.stream().filter(salesDeliveryDTO -> salesDeliveryDTO.getSalesDeliveryCreditType()==5
                    ||salesDeliveryDTO.getSalesDeliveryCreditType()==7||salesDeliveryDTO.getSalesDeliveryCreditType()==8||salesDeliveryDTO.getSalesDeliveryCreditType()==10).map(SalesDeliveryDTO::getSalesDeliveryCreditRatio).reduce(BigDecimal.ZERO, BigDecimal::add);
            if(sumRatio.compareTo(new BigDecimal(100))>0){
                return "The total ratio of Memo revenue EP cannot exceed 100%";
            }
            if(sumRatio2.compareTo(new BigDecimal(100))>0){
                return "The total ratio of Memo revenue EM cannot exceed 100%";
            }
            return null;
        }
        //合计ner
        BigDecimal sumNer = myBusinesses.stream().map(MyBusiness::getENer).reduce(BigDecimal.ZERO, BigDecimal::add);
       StringBuffer sb = new StringBuffer();
        sb.append("The total fixed amount of ");
        Boolean flag = false;
        //按照type分组，如果合计的amount大于ner则不通过
        Map<Integer, List<SalesDeliveryDTO>> typeMap = userAmountList.stream().collect(Collectors.groupingBy(SalesDeliveryDTO::getSalesDeliveryCreditType));
        for (Map.Entry<Integer, List<SalesDeliveryDTO>> entry : typeMap.entrySet()) {
            List<SalesDeliveryDTO> salesDeliveryDTOS = entry.getValue();
            BigDecimal sumAmount = salesDeliveryDTOS.stream().map(SalesDeliveryDTO::getSalesDeliveryCreditAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            if(sumAmount.compareTo(sumNer)>0){
                flag = true;
               if(entry.getKey()==3){
                   sb.append("Advisor EP ");
               }
               if(entry.getKey()==8){
                     sb.append("Advisor EM ");
               }
               if(entry.getKey()==SALES_DELIVERY_CREDIT_TYPE_COEEP){
                   sb.append("COE Credit EP ");
               }
if(entry.getKey()==SALES_DELIVERY_CREDIT_TYPE_COEEM){
                   sb.append("COE Credit EM ");
               }
// 9\10
                if(entry.getKey()==9){
                    sb.append("Product EP ");
                }
                if(entry.getKey()==10){
                    sb.append("Product EM ");
                }
            }
        }
        if(flag){
            sb.append("cannot exceed the current ETD NER.");
            return sb.toString();
        }
        //kpiList汇总type为 (3、4、6)(5\7\8) 的amount，如果useAmount是1则取SalesDeliveryCreditAmount，如果为0则取salesDeliveryCreditRatio*amount/100
        BigDecimal ep = BigDecimal.ZERO;
        BigDecimal em = BigDecimal.ZERO;
        for (SalesDeliveryDTO salesDeliveryDTO : kpiList) {
            if(salesDeliveryDTO.getSalesDeliveryCreditType()==3||salesDeliveryDTO.getSalesDeliveryCreditType()==4
                    ||salesDeliveryDTO.getSalesDeliveryCreditType()==6||salesDeliveryDTO.getSalesDeliveryCreditType()==9){
                if(EmptyUtils.isNotEmpty(salesDeliveryDTO.getUseAmount())&&salesDeliveryDTO.getUseAmount().equals(1)){
                    ep = ep.add(salesDeliveryDTO.getSalesDeliveryCreditAmount());
                }else {
                    ep = ep.add(salesDeliveryDTO.getSalesDeliveryCreditRatio().multiply(sumNer).divide(new BigDecimal(100)));
                }
            }
            if(salesDeliveryDTO.getSalesDeliveryCreditType()==5||salesDeliveryDTO.getSalesDeliveryCreditType()==7
                    ||salesDeliveryDTO.getSalesDeliveryCreditType()==8  ||salesDeliveryDTO.getSalesDeliveryCreditType()==10){
                if(EmptyUtils.isNotEmpty(salesDeliveryDTO.getUseAmount())&&salesDeliveryDTO.getUseAmount().equals(1)){
                    em = em.add(salesDeliveryDTO.getSalesDeliveryCreditAmount());
                }else {
                    em = em.add(salesDeliveryDTO.getSalesDeliveryCreditRatio().multiply(sumNer).divide(new BigDecimal(100)));
                }
            }
        }
        //比较ep、em和ner的大小 超出了则提示 The total amount of Memo revenue EP exceeds the current 100% ETD NER of this pipeline, please modify it
        if(ep.compareTo(sumNer)>0){
            return "The total amount of Memo revenue EP exceeds the current 100% ETD NER of this pipeline, please modify it";
        }
        if(em.compareTo(sumNer)>0){
            return "The total amount of Memo revenue EM exceeds the current 100% ETD NER of this pipeline, please modify it";
        }
        return null;
    }

    @Override
    public String checkPipelineAmount(PipelineApproveUpdateByIdDTO dto) {
        Pipeline pipeline = new Pipeline();
        pipeline.setPipelineCode(dto.getPipelineCode());
        //根据pipeline查询Mybusiness的ner
        List<SalesDeliveryDTO> kpiList = dto.getKpiList();
        //取出useAmount是1的
        List<SalesDeliveryDTO> userAmountList = kpiList.stream().filter(salesDeliveryDTO -> EmptyUtils.isNotEmpty(salesDeliveryDTO.getUseAmount())&& salesDeliveryDTO.getUseAmount().equals(1)).collect(Collectors.toList());
        String fiscal = fiscalCalenderService.getCurrentFiscalCalender();
        MyBusiness myBusiness = new MyBusiness();
        myBusiness.setOpportunityId(dto.getPipelineCode());
        myBusiness.setFiscalYear(fiscal);
        List<MyBusiness> myBusinesses = myBusinessService.queryAll(myBusiness);
        //如果有数据，按财年分组取最新财年的数据
        if(EmptyUtils.isNotEmpty(myBusinesses)){
            Map<String, List<MyBusiness>> map = myBusinesses.stream().collect(Collectors.groupingBy(MyBusiness::getFiscalYear));
                //取财年最大的
                myBusinesses = map.get(map.keySet().stream().max(Comparator.naturalOrder()).orElse(null));
        }
        //判断是否有type为345678的数据
        Boolean flags = false;
        flags = kpiList.stream().anyMatch(salesDeliveryDTO -> salesDeliveryDTO.getSalesDeliveryCreditType()==3||salesDeliveryDTO.getSalesDeliveryCreditType()==4||salesDeliveryDTO.getSalesDeliveryCreditType()==5||salesDeliveryDTO.getSalesDeliveryCreditType()==6
                ||salesDeliveryDTO.getSalesDeliveryCreditType()==7||salesDeliveryDTO.getSalesDeliveryCreditType()==8
                ||salesDeliveryDTO.getSalesDeliveryCreditType()==9||salesDeliveryDTO.getSalesDeliveryCreditType()==10);
        if(!flags)
        {
            return null;
        }
        if(EmptyUtils.isEmpty(myBusinesses)&&EmptyUtils.isNotEmpty(userAmountList)){
            return "The pipeline does not have NER，please Input the percentage";
        }
        //如果userAmountList为空跳过
        if(EmptyUtils.isEmpty(userAmountList)){
            //kpiList汇总type为 (3、4、6)(5\7\8) 的ratio,如何总计超过100，则提示The total ratio of Memo revenue EP cannot exceed 100%
            BigDecimal sumRatio = kpiList.stream().filter(salesDeliveryDTO -> salesDeliveryDTO.getSalesDeliveryCreditType()==3||salesDeliveryDTO.getSalesDeliveryCreditType()==4
                    ||salesDeliveryDTO.getSalesDeliveryCreditType()==6||salesDeliveryDTO.getSalesDeliveryCreditType()==9).map(SalesDeliveryDTO::getSalesDeliveryCreditRatio).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal sumRatio2 = kpiList.stream().filter(salesDeliveryDTO -> salesDeliveryDTO.getSalesDeliveryCreditType()==5
                    ||salesDeliveryDTO.getSalesDeliveryCreditType()==7||salesDeliveryDTO.getSalesDeliveryCreditType()==8||salesDeliveryDTO.getSalesDeliveryCreditType()==10).map(SalesDeliveryDTO::getSalesDeliveryCreditRatio).reduce(BigDecimal.ZERO, BigDecimal::add);
            if(sumRatio.compareTo(new BigDecimal(100))>0){
                return "The total ratio of Memo revenue EP cannot exceed 100%";
            }
            if(sumRatio2.compareTo(new BigDecimal(100))>0){
                return "The total ratio of Memo revenue EM cannot exceed 100%";
            }
            return null;
        }
        //合计ner
        BigDecimal sumNer = myBusinesses.stream().map(MyBusiness::getENer).reduce(BigDecimal.ZERO, BigDecimal::add);
        StringBuffer sb = new StringBuffer();
        sb.append("The total fixed amount of ");
        Boolean flag = false;
        //按照type分组，如果合计的amount大于ner则不通过
        Map<Integer, List<SalesDeliveryDTO>> typeMap = userAmountList.stream().collect(Collectors.groupingBy(SalesDeliveryDTO::getSalesDeliveryCreditType));
        for (Map.Entry<Integer, List<SalesDeliveryDTO>> entry : typeMap.entrySet()) {
            List<SalesDeliveryDTO> salesDeliveryDTOS = entry.getValue();
            BigDecimal sumAmount = salesDeliveryDTOS.stream().map(SalesDeliveryDTO::getSalesDeliveryCreditAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            if(sumAmount.compareTo(sumNer)>0){
                flag = true;
                if(entry.getKey()==3){
                    sb.append("Advisor EP ");
                }
                if(entry.getKey()==8){
                    sb.append("Advisor EM ");
                }
                if(entry.getKey()==SALES_DELIVERY_CREDIT_TYPE_COEEP){
                    sb.append("COE Credit EP ");
                }
                if(entry.getKey()==SALES_DELIVERY_CREDIT_TYPE_COEEM){
                    sb.append("COE Credit EM ");
                }
// 9\10
                if(entry.getKey()==9){
                    sb.append("Product EP ");
                }
                if(entry.getKey()==10){
                    sb.append("Product EM ");
                }
            }
        }
        if(flag){
            sb.append("cannot exceed the current ETD NER.");
            return sb.toString();
        }
        //kpiList汇总type为 (3、4、6)(5\7\8) 的amount，如果useAmount是1则取SalesDeliveryCreditAmount，如果为0则取salesDeliveryCreditRatio*amount/100
        BigDecimal ep = BigDecimal.ZERO;
        BigDecimal em = BigDecimal.ZERO;
        for (SalesDeliveryDTO salesDeliveryDTO : kpiList) {
            if(salesDeliveryDTO.getSalesDeliveryCreditType()==3||salesDeliveryDTO.getSalesDeliveryCreditType()==4
                    ||salesDeliveryDTO.getSalesDeliveryCreditType()==6||salesDeliveryDTO.getSalesDeliveryCreditType()==9){
                if(EmptyUtils.isNotEmpty(salesDeliveryDTO.getUseAmount())&&salesDeliveryDTO.getUseAmount().equals(1)){
                    ep = ep.add(salesDeliveryDTO.getSalesDeliveryCreditAmount());
                }else {
                    ep = ep.add(salesDeliveryDTO.getSalesDeliveryCreditRatio().multiply(sumNer).divide(new BigDecimal(100)));
                }
            }
            if(salesDeliveryDTO.getSalesDeliveryCreditType()==5||salesDeliveryDTO.getSalesDeliveryCreditType()==7
                    ||salesDeliveryDTO.getSalesDeliveryCreditType()==8  ||salesDeliveryDTO.getSalesDeliveryCreditType()==10){
                if(EmptyUtils.isNotEmpty(salesDeliveryDTO.getUseAmount())&&salesDeliveryDTO.getUseAmount().equals(1)){
                    em = em.add(salesDeliveryDTO.getSalesDeliveryCreditAmount());
                }else {
                    em = em.add(salesDeliveryDTO.getSalesDeliveryCreditRatio().multiply(sumNer).divide(new BigDecimal(100)));
                }
            }
        }
        //比较ep、em和ner的大小 超出了则提示 The total amount of Memo revenue EP exceeds the current 100% ETD NER of this pipeline, please modify it
        if(ep.compareTo(sumNer)>0){
            return "The total amount of Memo revenue EP exceeds the current 100% ETD NER of this pipeline, please modify it";
        }
        if(em.compareTo(sumNer)>0){
            return "The total amount of Memo revenue EM exceeds the current 100% ETD NER of this pipeline, please modify it";
        }
        return null;
    }


}
