package com.ey.tax.cloud.targets.dto.transfer;

import com.ey.cn.tax.framework.validator.UpdateByIdGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-04-18 15:34:34
 * 
 */
@Data
@Schema(description = "根据id批量更新实体")
public class SyncOpportunityUpdateByIdDTO {
    
    /**
     * 数据id
     */
    @Schema(description = "数据id")
    @NotBlank(message = "{SyncOpportunityDTO.id.NotBlank}", groups = {UpdateByIdGroup.class})
    private String id;

    /**
     * 商机id COLUMN:oppr_id
     */
    @Schema(description = "商机id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String opprId;

    /**
     * 商机编号（保留） COLUMN:oppr_code
     */
    @Schema(description = "商机编号（保留）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String opprCode;

    /**
     * 商机名称（保留） COLUMN:oppr_name
     */
    @Schema(description = "商机名称（保留）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String opprName;

    /**
     * oppr_business_unit_code COLUMN:oppr_business_unit_code
     */
    @Schema(description = "oppr_business_unit_code", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String opprBusinessUnitCode;

    /**
     * oppr_business_unit_name COLUMN:oppr_business_unit_name
     */
    @Schema(description = "oppr_business_unit_name", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String opprBusinessUnitName;

    /**
     * oppr_city_code COLUMN:oppr_city_code
     */
    @Schema(description = "oppr_city_code", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String opprCityCode;

    /**
     * oppr_city_name COLUMN:oppr_city_name
     */
    @Schema(description = "oppr_city_name", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String opprCityName;

    /**
     * oppr_sub_service_line_code COLUMN:oppr_sub_service_line_code
     */
    @Schema(description = "oppr_sub_service_line_code", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String opprSubServiceLineCode;

    /**
     * oppr_sub_service_line_name COLUMN:oppr_sub_service_line_name
     */
    @Schema(description = "oppr_sub_service_line_name", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String opprSubServiceLineName;

    /**
     * global_service_code COLUMN:global_service_code
     */
    @Schema(description = "global_service_code", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String globalServiceCode;

    /**
     * local_service_code COLUMN:local_service_code
     */
    @Schema(description = "local_service_code", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String localServiceCode;

    /**
     * pace_service_code COLUMN:pace_service_code
     */
    @Schema(description = "pace_service_code", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String paceServiceCode;

    /**
     * opname COLUMN:oppr_partner_name
     */
    @Schema(description = "opname", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String opprPartnerName;

    /**
     * opgpn COLUMN:oppr_partner_gpn
     */
    @Schema(description = "opgpn", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String opprPartnerGpn;

    /**
     * 商机负责人name COLUMN:pursuit_leader_name
     */
    @Schema(description = "商机负责人name", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Object pursuitLeaderName;

    /**
     * 商机负责人gpn COLUMN:pursuit_leader_gpn
     */
    @Schema(description = "商机负责人gpn", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Object pursuitLeaderGpn;

    /**
     * em_name COLUMN:oppr_manager_name
     */
    @Schema(description = "em_name", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Object opprManagerName;

    /**
     * em_gpn COLUMN:oppr_manager_gpn
     */
    @Schema(description = "em_gpn", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Object opprManagerGpn;

    /**
     * 参与合伙人name COLUMN:participated_partner_name
     */
    @Schema(description = "参与合伙人name", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Object participatedPartnerName;

    /**
     * 参与合伙人gpn COLUMN:participated_partner_gpn
     */
    @Schema(description = "参与合伙人gpn", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Object participatedPartnerGpn;

    /**
     * 参与合伙人占比份额 COLUMN:partner_proportion
     */
    @Schema(description = "参与合伙人占比份额", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Object partnerProportion;

    /**
     * 参与经理name COLUMN:participated_manager_name
     */
    @Schema(description = "参与经理name", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Object participatedManagerName;

    /**
     * 参与经理gpn COLUMN:participated_manager_gpn
     */
    @Schema(description = "参与经理gpn", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Object participatedManagerGpn;

    /**
     * 推荐人name COLUMN:identified_by_name
     */
    @Schema(description = "推荐人name", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Object identifiedByName;

    /**
     * 推荐人gpn COLUMN:identified_by_gpn
     */
    @Schema(description = "推荐人gpn", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Object identifiedByGpn;

    /**
     * 其他成员name COLUMN:pursuit_team_name
     */
    @Schema(description = "其他成员name", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Object pursuitTeamName;

    /**
     * 其他成员gpn COLUMN:pursuit_team_gpn
     */
    @Schema(description = "其他成员gpn", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Object pursuitTeamGpn;

    /**
     * 预估赢率 COLUMN:estimated_win_odds
     */
    @Schema(description = "预估赢率", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Short estimatedWinOdds;

    /**
     * 预计赢单日期 COLUMN:anticipated_win_date
     */
    @Schema(description = "预计赢单日期", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDateTime anticipatedWinDate;

    /**
     * 项目预计开始时间 COLUMN:anticipated_start_date
     */
    @Schema(description = "项目预计开始时间", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDateTime anticipatedStartDate;

    /**
     * 项目预计结束时间 COLUMN:anticipated_close_date
     */
    @Schema(description = "项目预计结束时间", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDateTime anticipatedCloseDate;

    /**
     * psm_prime_office_name COLUMN:psm_prime_office_code
     */
    @Schema(description = "psm_prime_office_name", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Object psmPrimeOfficeCode;

    /**
     * is_internal_referral COLUMN:is_internal_referral
     */
    @Schema(description = "is_internal_referral", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String isInternalReferral;

    /**
     * internal_referrer_gpn COLUMN:internal_referrer_gpn
     */
    @Schema(description = "internal_referrer_gpn", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String internalReferrerGpn;

    /**
     * internal_referrer_name COLUMN:internal_referrer_name
     */
    @Schema(description = "internal_referrer_name", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String internalReferrerName;

    /**
     * is_competitive_bid COLUMN:is_competitive_bid
     */
    @Schema(description = "is_competitive_bid", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String isCompetitiveBid;

    /**
     * 竞争者 COLUMN:competitor
     */
    @Schema(description = "竞争者", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Object competitor;

    /**
     * alliance_name COLUMN:alliance_name
     */
    @Schema(description = "alliance_name", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String allianceName;

    /**
     * is_eic_self COLUMN:is_eic_self
     */
    @Schema(description = "is_eic_self", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String isEicSelf;

    /**
     * efforts_reason COLUMN:efforts_reason
     */
    @Schema(description = "efforts_reason", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String effortsReason;

    /**
     * product_code COLUMN:product_code
     */
    @Schema(description = "product_code", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String productCode;

    /**
     * product_name COLUMN:product_name
     */
    @Schema(description = "product_name", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Object productName;

    /**
     * 商机描述 COLUMN:comments
     */
    @Schema(description = "商机描述", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String comments;

    /**
     * 解决方案行业 COLUMN:solution_sector_name
     */
    @Schema(description = "解决方案行业", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String solutionSectorName;

    /**
     * recurring_name COLUMN:recurring_name
     */
    @Schema(description = "recurring_name", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String recurringName;

    /**
     * 商机结果名称 COLUMN:oppr_outcome_name
     */
    @Schema(description = "商机结果名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String opprOutcomeName;

    /**
     * 商机结果确认日期，商机系统手动填写的日期 COLUMN:oppr_confirm_date
     */
    @Schema(description = "商机结果确认日期，商机系统手动填写的日期", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDateTime opprConfirmDate;

    /**
     * 商机结果原因 COLUMN:reason_code
     */
    @Schema(description = "商机结果原因", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String reasonCode;

    /**
     * 描述说明 COLUMN:description
     */
    @Schema(description = "描述说明", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String description;

    /**
     * client_name COLUMN:client_name
     */
    @Schema(description = "client_name", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String clientName;

    /**
     * client_cn_name COLUMN:client_cn_name
     */
    @Schema(description = "client_cn_name", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String clientCnName;

    /**
     * gfis_id COLUMN:gfis_id
     */
    @Schema(description = "gfis_id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String gfisId;

    /**
     * mdm_id COLUMN:mdm_id
     */
    @Schema(description = "mdm_id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String mdmId;

    /**
     * duns_number COLUMN:duns_number
     */
    @Schema(description = "duns_number", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String dunsNumber;

    /**
     * duns_name COLUMN:duns_name
     */
    @Schema(description = "duns_name", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String dunsName;

    /**
     * 客户id COLUMN:client_id
     */
    @Schema(description = "客户id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String clientId;

    /**
     * customer_classification_code COLUMN:customer_classification_code
     */
    @Schema(description = "customer_classification_code", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String customerClassificationCode;

    /**
     * stock_exchange_name COLUMN:stock_exchange_code
     */
    @Schema(description = "stock_exchange_name", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Object stockExchangeCode;

    /**
     * account_id COLUMN:account_id
     */
    @Schema(description = "account_id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String accountId;

    /**
     * 终端客户id COLUMN:end_client_id
     */
    @Schema(description = "终端客户id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String endClientId;

    /**
     * is_sec COLUMN:is_sec
     */
    @Schema(description = "is_sec", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String isSec;

    /**
     * client_sector_code COLUMN:client_sector_code
     */
    @Schema(description = "client_sector_code", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String clientSectorCode;

    /**
     * client_subsector COLUMN:client_subsector_code
     */
    @Schema(description = "client_subsector", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String clientSubsectorCode;

    /**
     * company_type COLUMN:company_type_code
     */
    @Schema(description = "company_type", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Object companyTypeCode;

    /**
     * account_segment_name COLUMN:account_segment_code
     */
    @Schema(description = "account_segment_name", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String accountSegmentCode;

    /**
     * account_sub_segment_name COLUMN:account_sub_segment_name
     */
    @Schema(description = "account_sub_segment_name", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String accountSubSegmentName;

    /**
     * uduns_number COLUMN:uduns_number
     */
    @Schema(description = "uduns_number", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String udunsNumber;

    /**
     * uduns_name COLUMN:uduns_name
     */
    @Schema(description = "uduns_name", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String udunsName;

    /**
     * account_sector COLUMN:account_sector_code
     */
    @Schema(description = "account_sector", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String accountSectorCode;

    /**
     * account_subsector COLUMN:account_subsector_code
     */
    @Schema(description = "account_subsector", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String accountSubsectorCode;

    /**
     * client_area_name COLUMN:client_area_name
     */
    @Schema(description = "client_area_name", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String clientAreaName;

    /**
     * client_region_name COLUMN:client_region_name
     */
    @Schema(description = "client_region_name", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String clientRegionName;

    /**
     * client_province_name COLUMN:client_province_name
     */
    @Schema(description = "client_province_name", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String clientProvinceName;

    /**
     * client_city_name COLUMN:client_city_name
     */
    @Schema(description = "client_city_name", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String clientCityName;

    /**
     * client_market_segment_name COLUMN:client_market_segment_name
     */
    @Schema(description = "client_market_segment_name", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String clientMarketSegmentName;

    /**
     * account_area_name COLUMN:account_area_name
     */
    @Schema(description = "account_area_name", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String accountAreaName;

    /**
     * account_region_name COLUMN:account_region_name
     */
    @Schema(description = "account_region_name", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String accountRegionName;

    /**
     * account_province_name COLUMN:account_province_name
     */
    @Schema(description = "account_province_name", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String accountProvinceName;

    /**
     * account_city_name COLUMN:account_city_name
     */
    @Schema(description = "account_city_name", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String accountCityName;

    /**
     * oppr_market_segment_name COLUMN:oppr_market_segment_name
     */
    @Schema(description = "oppr_market_segment_name", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String opprMarketSegmentName;

    /**
     * client_office_name COLUMN:client_office_name
     */
    @Schema(description = "client_office_name", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String clientOfficeName;

    /**
     * client_geonetwork_name COLUMN:client_geonetwork_name
     */
    @Schema(description = "client_geonetwork_name", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String clientGeonetworkName;

    /**
     * 客户总部 COLUMN:client_country_code
     */
    @Schema(description = "客户总部", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String clientCountryCode;

    /**
     * client_is_personal COLUMN:client_is_personal
     */
    @Schema(description = "client_is_personal", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String clientIsPersonal;

    /**
     * client_address_type COLUMN:client_address_type
     */
    @Schema(description = "client_address_type", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String clientAddressType;

    /**
     * client_address COLUMN:client_address
     */
    @Schema(description = "client_address", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String clientAddress;

    /**
     * client_channel_name COLUMN:client_channel_code
     */
    @Schema(description = "client_channel_name", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String clientChannelCode;

    /**
     * 商机总金额币种名称 COLUMN:oppr_currency
     */
    @Schema(description = "商机总金额币种名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String opprCurrency;

    /**
     * 商机总金额 COLUMN:oppr_amount
     */
    @Schema(description = "商机总金额", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Short opprAmount;

    /**
     * 是否涉及增值税 COLUMN:is_inclusive_vat
     */
    @Schema(description = "是否涉及增值税", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String isInclusiveVat;

    /**
     * all_pace_info_json COLUMN:all_pace_info_json
     */
    @Schema(description = "all_pace_info_json", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Object allPaceInfoJson;

    /**
     * eng_info_json COLUMN:eng_info_json
     */
    @Schema(description = "eng_info_json", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Object engInfoJson;

    /**
     * 商机源于的event COLUMN:event
     */
    @Schema(description = "商机源于的event", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String event;

    /**
     * 商机源于的event描述 COLUMN:event_desc
     */
    @Schema(description = "商机源于的event描述", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String eventDesc;

    /**
     * 数据分区 COLUMN:ds
     */
    @Schema(description = "数据分区", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String ds;

    /**
     * 数据跑批时间 COLUMN:etl_time
     */
    @Schema(description = "数据跑批时间", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDateTime etlTime;
}