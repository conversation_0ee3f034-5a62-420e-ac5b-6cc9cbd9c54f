package com.ey.tax.cloud.targets.dto.transfer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-04-17 16:03:29
 * 
 */
@Data
@Schema(description = "根据条件分页查询实体")
public class SyncTaxLocationQueryPageDTO {
    /**
     * COLUMN:country_code
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String countryCode;

    /**
     * COLUMN:country_name
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String countryName;

    /**
     * COLUMN:ds
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String ds;
}