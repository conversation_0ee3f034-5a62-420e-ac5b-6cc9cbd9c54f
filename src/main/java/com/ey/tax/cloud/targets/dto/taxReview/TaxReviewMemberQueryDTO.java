package com.ey.tax.cloud.targets.dto.taxReview;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-02-26 11:15:06
 * 
 */
@Data
@Schema(description = "根据条件查询实体")
public class TaxReviewMemberQueryDTO {
    /**
     * Tax review id COLUMN:tax_review_id
     */
    @Schema(description = "Tax review id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String taxReviewId;

    /**
     * 参与人员类型（staff、senior） COLUMN:type
     */
    @Schema(description = "参与人员类型（staff、senior）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String type;

    /**
     * 参与人员id COLUMN:user_id
     */
    @Schema(description = "参与人员id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String userId;
}