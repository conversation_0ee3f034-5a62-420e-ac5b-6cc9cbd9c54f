package com.ey.tax.cloud.targets.dto.data;

import com.ey.cn.tax.framework.dto.BaseRepDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2025-07-14 11:36:51
 * 
 */
@Data
@Schema(description = "查询返回实体")
public class ServicelineRepDTO extends BaseRepDTO {
    /**
     * COLUMN:fiscal_year
     */
    @Schema(description = "null")
    private String fiscalYear;

    /**
     * 字典key COLUMN:item_key
     */
    @Schema(description = "字典key")
    private String itemKey;

    /**
     * 字典value COLUMN:item_value
     */
    @Schema(description = "字典value")
    private String itemValue;

    /**
     * 父节点 COLUMN:item_parent
     */
    @Schema(description = "父节点")
    private String itemParent;

    /**
     * 扩展字段1 COLUMN:ext_one
     */
    @Schema(description = "扩展字段1")
    private String extOne;

    /**
     * 扩展字段2 COLUMN:ext_two
     */
    @Schema(description = "扩展字段2")
    private String extTwo;

    /**
     * 字典排序，由大到小 COLUMN:order_num
     */
    @Schema(description = "字典排序，由大到小")
    private Integer orderNum;

    /**
     * 状态 COLUMN:status
     */
    @Schema(description = "状态")
    private Integer status;

    /**
     * 标签（user：只在user查询中展示） COLUMN:tag
     */
    @Schema(description = "标签（user：只在user查询中展示）")
    private String tag;

    /**
     * 层级 COLUMN:layer
     */
    @Schema(description = "层级")
    private Integer layer;

    @Schema(description = "备注")
    private String remark;
}