package com.ey.tax.cloud.targets.service.data;

import com.ey.cn.tax.framework.service.BaseService;
import com.ey.tax.cloud.targets.entity.data.DictionaryItem;
import com.ey.tax.cloud.targets.entity.pipeline.PipelineConfig;

import java.util.Collection;
import java.util.List;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-01-17 14:35:36
 *
 */
public interface DictionaryItemService extends BaseService<DictionaryItem> {

    void saveForJob(Collection<DictionaryItem> entities);

    List<DictionaryItem> queryByPara2(DictionaryItem entity);

}
