package com.ey.tax.cloud.targets.utils;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName SplitUtils
 * <AUTHOR>
 * @Date 2023/6/20 15:34
 * @Version 1.0
 **/
public class SplitUtils {

    /**
     * 按照copy个组分
     *
     * @param copy
     * @return
     */
    public static <T> List<List<T>> splitByCopy(List<T> objects, int copy) {
        int listCount = copy;
        if(objects.size() <= copy){
            listCount = objects.size();
        }
        List<List<T>> results = new ArrayList<>();
        for (int i = 0; i < listCount; i++) {
            results.add(new ArrayList<>());
        }
        for (int i = 0; i < objects.size(); i++) {
            int index = i % copy;
            List<T> result = results.get(index);
            result.add(objects.get(i));
        }
        return results;
    }

    public static void main(String[] args) {
        List<String> ss = new ArrayList<>();
        for(int i =0;i<46777;i++){
            ss.add("" + i);
        }
        List<List<String>> sss = SplitUtils.splitByCopy(ss, 6);
        System.out.println();
    }

}
