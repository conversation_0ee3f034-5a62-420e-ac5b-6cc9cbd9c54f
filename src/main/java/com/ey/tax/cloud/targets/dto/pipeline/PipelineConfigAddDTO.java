package com.ey.tax.cloud.targets.dto.pipeline;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-01-24 16:14:31
 *
 */
@Data
@Schema(description = "新增实体")
public class PipelineConfigAddDTO {
    /**
     * 字段 COLUMN:column_name
     */
    @Schema(description = "字段", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String columnName;

    /**
     * 是否可编辑 COLUMN:is_edit
     */
    @Schema(description = "是否可编辑", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String isEdit;

    /**
     * 编辑后是否审批 COLUMN:is_edit_approve
     */
    @Schema(description = "编辑后是否审批", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String isEditApprove;

    /**
     * 颜色 COLUMN:color
     */
    @Schema(description = "颜色", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String color;

    /**
     * 表名 COLUMN:table_code
     */
    @Schema(description = "表名code pipeline:target_pipeline", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String tableCode;



    /**
     * table名称 COLUMN:table_name
     */
    @Schema(description = "指标名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String tableName;

    /**
     * 字段显示名称 COLUMN:column_display_name
     */
    @Schema(description = "字段显示名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String columnDisplayName;



    /**
     * 提示信息 COLUMN:column_tips
     */
    @Schema(description = "提示信息", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String columnTips;

    /**
     * 财年
     */
    @Schema(description = "财年", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String fiscalYear;

    /**
     * 备注 COLUMN:remark
     */
    @Schema(description = "备注", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String remark;

    /**
     * 子名称 COLUMN:sub_name
     */
    @Schema(description = "子名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String subName;
    @Schema(description = "角色", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String role;

    @Schema(description = "是否为空", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String isBlank;

}
