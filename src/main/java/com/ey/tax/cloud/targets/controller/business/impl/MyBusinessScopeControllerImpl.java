package com.ey.tax.cloud.targets.controller.business.impl;

import com.ey.cn.tax.framework.context.EyUserContextHolder;
import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.dto.SearchDTO;
import com.ey.cn.tax.framework.entity.Search;
import com.ey.cn.tax.framework.utils.ConvertUtils;
import com.ey.cn.tax.framework.utils.EmptyUtils;
import com.ey.cn.tax.framework.web.annotation.EyRestController;
import com.ey.cn.tax.framework.web.base.AbstractController;
import com.ey.tax.cloud.targets.constant.CommonConstants;
import com.ey.tax.cloud.targets.controller.business.MyBusinessScopeController;
import com.ey.tax.cloud.targets.dto.business.MyBusinessScopeAddDTO;
import com.ey.tax.cloud.targets.dto.business.MyBusinessScopeBatchUpdateDTO;
import com.ey.tax.cloud.targets.dto.business.MyBusinessScopeDTO;
import com.ey.tax.cloud.targets.dto.business.MyBusinessScopeDeleteByIdDTO;
import com.ey.tax.cloud.targets.dto.business.MyBusinessScopeDeleteByIdListDTO;
import com.ey.tax.cloud.targets.dto.business.MyBusinessScopeQueryByIdDTO;
import com.ey.tax.cloud.targets.dto.business.MyBusinessScopeQueryByIdListDTO;
import com.ey.tax.cloud.targets.dto.business.MyBusinessScopeQueryDTO;
import com.ey.tax.cloud.targets.dto.business.MyBusinessScopeQueryPageDTO;
import com.ey.tax.cloud.targets.dto.business.MyBusinessScopeRepDTO;
import com.ey.tax.cloud.targets.dto.business.MyBusinessScopeUpdateByIdDTO;
import com.ey.tax.cloud.targets.entity.business.MyBusinessScope;
import com.ey.tax.cloud.targets.entity.business.MyBusinessSelf;
import com.ey.tax.cloud.targets.entity.pipeline.Pipeline;
import com.ey.tax.cloud.targets.entity.pipeline.SalesDelivery;
import com.ey.tax.cloud.targets.entity.user.UserAttribute;
import com.ey.tax.cloud.targets.service.business.MyBusinessScopeService;
import com.ey.tax.cloud.targets.service.business.MyBusinessSelfService;
import com.ey.tax.cloud.targets.service.data.DictionaryService;
import com.ey.tax.cloud.targets.service.data.FiscalCalenderService;
import com.ey.tax.cloud.targets.service.pipeline.PipelineService;
import com.ey.tax.cloud.targets.service.pipeline.SalesDeliveryService;
import com.ey.tax.cloud.targets.service.user.UserAttributeService;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-07-25 18:29:35
 *
 */
@EyRestController(path ="/v1/myBusinessScope")
public class MyBusinessScopeControllerImpl extends AbstractController<MyBusinessScopeService, MyBusinessScopeDTO, MyBusinessScope> implements MyBusinessScopeController {

    @Autowired
    private MyBusinessSelfService myBusinessSelfService;
    @Autowired
    private PipelineService pipelineService;
    @Autowired
    private SalesDeliveryService    salesDeliveryService;
    @Autowired
    private UserAttributeService userAttributeService;
    @Autowired
    private FiscalCalenderService fiscalCalenderService;
    @Autowired
    private DictionaryService dictionaryService;

    /**
     * add MyBusinessScope from http
     */
    @Override
    public ResponseDTO<Void> add(MyBusinessScopeAddDTO dto) {
        MyBusinessScope entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        service.save(entity);
        return ResponseDTO.success();
    }

    /**
     * addList MyBusinessScope from http
     */
    @Override
    public ResponseDTO<Void> addList(List<MyBusinessScopeAddDTO> dtos) {
        List<MyBusinessScope> entitys = ConvertUtils.convertDTOList2EntityList(dtos, entityClass);
        service.save(entitys);
        return ResponseDTO.success();
    }

    /**
     * delete MyBusinessScope from http
     */
    @Override
    public ResponseDTO<Void> deleteById(MyBusinessScopeDeleteByIdDTO dto) {
        service.deleteById(dto.getId());
        return ResponseDTO.success();
    }

    /**
     * deleteList MyBusinessScope from http
     */
    @Override
    public ResponseDTO<Void> deleteByIdList(MyBusinessScopeDeleteByIdListDTO dto) {
        getService().deleteByIds(dto.getIds());
        return ResponseDTO.success();
    }

    /**
     * update MyBusinessScope from http
     */
    @Override
    public ResponseDTO<Void> updateById(MyBusinessScopeUpdateByIdDTO dto) {
        MyBusinessScope entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        getService().update(entity);
        return ResponseDTO.success();
    }

    /**
     * updateBatch MyBusinessScope from http
     */
    @Override
    public ResponseDTO<Void> updateBatch(List<MyBusinessScopeBatchUpdateDTO> dtos) {
        List<MyBusinessScope> entities = ConvertUtils.convertDTOList2EntityList(dtos, entityClass);
        getService().updateBatchByIds(entities);
        return ResponseDTO.success();
    }

    /**
     * query MyBusinessScope from http
     */
    @Override
    public ResponseDTO<MyBusinessScopeRepDTO> queryById(MyBusinessScopeQueryByIdDTO dto) {
        MyBusinessScope entity = getService().queryById(dto.getId());
        MyBusinessScopeRepDTO rDTO = ConvertUtils.convertEntity2DTO(entity, MyBusinessScopeRepDTO.class);
        return ResponseDTO.success(rDTO);
    }

    /**
     * queryByIdList MyBusinessScope from http
     */
    @Override
    public ResponseDTO<List<MyBusinessScopeRepDTO>> queryByIdList(MyBusinessScopeQueryByIdListDTO dto) {
        List<MyBusinessScope> entities = getService().queryByIds(dto.getIds());
        return ResponseDTO.success(ConvertUtils.convertEntityList2DTOList(entities, MyBusinessScopeRepDTO.class));
    }

    /**
     * queryList MyBusinessScope from http
     */
    @Override
    public ResponseDTO<List<MyBusinessScopeRepDTO>> queryList(MyBusinessScopeQueryDTO dto) {
        MyBusinessScope entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        List<MyBusinessScope> entities = getService().queryByPara(entity);
        return ResponseDTO.success(ConvertUtils.convertEntityList2DTOList(entities, MyBusinessScopeRepDTO.class));
    }

    /**
     * Page MyBusinessScope from http
     */
    @Override
    public ResponseDTO<SearchDTO<MyBusinessScopeRepDTO>> queryPage(SearchDTO<MyBusinessScopeQueryPageDTO> searchDTO) {
        Search<MyBusinessScope> search = ConvertUtils.convertSearchDTO2Search(searchDTO, entityClass);
        String userId = EyUserContextHolder.get().getAuthUserId();
        search.getQueryParams().setUserId(userId);
        UserAttribute userAttribute = userAttributeService.queryByIdFromThread(userId, search.getQueryParams().getFiscalYear());
        if(CommonConstants.BOSS_NAME_LIST.contains(userAttribute.getUserEmail())&&searchDTO.getQueryParams().getType().equalsIgnoreCase("self")){
            return ResponseDTO.success(new SearchDTO<>());
        }
        if(CommonConstants.BOSS_NAME_LIST.contains(userAttribute.getUserEmail())){
            search.getQueryParams().setUserId(null);
        }
        if(searchDTO.getQueryParams().getType().equalsIgnoreCase("scope")){
            getService().queryPageByPara(search);
        }else {
            myBusinessSelfService.queryPageByParaScope(search);
        }
        //从record中取出pipelineId
        List<String> pipelineIds = search.getRecords().stream().map(MyBusinessScope::getPipelineId).toList();
        //查询
        List<Pipeline> pipelines = pipelineService.queryByIds(pipelineIds);
        //根据id生成map
        Map<String, Pipeline> pipelineMap = pipelines.stream().collect(Collectors.toMap(Pipeline::getId, pipeline -> pipeline));
        //查询salesDelivery
        List<SalesDelivery> salesDeliverieList = salesDeliveryService.selectByPipelineIds(pipelineIds, userId);
        //根据pipeline分组
        Map<String, List<SalesDelivery>> salesDeliveryMap = salesDeliverieList.stream().collect(Collectors.groupingBy(SalesDelivery::getPipelineId));
        for (MyBusinessScope record : search.getRecords()) {
            //fiscalYear
            record.setFiscalYear(dictionaryService.getDictName("fiscalYear", record.getFiscalYear()));

            //查询pipeline
            Pipeline p = pipelineMap.get(record.getPipelineId());
            /**
             * 灰色：
             *           pace_status为空
             * 黄色：
             *           pace_status=0
             * 绿色：
             *           pace_status=1
             */
            if (p == null) {
                record.setPace(0);
            }else if(p.getPaceStatus() == 0){
                record.setPace(1);
            }else if(p.getPaceStatus() == 1){
                record.setPace(2);
            }
            if(EmptyUtils.isNotEmpty(p)){
                record.setClientName(p.getClientName());
                if(EmptyUtils.isNotEmpty(userAttributeService.queryByIdFromThread(p.getEngManagerId(),fiscalCalenderService.getCurrentFiscalCalender()))){
                    record.setEngManager(userAttributeService.queryByIdFromThread(p.getEngManagerId(),fiscalCalenderService.getCurrentFiscalCalender()).getUserName());
                }
                if(EmptyUtils.isNotEmpty(userAttributeService.queryByIdFromThread(p.getOpprPartnerId(),fiscalCalenderService.getCurrentFiscalCalender()))) {
                    record.setEngPartner(userAttributeService.queryByIdFromThread(p.getOpprPartnerId(), fiscalCalenderService.getCurrentFiscalCalender()).getUserName());
                }
            }
            //Percentage 展示当前用户在Pipeline上的分fee比例
            //如果是老板则为100
            if(CommonConstants.BOSS_NAME_LIST.contains(userAttribute.getUserEmail())){
               if(EmptyUtils.isNotEmpty(p)) {
                   record.setAllocatedValue(p.getAmount());
               }
            }else {
                if(EmptyUtils.isNotEmpty(record.getPipelineId())) {
                    List<SalesDelivery> salesDeliveries = salesDeliveryMap.get(record.getPipelineId());
                    if (EmptyUtils.isNotEmpty(salesDeliveries)) {
                        record.setPercentage(salesDeliveries.get(0).getSalesDeliveryCreditRatio());
                        // Allocated Value CNY pipeline的amount * 用户占比
                        record.setAllocatedValue(p.getAmount().multiply(salesDeliveries.get(0).getSalesDeliveryCreditRatio()).multiply(new BigDecimal("0.01")));
                    }
                }
            }
        }

        return ResponseDTO.success(ConvertUtils.convertSearch2SearchDTO(search, MyBusinessScopeRepDTO.class));
    }

    @Override
    public ResponseDTO<Map<String,Object>> queryTotal(MyBusinessScopeQueryPageDTO myBusinessScopeDTO) {
        MyBusinessScope myBusinessScope = ConvertUtils.convertDTO2Entity(myBusinessScopeDTO, MyBusinessScope.class);
        String userId = EyUserContextHolder.get().getAuthUserId();
        myBusinessScope.setUserId(userId);
        List<Map<String, Object>> list = getService().queryTotalByPara(myBusinessScope);
        if(EmptyUtils.isEmpty(list) ){
            Map<String,Object> map = new HashMap<>();
            map.put("ter",0);
            map.put("ner",0);
            map.put("amount",0);
            map.put("wip",0);
            map.put("ar",0);

            return ResponseDTO.success(map);
        }
        return ResponseDTO.success(list.get(0));
    }

    /**
     * Count MyBusinessScope from http
     */
    @Override
    public ResponseDTO<Long> queryCount(MyBusinessScopeQueryDTO dto) {
        MyBusinessScope entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        Long count = getService().queryCountByPara(entity);
        return ResponseDTO.success(count);
    }

    /**
     * One MyBusinessScope from http
     */
    @Override
    public ResponseDTO<MyBusinessScopeRepDTO> queryOne(MyBusinessScopeQueryDTO dto) {
        MyBusinessScope qEntity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        MyBusinessScope rEntity = getService().queryOneByPara(qEntity);
        MyBusinessScopeRepDTO rDTO = ConvertUtils.convertEntity2DTO(rEntity, MyBusinessScopeRepDTO.class);
        return ResponseDTO.success(rDTO);
    }

    /**
     * exist MyBusinessScope from http
     */
    @Override
    public ResponseDTO<Boolean> exist(MyBusinessScopeQueryDTO dto) {
        boolean resullt = getService().existByPara(ConvertUtils.convertDTO2Entity(dto, entityClass));
        return ResponseDTO.success(resullt);
    }
}
