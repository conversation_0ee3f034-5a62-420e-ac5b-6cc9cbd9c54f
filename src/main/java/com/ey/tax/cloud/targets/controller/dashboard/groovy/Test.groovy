package com.ey.tax.cloud.targets.controller.dashboard.groovy;

import com.ey.cn.tax.framework.execution.IExecutionContext
import com.ey.cn.tax.lite.execution.dataset.IDatasetGroovyRunnable;
import com.ey.tax.cloud.targets.utils.ExceptionUtils;
import org.apache.commons.codec.binary.Base64;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;
import com.ey.cn.tax.framework.utils.JacksonUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;

class Test implements IDatasetGroovyRunnable {

    private RestTemplate restTemplate;

    private static final String ENV = "PRE";

    private static final String CLOUDAPI_LF = "\n";
    private static final String HTTP_METHOD = "POST";

    private static final String API_TYPE_NAME = "list";

    private static final String HOST = "https://7efeed4ac5b94e42b40fd1ab3f5a6715-cn-shanghai-vpc.alicloudapi.com";
    private static final String APP_KEY = "*********";
    private static final String APP_SECRET = "dJfXKZFZoZb0tSjvl8fImM0m5nvMckCl";
    private static final String API_ID = "12043";

    private static final String REQUEST_JSON_BODY = "{\n" +
            "    \"conditions\": {\n" +
            "        \"engagement_code_8\": \"********\"\n" +
            "    },\n" +
            "    \"orderBys\": [],\n" +
            "    \"returnFields\": [\n" +
            "        \"engagement_name\",\"engagement_code\",\"sub_mgmt_unit_code\",\"engagement_status\",\"base_currency\",\"Planned_Total_Engagement_Revenue\",\"e_net_unbld_inv\",\"e_Revenue_Days\",\"e_tot_exp\",\"e_ter\",\"e_ner\",\"e_ser\",\"e_margin\",\"e_margin_pct\",\"e_net_rev_per_hour\",\"client_name\",\"client_id\",\"account_name\",\"account_id\",\"e_chg_hrs\",\"engagement_partner_gpn\",\"engagement_partner_name\",\"engagement_manager_name\",\"engagement_manager_gpn\",\"contract_amount\",\"e_tot_ar\",\"e_bld_fee_exp\",\"e_col_fee_exp\",\"fiscal_year\",\"fiscal_week\",\"opportunity_id\"\n" +
            "    ],\n" +
            "    \"useModelCache\": false,\n" +
            "    \"useResultCache\": false\n" +
            "}";

    private static final String url = "https://7efeed4ac5b94e42b40fd1ab3f5a6715-cn-shanghai-vpc.alicloudapi.com/list/12043?appKey=*********&env=PRE";

    @Override
    public Object run(Object args, Map<String, Object> dependentDatasets, IExecutionContext executionContext) {
        restTemplate = (RestTemplate) executionContext.getBean("ignoreHttpsRestTemplate").get();
        StringBuilder sb = new StringBuilder();
        String responseBody = null;
        try {
            // 设置请求头
            Map<String, String> headers = new HashMap();
            Date current = new Date();
            //x-ca-timestamp,设置请求头中的时间戳，以timeIntervalSince1970的形式
            headers.put("x-ca-timestamp", String.valueOf(current.getTime()));
            //x-ca-nonce,请求放重放Nonce,15分钟内保持唯一,建议使用UUID
            headers.put("x-ca-nonce", UUID.randomUUID().toString());
            headers.put("date", getHttpDateHeaderValue(current));
            String md5 = base64AndMD5(REQUEST_JSON_BODY.getBytes(StandardCharsets.UTF_8));
            headers.put("content-md5", md5);
            headers.put("host", HOST);
            //x-ca-key,设置请求头中的Api绑定的的AppKey
            headers.put("x-ca-key", APP_KEY);
            //content-type,设置请求数据类型
            headers.put("content-type", "application/octet-stream; charset=utf-8");
            //accept,设置应答数据类型
            headers.put("accept", "application/json; charset=utf-8");
            //x-ca-signature-method,签名加密方式
            headers.put("x-ca-signature-method", "HmacSHA256");
            //x-ca-stage,生产环境标识
            headers.put("x-ca-stage", "PRE");
            //x-ca-signature,签名用作服务器校验
            String stringToSign = buildStringToSign(headers);
            //sb.append("stringToSign[" + stringToSign + "]")
            String signature = sign(stringToSign);
            headers.put("x-ca-signature", signature);
            sb.append(url)
            sb.append("\r\n")
            sb.append(JacksonUtils.toJson(headers))
            sb.append("\r\n")
            // 设置请求体
            // 创建HttpEntity对象，设置请求头和请求体
            HttpHeaders httpHeaders = new HttpHeaders();
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                httpHeaders.add(entry.getKey(), entry.getValue());
            }
            sb.append(REQUEST_JSON_BODY)
            sb.append("\r\n")
            HttpEntity<String> requestEntity = new HttpEntity<>(REQUEST_JSON_BODY, httpHeaders);
            ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);
            responseBody = responseEntity.getBody();
            //sb.append("\n");
            sb.append(responseBody);
        } catch (Exception e) {
            responseBody = ExceptionUtils.getFullStackTrace(e);
            sb.append("\n");
            sb.append(responseBody);
        }
        return sb.toString();
    }

    private static String getHttpDateHeaderValue(Date date) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss z", Locale.US);
        dateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
        return dateFormat.format(date);
    }

    public static String base64AndMD5(byte[] bytes) {
        if (bytes == null) {
            throw new IllegalArgumentException("bytes can not be null");
        }
        try {
            final MessageDigest md = MessageDigest.getInstance("MD5");
            md.reset();
            md.update(bytes);
            byte[] md5Result = md.digest();
            String base64Result = Base64.encodeBase64String(md5Result);
            /*
             * 正常情况下，base64的结果为24位，因与服务器有约定，在超过24位的情况下，截取前24位
             */
            return base64Result.length() > 24 ? base64Result.substring(0, 23) : base64Result;
        } catch (final NoSuchAlgorithmException e) {
            throw new IllegalArgumentException("unknown algorithm MD5");
        }
    }

    /**
     * 构建需要参与签名的字符串
     */
    public static String buildStringToSign(Map<String, String> headers) {

        StringBuilder sb = new StringBuilder();
        sb.append(HTTP_METHOD).append(CLOUDAPI_LF);

        //如果有@"Accept"头，这个头需要参与签名
        if (headers.get("accept") != null) {
            sb.append(headers.get("accept"));
        }
        sb.append(CLOUDAPI_LF);

        //如果有@"Content-MD5"头，这个头需要参与签名
        if (headers.get("content-md5") != null) {
            sb.append(headers.get("content-md5"));
        }
        sb.append(CLOUDAPI_LF);

        //如果有@"Content-Type"头，这个头需要参与签名
        if (headers.get("content-type") != null) {
            sb.append(headers.get("content-type"));
        }
        sb.append(CLOUDAPI_LF);

        //签名优先读取HTTP_CA_HEADER_DATE，因为通过浏览器过来的请求不允许自定义Date（会被浏览器认为是篡改攻击）
        if (headers.get("date") != null) {
            sb.append(headers.get("date"));
        }
        sb.append(CLOUDAPI_LF);

        //将headers合成一个字符串
        sb.append(buildHeaders(headers));

        //url
        String path = String.format("/%s/%s", API_TYPE_NAME, API_ID);
        sb.append(path).append("?appKey=").append(APP_KEY).append("&env=").append(ENV);
        return sb.toString();
    }

    /**
     * 将headers合成一个字符串
     * 需要注意的是，HTTP头需要按照字母排序加入签名字符串
     * 同时所有加入签名的头的列表，需要用逗号分隔形成一个字符串，加入一个新HTTP头@"X-Ca-Signature-Headers"
     */
    private static String buildHeaders(Map<String, String> headers) {
        //使用TreeMap,默认按照字母排序
        Map<String, String> headersToSign = new TreeMap<>();

        StringBuilder signHeadersStringBuilder = new StringBuilder();

        int flag = 0;
        for (Map.Entry<String, String> header : headers.entrySet()) {
            if (header.getKey().startsWith("x-ca-")) {
                if (flag != 0) {
                    signHeadersStringBuilder.append(",");
                }
                flag++;
                signHeadersStringBuilder.append(header.getKey());
                headersToSign.put(header.getKey(), header.getValue());
            }
        }

        //同时所有加入签名的头的列表，需要用逗号分隔形成一个字符串，加入一个新HTTP头@"X-Ca-Signature-Headers"
        headers.put("x-ca-signature-headers", signHeadersStringBuilder.toString());

        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> e : headersToSign.entrySet()) {
            sb.append(e.getKey()).append(':').append(e.getValue()).append(CLOUDAPI_LF);
        }
        return sb.toString();
    }

    /**
     * 生成签名串
     */
    public static String sign(String stringToSign) throws NoSuchAlgorithmException, InvalidKeyException {
        Mac hmacSha256 = Mac.getInstance("HmacSHA256");
        byte[] keyBytes = APP_SECRET.getBytes(StandardCharsets.UTF_8);
        hmacSha256.init(new SecretKeySpec(keyBytes, 0, keyBytes.length, "HmacSHA256"));
        return new String(Base64.encodeBase64(hmacSha256.doFinal(stringToSign.getBytes(StandardCharsets.UTF_8))));
    }

}
