package com.ey.tax.cloud.targets.controller.dashboard.groovy;

import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.execution.ExecutionCommand;
import com.ey.cn.tax.framework.execution.IExecutionContext;
import com.ey.cn.tax.framework.utils.EmptyUtils;
import com.ey.cn.tax.lite.api.domain.service.ILiteDatasetExecutionService;
import com.ey.cn.tax.lite.execution.dataset.IDatasetGroovyRunnable;
import com.ey.cn.tax.lite.impl.port.input.controller.LiteDatasetController;
import com.ey.cn.tax.lite.impl.port.input.dto.LiteDatasetExecutionDTO;
import com.ey.tax.cloud.targets.constant.DashboardConstants;
import com.ey.tax.cloud.targets.dto.dashboard.ForecastResultDTO;
import com.ey.tax.cloud.targets.dto.dashboard.ForecastSubResultDTO;
import com.ey.tax.cloud.targets.entity.auth.AuthGroupDetail;
import com.ey.tax.cloud.targets.entity.user.UserAttribute;
import com.ey.tax.cloud.targets.service.dashboard.DashboardService;
import com.ey.tax.cloud.targets.service.data.FiscalCalenderService;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

class ForecastGroovy implements IDatasetGroovyRunnable {

    // 常量定义，避免重复创建相同对象
    private static final BigDecimal ZERO = BigDecimal.ZERO;
    private static final int WEEKS_IN_YEAR = 52;

    // 服务依赖
    private FiscalCalenderService fiscalCalenderService;
    private DashboardService dashboardService;

    @Override
    public Object run(Object args, Map<String, Object> dependentDatasets, IExecutionContext executionContext) {
        // 1. 初始化基础参数
        Map<String, Object> argsMap = (Map<String, Object>) args;
        String dataScope = (String) argsMap.get("dataScope");
        boolean isSelf = (boolean) argsMap.get("isSelf");

        // 2. 获取所需服务
        fiscalCalenderService = executionContext.getBean(FiscalCalenderService.class).get();
        dashboardService = executionContext.getBean(DashboardService.class).get();
        ILiteDatasetExecutionService liteDatasetController = executionContext.getBean(ILiteDatasetExecutionService.class).get();

        // 3. 准备日期相关参数
        String currentFiscalCalender = fiscalCalenderService.getCurrentFiscalCalender();
        String currentYear = (String) argsMap.get("fiscalYear");
        String currentWeek = getCurrentWeek(currentFiscalCalender, currentYear);

        int iCurrentYear = Integer.parseInt(currentYear);
        String lastYear = String.valueOf(iCurrentYear - 1);

        // 4. 确定查询代码和参数
        final String forecastCode = DashboardConstants.DATA_SCOPE_PROM.equals(dataScope)
                ? "DASH_FORECAST_PROM" : "DASH_FORECAST_SELF";
        final String performanceCode = DashboardConstants.DATA_SCOPE_PROM.equals(dataScope)
                ? "DASH_PERFORMANCE_PROM" : "DASH_PERFORMANCE_SELF";

        // 5. 构建授权SQL（如果需要）
        if (DashboardConstants.DATA_SCOPE_PROM.equals(dataScope)) {
            prepareAuthSql(argsMap, isSelf);
        }

        // 6. 准备查询参数
        Map<String, Object> forecastArgsMap = new HashMap<>(argsMap);

        Map<String, Object> performanceArgsMap = new HashMap<>(argsMap);
        performanceArgsMap.put("currentYear", currentYear);
        performanceArgsMap.put("lastYear", lastYear);

        // 7. 并行执行两个查询
        try {
            CompletableFuture<List<Map<String, Object>>> forecastFuture = executeQueryAsync(
                    liteDatasetController, forecastCode, forecastArgsMap);

            CompletableFuture<List<Map<String, Object>>> performanceFuture = executeQueryAsync(
                    liteDatasetController, performanceCode, performanceArgsMap);

            // 等待所有查询完成
            CompletableFuture.allOf(forecastFuture, performanceFuture).join();

            // 8. 准备结果容器
            List<ForecastResultDTO> forecastResultDTOList = new ArrayList<>();
            Map<String, Object> result = new HashMap<>(3);
            BigDecimal currentWeekNer = ZERO;
            BigDecimal currentWeekTer = ZERO;

            // 9. 处理性能数据
            List<Map<String, Object>> performanceDto = performanceFuture.get();
            if (EmptyUtils.isNotEmpty(performanceDto)) {
                processPerformanceData(performanceDto, forecastResultDTOList, currentYear, lastYear);
            }

            // 10. 处理预测数据
            List<Map<String, Object>> forecastDto = forecastFuture.get();
            if (EmptyUtils.isNotEmpty(forecastDto)) {
                Map<String, BigDecimal> currentWeekValues =
                        processForecastData(forecastDto, forecastResultDTOList, currentWeek);
                currentWeekNer = currentWeekValues.getOrDefault("ner", ZERO);
                currentWeekTer = currentWeekValues.getOrDefault("ter", ZERO);
            }

            // 11. 对所有结果按周排序
            forecastResultDTOList.forEach(dto ->
                    dto.getValue().sort(Comparator.comparing(ForecastSubResultDTO::getWeek)));

            // 12. 构建最终结果
            result.put("currentWeek", Integer.parseInt(currentWeek));
            result.put("ter", currentWeekTer);
            result.put("ner", currentWeekNer);
            result.put("data", forecastResultDTOList);

            return result;

        } catch (InterruptedException | ExecutionException e) {
            Thread.currentThread().interrupt();
            // 在实际代码中应该有适当的日志记录
            return createEmptyResult(currentWeek);
        }
    }

    /**
     * 获取当前周
     */
    private String getCurrentWeek(String currentFiscalCalender, String currentYear) {
        String currentWeek = fiscalCalenderService.getCurrentWeek();
        if (!currentFiscalCalender.equals(currentYear)) {
            currentWeek = "1";
        }
        return currentWeek;
    }

    /**
     * 准备授权SQL
     */
    private void prepareAuthSql(Map<String, Object> argsMap, boolean isSelf) {
        List<List<AuthGroupDetail>> authGroups = (List<List<AuthGroupDetail>>) argsMap.get("authGroups");
        UserAttribute userAttribute = (UserAttribute) argsMap.get("userAttribute");
        String authSql = dashboardService.buildAuthSql(isSelf, authGroups, userAttribute,
                "attr.sl1", "attr.ssl2", "attr.ssl3",
                "attr.city", "attr.region",
                "attr.level", "attr.group_id", "attr.level_group",
                null, null, null, null, "attr.user_id");
        argsMap.put("authSql", authSql);
    }

    /**
     * 创建空结果
     */
    private Map<String, Object> createEmptyResult(String currentWeek) {
        Map<String, Object> result = new HashMap<>(3);
        result.put("currentWeek", Integer.parseInt(currentWeek));
        result.put("ter", ZERO);
        result.put("ner", ZERO);
        result.put("data", new ArrayList<>());
        return result;
    }

    /**
     * 异步执行查询
     */
    private CompletableFuture<List<Map<String, Object>>> executeQueryAsync(
            ILiteDatasetExecutionService service, String code, Map<String, Object> argsMap) {
        return CompletableFuture.supplyAsync(() -> {
            LiteDatasetExecutionDTO dto = new LiteDatasetExecutionDTO();
            dto.setCode(code);
            dto.setArgs(argsMap);
            ExecutionCommand cmd = ExecutionCommand.builder().args(argsMap).build();
            return service.execute(dto, cmd);
        });
    }

    /**
     * 处理性能数据
     */
    private void processPerformanceData(
            List<Map<String, Object>> performanceDto,
            List<ForecastResultDTO> forecastResultDTOList,
            String currentYear,
            String lastYear) {

        // 合并周数据
        Map<String, Map<String, Object>> newPerformanceResultMap = mergeWeeklyData(performanceDto);
        List<Map<String, Object>> performanceResult = new ArrayList<>(newPerformanceResultMap.values());

        // 补齐所有周
        ensureAllWeeksPresent(performanceResult, currentYear, lastYear);

        // 创建结果容器
        ForecastResultDTO actualTerDto = createForecastResultDTO("Actual TER");
        ForecastResultDTO actualNerDto = createForecastResultDTO("Actual NER");
        ForecastResultDTO priorTerDto = createForecastResultDTO("Prior year TER");
        ForecastResultDTO priorNerDto = createForecastResultDTO("Prior year NER");

        // 添加到结果列表
        forecastResultDTOList.add(actualTerDto);
        forecastResultDTOList.add(actualNerDto);
        forecastResultDTOList.add(priorTerDto);
        forecastResultDTOList.add(priorNerDto);

        // 处理每个结果项
        for (Map<String, Object> subResult : performanceResult) {
            String year = (String) subResult.get("year");
            String week = (String) subResult.get("week");

            if (year.equals(currentYear)) {
                // 处理当前年度数据
                BigDecimal actualNer = getDecimalValue(subResult, "ner");
                BigDecimal actualTer = getDecimalValue(subResult, "ter");

                addSubResultIfNonZero(actualTerDto, week, actualTer);
                addSubResultIfNonZero(actualNerDto, week, actualNer);
            } else {
                // 处理上一年度数据
                BigDecimal priorNer = getDecimalValue(subResult, "ner");
                BigDecimal priorTer = getDecimalValue(subResult, "ter");

                addSubResultIfNonZero(priorTerDto, week, priorTer);
                addSubResultIfNonZero(priorNerDto, week, priorNer);
            }
        }
    }

    /**
     * 处理预测数据
     */
    private Map<String, BigDecimal> processForecastData(
            List<Map<String, Object>> forecastDto,
            List<ForecastResultDTO> forecastResultDTOList,
            String currentWeek) {

        // 合并周数据
        Map<String, Map<String, Object>> newForecastResultMap = mergeWeeklyData(forecastDto);
        List<Map<String, Object>> forecastResult = new ArrayList<>(newForecastResultMap.values());

        // 补齐所有周
        ensureAllWeeksPresent(forecastResult);

        // 创建结果容器
        ForecastResultDTO forecastTerDto = createForecastResultDTO("Forecasted TER");
        ForecastResultDTO forecastNerDto = createForecastResultDTO("Forecasted NER");

        // 添加到结果列表
        forecastResultDTOList.add(forecastTerDto);
        forecastResultDTOList.add(forecastNerDto);

        // 跟踪当前周的值
        Map<String, BigDecimal> currentWeekValues = new HashMap<>(2);

        // 处理每个结果项
        for (Map<String, Object> subResult : forecastResult) {
            String week = (String) subResult.get("week");
            BigDecimal nerValue = getDecimalValue(subResult, "ner");
            BigDecimal terValue = getDecimalValue(subResult, "ter");

            // 添加到结果列表
            addSubResult(forecastTerDto, week, terValue);
            addSubResult(forecastNerDto, week, nerValue);

            // 记录当前周的值
            if (currentWeek.equals(week)) {
                currentWeekValues.put("ner", nerValue);
                currentWeekValues.put("ter", terValue);
            }
        }

        return currentWeekValues;
    }

    /**
     * 合并周数据
     */
    private Map<String, Map<String, Object>> mergeWeeklyData(List<Map<String, Object>> dataList) {
        Map<String, Map<String, Object>> resultMap = new HashMap<>();

        for (Map<String, Object> item : dataList) {
            String year = (String) item.get("year");
            String week = (String) item.get("week");
            String key = year + week;

            Map<String, Object> resultItem = resultMap.computeIfAbsent(key, k -> {
                Map<String, Object> newItem = new HashMap<>();
                newItem.put("ner", ZERO);
                newItem.put("ter", ZERO);
                newItem.put("year", year);
                newItem.put("week", week);
                return newItem;
            });

            // 累加NER值
            Object nerObj = item.get("ner");
            if (EmptyUtils.isNotEmpty(nerObj)) {
                BigDecimal currentNer = (BigDecimal) resultItem.get("ner");
                resultItem.put("ner", currentNer.add((BigDecimal) nerObj));
            }

            // 累加TER值
            Object terObj = item.get("ter");
            if (EmptyUtils.isNotEmpty(terObj)) {
                BigDecimal currentTer = (BigDecimal) resultItem.get("ter");
                resultItem.put("ter", currentTer.add((BigDecimal) terObj));
            }
        }

        return resultMap;
    }

    /**
     * 确保所有周都存在（单年）
     */
    private void ensureAllWeeksPresent(List<Map<String, Object>> dataList) {
        // 创建周映射
        Map<String, Map<String, Object>> weekMap = dataList.stream()
                .collect(Collectors.toMap(
                        item -> (String) item.get("week"),
                        Function.identity(),
                        (v1, v2) -> v1
                ));

        // 补充缺失的周
        for (int i = 1; i <= WEEKS_IN_YEAR; i++) {
            String week = String.valueOf(i);
            if (!weekMap.containsKey(week)) {
                Map<String, Object> newWeek = new HashMap<>();
                newWeek.put("ner", ZERO);
                newWeek.put("ter", ZERO);
                newWeek.put("week", week);
                dataList.add(newWeek);
            }
        }
    }

    /**
     * 确保所有周都存在（两年）
     */
    private void ensureAllWeeksPresent(List<Map<String, Object>> dataList, String currentYear, String lastYear) {
        // 创建年周映射
        Map<String, Map<String, Object>> yearWeekMap = dataList.stream()
                .collect(Collectors.toMap(
                        item -> ((String) item.get("year")) + ((String) item.get("week")),
                        Function.identity(),
                        (v1, v2) -> v1
                ));

        // 补充缺失的周
        for (int i = 1; i <= WEEKS_IN_YEAR; i++) {
            String week = String.valueOf(i);

            // 当前年
            String currentYearKey = currentYear + week;
            if (!yearWeekMap.containsKey(currentYearKey)) {
                Map<String, Object> newWeek = new HashMap<>();
                newWeek.put("ner", ZERO);
                newWeek.put("ter", ZERO);
                newWeek.put("week", week);
                newWeek.put("year", currentYear);
                dataList.add(newWeek);
            }

            // 上一年
            String lastYearKey = lastYear + week;
            if (!yearWeekMap.containsKey(lastYearKey)) {
                Map<String, Object> newWeek = new HashMap<>();
                newWeek.put("ner", ZERO);
                newWeek.put("ter", ZERO);
                newWeek.put("week", week);
                newWeek.put("year", lastYear);
                dataList.add(newWeek);
            }
        }
    }

    /**
     * 创建预测结果DTO
     */
    private ForecastResultDTO createForecastResultDTO(String name) {
        ForecastResultDTO dto = new ForecastResultDTO();
        dto.setName(name);
        return dto;
    }

    /**
     * 获取BigDecimal值，处理null情况
     */
    private BigDecimal getDecimalValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return EmptyUtils.isNotEmpty(value) ? (BigDecimal) value : ZERO;
    }

    /**
     * 添加子结果
     */
    private void addSubResult(ForecastResultDTO dto, String week, BigDecimal value) {
        ForecastSubResultDTO subResult = new ForecastSubResultDTO();
        subResult.setName("w" + week);
        subResult.setValue(value);
        subResult.setWeek(Integer.parseInt(week));
        dto.getValue().add(subResult);
    }

    /**
     * 只有在值不为零时添加子结果
     */
    private void addSubResultIfNonZero(ForecastResultDTO dto, String week, BigDecimal value) {
        if (ZERO.compareTo(value) != 0) {
            ForecastSubResultDTO subResult = new ForecastSubResultDTO();
            subResult.setName("w" + week);
            subResult.setValue(value);
            subResult.setWeek(Integer.parseInt(week));
            dto.getValue().add(subResult);
        }
    }
}
