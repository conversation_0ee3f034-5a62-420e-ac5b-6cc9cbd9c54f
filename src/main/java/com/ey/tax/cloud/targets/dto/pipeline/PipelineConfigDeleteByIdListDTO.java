package com.ey.tax.cloud.targets.dto.pipeline;

import com.ey.cn.tax.framework.validator.DeleteByIdListGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-01-24 16:14:31
 *
 */
@Data
@Schema(description = "根据id列表删除实体")
public class PipelineConfigDeleteByIdListDTO {

    /**
     * 数据id列表
     */
    @Schema(description = "数据id列表")
    @NotNull(message = "{PipelineConfigDTO.ids.NotNull}", groups = {DeleteByIdListGroup.class})
    @Size(min = 1, message = "{PipelineConfigDTO.ids.Size}", groups = {DeleteByIdListGroup.class})
    private List<String> ids;
}
