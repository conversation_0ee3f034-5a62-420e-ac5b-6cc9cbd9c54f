package com.ey.tax.cloud.targets.service.business;

import com.ey.cn.tax.framework.entity.Search;
import com.ey.cn.tax.framework.service.BaseService;
import com.ey.tax.cloud.targets.entity.business.MyBusiness;

import java.util.List;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-07-25 18:29:35
 *
 */
public interface MyBusinessService extends BaseService<MyBusiness> {

    Search<MyBusiness> queryOtherPageByPara(Search<MyBusiness> search);

    List<MyBusiness> queryByPara(MyBusiness entity);

    List<MyBusiness> queryList(MyBusiness entity);
    List<MyBusiness> queryAll(MyBusiness entity);
    List<MyBusiness> queryForPipeline(MyBusiness entity);
}
