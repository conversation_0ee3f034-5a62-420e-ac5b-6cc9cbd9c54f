package com.ey.tax.cloud.targets.dto.resource;

import com.ey.cn.tax.framework.validator.BatchUpdateGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import java.math.BigDecimal;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-01-17 14:35:35
 * 
 */
@Data
@Schema(description = "根据id批量更新实体")
public class PerformanceBatchUpdateDTO {
    
    /**
     * 数据id
     */
    @Schema(description = "数据id")
    @NotBlank(message = "{PerformanceDTO.id.NotBlank}", groups = {BatchUpdateGroup.class})
    private String id;

    /**
     * COLUMN:user_id
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String userId;

    /**
     * COLUMN:pipeline_id
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String pipelineId;

    /**
     * COLUMN:name
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String name;

    /**
     * COLUMN:level
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String level;

    /**
     * COLUMN:week
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer week;

    /**
     * COLUMN:ter_usd
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal terUsd;

    /**
     * COLUMN:ner_usd
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal nerUsd;

    /**
     * COLUMN:margin
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal margin;

    /**
     * COLUMN:revenue_day
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer revenueDay;

    /**
     * COLUMN:ut
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal ut;

    /**
     * COLUMN:gds_penetration
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal gdsPenetration;

    /**
     * COLUMN:sales_usd
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal salesUsd;

    /**
     * COLUMN:em_efforts_new_client
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal emEffortsNewClient;

    /**
     * COLUMN:em_efforts_new_eng
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal emEffortsNewEng;

    /**
     * COLUMN:pipelines_usd
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal pipelinesUsd;

    /**
     * COLUMN:opportunity_type
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String opportunityType;
}