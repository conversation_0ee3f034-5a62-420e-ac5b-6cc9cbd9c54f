package com.ey.tax.cloud.targets.service.business;

import com.ey.cn.tax.framework.service.BaseService;
import com.ey.tax.cloud.targets.entity.business.SyncMyBusiness;

import java.util.List;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-07-29 10:48:38
 *
 */
public interface SyncMyBusinessService extends BaseService<SyncMyBusiness> {

    String selectMaxWeek(String fiscalYear);

    List<SyncMyBusiness> queryList(SyncMyBusiness entity);
}
