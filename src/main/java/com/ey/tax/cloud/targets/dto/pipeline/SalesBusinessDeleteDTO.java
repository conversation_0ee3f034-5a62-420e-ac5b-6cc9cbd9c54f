package com.ey.tax.cloud.targets.dto.pipeline;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2025-04-07 14:45:16
 * 
 */
@Data
@Schema(description = "根据条件删除实体")
public class SalesBusinessDeleteDTO {
    /**
     * pipelineid COLUMN:pipeline_id
     */
    @Schema(description = "pipelineid", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String pipelineId;

    /**
     * 销售交付人员id COLUMN:sales_delivery_credit
     */
    @Schema(description = "销售交付人员id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String salesDeliveryCredit;

    /**
     * 销售交付人员类型（p1、m2、tpc3） COLUMN:sales_delivery_credit_type
     */
    @Schema(description = "销售交付人员类型（p1、m2、tpc3）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer salesDeliveryCreditType;

    /**
     * COLUMN:sales_delivery_credit_ratio
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Short salesDeliveryCreditRatio;

    /**
     * ner COLUMN:ner
     */
    @Schema(description = "ner", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal ner;

    /**
     * 财年 COLUMN:fiscal_year
     */
    @Schema(description = "财年", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String fiscalYear;

    /**
     * COLUMN:business_id
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String businessId;

    /**
     * 按照金额而非比例分费 COLUMN:use_amount
     */
    @Schema(description = "按照金额而非比例分费", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer useAmount;
}