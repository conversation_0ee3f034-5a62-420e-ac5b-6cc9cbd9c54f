package com.ey.tax.cloud.targets.dto.pipeline;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-01-16 15:29:01
 * 
 */
@Data
@Schema(description = "根据条件分页查询实体")
public class SalesDeliveryQueryPageDTO {
    /**
     * pipelineid COLUMN:pipeline_id
     */
    @Schema(description = "pipelineid", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String pipelineId;

    /**
     * 销售交付人员id COLUMN:sales_delivery_credit
     */
    @Schema(description = "销售交付人员id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String salesDeliveryCredit;

    /**
     * 销售交付人员业绩占比 COLUMN:sales_delivery_credit_ratio
     */
    @Schema(description = "销售交付人员业绩占比", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal salesDeliveryCreditRatio;

    /**
     * 上次销售交付人员业绩占比 COLUMN:sales_delivery_credit_ratio_last
     */
    @Schema(description = "上次销售交付人员业绩占比", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal salesDeliveryCreditRatioLast;

    /**
     * 销售交付人员类型（p、m、tpc） COLUMN:sales_delivery_credit_type
     */
    @Schema(description = "销售交付人员类型（p、m、tpc）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer salesDeliveryCreditType;

    /**
     * 销售交付人员业绩金额 COLUMN:sales_delivery_credit_amount
     */
    @Schema(description = "销售交付人员业绩金额", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal salesDeliveryCreditAmount;

    /**
     * 上次销售交付人员业绩金额 COLUMN:sales_delivery_credit_amount_last
     */
    @Schema(description = "上次销售交付人员业绩金额", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal salesDeliveryCreditAmountLast;

    /**
     * 是否还在项目 COLUMN:is_part
     */
    @Schema(description = "是否还在项目", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer isPart;

    /**
     * 财年 COLUMN:fiscal_year
     */
    @Schema(description = "财年", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String fiscalYear;
}
