package com.ey.tax.cloud.targets.service.kpi.impl;

import com.ey.cn.tax.framework.mybatisplus.extension.service.AbstractService;
import com.ey.tax.cloud.targets.entity.kpi.KpiSupplementaryHis;
import com.ey.tax.cloud.targets.mapper.kpi.KpiSupplementaryHisMapper;
import com.ey.tax.cloud.targets.service.kpi.KpiSupplementaryHisService;
import org.springframework.stereotype.Service;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-03-15 10:34:04
 * 
 */
@Service
public class KpiSupplementaryHisServiceImpl extends AbstractService<KpiSupplementaryHisMapper, KpiSupplementaryHis> implements KpiSupplementaryHisService {
}