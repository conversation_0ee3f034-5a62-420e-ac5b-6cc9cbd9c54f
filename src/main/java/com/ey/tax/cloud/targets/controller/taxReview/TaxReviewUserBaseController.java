package com.ey.tax.cloud.targets.controller.taxReview;

import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.dto.SearchDTO;
import com.ey.cn.tax.framework.validator.AddGroup;
import com.ey.cn.tax.framework.validator.DeleteByIdGroup;
import com.ey.cn.tax.framework.validator.DeleteByIdListGroup;
import com.ey.cn.tax.framework.validator.QueryByIdGroup;
import com.ey.cn.tax.framework.validator.QueryByIdListGroup;
import com.ey.cn.tax.framework.validator.QueryPageGroup;
import com.ey.cn.tax.framework.validator.UpdateByIdGroup;
import com.ey.cn.tax.framework.web.annotation.EyPostMapping;
import com.ey.cn.tax.framework.web.base.BaseController;
import com.ey.tax.cloud.targets.dto.taxReview.TaxReviewUserBaseAddDTO;
import com.ey.tax.cloud.targets.dto.taxReview.TaxReviewUserBaseBatchUpdateDTO;
import com.ey.tax.cloud.targets.dto.taxReview.TaxReviewUserBaseDTO;
import com.ey.tax.cloud.targets.dto.taxReview.TaxReviewUserBaseDeleteByIdDTO;
import com.ey.tax.cloud.targets.dto.taxReview.TaxReviewUserBaseDeleteByIdListDTO;
import com.ey.tax.cloud.targets.dto.taxReview.TaxReviewUserBaseQueryByIdDTO;
import com.ey.tax.cloud.targets.dto.taxReview.TaxReviewUserBaseQueryByIdListDTO;
import com.ey.tax.cloud.targets.dto.taxReview.TaxReviewUserBaseQueryDTO;
import com.ey.tax.cloud.targets.dto.taxReview.TaxReviewUserBaseQueryPageDTO;
import com.ey.tax.cloud.targets.dto.taxReview.TaxReviewUserBaseRepDTO;
import com.ey.tax.cloud.targets.dto.taxReview.TaxReviewUserBaseUpdateByIdDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-02-26 10:51:54
 *
 */
@Tag(name="taxReview用户基础信息")
public interface TaxReviewUserBaseController extends BaseController<TaxReviewUserBaseDTO> {
    /**
     * add TaxReviewUserBase from http
     */
    @Operation(summary = "新增", description = "新增一条数据")
    @EyPostMapping(value = "/add.do")
    ResponseDTO<Void> add(@RequestBody @Validated({AddGroup.class}) TaxReviewUserBaseAddDTO dto);

    /**
     * addList TaxReviewUserBase from http
     */
    @Operation(summary = "批量新增", description = "批量新增数据")
    @EyPostMapping(value = "/addList.do")
    ResponseDTO<Void> addList(@RequestBody List<TaxReviewUserBaseAddDTO> dtos);

    /**
     * delete TaxReviewUserBase from http
     */
    @Operation(summary = "根据id删除", description = "根据id删除一条数据")
    @EyPostMapping(value = "/deleteById.do")
    ResponseDTO<Void> deleteById(@RequestBody @Validated({DeleteByIdGroup.class}) TaxReviewUserBaseDeleteByIdDTO dto);

    /**
     * deleteList TaxReviewUserBase from http
     */
    @Operation(summary = "根据id列表删除", description = "根据id列表批量删除数据")
    @EyPostMapping(value = "/deleteByIdList.do")
    ResponseDTO<Void> deleteByIdList(@RequestBody @Validated({DeleteByIdListGroup.class}) TaxReviewUserBaseDeleteByIdListDTO dto);

    /**
     * update TaxReviewUserBase from http
     */
    @Operation(summary = "根据id更新", description = "根据id更新一条数据")
    @EyPostMapping(value = "/updateById.do")
    ResponseDTO<Void> updateById(@RequestBody @Validated({UpdateByIdGroup.class}) TaxReviewUserBaseUpdateByIdDTO dto);

    /**
     * updateBatch TaxReviewUserBase from http
     */
    @Operation(summary = "批量更新", description = "批量更新数据")
    @EyPostMapping(value = "/updateBatch.do")
    ResponseDTO<Void> updateBatch(@RequestBody List<TaxReviewUserBaseBatchUpdateDTO> dtos);

    /**
     * query TaxReviewUserBase from http
     */
    @Operation(summary = "根据id查询", description = "根据id查询数据")
    @EyPostMapping(value = "/queryById.do")
    ResponseDTO<TaxReviewUserBaseRepDTO> queryById(@RequestBody @Validated({QueryByIdGroup.class}) TaxReviewUserBaseQueryByIdDTO dto);

    /**
     * queryByIdList TaxReviewUserBase from http
     */
    @Operation(summary = "根据id列表查询", description = "根据id列表查询数据")
    @EyPostMapping(value = "/queryByIdList.do")
    ResponseDTO<List<TaxReviewUserBaseRepDTO>> queryByIdList(@RequestBody @Validated({QueryByIdListGroup.class}) TaxReviewUserBaseQueryByIdListDTO dto);

    /**
     * queryList TaxReviewUserBase from http
     */
    @Operation(summary = "查询列表", description = "查询数据列表")
    @EyPostMapping(value = "/queryList.do")
    ResponseDTO<List<TaxReviewUserBaseRepDTO>> queryList(@RequestBody TaxReviewUserBaseQueryDTO dto);

    /**
     * Page TaxReviewUserBase from http
     */
    @Operation(summary = "分页查询", description = "根据条件分页查询")
    @EyPostMapping(value = "/queryPage.do")
    ResponseDTO<SearchDTO<TaxReviewUserBaseRepDTO>> queryPage(@RequestBody @Validated({QueryPageGroup.class}) SearchDTO<TaxReviewUserBaseQueryPageDTO> searchDTO);

    /**
     * Count TaxReviewUserBase from http
     */
    @Operation(summary = "查询数量", description = "根据条件查询数量")
    @EyPostMapping(value = "/queryCount.do")
    ResponseDTO<Long> queryCount(@RequestBody TaxReviewUserBaseQueryDTO taxReviewUserBaseDTO);

    /**
     * One TaxReviewUserBase from http
     */
    @Operation(summary = "查询单个数据", description = "根据条件查询一条数据,如果查询到多条则返回异常.")
    @EyPostMapping(value = "/queryOne.do")
    ResponseDTO<TaxReviewUserBaseRepDTO> queryOne(@RequestBody TaxReviewUserBaseQueryDTO taxReviewUserBaseDTO);

    /**
     * exist TaxReviewUserBase from http
     */
    @Operation(summary = "查询是否存在", description = "根据条件查询是否存在")
    @EyPostMapping(value = "/exist.do")
    ResponseDTO<Boolean> exist(@RequestBody TaxReviewUserBaseQueryDTO taxReviewUserBaseDTO);
}
