package com.ey.tax.cloud.targets.entity.transfer;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ey.cn.tax.framework.mybatisplus.core.entity.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-04-23 14:08:20
 * 
 */
@Data
@TableName("sync_tax_performance")
public class SyncTaxPerformance extends BaseEntity {
    /**
     * COLUMN:gpn
     */
    private String gpn;

    /**
     * COLUMN:name
     */
    private String name;

    /**
     * COLUMN:sub_service_line_name
     */
    private String subServiceLineName;

    /**
     * COLUMN:competency_name
     */
    private String competencyName;

    /**
     * COLUMN:market_segment_name
     */
    private String marketSegmentName;

    /**
     * COLUMN:dict_city_name
     */
    private String dictCityName;

    /**
     * COLUMN:rank_name
     */
    private String rankName;

    /**
     * COLUMN:fytd_ter_usd
     */
    private BigDecimal fytdTerUsd;

    /**
     * COLUMN:fytd_ner_usd
     */
    private BigDecimal fytdNerUsd;

    /**
     * COLUMN:fytd_margin_usd
     */
    private BigDecimal fytdMarginUsd;

    /**
     * COLUMN:fytd_margin_persent
     */
    private BigDecimal fytdMarginPersent;

    /**
     * COLUMN:revenue_day
     */
    private Integer revenueDay;

    /**
     * COLUMN:ds
     */
    private String ds;

    /**
     * level COLUMN:fiscal_year
     */
    private String fiscalYear;

    /**
     * COLUMN:fiscal_week
     */
    private String fiscalWeek;

    /**
     * effective_utilization COLUMN:effective_utilization
     */
    private BigDecimal effectiveUtilization;

    /**
     * fytd_gds_penetration COLUMN:fytd_gds_penetration
     */
    private BigDecimal fytdGdsPenetration;

}