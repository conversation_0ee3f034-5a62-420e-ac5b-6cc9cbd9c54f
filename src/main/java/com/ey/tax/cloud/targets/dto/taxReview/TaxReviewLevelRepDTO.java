package com.ey.tax.cloud.targets.dto.taxReview;

import com.ey.cn.tax.framework.dto.BaseRepDTO;
import com.ey.tax.cloud.targets.dto.data.DictionaryItemRepDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-01-30 14:57:07
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "level查询返回实体")
public class TaxReviewLevelRepDTO extends BaseRepDTO {

    @Schema(description = "level")
    private List<DictionaryItemRepDTO> level;

}
