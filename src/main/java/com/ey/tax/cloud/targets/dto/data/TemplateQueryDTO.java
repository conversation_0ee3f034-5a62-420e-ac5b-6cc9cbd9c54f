package com.ey.tax.cloud.targets.dto.data;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-02-19 11:47:19
 * 
 */
@Data
@Schema(description = "根据条件查询实体")
public class TemplateQueryDTO {
    /**
     * 模板编码 COLUMN:temp_code
     */
    @Schema(description = "模板编码", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String tempCode;

    /**
     * 模板名称 COLUMN:temp_name
     */
    @Schema(description = "模板名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String tempName;

    /**
     * 状态 COLUMN:status
     */
    @Schema(description = "状态", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String status;

    /**
     * 文件ID COLUMN:file_id
     */
    @Schema(description = "文件ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String fileId;
}