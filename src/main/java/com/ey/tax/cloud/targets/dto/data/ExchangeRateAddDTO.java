package com.ey.tax.cloud.targets.dto.data;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-01-30 14:57:08
 * 
 */
@Data
@Schema(description = "新增实体")
public class ExchangeRateAddDTO {
    /**
     * 财年 COLUMN:fiscal_year
     */
    @Schema(description = "财年", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String fiscalYear;

    /**
     * 来源:tiger:0,导入:1 COLUMN:exchange_source
     */
    @Schema(description = "来源:tiger:0,导入:1", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer exchangeSource;

    /**
     * 汇率4位小数 COLUMN:exchange_rate
     */
    @Schema(description = "汇率4位小数", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal exchangeRate;

    /**
     * 币种 COLUMN:exchange_currency
     */
    @Schema(description = "币种", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String exchangeCurrency;
}