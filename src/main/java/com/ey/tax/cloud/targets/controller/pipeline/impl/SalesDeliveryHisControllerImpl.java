package com.ey.tax.cloud.targets.controller.pipeline.impl;

import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.dto.SearchDTO;
import com.ey.cn.tax.framework.entity.Search;
import com.ey.cn.tax.framework.utils.ConvertUtils;
import com.ey.cn.tax.framework.web.annotation.EyRestController;
import com.ey.cn.tax.framework.web.base.AbstractController;
import com.ey.tax.cloud.targets.controller.pipeline.SalesDeliveryHisController;
import com.ey.tax.cloud.targets.dto.pipeline.SalesDeliveryHisAddDTO;
import com.ey.tax.cloud.targets.dto.pipeline.SalesDeliveryHisBatchUpdateDTO;
import com.ey.tax.cloud.targets.dto.pipeline.SalesDeliveryHisDTO;
import com.ey.tax.cloud.targets.dto.pipeline.SalesDeliveryHisDeleteByIdDTO;
import com.ey.tax.cloud.targets.dto.pipeline.SalesDeliveryHisDeleteByIdListDTO;
import com.ey.tax.cloud.targets.dto.pipeline.SalesDeliveryHisQueryByIdDTO;
import com.ey.tax.cloud.targets.dto.pipeline.SalesDeliveryHisQueryByIdListDTO;
import com.ey.tax.cloud.targets.dto.pipeline.SalesDeliveryHisQueryDTO;
import com.ey.tax.cloud.targets.dto.pipeline.SalesDeliveryHisQueryPageDTO;
import com.ey.tax.cloud.targets.dto.pipeline.SalesDeliveryHisRepDTO;
import com.ey.tax.cloud.targets.dto.pipeline.SalesDeliveryHisUpdateByIdDTO;
import com.ey.tax.cloud.targets.entity.pipeline.SalesDeliveryHis;
import com.ey.tax.cloud.targets.service.pipeline.SalesDeliveryHisService;
import java.util.List;
import java.util.Map;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-01-16 15:29:03
 * 
 */
@EyRestController(path ="/v1/salesDeliveryHis")
public class SalesDeliveryHisControllerImpl extends AbstractController<SalesDeliveryHisService, SalesDeliveryHisDTO, SalesDeliveryHis> implements SalesDeliveryHisController {

    /**
     * add SalesDeliveryHis from http
     */
    @Override
    public ResponseDTO<Void> add(SalesDeliveryHisAddDTO dto) {
        SalesDeliveryHis entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        service.save(entity);
        return ResponseDTO.success();
    }

    /**
     * addList SalesDeliveryHis from http
     */
    @Override
    public ResponseDTO<Void> addList(List<SalesDeliveryHisAddDTO> dtos) {
        List<SalesDeliveryHis> entitys = ConvertUtils.convertDTOList2EntityList(dtos, entityClass);
        service.save(entitys);
        return ResponseDTO.success();
    }

    /**
     * delete SalesDeliveryHis from http
     */
    @Override
    public ResponseDTO<Void> deleteById(SalesDeliveryHisDeleteByIdDTO dto) {
        service.deleteById(dto.getId());
        return ResponseDTO.success();
    }

    /**
     * deleteList SalesDeliveryHis from http
     */
    @Override
    public ResponseDTO<Void> deleteByIdList(SalesDeliveryHisDeleteByIdListDTO dto) {
        getService().deleteByIds(dto.getIds());
        return ResponseDTO.success();
    }

    /**
     * update SalesDeliveryHis from http
     */
    @Override
    public ResponseDTO<Void> updateById(SalesDeliveryHisUpdateByIdDTO dto) {
        SalesDeliveryHis entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        getService().update(entity);
        return ResponseDTO.success();
    }

    /**
     * updateBatch SalesDeliveryHis from http
     */
    @Override
    public ResponseDTO<Void> updateBatch(List<SalesDeliveryHisBatchUpdateDTO> dtos) {
        List<SalesDeliveryHis> entities = ConvertUtils.convertDTOList2EntityList(dtos, entityClass);
        getService().updateBatchByIds(entities);
        return ResponseDTO.success();
    }

    /**
     * query SalesDeliveryHis from http
     */
    @Override
    public ResponseDTO<SalesDeliveryHisRepDTO> queryById(SalesDeliveryHisQueryByIdDTO dto) {
        SalesDeliveryHis entity = getService().queryById(dto.getId());
        SalesDeliveryHisRepDTO rDTO = ConvertUtils.convertEntity2DTO(entity, SalesDeliveryHisRepDTO.class);
        return ResponseDTO.success(rDTO);
    }

    /**
     * queryByIdList SalesDeliveryHis from http
     */
    @Override
    public ResponseDTO<List<SalesDeliveryHisRepDTO>> queryByIdList(SalesDeliveryHisQueryByIdListDTO dto) {
        List<SalesDeliveryHis> entities = getService().queryByIds(dto.getIds());
        return ResponseDTO.success(ConvertUtils.convertEntityList2DTOList(entities, SalesDeliveryHisRepDTO.class));
    }

    /**
     * queryList SalesDeliveryHis from http
     */
    @Override
    public ResponseDTO<List<SalesDeliveryHisRepDTO>> queryList(SalesDeliveryHisQueryDTO dto) {
        SalesDeliveryHis entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        List<SalesDeliveryHis> entities = getService().queryByPara(entity);
        return ResponseDTO.success(ConvertUtils.convertEntityList2DTOList(entities, SalesDeliveryHisRepDTO.class));
    }

    /**
     * Page SalesDeliveryHis from http
     */
    @Override
    public ResponseDTO<SearchDTO<SalesDeliveryHisRepDTO>> queryPage(SearchDTO<SalesDeliveryHisQueryPageDTO> searchDTO) {
        Search<SalesDeliveryHis> search = ConvertUtils.convertSearchDTO2Search(searchDTO, entityClass);
        getService().queryPageByPara(search);
        return ResponseDTO.success(ConvertUtils.convertSearch2SearchDTO(search, SalesDeliveryHisRepDTO.class));
    }

    /**
     * Count SalesDeliveryHis from http
     */
    @Override
    public ResponseDTO<Long> queryCount(SalesDeliveryHisQueryDTO dto) {
        SalesDeliveryHis entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        Long count = getService().queryCountByPara(entity);
        return ResponseDTO.success(count);
    }

    /**
     * One SalesDeliveryHis from http
     */
    @Override
    public ResponseDTO<SalesDeliveryHisRepDTO> queryOne(SalesDeliveryHisQueryDTO dto) {
        SalesDeliveryHis qEntity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        SalesDeliveryHis rEntity = getService().queryOneByPara(qEntity);
        SalesDeliveryHisRepDTO rDTO = ConvertUtils.convertEntity2DTO(rEntity, SalesDeliveryHisRepDTO.class);
        return ResponseDTO.success(rDTO);
    }

    /**
     * exist SalesDeliveryHis from http
     */
    @Override
    public ResponseDTO<Boolean> exist(SalesDeliveryHisQueryDTO dto) {
        boolean resullt = getService().existByPara(ConvertUtils.convertDTO2Entity(dto, entityClass));
        return ResponseDTO.success(resullt);
    }
}