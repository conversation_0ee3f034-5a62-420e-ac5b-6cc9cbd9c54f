package com.ey.tax.cloud.targets.controller.feign;

import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.web.annotation.EyPostMapping;
import com.ey.tax.cloud.targetapi.dto.TargetUserDTO;
import com.ey.tax.cloud.targetapi.request.TargetUserFindByKeywordRequest;
import com.ey.tax.cloud.targetapi.request.TargetUserFindByLevelsRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * @date 2023-09-26 15:09:02
 */
@Tag(name = "Target用户-Feign")
public interface FeignTargetUserController {
    /**
     * <p>
     * 根据职级查询用户数据
     * </p>
     *
     * @param request
     * @return com.ey.cn.tax.framework.dto.ResponseDTO<java.util.List < com.ey.tax.cloud.targetapi.dto.TargetUserDTO>>
     * <AUTHOR>
     * @date 18:16 2023/9/26
     */
    @EyPostMapping("/findByLevels")
    @Operation(summary = "根据职级查询用户数据", description = "根据职级查询用户数据")
    ResponseDTO<List<TargetUserDTO>> findByLevels(@RequestBody TargetUserFindByLevelsRequest request);

    /**
     * <p>
     * 根据关键字查询用户数据
     * </p>
     *
     * @param request
     * @return com.ey.cn.tax.framework.dto.ResponseDTO<java.util.List < com.ey.tax.cloud.targetapi.dto.TargetUserDTO>>
     * <AUTHOR> Zhang
     * @date 18:17 2023/9/26
     */
    @EyPostMapping("/findByKeyword")
    @Operation(summary = "根据关键字查询用户数据", description = "根据关键字查询用户数据")
    ResponseDTO<List<TargetUserDTO>> findByKeyword(@RequestBody TargetUserFindByKeywordRequest request);
}
