package com.ey.tax.cloud.targets.service.resource.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ey.cn.tax.framework.context.EyCommonContextHolder;
import com.ey.cn.tax.framework.context.EyUserContextHolder;
import com.ey.cn.tax.framework.entity.ICustomBaseEntity;
import com.ey.cn.tax.framework.entity.Search;
import com.ey.cn.tax.framework.mybatisplus.core.mapper.IEyBaseMapper;
import com.ey.cn.tax.framework.mybatisplus.core.query.EyQueryWrapper;
import com.ey.cn.tax.framework.mybatisplus.core.toolkit.EyWrappers;
import com.ey.cn.tax.framework.mybatisplus.extension.service.AbstractService;
import com.ey.cn.tax.framework.mybatisplus.utils.PageUtils;
import com.ey.cn.tax.framework.utils.EmptyUtils;
import com.ey.cn.tax.framework.utils.GroovyUtils;
import com.ey.tax.cloud.targets.constant.TargetAuthConstants;
import com.ey.tax.cloud.targets.entity.auth.AuthGroupDetail;
import com.ey.tax.cloud.targets.entity.resource.MemoRevenue;
import com.ey.tax.cloud.targets.entity.resource.MemoRevenue;
import com.ey.tax.cloud.targets.entity.resource.Performance;
import com.ey.tax.cloud.targets.entity.user.UserAttribute;
import com.ey.tax.cloud.targets.mapper.resource.MemoRevenueMapper;
import com.ey.tax.cloud.targets.service.product.ProductService;
import com.ey.tax.cloud.targets.service.resource.MemoRevenueService;
import com.ey.tax.cloud.targets.service.user.UserAttributeService;
import com.ey.tax.cloud.targets.utils.CommonUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.collections.api.factory.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-01-17 14:35:34
 *
 */
@Service
public class MemoRevenueServiceImpl extends AbstractService<MemoRevenueMapper, MemoRevenue> implements MemoRevenueService {

    @Autowired
    private ProductService productService;
    @Autowired
    private UserAttributeService userAttributeService;

    @Transactional(
            readOnly = true
    )
    @Override
    public Search<MemoRevenue> queryPageByPara(Search<MemoRevenue> search) {
        this.logger.debug("queryPageByPara {}:{}", this.getEntitySimpleName(), search);
        ICustomBaseEntity queryParams = (ICustomBaseEntity)search.getQueryParams();
        EyQueryWrapper<ICustomBaseEntity> queryWrapper = EyWrappers.query(queryParams);
        if (queryParams.getQueryExtension() == null) {
            queryWrapper.extension(search.getQueryExtension());
        }

        if (!queryWrapper.isOrdered()) {
            queryWrapper.orderByDesc("create_time");
        }
        if(EmptyUtils.isNotEmpty(search.getQueryParams().getProductList())){
            queryWrapper.in("products",search.getQueryParams().getProductList());
        }
        if(EmptyUtils.isNotEmpty(search.getQueryParams().getClientHQList())){
            queryWrapper.in("client_hq",search.getQueryParams().getClientHQList());
        }
        if(EmptyUtils.isNotEmpty(search.getQueryParams().getClientTypeList())){
            queryWrapper.in("client_type",search.getQueryParams().getClientTypeList());
        }
       if(EmptyUtils.isNotEmpty(search.getQueryParams().getUserIdList())){
           //tal_id或者em_id或者ep_id包含在userIdList中
           queryWrapper
                   .nested(i -> i.in("tal_id", search.getQueryParams().getUserIdList())
                           .or()
                           .in("em_id", search.getQueryParams().getUserIdList())
                           .or()
                           .in("ep_id", search.getQueryParams().getUserIdList()));
        }
        IPage<MemoRevenue> iPage = ((IEyBaseMapper)this.baseMapper).selectPage((Page) GroovyUtils.convertTo(Page.class, search.getPage(), true, new String[0]), queryWrapper);
        List<MemoRevenue> records = iPage.getRecords();
        search.setEntities(records);
        if (CollectionUtils.isEmpty(records)) {
            search.getPage().setTotalCount(0L);
        } else {
            this.processUserName((Collection)records);
            search.getPage().setTotalCount(iPage.getTotal());
        }

        return search;
    }

    @Override
    public Search<MemoRevenue> queryPageByParaAuth(Search<MemoRevenue> search) {
        this.logger.debug("queryPageByPara {}:{}", this.getEntitySimpleName(), search);
        String usrId = EyUserContextHolder.get().getAuthUserId();
        List<List<AuthGroupDetail>> list  = null;
        //范围内权限List
        if(EyCommonContextHolder.get(TargetAuthConstants.KEY_TARGET_AUTH_RANGE_LIST).isPresent()){
            list = (List<List<AuthGroupDetail>>) EyCommonContextHolder.get(TargetAuthConstants.KEY_TARGET_AUTH_RANGE_LIST).get();
        }
        //是否仅自己
        boolean isSelf = false;
        if(EyCommonContextHolder.get(TargetAuthConstants.KEY_TARGET_AUTH_IS_SLEF).isPresent()){
            isSelf = ((Boolean) EyCommonContextHolder.get(TargetAuthConstants.KEY_TARGET_AUTH_IS_SLEF).get());
        }
        QueryWrapper<MemoRevenue> queryWrapper = Wrappers.query(search.getQueryParams());
        queryWrapper = processSearchConditon(search.getQueryParams(),queryWrapper);
        //排序再加一个QueryWrapper
        EyQueryWrapper<MemoRevenue> order = new EyQueryWrapper<>();
        if(EmptyUtils.isNotEmpty(search.getQueryExtension())&&EmptyUtils.isNotEmpty(search.getQueryExtension().getSorts())){
            search.getQueryExtension().getSorts().forEach(orders->{
                if(orders.getProperty().equals("engManagerName")){
                    orders.setProperty("eng_manager_id");
                }
                else if(orders.getProperty().equals("pursuitLeaderName")){
                    orders.setProperty("pursuit_leader_id");
                }
                else if(orders.getProperty().equals("opprPartnerName")){
                    orders.setProperty("oppr_partner_id");
                }else {
                    orders.setProperty(CommonUtils.toSnakeCase(orders.getProperty()));
                }

            });
            order.extension(search.getQueryExtension());
        }else {
            order.orderByDesc("create_time");
        }
        UserAttribute user = new UserAttribute();
        user.setUserId(usrId);
        user.setFiscalYear(search.getQueryParams().getFiscalYear());
        user = userAttributeService.queryOneByPara(user);
        IPage<MemoRevenue> iPage = baseMapper.selectPageForAuth(PageUtils.convertPageToIPage(search.getPage()), queryWrapper,usrId,user,list,isSelf,order);
        List<MemoRevenue> records = iPage.getRecords();
        search.setEntities(records);
        if (CollectionUtils.isEmpty(records)) {
            search.getPage().setTotalCount(0L);
        } else {
            this.processUserName((Collection)records);
            search.getPage().setTotalCount(iPage.getTotal());
        }

        return search;
    }

    @Override
    public List<MemoRevenue> selectTarasMemoRevenue(String fiscalYear) {
        return baseMapper.selectTarasMemoRevenue(fiscalYear);
    }


    private QueryWrapper<MemoRevenue> processSearchConditon(MemoRevenue performance, QueryWrapper<MemoRevenue> queryWrapper) {
        queryWrapper.eq("is_del",0);
        if(EmptyUtils.isNotEmpty(performance.getUserId())){
            queryWrapper.eq("user_id",performance.getUserId());
        }
        if(EmptyUtils.isNotEmpty(performance.getUserIdList())){
            queryWrapper
                    .nested(i -> i.in("tal_id", performance.getUserIdList())
                            .or()
                            .in("em_id", performance.getUserIdList())
                            .or()
                            .in("ep_id", performance.getUserIdList()));
        }
        if(EmptyUtils.isNotEmpty(performance.getFiscalYear())){
            queryWrapper.eq("fiscal_year",performance.getFiscalYear());
        }
        if(EmptyUtils.isNotEmpty(performance.getProductList())){
            queryWrapper.in("products",performance.getProductList());
        }
        //products
        if(EmptyUtils.isNotEmpty(performance.getProducts())){
            queryWrapper.eq("products",performance.getProducts());
        }
        if(EmptyUtils.isNotEmpty(performance.getClientHQList())){
            queryWrapper.in("client_hq",performance.getClientHQList());
        }
        if(EmptyUtils.isNotEmpty(performance.getClientTypeList())){
            queryWrapper.in("client_type",performance.getClientTypeList());
        }
        //client
        if(EmptyUtils.isNotEmpty(performance.getClient())){
            queryWrapper.apply("client ilike '%"+performance.getClient()+"%'");
        }
        return queryWrapper;
    }



    @Override
    protected Collection<MemoRevenue> processUserName(Collection<MemoRevenue> entities) {
        if (CollectionUtils.isNotEmpty(entities)) {
            Set<String> ids = Sets.mutable.empty();
            for (MemoRevenue t : entities) {
                String createUid = t.getCreateUid();
                if (StringUtils.isNotBlank(createUid)) {
                    ids.add(createUid);
                }
                String updateUid = t.getUpdateUid();
                if (StringUtils.isNotBlank(updateUid)) {
                    ids.add(updateUid);
                }
            }
            Map<String, String> userMap = getUserNamesByIds(ids);
            for (MemoRevenue t : entities) {
                String createUid = t.getCreateUid();
                if (StringUtils.isNotBlank(createUid)) {
                    if(EmptyUtils.isNotEmpty(userMap.get(createUid))){
                        t.setCreateUserName(userMap.get(createUid).split("@")[0]);
                    }

                }
                String updateUid = t.getUpdateUid();
                if (StringUtils.isNotBlank(updateUid)) {
                    if(EmptyUtils.isNotEmpty(userMap.get(updateUid))){
                        t.setUpdateUserName(userMap.get(updateUid).split("@")[0]);
                    }
                }
            }
            return entities;
        } else {
            return Collections.emptyList();
        }
    }
}
