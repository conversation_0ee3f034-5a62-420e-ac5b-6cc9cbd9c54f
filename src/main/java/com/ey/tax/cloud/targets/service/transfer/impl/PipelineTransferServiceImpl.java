package com.ey.tax.cloud.targets.service.transfer.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.ey.cn.tax.framework.entity.Page;
import com.ey.cn.tax.framework.entity.Search;
import com.ey.cn.tax.framework.query.Direction;
import com.ey.cn.tax.framework.query.QueryExtension;
import com.ey.cn.tax.framework.query.StringSort;
import com.ey.cn.tax.framework.utils.DataUtils;
import com.ey.cn.tax.framework.utils.EmptyUtils;
import com.ey.cn.tax.framework.utils.JacksonUtils;
import com.ey.cn.tax.lite.impl.port.input.controller.LiteDatasetController;
import com.ey.cn.tax.lite.impl.port.input.dto.LiteDatasetExecutionDTO;
import com.ey.tax.cloud.targets.config.JsonbConverter;
import com.ey.tax.cloud.targets.constant.BooleanConstants;
import com.ey.tax.cloud.targets.constant.BusFaultConstants;
import com.ey.tax.cloud.targets.constant.DictConstants;
import com.ey.tax.cloud.targets.constant.PipelineConstants;
import com.ey.tax.cloud.targets.entity.data.Dictionary;
import com.ey.tax.cloud.targets.entity.data.DictionaryItem;
import com.ey.tax.cloud.targets.entity.data.ExchangeRate;
import com.ey.tax.cloud.targets.entity.data.FiscalCalender;
import com.ey.tax.cloud.targets.entity.pipeline.*;
import com.ey.tax.cloud.targets.entity.product.Product;
import com.ey.tax.cloud.targets.entity.transfer.SyncOpportunity;
import com.ey.tax.cloud.targets.entity.transfer.SyncOpportunityMiddle;
import com.ey.tax.cloud.targets.entity.transfer.SyncTaxClient;
import com.ey.tax.cloud.targets.entity.user.UserAttribute;
import com.ey.tax.cloud.targets.entity.user.UserGroup;
import com.ey.tax.cloud.targets.service.data.DictionaryItemService;
import com.ey.tax.cloud.targets.service.data.DictionaryService;
import com.ey.tax.cloud.targets.service.data.ExchangeRateService;
import com.ey.tax.cloud.targets.service.data.FiscalCalenderService;
import com.ey.tax.cloud.targets.service.pipeline.*;
import com.ey.tax.cloud.targets.service.product.ProductService;
import com.ey.tax.cloud.targets.service.transfer.DataTransferService;
import com.ey.tax.cloud.targets.service.transfer.SyncOpportunityMiddleService;
import com.ey.tax.cloud.targets.service.transfer.SyncOpportunityService;
import com.ey.tax.cloud.targets.service.transfer.SyncTaxClientService;
import com.ey.tax.cloud.targets.service.user.UserAttributeService;
import com.ey.tax.cloud.targets.service.user.UserGroupService;
import com.ey.tax.cloud.targets.utils.CommonUtils;
import com.ey.tax.cloud.targets.utils.ExceptionUtils;
import com.xxl.job.core.context.XxlJobHelper;
import org.jose4j.json.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Array;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("pipeline")

public class PipelineTransferServiceImpl extends AbstractTransferService implements DataTransferService {

    private static final int PAGE_SIZE = 5000;

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private SyncOpportunityService syncOpportunityService;

    @Autowired
    private PipelineForTransferService pipelineForTransferService;

    @Autowired
    private EngagementService engagementService;

    @Autowired
    private ProductService productService;

    @Autowired
    private SyncTaxClientService syncTaxClientService;

    @Autowired
    private ExchangeRateService exchangeRateService;

    @Autowired
    private UserAttributeService userAttributeService;

    @Autowired
    private UserGroupService userGroupService;

    @Autowired
    private FiscalCalenderService fiscalCalenderService;

    @Autowired
    private SalesDeliveryService salesDeliveryService;

    @Autowired
    private SalesDeliveryDataService salesDeliveryDataService;
    @Autowired
    private PipelineService pipelineService;

    @Autowired
    private SyncOpportunityMiddleService syncOpportunityMiddleService;

    @Autowired
    private LiteDatasetController liteDatasetController;

    private final static String TYPE_EP = "ep";

    private final static String TYPE_EM = "em";

    private final static String TYPE_LEADER = "leader";

    private final static Integer TYPE_INTEGER_EP = 1;

    private final static Integer TYPE_INTEGER_EM = 2;

    private String RANK_ID_EP;

    private String RANK_ID_ED;

    private String RANK_ID_EM;

    private int count = 0;

    private UserAttribute defaultEmAttribute;

    private UserAttribute defaultEpAttribute;

    private Map<String, DictionaryItem> currencyObjectMap;

    private Map<String, FiscalCalender> fiscalCalenderMap;

    private Map<String, Product> disProductMap = new HashMap<>();
    private Map<String, String> productMapping = new HashMap<>();

    private String pipelineCode;

    private String clientId;

    private List<String> pipelineConfigList = new ArrayList<>();
    @Autowired
    private PipelineConfigService pipelineConfigService;

    private Map<String, String> errorCache = new HashMap<>();
    @Autowired
    private DictionaryService dictionaryService;
    @Autowired
    private DictionaryItemService dictionaryItemService;

    /**
     * pipeline转换逻辑:
     * 1.tiger会定时推数据到中间表.
     * 2.target定时将数据读取后进行字段转换,然后插入到pipeline主表
     * 3.转换过程中,主要涉及到数据字典转换和字段转换,比如json字典
     * <p>
     * 特殊逻辑:pipeline存量在10万以内,转换逻辑为加载本地pipeline到内存,分页查询tiger推的数据.
     * 1.分页加载pipeline,每页5000条加载本地pipeline到内存,以map形式缓存,key为code.
     * 2.分页查询tiger推的pipeline,每页5000条.
     * 3.遍历tiger的pipeline的每一页,使用pipeline的code从map中remove本地pipeline.
     * 4.判断pipeline的状态,进行新增,更新,等操作.
     * 5.tiger的pipeline全都处理一遍后,如果map中有剩余数据,则进行删除逻辑.
     * 6.将非历史迁移数据(根据历史数据标记为确定),进行删除操作.
     */
    @Override
    public void process(Map<String, String> params) {
        defaultEmAttribute = null;
        defaultEpAttribute = null;
        pipelineCode = null;
        clientId = null;
        fiscalCalenderMap = null;
        errorCache.clear();
        if(EmptyUtils.isNotEmpty(params)){
            pipelineCode = params.get("pipelineCode");
            if(EmptyUtils.isNotEmpty(pipelineCode)){
                SyncOpportunity qSyncOpportunity = new SyncOpportunity();
                qSyncOpportunity.setOpprCode(pipelineCode);
                SyncOpportunity rSyncOpportunity = syncOpportunityService.queryOneByPara(qSyncOpportunity);
                if(EmptyUtils.isNotEmpty(rSyncOpportunity)){
                    clientId = rSyncOpportunity.getAccountId();
                }
                if(EmptyUtils.isEmpty(clientId)){
                    clientId = "null";
                }
            }
        }
        count = 0;
        //加载rank
        UserGroup qUserGroup = new UserGroup();
        qUserGroup.setCode("EP");
        qUserGroup.setType("group");
        UserGroup epUserGroup = userGroupService.queryOneByPara(qUserGroup);
        RANK_ID_EP = epUserGroup.getId();
        XxlJobHelper.log("load ep id {}", RANK_ID_EP);
        qUserGroup.setCode("ED");
        qUserGroup.setType("group");
        UserGroup edUserGroup = userGroupService.queryOneByPara(qUserGroup);
        RANK_ID_ED = edUserGroup.getId();
        XxlJobHelper.log("load ed id {}", RANK_ID_ED);
        qUserGroup.setCode("EM");
        qUserGroup.setType("group");
        UserGroup emUserGroup = userGroupService.queryOneByPara(qUserGroup);
        RANK_ID_EM = emUserGroup.getId();
        XxlJobHelper.log("load em id {}", RANK_ID_EM);
        //加载数据字典
        List<String> dictCodes = new ArrayList<>();
        dictCodes.add(DictConstants.TARGET_DICT_CODE_SECTOR);
        dictCodes.add(DictConstants.TARGET_DICT_CODE_CLIENT_TYPE);
        dictCodes.add(DictConstants.TARGET_DICT_CODE_STOCK_MARKET);
        dictCodes.add(DictConstants.TARGET_DICT_CODE_OFFICE_LOCATION);
        dictCodes.add(DictConstants.TARGET_DICT_CODE_CHANNEL);
        dictCodes.add(DictConstants.TARGET_DICT_CODE_CUSTOMER_CLASS);
        dictCodes.add(DictConstants.TARGET_DICT_CODE_LEAD_SOURCE);
        dictCodes.add(DictConstants.TARGET_DICT_CODE_IS_EIF_SELF);
        dictCodes.add(DictConstants.TARGET_DICT_CODE_RECURRING);
        dictCodes.add(DictConstants.TARGET_DICT_CODE_WON_REASON);
        dictCodes.add(DictConstants.TARGET_DICT_CODE_CURRENCY);
        dictCodes.add(DictConstants.TARGET_DICT_CODE_YES_NO);
        dictCodes.add(DictConstants.TARGET_DICT_CODE_PRIME_OFFICE);
        dictCodes.add("outbound");
        dictCodes.add("source");
        dictCodes.add("recurring");
        Map<String, Map<String, String>> dictMap = new HashMap<>();
        for (String dictCode : dictCodes) {
            Map<String, String> dictMapping = this.getDictionaryMapping(dictCode);
            dictMap.put(dictCode, dictMapping);
        }
        currencyObjectMap = this.getDictionaryObjectMapping(DictConstants.TARGET_DICT_CODE_CURRENCY);
        XxlJobHelper.log("load dict");
        //加载财年
        FiscalCalender qFiscalCalender = new FiscalCalender();
        List<FiscalCalender> fiscalCalenderList = fiscalCalenderService.queryByPara(qFiscalCalender);
        fiscalCalenderMap = new HashMap<>();
        DateTimeFormatter fateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MMM-dd");
        for(FiscalCalender fiscalCalender:fiscalCalenderList){
            String key = fiscalCalender.getCalendarDate().format(fateTimeFormatter);;
            fiscalCalenderMap.put(key, fiscalCalender);
        }
        PipelineConfig pipelineConfig = new PipelineConfig();
        pipelineConfig.setTableCode(PipelineConstants.PIPELINE_TABLE_CODE);
        pipelineConfig.setFiscalYear(fiscalCalenderService.getCurrentFiscalCalender());
        List<PipelineConfig> configs = pipelineConfigService.queryByPara(pipelineConfig);
        //configs取isBlank为1的字段
        pipelineConfigList = configs.stream().filter(c->EmptyUtils.isNotEmpty(c.getIsBlank())).filter(pipelineConfig1 -> pipelineConfig1.getIsBlank().equals(BooleanConstants.BOOLEAN_1)).map(PipelineConfig::getColumnName).collect(Collectors.toList());

        XxlJobHelper.log("load fiscal calender");
        //加载汇率
        String currentFy = fiscalCalenderService.getCurrentFiscalCalender();
        ExchangeRate qExchangeRate = new ExchangeRate();
        qExchangeRate.setFiscalYear(currentFy);
        List<ExchangeRate> exchangeRateList = exchangeRateService.queryByPara(qExchangeRate);
        Map<String, ExchangeRate> exchangeRateMap = new HashMap<>();
        for (ExchangeRate exchangeRate : exchangeRateList) {
            exchangeRateMap.put(exchangeRate.getExchangeCurrency(), exchangeRate);
        }
        XxlJobHelper.log("load exchange");
        //加载产品
        Product p = new Product();
        p.setStatus(1);
        List<Product> productList = productService.queryByPara(p);
        //过滤level0为空的
        productList = productList.stream().filter(product -> EmptyUtils.isNotEmpty(product.getLevel0())).collect(Collectors.toList());
        Map<String, Product> productMap = new HashMap<>();

        for (Product product : productList) {
            productMap.put(product.getProductCode()+product.getFiscalYear(), product);
        }
        //查询失效产品
        Product disProduct = new Product();
        disProduct.setStatus(0);
        List<Product> disProductList = productService.queryByPara(disProduct);
        disProductMap = new HashMap<>();
        for (Product product : disProductList) {
            disProductMap.put(product.getProductCode()+product.getFiscalYear(), product);
        }
        productMapping = new HashMap<>();
        productMapping = this.getDictionaryItem(DictConstants.TIGER_DICT_CODE_DELETE_PRODUCT_MAPPING);
        XxlJobHelper.log("load product");
        //加载用户信息
        UserAttribute qUserAttribute = new UserAttribute();
        qUserAttribute.setFiscalYear(currentFy);
        qUserAttribute.setStatus(BooleanConstants.BOOLEAN_ON);
        List<UserAttribute> userAttributeList = userAttributeService.queryByPara(qUserAttribute);
        Map<String, UserAttribute> userAttributeMap = new HashMap<>();

        for (UserAttribute userAttribute : userAttributeList) {
            userAttributeMap.put(userAttribute.getGpn(), userAttribute);
            if (BooleanConstants.BOOLEAN_ON.equals(userAttribute.getIsSecretary())) {
                if(userAttribute.getGroupId().equals(RANK_ID_EP)){
                    defaultEpAttribute = userAttribute;
                }else if(userAttribute.getGroupId().equals(RANK_ID_EM)){
                    defaultEmAttribute = userAttribute;
                }
            }
        }
        if(EmptyUtils.isEmpty(defaultEpAttribute)){
            XxlJobHelper.log("default ep not found");
            return;
        }
        if(EmptyUtils.isEmpty(defaultEmAttribute)){
            XxlJobHelper.log("default ep not found");
            return;
        }
        XxlJobHelper.log("load user");
        List<PipelineForTransfer> insertPipeline = new ArrayList<>();
        List<Engagement> insertEngagement = new ArrayList<>();
        List<PipelineForTransfer> updatePipeline = new ArrayList<>();
        List<SalesDelivery> insertSalesDelivery = new ArrayList<>();
        List<String> deleteSalesDeliveryPipelineIds = new ArrayList<>();
        Map<String, PipelineForTransfer> pipelineMap = loadLocalPipeline();
        XxlJobHelper.log("load old pipeline: " + pipelineMap.size());

        Map<String, SyncTaxClient> syncTaxClientMap = loadSyncTaxClient();
        XxlJobHelper.log("load client: " + syncTaxClientMap.size());
        if(syncTaxClientMap.size() <= 0 && EmptyUtils.isEmpty(pipelineCode)){
            logger.error("client count is 0");
            submitBusinessFault(BusFaultConstants.BUS_TYPE_PIPELINE_TRANSF, "client count is 0");
            return;
        }

        Search<SyncOpportunity> search = new Search<>();
        SyncOpportunity syncOpportunity = new SyncOpportunity();
        syncOpportunity.setOpprCode(pipelineCode);
        search.setQueryParams(syncOpportunity);
        Page page = new Page();
        page.setCurrentPage(1);
        page.setPageSize(PAGE_SIZE);
        QueryExtension queryExtension = new QueryExtension();
        queryExtension.setPage(page);
        List<StringSort> sorts = new ArrayList<>();
        StringSort stringSort= new StringSort();
        stringSort.setProperty("oppr_code");
        stringSort.setDirection(Direction.ASC);
        sorts.add(stringSort);
        queryExtension.setSorts(sorts);
        search.setQueryExtension(queryExtension);
        Search<SyncOpportunity> rSearch = syncOpportunityService.queryPageByPara(search);

        long totalCount = rSearch.getPage().getTotalCount();
        if(totalCount <= 0){
            logger.error("Opportunity count is 0");
            submitBusinessFault(BusFaultConstants.BUS_TYPE_PIPELINE_TRANSF, "Opportunity count is 0");
            return;
        }
        LiteDatasetExecutionDTO delete = new LiteDatasetExecutionDTO();
        delete.setCode("DELETE_MIDDLE");
        liteDatasetController.execute(delete);

        XxlJobHelper.log("delete all engagement start");
        engagementService.realDeleteByPara(new Engagement());
        XxlJobHelper.log("delete all engagement end");

        long currentCount = rSearch.getPage().getPageSize();
        if (EmptyUtils.isNotEmpty(rSearch.getEntities())) {
            XxlJobHelper.log("sync opportunity : " + JSONObject.toJSONString(((List<SyncOpportunity>) rSearch.getEntities()).get(0)));
            transfer(pipelineMap, (List<SyncOpportunity>) rSearch.getEntities(), insertPipeline,
                    updatePipeline, dictMap, userAttributeMap, productMap,
                    exchangeRateMap, insertSalesDelivery,
                    deleteSalesDeliveryPipelineIds, insertEngagement,
                    syncTaxClientMap);
            List< SyncOpportunityMiddle> syncOpportunityMiddleList = new ArrayList<>();
            for (SyncOpportunity syncOpportunity1 : rSearch.getEntities()) {
                SyncOpportunityMiddle syncOpportunityMiddle = new SyncOpportunityMiddle();
                JsonbConverter.copyPropertiesWithAutomaticJsonbHandling(syncOpportunity1, syncOpportunityMiddle);
                syncOpportunityMiddle.setId(null);
                syncOpportunityMiddleList.add(syncOpportunityMiddle);
            }
            try {
                syncOpportunityMiddleService.save(syncOpportunityMiddleList);
            }catch (Exception e){
                XxlJobHelper.log(e.getMessage());
            }
            if(currentCount > totalCount){
                currentCount = totalCount;
            }
            XxlJobHelper.log("process new pipeline status: [" + currentCount + "/" + totalCount + "]");
        }

        while (hasNext(rSearch.getPage())) {
            page.setCurrentPage(page.getCurrentPage() + 1);
            XxlJobHelper.log("process new pipeline page: " + page.getCurrentPage());
            rSearch = syncOpportunityService.queryPageByPara(search);
            if (EmptyUtils.isNotEmpty(rSearch.getEntities())) {
                transfer(pipelineMap, (List<SyncOpportunity>) rSearch.getEntities(), insertPipeline,
                        updatePipeline, dictMap, userAttributeMap, productMap,
                        exchangeRateMap, insertSalesDelivery,
                        deleteSalesDeliveryPipelineIds, insertEngagement,
                        syncTaxClientMap);
                currentCount = currentCount + rSearch.getPage().getPageSize();
                List< SyncOpportunityMiddle>  syncOpportunityMiddleList = new ArrayList<>();
                for (SyncOpportunity syncOpportunity1 : rSearch.getEntities()) {
                    SyncOpportunityMiddle syncOpportunityMiddle = new SyncOpportunityMiddle();
                    JsonbConverter.copyPropertiesWithAutomaticJsonbHandling(syncOpportunity1, syncOpportunityMiddle);
                    syncOpportunityMiddle.setId(null);
                    syncOpportunityMiddleList.add(syncOpportunityMiddle);
                }
                XxlJobHelper.log("page:"+rSearch.getPage().getCurrent());

                syncOpportunityMiddleService.save(syncOpportunityMiddleList);
                if(currentCount > totalCount){
                    currentCount = totalCount;
                }
                XxlJobHelper.log("process new pipeline status: [" + currentCount + "/" + totalCount + "]");
            }
        }

        XxlJobHelper.log("pipeline process finish");
        XxlJobHelper.log("flush data");
        this.flush(insertPipeline, updatePipeline, insertSalesDelivery, deleteSalesDeliveryPipelineIds, insertEngagement);
        if (EmptyUtils.isNotEmpty(pipelineMap)) {
            List<String> ids = new ArrayList<>();
            for (PipelineForTransfer pipeline : pipelineMap.values()) {
                ids.add(pipeline.getId());
            }
            //logger.info("delete pipeline size: {}", ids.size());
            //pipelineService.deleteByIds(ids);
        }
        //数据同步完成后刷新data表
        XxlJobHelper.log("sync data");
        try {
            pipelineService.dealSame();
            salesDeliveryDataService.syncData();
        }catch (Exception e){
            logger.error("pipeline sync data error", e);
            XxlJobHelper.log("pipeline sync data error");
            String error = ExceptionUtils.getFullStackTrace(e);
            XxlJobHelper.log(error);
            submitBusinessFault(BusFaultConstants.BUS_TYPE_PIPELINE_TRANSF, error);
        }
        XxlJobHelper.log("job end");
    }

    private void transfer(Map<String, PipelineForTransfer> pipelineMap, List<SyncOpportunity> syncOpportunityList,
                          List<PipelineForTransfer> insertPipeline, List<PipelineForTransfer> updatePipeline,
                          Map<String, Map<String, String>> dictMap, Map<String, UserAttribute> userAttributeMap,
                          Map<String, Product> productMap, Map<String, ExchangeRate> exchangeRateMap,
                          List<SalesDelivery> insertSalesDelivery, List<String> deleteSalesDeliveryPipelineIds,
                          List<Engagement> insertEngagement, Map<String, SyncTaxClient> syncTaxClientMap) {
        for (SyncOpportunity syncOpportunity : syncOpportunityList) {
            count = count + 1;
            if(count % 100 == 0){
                XxlJobHelper.log("current count: " + count);
            }
            String opprCode = syncOpportunity.getOpprCode();
            PipelineForTransfer pipeline = pipelineMap.get(opprCode);
            if (EmptyUtils.isEmpty(pipeline)) {
                //新增
                PipelineForTransfer newPipeline = new PipelineForTransfer();
                newPipeline.setId(DataUtils.getUUID32()+"-tiger");
                List<SalesDelivery> newSalesDeliveryList = null;
                try {
                    newSalesDeliveryList = setFields(syncOpportunity, newPipeline, dictMap, userAttributeMap,
                            productMap, exchangeRateMap, insertEngagement, syncTaxClientMap);
                } catch (Exception e) {
                    e.printStackTrace();
                    //异常则部分成功
                    logger.error("process opportunity error id:{} code:{}", syncOpportunity.getId(), syncOpportunity.getOpprCode());
                    XxlJobHelper.log("process opportunity error id:{} code:{}", syncOpportunity.getId(), syncOpportunity.getOpprCode());
                    logger.error("exception detail", e);
                    XxlJobHelper.log(ExceptionUtils.getFullStackTrace(e));
                    continue;
                }
                if (EmptyUtils.isNotEmpty(newSalesDeliveryList)) {
                    insertPipeline(insertPipeline, newPipeline, insertSalesDelivery, newSalesDeliveryList, insertEngagement);
                }else{
                    insertEngagement.clear();
                }
            } else {
                //更新
                String status = pipeline.getStatus();
                String confirmStatus = pipeline.getConfirmStatus();
                if (PipelineConstants.PIPELINE_STATUS_WON.equals(status) &&
                        (PipelineConstants.APPROVE_STATUS_PROCESSED.equals(confirmStatus) ||
                                PipelineConstants.APPROVE_STATUS_AMENDMENT_SUBMITTED.equals(confirmStatus) ||
                                PipelineConstants.APPROVE_STATUS_RETURNED.equals(confirmStatus))) {
                    //1、Pipeline中，status=won且confirm status=processed的数据，不覆盖
                    //处理engCode中新增的人
                    List<SalesDelivery> newSalesDeliveryList = null;
                    List<Engagement> newEngagementList = new ArrayList<>();
                    try {
                        newSalesDeliveryList = newSalesDelivery(syncOpportunity, pipeline, userAttributeMap, newEngagementList);
                    } catch (Exception e) {
                        e.printStackTrace();
                        //异常则部分成功
                        logger.error("process opportunity id:{} code:{}", syncOpportunity.getId(), syncOpportunity.getOpprCode());
                        XxlJobHelper.log("process opportunity error id:{} code:{}", syncOpportunity.getId(), syncOpportunity.getOpprCode());
                        logger.error("exception detail", e);
                        XxlJobHelper.log(ExceptionUtils.getFullStackTrace(e));
                        continue;
                    }
                    if (EmptyUtils.isNotEmpty(newSalesDeliveryList)) {
                        XxlJobHelper.log("new sales opportunity id:{} code:{} size:{}", syncOpportunity.getId(), syncOpportunity.getOpprCode(), newSalesDeliveryList.size());
                        salesDeliveryService.save(newSalesDeliveryList);
                    }
                    //engagementService.realDeleteByPipelineId(pipeline.getId());
                    if (EmptyUtils.isNotEmpty(newEngagementList)) {
                        engagementService.save(newEngagementList);
                    }
                } else {
                    List<SalesDelivery> newSalesDeliveryList = null;
                    try {
                        newSalesDeliveryList = setFields(syncOpportunity, pipeline, dictMap, userAttributeMap,
                            productMap, exchangeRateMap, insertEngagement, syncTaxClientMap);
                    } catch (Exception e) {
                        e.printStackTrace();
                        //异常则部分成功
                        logger.error("process opportunity id:{} code:{}", syncOpportunity.getId(), syncOpportunity.getOpprCode());
                        XxlJobHelper.log("process opportunity error id:{} code:{}", syncOpportunity.getId(), syncOpportunity.getOpprCode());
                        logger.error("exception detail", e);
                        XxlJobHelper.log(ExceptionUtils.getFullStackTrace(e));
                        continue;
                    }
                    if(EmptyUtils.isNotEmpty(newSalesDeliveryList)){
                        deleteSalesDeliveryPipelineIds.add(pipeline.getId());
                        updatePipeline(updatePipeline, pipeline, insertSalesDelivery, newSalesDeliveryList, deleteSalesDeliveryPipelineIds, insertEngagement);
                    }
                }
            }
        }
    }

    private void buildEngagementList(List<Map> engInfoList,
                                     List<Engagement> newEngagementList,
                                     PipelineForTransfer pipeline,
                                     Map<String, UserAttribute> userAttributeMap){
        if(EmptyUtils.isEmpty(engInfoList)){
            return;
        }
        for (Map<String, Object> map : engInfoList) {
            Object oEngagementManagerGpn = map.get("engagement_manager_gpn");
            Object oEngagementPartnerGpn = map.get("engagement_partner_gpn");
            Object oEngCode = map.get("eng_code");
            if(EmptyUtils.isEmpty(oEngCode)){
                continue;
            }
            Object oEngagementName = map.get("engagement_name");
            Object oCreateDate = map.get("create_date");
            Object oEngagementStatus = map.get("engagement_status");
            Object oEffectiveDate = map.get("effective_date");
            Engagement newEngagement = new Engagement();
            newEngagement.setPipelineId(pipeline.getId());
            if(EmptyUtils.isNotEmpty(oEngagementName)){
                newEngagement.setEngagementName((String) oEngagementName);
            }
            if(EmptyUtils.isNotEmpty(oEngCode)){
                newEngagement.setEngCode((String) oEngCode);
            }
            if(EmptyUtils.isNotEmpty(oEngagementStatus)){
                newEngagement.setEngagementStatus((String) oEngagementStatus);
            }
            if(EmptyUtils.isNotEmpty(oEngagementManagerGpn)){
                newEngagement.setEngagementManagerGpn((String) oEngagementManagerGpn);
                UserAttribute engagementManagerUserAttribute = userAttributeMap.get((String)oEngagementManagerGpn);
                if (EmptyUtils.isNotEmpty(engagementManagerUserAttribute)) {
                    newEngagement.setEngagementManagerId(engagementManagerUserAttribute.getUserId());
                }
            }
            if(EmptyUtils.isNotEmpty(oEngagementPartnerGpn)){
                newEngagement.setEngagementPartnerGpn((String) oEngagementPartnerGpn);
                UserAttribute engagementPartnerUserAttribute = userAttributeMap.get((String)oEngagementPartnerGpn);
                if (EmptyUtils.isNotEmpty(engagementPartnerUserAttribute)) {
                    newEngagement.setEngagementPartnerId(engagementPartnerUserAttribute.getUserId());
                }
            }
            if(EmptyUtils.isNotEmpty(oCreateDate)){
                LocalDateTime localDate =
                        LocalDateTime.of(LocalDate.parse(oCreateDate.toString(), DateTimeFormatter.ofPattern("yyyy-MM-dd")), LocalTime.MIN);
                newEngagement.setEngCreateDate(localDate);
            }
            if(EmptyUtils.isNotEmpty(oEffectiveDate)){
                LocalDateTime localDate =
                        LocalDateTime.of(LocalDate.parse(oEffectiveDate.toString(), DateTimeFormatter.ofPattern("yyyy-MM-dd")), LocalTime.MIN);
                newEngagement.setEffectiveDate(localDate);
            }
            newEngagementList.add(newEngagement);
        }
    }

    private List<SalesDelivery> newSalesDelivery(SyncOpportunity syncOpportunity,
                                                 PipelineForTransfer pipeline,
                                                 Map<String, UserAttribute> userAttributeMap,
                                                 List<Engagement> newEngagementList) {
        Object oEngInfoJson = syncOpportunity.getEngInfoJson();
        List<Map> engInfoList = null;
        if (EmptyUtils.isEmpty(oEngInfoJson)) {
            return null;
        }
        String sEngInfoJson =  JSONObject.toJSONString(oEngInfoJson);
        try {
            engInfoList = JSON.parseArray(sEngInfoJson, Map.class);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (EmptyUtils.isEmpty(engInfoList)) {
            return null;
        }
        buildEngagementList(engInfoList, newEngagementList, pipeline, userAttributeMap);
        //查询pipeline的当前分费情况
        SalesDelivery qSalesDelivery = new SalesDelivery();
        qSalesDelivery.setPipelineId(pipeline.getId());
        List<SalesDelivery> salesDeliveryList = salesDeliveryService.queryByPara(qSalesDelivery);
        List<SalesDelivery> newSalesDeliveryList = new ArrayList<>();
        Map<String, SalesDelivery> salesDeliveryMap = new HashMap<>();
        if (EmptyUtils.isNotEmpty(salesDeliveryList)) {
            for (SalesDelivery salesDelivery : salesDeliveryList) {
                salesDeliveryMap.put(salesDelivery.getSalesDeliveryCredit(), salesDelivery);
            }
        }
        for (Map<String, Object> map : engInfoList) {
            Object oEngagementManagerGpn = map.get("engagement_manager_gpn");
            Object oEngagementPartnerGpn = map.get("engagement_partner_gpn");
            if (EmptyUtils.isNotEmpty(oEngagementManagerGpn)) {
                String engagementManagerGpn = (String) oEngagementManagerGpn;
                UserAttribute engagementManagerUserAttribute = userAttributeMap.get(engagementManagerGpn);
                if (EmptyUtils.isEmpty(engagementManagerUserAttribute)) {
                    //logger.error("gpn not found: {}", engagementManagerGpn);
                    XxlJobHelper.log("pipeline code: {} gpn not found: {}", syncOpportunity.getOpprCode(), engagementManagerGpn);
                    continue;
                }
                String userId = engagementManagerUserAttribute.getUserId();
                SalesDelivery managerSalesDelivery = salesDeliveryMap.get(userId);
                if (EmptyUtils.isEmpty(managerSalesDelivery)) {
                    SalesDelivery salesDelivery = new SalesDelivery();
                    salesDelivery.setPipelineId(pipeline.getId());
                    salesDelivery.setSalesDeliveryCredit(userId);
                    salesDelivery.setFiscalYear(pipeline.getWonFy());
                    salesDelivery.setSalesDeliveryCreditType(TYPE_INTEGER_EM);
                    salesDelivery.setSalesDeliveryCreditRatio(BigDecimal.ZERO);
                    salesDelivery.setSalesDeliveryCreditAmount(BigDecimal.ZERO);
                    salesDelivery.setSalesDeliveryCreditAmountUsd(BigDecimal.ZERO);
                    newSalesDeliveryList.add(salesDelivery);
                    salesDeliveryMap.put(userId, salesDelivery);
                }
            }
            if (EmptyUtils.isNotEmpty(oEngagementPartnerGpn)) {
                String engagementPartnerGpn = (String) oEngagementPartnerGpn;
                UserAttribute engagementPartnerUserAttribute = userAttributeMap.get(engagementPartnerGpn);
                if (EmptyUtils.isEmpty(engagementPartnerUserAttribute)) {
                    //logger.error("gpn not found: {}", engagementPartnerGpn);
                    XxlJobHelper.log("pipeline code: {} gpn not found: {}", syncOpportunity.getOpprCode(), engagementPartnerGpn);
                    continue;
                }
                String userId = engagementPartnerUserAttribute.getUserId();
                SalesDelivery partnerSalesDelivery = salesDeliveryMap.get(userId);
                if (EmptyUtils.isEmpty(partnerSalesDelivery)) {
                    SalesDelivery salesDelivery = new SalesDelivery();
                    salesDelivery.setPipelineId(pipeline.getId());
                    salesDelivery.setSalesDeliveryCredit(userId);
                    salesDelivery.setFiscalYear(pipeline.getWonFy());
                    salesDelivery.setSalesDeliveryCreditType(TYPE_INTEGER_EP);
                    salesDelivery.setSalesDeliveryCreditRatio(BigDecimal.ZERO);
                    salesDelivery.setSalesDeliveryCreditAmount(BigDecimal.ZERO);
                    salesDelivery.setSalesDeliveryCreditAmountUsd(BigDecimal.ZERO);
                    newSalesDeliveryList.add(salesDelivery);
                    salesDeliveryMap.put(userId, salesDelivery);
                }
            }
        }
        return newSalesDeliveryList;
    }

    private String transferDict(String dictCode, String key, Map<String, Map<String, String>> dictMap) {
        key = "tiger-" + key;
        Map<String, String> mapping = dictMap.get(dictCode);
        String result = mapping.get(key);
        if (EmptyUtils.isEmpty(result)) {
            logger.error("dictCode: {} key: {} not found", dictCode, key);
            XxlJobHelper.log("dictCode: {} key: {} not found", dictCode, key);
        }
        return result;
    }

    private List<SalesDelivery> setFields(SyncOpportunity syncOpportunity, PipelineForTransfer pipeline,
                                          Map<String, Map<String, String>> dictMap, Map<String,
                                          UserAttribute> userAttributeMap, Map<String, Product> productMap,
                                          Map<String, ExchangeRate> exchangeRateMap,
                                          List<Engagement> insertEngagement, Map<String, SyncTaxClient> syncTaxClientMap) {
        //Oppo code	pipeline_code	oppr_code
        String opprCode = syncOpportunity.getOpprCode();
        pipeline.setPipelineCode(opprCode);
        BigDecimal amount = syncOpportunity.getOpprAmount();
        if (EmptyUtils.isEmpty(opprCode)) {
            XxlJobHelper.log("opprCode: is null id: {}", syncOpportunity.getId());
            return null;
        }
        if (EmptyUtils.isEmpty(amount)) {
            amount = BigDecimal.ZERO;
        }
        DateTimeFormatter formatterr = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 使用parse方法将字符串转换为LocalDateTime
        LocalDateTime dateTime = LocalDateTime.parse(syncOpportunity.getCreateTimeNew(), formatterr);

        //createrTime
        pipeline.setTigerCreateTime(dateTime);
        //Client Name	client_name	client_name
        pipeline.setClientName(syncOpportunity.getClientName());
        //Client Code	client_code	client_id
        pipeline.setClientCode(syncOpportunity.getClientId());
        //Sector	industry_sector	client_sector_code
        String clientSectorCode = syncOpportunity.getClientSectorCode();
        if (EmptyUtils.isNotEmpty(clientSectorCode)) {
            pipeline.setIndustrySector(transferDict(DictConstants.TARGET_DICT_CODE_SECTOR, clientSectorCode, dictMap));
        }else{
            pipeline.setIndustrySector(null);
        }
        //	industry_sub_sector	client_subsector_code
        String clientSubsectorCode = syncOpportunity.getClientSubsectorCode();
        if (EmptyUtils.isNotEmpty(clientSectorCode)) {
            pipeline.setIndustrySubSector(transferDict(DictConstants.TARGET_DICT_CODE_SECTOR, clientSubsectorCode, dictMap));
        }else{
            pipeline.setIndustrySubSector(null);
        }
        //Type	company_type	company_type_code
        Object oCompanyTypeCode = syncOpportunity.getCompanyTypeCode();
        List<String> companyTypeCodeList = objectToStringList(oCompanyTypeCode);
        String account = syncOpportunity.getAccountId();
        pipeline.setAccountId(account);
        if (EmptyUtils.isNotEmpty(companyTypeCodeList)) {
            String accountId = syncOpportunity.getAccountId();
            List<String> targetCompanyTypeList = new ArrayList<>();
            List<String> newCompanyTypeList = new ArrayList<>();
            for (String sCompanyTypeCode : companyTypeCodeList) {
                String targetCompanyTypeKey = transferDict(DictConstants.TARGET_DICT_CODE_CLIENT_TYPE, sCompanyTypeCode, dictMap);
                if (EmptyUtils.isNotEmpty(targetCompanyTypeKey)) {
                    targetCompanyTypeList.add(targetCompanyTypeKey);
                }
            }
            if(targetCompanyTypeList.contains("State-Owned Enterprise（SOE）")||targetCompanyTypeList.contains("Specialized Enterprise (SE/央企)")){
                if(EmptyUtils.isNotEmpty(accountId)){
                    SyncTaxClient syncTaxClient = syncTaxClientMap.get(accountId);
                    if(EmptyUtils.isNotEmpty(syncTaxClient)){
                        //Head Office Location	head_office_location	client_headquarters
                        String countryRegion = syncTaxClient.getCountryRegion();
                        if(EmptyUtils.isNotEmpty(countryRegion)){
                            if("CN".equals(countryRegion)||"HK".equals(countryRegion)||"MO".equals(countryRegion)){
                                newCompanyTypeList.add("State-Owned Enterprise（SOE）");
                            }else {
                                newCompanyTypeList.add("Multinational Corporation（MNC）");
                            }
                        }else {
                            newCompanyTypeList.add("Multinational Corporation（MNC）");
                        }
                    }else {
                        newCompanyTypeList.add("Multinational Corporation（MNC）");
                    }
                }
            }else {
                if(EmptyUtils.isNotEmpty(accountId)) {
                    SyncTaxClient syncTaxClient = syncTaxClientMap.get(accountId);
                    if (EmptyUtils.isNotEmpty(syncTaxClient)) {
                        String countryRegion = syncTaxClient.getCountryRegion();
                        if(EmptyUtils.isNotEmpty(countryRegion)){
                            if("CN".equals(countryRegion)||"HK".equals(countryRegion)||"MO".equals(countryRegion)){
                                newCompanyTypeList.add("Private Entity（POE）");
                            }else if("KY".equals(countryRegion)){
                                  String clientRegionName = syncTaxClient.getClientRegionName();
                                  //Greater China
                                if(EmptyUtils.isNotEmpty(clientRegionName)&&"Greater China".equals(clientRegionName)) {
                                    String market = syncTaxClient.getMarketSegmentName();
                                    if (EmptyUtils.isNotEmpty(market) && !"Greater China-Taiwan".equals(market)) {
                                        newCompanyTypeList.add("Private Entity（POE）");
                                    } else {
                                        newCompanyTypeList.add("Multinational Corporation（MNC）");
                                    }
                                }else {
                                    newCompanyTypeList.add("Multinational Corporation（MNC）");
                                }
                            }else {
                                newCompanyTypeList.add("Multinational Corporation（MNC）");
                            }
                        }else {
                            newCompanyTypeList.add("Multinational Corporation（MNC）");
                        }
                    }
                }
            }
            pipeline.setCompanyType(newCompanyTypeList);
        }else{
            pipeline.setCompanyType(null);
        }
        //is_sec	is_sec	is_sec
        String isSec = syncOpportunity.getIsSec();
        if(EmptyUtils.isNotEmpty(isSec)){
            if (BooleanConstants.BOOLEAN_1.equals(isSec)) {
                pipeline.setIsSec(BooleanConstants.BOOLEAN_ON);
            } else if (BooleanConstants.BOOLEAN_0.equals(isSec)) {
                pipeline.setIsSec(BooleanConstants.BOOLEAN_OFF);
            }
        }else{
            pipeline.setIsSec(null);
        }
        //Listed	Listed	① 先判断stock_exchange是否为空-->Listed
        //Tiger     Target
        // (空)        NO
        //不为空时，进一步判断 is_sec
        //②再判断 is sec的情况-->Listed
        //Tiger     Target
        //  Y       Yes-US-Sec
        //  N       Yes-Other
        //数据字典的key-->
        //stock_exchange	stock_exchange_code
        Object oStockExchangeCode = syncOpportunity.getStockExchangeCode();
        if (EmptyUtils.isNotEmpty(oStockExchangeCode)) {
            if (BooleanConstants.BOOLEAN_1.equals(isSec)) {
                pipeline.setListed(DictConstants.TARGET_DICT_ITEM_LISTED_YES_US_SEC);
            } else if (BooleanConstants.BOOLEAN_0.equals(isSec)) {
                pipeline.setListed(DictConstants.TARGET_DICT_ITEM_LISTED_YES_OTHER);
            }
            List<String> stockExchangeCodeList = objectToStringList(oStockExchangeCode);
            if (EmptyUtils.isNotEmpty(stockExchangeCodeList)) {
                List<String> targetstockExchangeList = new ArrayList<>();
                for (String sStockExchangeCode : stockExchangeCodeList) {
                    String targetStockExchangeCodeKey = transferDict(DictConstants.TARGET_DICT_CODE_STOCK_MARKET, sStockExchangeCode, dictMap);
                    if (EmptyUtils.isNotEmpty(targetStockExchangeCodeKey)) {
                        targetstockExchangeList.add(targetStockExchangeCodeKey);
                    }
                }
                pipeline.setStockExchange(targetstockExchangeList);
            }
        }else{
            pipeline.setListed(DictConstants.TARGET_DICT_ITEM_LISTED_NO);
            pipeline.setStockExchange(null);
        }
        String accountId = syncOpportunity.getAccountId();
        if(EmptyUtils.isNotEmpty(accountId)){
            SyncTaxClient syncTaxClient = syncTaxClientMap.get(accountId);
            if(EmptyUtils.isNotEmpty(syncTaxClient)){
                //Head Office Location	head_office_location	client_headquarters
                String countryRegion = syncTaxClient.getCountryRegion();
                if(EmptyUtils.isNotEmpty(countryRegion)){
                    pipeline.setHeadOfficeLocation(transferDict(DictConstants.TARGET_DICT_CODE_OFFICE_LOCATION, countryRegion, dictMap));
                }
            }
        }
        //Client Channel	client_channel	client_channel_code
        String clientChannelCode = syncOpportunity.getClientChannelCode();
        if (EmptyUtils.isNotEmpty(clientChannelCode)) {
            pipeline.setClientChannel(transferDict(DictConstants.TARGET_DICT_CODE_CHANNEL, clientChannelCode, dictMap));
        }else{
            pipeline.setClientChannel(null);
        }
        //Customer Classification 	customer_classification	customer_classification_code
        String customerClassificationCode = syncOpportunity.getCustomerClassificationCode();
        if (EmptyUtils.isNotEmpty(customerClassificationCode)) {
            pipeline.setCustomerClassification(transferDict(DictConstants.TARGET_DICT_CODE_CUSTOMER_CLASS, customerClassificationCode, dictMap));
        }else{
            pipeline.setCustomerClassification(null);
        }
        //Oppo name	pipeline_name	oppr_name
        pipeline.setPipelineName(syncOpportunity.getOpprName());
        //status	status	oppr_outcome_name	数据字典的key
        //Tiger                     Target
        // Not yet...               Pursuit
        // Won                      Won
        // Lost                       lost
        //Declined                Declined
        String opprOutcomeName = syncOpportunity.getOpprOutcomeName();
        String status = null;
        if (DictConstants.TIGER_DICT_ITEM_OUT_COME_NAME_WON.equals(opprOutcomeName)) {
            status = DictConstants.TARGET_DICT_ITEM_STATUS_WON;
            pipeline.setWonTime(LocalDateTime.now());
        } else if (DictConstants.TIGER_DICT_ITEM_OUT_COME_NAME_LOST.equals(opprOutcomeName)) {
            status = DictConstants.TARGET_DICT_ITEM_STATUS_LOST;
        } else if (DictConstants.TIGER_DICT_ITEM_OUT_COME_NAME_DECLINED.equals(opprOutcomeName)) {
            status = DictConstants.TARGET_DICT_ITEM_STATUS_DECLINED;
        } else {
            status = DictConstants.TARGET_DICT_ITEM_STATUS_PURSUIT;
        }
        pipeline.setStatus(status);
        //所有项目信息	eng_info_json
        // Json String-Array String	eng_code	是	解析所有项目，
        // 拿到open Code Date（effective date）为最早的项目信息，把OPEN的时间放进first_confirm_date，
        // 项目EP，EM的GPN去重（非主EP、EM；因为主EP，主EM是100%），根据level加入sales的分fee信息中，默认为0%；
        // 如果有eng code却没有Open Code date，则获取Create Date;
        //if(有O的项目信息)找到最早的effective date；如果找不到就获取最早create date
        //else(找最早的create Date)
        Object oEngInfoJson = syncOpportunity.getEngInfoJson();
        List<Map> engInfoList = null;
        List<Engagement> newEngagementList = new ArrayList<>();

        // 方案1：如果oEngInfoJson已经是一个Java对象列表
        if(EmptyUtils.isNotEmpty(oEngInfoJson)) {
            String jsonStr = JSONObject.toJSONString(oEngInfoJson);
            engInfoList = JSON.parseArray(jsonStr, Map.class);
            buildEngagementList(engInfoList, newEngagementList, pipeline, userAttributeMap);
        }
        //pursuit leader找不到放Other Ep
        List<String> pursuitLeaderGpnList = objectToStringList(syncOpportunity.getPursuitLeaderGpn());
        String pursuitLeaderGpn = null;
        String pursuitLeaderUserId = null;
        if (EmptyUtils.isNotEmpty(pursuitLeaderGpnList)) {
            pursuitLeaderGpn = pursuitLeaderGpnList.get(0);
            UserAttribute pursuitLeaderUserAttribute = userAttributeMap.get(pursuitLeaderGpn);
            if(EmptyUtils.isNotEmpty(pursuitLeaderUserAttribute)){
                pursuitLeaderUserId = pursuitLeaderUserAttribute.getUserId();
            }else{
                pursuitLeaderUserId = defaultEpAttribute.getUserId();
                logger.error("pipeline: {} set {} user default", pipeline.getPipelineCode(), TYPE_LEADER);
                XxlJobHelper.log("pipeline: {} set {} user default", pipeline.getPipelineCode(), TYPE_LEADER);
            }
        }else{
            pursuitLeaderUserId = defaultEpAttribute.getUserId();
            logger.error("pipeline: {} set {} user default", pipeline.getPipelineCode(), TYPE_LEADER);
            XxlJobHelper.log("pipeline: {} set {} user default", pipeline.getPipelineCode(), TYPE_LEADER);
        }
        pipeline.setPursuitLeaderId(pursuitLeaderUserId);
        //根据partner找,找不到从eng_info_json里找
        String opprPartnerGpn = syncOpportunity.getOpprPartnerGpn();
        UserAttribute opprPartnerUserAttribute = getUserId(opprPartnerGpn, engInfoList
                , userAttributeMap, pipeline, TYPE_EP);

        //根据manager找,找不到从eng_info_json里找
        List<String> opprManagerGpnList = objectToStringList(syncOpportunity.getOpprManagerGpn());
        String opprManagerGpn = null;
        if (EmptyUtils.isNotEmpty(opprManagerGpnList)) {
            opprManagerGpn = opprManagerGpnList.get(0);
        }
        UserAttribute opprManagerUserAttribute = getUserId(opprManagerGpn, engInfoList
                , userAttributeMap, pipeline, TYPE_EM);

        LocalDateTime effectiveDate = null;
        LocalDateTime createDate = null;
        String effectiveEngCode = null;
        String createDateEngCode = null;
        String engCode = null;

        Map<String, String> engagementManagerGpnMap = new HashMap<>();
        Map<String, String> engagementPartnerGpnMap = new HashMap<>();

        //遍历eng的同时找到时间最早的em和ep
        UserAttribute engEffectiveDateEmUserAttribute = null;
        UserAttribute engEffectiveDateEpUserAttribute = null;
        UserAttribute engCreateDateEmUserAttribute = null;
        UserAttribute engCreateDateEpUserAttribute = null;
        UserAttribute engEmUserAttribute = null;
        UserAttribute engEpUserAttribute = null;
        if (EmptyUtils.isNotEmpty(engInfoList)) {
            for (Map<String, Object> map : engInfoList) {
                Object oEffectiveDate = map.get("effective_date");
                Object oCreateDate = map.get("create_date");
                Object oEngCode = map.get("eng_code");
                Object oEngagementStatus = map.get("engagement_status");
                Object oEngagementManagerGpn = map.get("engagement_manager_gpn");
                Object oEngagementPartnerGpn = map.get("engagement_partner_gpn");
                engagementManagerGpnMap.put(oEngagementManagerGpn.toString(), oEngagementManagerGpn.toString());
                engagementPartnerGpnMap.put(oEngagementPartnerGpn.toString(), oEngagementPartnerGpn.toString());
                UserAttribute engEmUserAttributeTemp = userAttributeMap.get(oEngagementManagerGpn.toString());
                UserAttribute engEpUserAttributeTemp = userAttributeMap.get(oEngagementPartnerGpn.toString());
                if ("O".equals(oEngagementStatus)) {
                    if (EmptyUtils.isNotEmpty(oEffectiveDate)) {
                        LocalDateTime localDate =
                                LocalDateTime.of(LocalDate.parse(oEffectiveDate.toString(), DateTimeFormatter.ofPattern("yyyy-MM-dd")), LocalTime.MIN);
                        if (EmptyUtils.isEmpty(effectiveDate)) {
                            effectiveDate = localDate;
                            effectiveEngCode = oEngCode.toString();
                            if(EmptyUtils.isNotEmpty(engEmUserAttributeTemp)){
                                engEffectiveDateEmUserAttribute = engEmUserAttributeTemp;
                            }
                            if(EmptyUtils.isNotEmpty(engEpUserAttributeTemp)){
                                engEffectiveDateEpUserAttribute = engEpUserAttributeTemp;
                            }
                        } else {
                            if (localDate.isBefore(effectiveDate)) {
                                effectiveDate = localDate;
                                effectiveEngCode = oEngCode.toString();
                                if(EmptyUtils.isNotEmpty(engEmUserAttributeTemp)){
                                    engEffectiveDateEmUserAttribute = engEmUserAttributeTemp;
                                }
                                if(EmptyUtils.isNotEmpty(engEpUserAttributeTemp)){
                                    engEffectiveDateEpUserAttribute = engEpUserAttributeTemp;
                                }
                            }
                        }
                    }
                }
                if (EmptyUtils.isNotEmpty(oCreateDate)) {
                    LocalDateTime localDate =
                            LocalDateTime.of(LocalDate.parse(oCreateDate.toString(), DateTimeFormatter.ofPattern("yyyy-MM-dd")), LocalTime.MIN);
                    if (EmptyUtils.isEmpty(createDate)) {
                        createDate = localDate;
                        createDateEngCode = oEngCode.toString();
                        if(EmptyUtils.isNotEmpty(engEmUserAttributeTemp)){
                            engCreateDateEmUserAttribute = engEmUserAttributeTemp;
                        }
                        if(EmptyUtils.isNotEmpty(engEpUserAttributeTemp)){
                            engCreateDateEpUserAttribute = engEpUserAttributeTemp;
                        }
                    } else {
                        if (localDate.isBefore(createDate)) {
                            createDate = localDate;
                            createDateEngCode = oEngCode.toString();
                            if(EmptyUtils.isNotEmpty(engEmUserAttributeTemp)){
                                engCreateDateEmUserAttribute = engEmUserAttributeTemp;
                            }
                            if(EmptyUtils.isNotEmpty(engEpUserAttributeTemp)){
                                engCreateDateEpUserAttribute = engEpUserAttributeTemp;
                            }
                        }
                    }
                }
            }
            if (EmptyUtils.isNotEmpty(effectiveDate)) {
                pipeline.setFirstConfirmDate(effectiveDate);
                String key = effectiveDate.format(DateTimeFormatter.ofPattern("yyyy-MMM-dd"));
                FiscalCalender fiscalCalender = fiscalCalenderMap.get(key);
                pipeline.setWonFy(fiscalCalender.getFiscalYear());
                engCode = effectiveEngCode;
            } else {
                pipeline.setFirstConfirmDate(createDate);
                String key = createDate.format(DateTimeFormatter.ofPattern("yyyy-MMM-dd"));
                FiscalCalender fiscalCalender = fiscalCalenderMap.get(key);
                pipeline.setWonFy(fiscalCalender.getFiscalYear());
                engCode = createDateEngCode;
            }
        }
        //处理eng中能找到的gpn
        if(opprPartnerUserAttribute.equals(defaultEpAttribute) && opprManagerUserAttribute.equals(defaultEmAttribute)) {
            if(EmptyUtils.isNotEmpty(engEffectiveDateEmUserAttribute)){
                engEmUserAttribute = engEffectiveDateEmUserAttribute;
            }else{
                engEmUserAttribute = engCreateDateEmUserAttribute;
            }
            if(EmptyUtils.isNotEmpty(engEffectiveDateEpUserAttribute)){
                engEpUserAttribute = engEffectiveDateEpUserAttribute;
            }else{
                engEpUserAttribute = engCreateDateEpUserAttribute;
            }
            if (EmptyUtils.isNotEmpty(engEmUserAttribute)) {
                opprManagerUserAttribute = engEmUserAttribute;
            }
            if (EmptyUtils.isNotEmpty(engEpUserAttribute)) {
                opprPartnerUserAttribute = engEpUserAttribute;
            }

        }
        //如果em和ep都没找到则丢弃
        if(opprPartnerUserAttribute.equals(defaultEpAttribute) && opprManagerUserAttribute.equals(defaultEmAttribute)){
            logger.error("pipeline: {} ep and em not found skip this pipeline", pipeline.getPipelineCode());
            XxlJobHelper.log("pipeline: {} ep and em not found skip this pipeline", pipeline.getPipelineCode());
            return null;
        }

        if (EmptyUtils.isNotEmpty(opprManagerUserAttribute)) {
            pipeline.setEngManagerId(opprManagerUserAttribute.getUserId());
        }
        if (EmptyUtils.isNotEmpty(opprPartnerUserAttribute)) {
            pipeline.setOpprPartnerId(opprPartnerUserAttribute.getUserId());
            //Location	location_code		存Pipeline上 主EP的Location
            pipeline.setLocation(opprPartnerUserAttribute.getCity());
        }

        //赢的概率	estimated_win_odds	String	win_rate	是	示例数据：70.0
        BigDecimal estimatedWinOdds = syncOpportunity.getEstimatedWinOdds();
        if (EmptyUtils.isNotEmpty(estimatedWinOdds)) {
            pipeline.setWinRate(estimatedWinOdds.intValue());
        }
        // 定义日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        //预计赢单时间	anticipated_win_date	Date	anticipated_win_date	是
        if(EmptyUtils.isNotEmpty(syncOpportunity.getAnticipatedWinDate())){
            pipeline.setAnticipatedWinDate(LocalDateTime.parse(syncOpportunity.getAnticipatedWinDate(), formatter));
        }
        LocalDateTime kickoffWeek = null;
        if(EmptyUtils.isNotEmpty(syncOpportunity.getAnticipatedStartDate())){
            kickoffWeek = LocalDateTime.parse(syncOpportunity.getAnticipatedStartDate(), formatter);
        }
        LocalDateTime closingWeek = null;
        if(EmptyUtils.isNotEmpty(syncOpportunity.getAnticipatedCloseDate())){
            closingWeek = LocalDateTime.parse(syncOpportunity.getAnticipatedCloseDate(), formatter);
        }
        if(EmptyUtils.isNotEmpty(kickoffWeek) && EmptyUtils.isNotEmpty(closingWeek)){
            if(kickoffWeek.isAfter(closingWeek)){
                kickoffWeek =  LocalDateTime.parse(syncOpportunity.getAnticipatedCloseDate(), formatter);
                closingWeek = LocalDateTime.parse(syncOpportunity.getAnticipatedStartDate(), formatter);
            }
        }
        //预计项目开始时间	anticipated_start_date	Date	kickoff_week	是
        pipeline.setKickoffWeek(kickoffWeek);
        //预计项目结束时间	anticipated_close_date	Date	closing_week	是
        pipeline.setClosingWeek(closingWeek);
        //EY Office	psm_prime_office_code	String	psm_prime_office
        // 是	prime EY Office 是空的话，is_subcode为N，不为空is_subcode为Y（数据字典的key）
        List<String> psmPrimeOfficeCodeList = objectToStringList(syncOpportunity.getPsmPrimeOfficeCode());
        if (EmptyUtils.isEmpty(psmPrimeOfficeCodeList)) {
            pipeline.setIsSubcode(BooleanConstants.BOOLEAN_OFF);
        } else {
            String firstCode = psmPrimeOfficeCodeList.get(0);
            if(BooleanConstants.BOOLEAN_0.equals(firstCode)){
                pipeline.setIsSubcode(BooleanConstants.BOOLEAN_OFF);
            }else {
                pipeline.setIsSubcode(BooleanConstants.BOOLEAN_ON);
                //取psmPrimeOfficeCodeList最后一个元素
                String psmPrimeOffice = psmPrimeOfficeCodeList.get(psmPrimeOfficeCodeList.size() - 1);
                String targetPsmPrimeOffice = transferDict(DictConstants.TARGET_DICT_CODE_PRIME_OFFICE, psmPrimeOffice, dictMap);
                pipeline.setPsmPrimeOffice(targetPsmPrimeOffice);
            }
        }
        //Bidding	is_competitive_bid	String	bidding/lead_source	是	"数据是Y：1，N：0；
        //lead_source：Y的时候需要录入bidding的结果是Open bid，N的时候录入为 PartnerShip；
        //Bidding：和Lead Source取同一个字段（取自is_competitive_bid ，Y-open bid ，N-single source）
        String isCompetitiveBid = syncOpportunity.getIsCompetitiveBid();
        if(EmptyUtils.isNotEmpty(isCompetitiveBid)){
            if (BooleanConstants.BOOLEAN_1.equals(isCompetitiveBid)) {
                pipeline.setBidding(DictConstants.TIGER_DICT_ITEM_KEY_LEADER_SOURCE_OPEN_BID);
            } else {
                pipeline.setBidding(DictConstants.TIGER_DICT_ITEM_KEY_LEADER_SOURCE_SINGLE_SOURCE);
            }
        }else{
            pipeline.setBidding(null);
        }
        //是否EIC自己的努力	is_eic_self	String	eic_own_effort	是	Y：1-->Y-new client（数据字典的key）
        String isEicSelf = syncOpportunity.getIsEicSelf();
        if (EmptyUtils.isNotEmpty(isEicSelf)) {
            pipeline.setEicOwnEffort(Integer.parseInt(isEicSelf));
            //target_pipeline	/	efforts_approve		依据 is_eic_self 字段判断规则
            //is_eic_self   →  efforts_approve
            //N                             NO
            //Y                             PendingApprove
            if (BooleanConstants.BOOLEAN_0.equals(isEicSelf)) {
                pipeline.setEffortsApprove(DictConstants.TIGER_DICT_ITEM_EFFORTS_APPROVE_NO);
            } else if (BooleanConstants.BOOLEAN_1.equals(isEicSelf)) {
                pipeline.setEffortsApprove(DictConstants.TIGER_DICT_ITEM_EFFORTS_APPROVE_PENDING);
            } else {
                pipeline.setEffortsApprove(null);
            }
        }else{
            pipeline.setEicOwnEffort(null);
            pipeline.setEffortsApprove(null);
        }

        //是自己努力的原因	efforts_reason	String	efforts_description	是	为is_eic_self=1的时候取
        pipeline.setEffortsDescription(syncOpportunity.getEffortsReason());

        //pipeline.setRecurring(syncOpportunity.getRecurringName());
        //赢单原因	reason_code	String	won_reason	是	数据字典的key（win_reason）
        String reasonCode = syncOpportunity.getReasonCode();
        if (EmptyUtils.isNotEmpty(reasonCode)) {
            pipeline.setWonReason(transferDict(DictConstants.TARGET_DICT_CODE_WON_REASON, reasonCode, dictMap));
        }else{
            pipeline.setWonReason(null);
        }
        //描述	description	String	description	是
        String description = syncOpportunity.getDescription();
        if(EmptyUtils.isEmpty(description)){
            pipeline.setDescription(null);
        }else{
            pipeline.setDescription(description);
        }
        //商机币种	oppr_currency	String	currency	是	数据字典的key（Currency）
        String currency = transferDict(DictConstants.TARGET_DICT_CODE_CURRENCY, syncOpportunity.getOpprCurrency(), dictMap);
        DictionaryItem currencyDictionaryItem = currencyObjectMap.get("tiger-"+syncOpportunity.getOpprCurrency());
        if(EmptyUtils.isEmpty(currencyDictionaryItem)){
            XxlJobHelper.log("pipeline: {} exchangeRate not found: {}", pipeline.getPipelineCode(), currency);
            return null;
        }
        currency = currencyDictionaryItem.getItemValue();
        pipeline.setCurrency(currency);
        //商机金额	oppr_amount	String	amount	是
        pipeline.setAmountCurrency(amount);
        //换算rmb
        ExchangeRate exchangeRate = exchangeRateMap.get(currency);
        if(EmptyUtils.isEmpty(exchangeRate)){
            exchangeRate = exchangeRateMap.get(currencyDictionaryItem.getItemKey());
        }
        if(EmptyUtils.isEmpty(exchangeRate)){
            XxlJobHelper.log("pipeline: {} exchangeRate not found: {}", pipeline.getPipelineCode(), currency);
            String errorCurrency = errorCache.get(currency);
            if(EmptyUtils.isEmpty(errorCurrency)){
                errorCache.put(errorCurrency, errorCurrency);
                String content = "pipeline: " + pipeline.getPipelineCode() + " exchangeRate not found: " + currency;
                submitBusinessFault(BusFaultConstants.BUS_TYPE_PIPELINE_TRANSF, content);
            }
            return null;
        }
        BigDecimal amountRmb = exchangeRate.getExchangeRate().multiply(amount);
        pipeline.setAmount(amountRmb);
        exchangeRate = exchangeRateMap.get("USD");
        BigDecimal amountUsd = amountRmb.divide(exchangeRate.getExchangeRate(), 11, RoundingMode.HALF_UP);
        pipeline.setAmountUsd(amountUsd);
        //target_pipeline	Eng Code	eng_code	eng_info_json
        pipeline.setEngCode(engCode);
        //target_pipeline		pursuit_fy	create_time	年 对应的财年
        LocalDateTime createTime =dateTime;
        if (EmptyUtils.isNotEmpty(createTime)) {
            FiscalCalender fiscalCalender = fiscalCalenderService.queryFiscalCalender(createTime);
            if(EmptyUtils.isEmpty(fiscalCalender)){
                XxlJobHelper.log("pipeline{} createTime not found: {}", pipeline.getPipelineCode(), createTime.toString());
            }else{
                pipeline.setPursuitFy(fiscalCalender.getFiscalYear());
            }
        }


        //产品code	product_code	String	product_id	是	取最后一个叶子节点，匹配target_product的product_code，拿到id放入target_pipeline
        XxlJobHelper.log("productCode: {}", syncOpportunity.getProductCode());
        List<String> productCodeList = objectToStringList(syncOpportunity.getProductCode());
        if (EmptyUtils.isNotEmpty(productCodeList)) {
            String productCode = productCodeList.get(productCodeList.size() - 1);
            Product product = productMap.get(productCode+pipeline.getPursuitFy());
            //如果product为空，则查找失效产品
            if (EmptyUtils.isEmpty(product)) {
               /* XxlJobHelper.log("disProductMap:"+ JsonUtil.toJson(disProductMap));
                XxlJobHelper.log("productMapping:"+ JsonUtil.toJson(productMapping));
                XxlJobHelper.log("productCode+pipeline.getPursuitFy():"+productCode+pipeline.getPursuitFy());*/
                Product  product2 = disProductMap.get(productCode+pipeline.getPursuitFy());
                if(EmptyUtils.isNotEmpty(product2)){
                    //查找mapping
                    String nproduct = productMapping.get(product2.getProductCode());
                    if(EmptyUtils.isNotEmpty(nproduct)){
                        XxlJobHelper.log("pipeline: {} product mapping: {} to {}", pipeline.getPipelineCode(), productCode, nproduct+pipeline.getPursuitFy());
                        product = productMap.get(nproduct+pipeline.getPursuitFy());
                        pipeline.setProductId(product.getProductId());
                    }
                }
            } else if (EmptyUtils.isNotEmpty(product)) {
                pipeline.setProductId(productMap.get(productCode+pipeline.getPursuitFy()).getProductId());
            } else {
                logger.error("product code {} not found", productCode);
                XxlJobHelper.log("product code {} not found", productCode);
            }
        }else{
            pipeline.setProductId(null);
        }

        //是否含税	is_inclusive_vat	String	is_inclusive_vat	是	数据字典的key
        String isInclusiveVat = syncOpportunity.getIsInclusiveVat();
        if (EmptyUtils.isNotEmpty(isInclusiveVat)) {
            pipeline.setIsInclusiveVat(Integer.parseInt(isInclusiveVat));
        }
        pipeline.setEvent(syncOpportunity.getEvent());
        pipeline.setOtherEvent(syncOpportunity.getEventDesc());
        //所有PACE信息	all_pace_info_json	Json String-Array String	pace_status	是
        // 解析所有Pace，PaceStatus为全部Approve则为完成，其他为NO
        Object oAllPaceInfoJson = syncOpportunity.getAllPaceInfoJson();
        List<Map> paceInfoList = null;
        if (EmptyUtils.isNotEmpty(oAllPaceInfoJson)) {
            String sAllPaceInfoJson = oAllPaceInfoJson.toString();
            sAllPaceInfoJson = sAllPaceInfoJson.replace("\"{", "{")
                    .replace("}\"", "}")
                    .replace("\\\"", "\"");
            paceInfoList = JacksonUtils.readValue(sAllPaceInfoJson, List.class);
        }
        boolean flag = true;
        if (EmptyUtils.isNotEmpty(paceInfoList)) {
            for (Map map : paceInfoList) {
                Object oPaceStatus = map.get("pace_status");
                if (!"Approved".equals(oPaceStatus)) {
                    flag = false;
                    break;
                }
            }
        }else {
            flag = false;
        }
        if (flag) {
            pipeline.setPaceStatus(BooleanConstants.BOOLEAN_ON);
        } else {
            pipeline.setPaceStatus(BooleanConstants.BOOLEAN_OFF);
        }
        pipeline.setDeliveryRisk("Low");
        pipeline.setContractorUsed(BooleanConstants.BOOLEAN_OFF);
        pipeline.setOutsource(BigDecimal.ZERO);
        //confirm_status		status=won时给 confirm status赋值
        //
        //1、Status=won时进一步判断 eng_code 是否有值
        //eng_code 有值  →  confirm status=processed
        //eng_code 为空  →  confirm status=空
        if (DictConstants.TARGET_DICT_ITEM_STATUS_WON.equals(status)) {
            if (EmptyUtils.isNotEmpty(engCode)) {
                pipeline.setConfirmStatus(PipelineConstants.APPROVE_STATUS_PROCESSED);
            }
        }
        //isOverseas
        if(EmptyUtils.isNotEmpty(syncOpportunity.getIsOverseas())) {
            if (syncOpportunity.getIsOverseas()==0) {
                String out = "NO";
                pipeline.setOutboundRelated(out);
            } else {
                String out = "outbound_related";
                pipeline.setOutboundRelated(out);
            }

        }

        //outboundCountryCode
        if(EmptyUtils.isNotEmpty(syncOpportunity.getOutboundCountryCode())){
            //根据;分割
            List<String> outboundCountryCodeList = List.of(syncOpportunity.getOutboundCountryCode().split(";"));
            //每个元素前加上tiger-，然后使用,拼接
            String outboundCountryCode = outboundCountryCodeList.stream().map(s -> "tiger-" + s).collect(Collectors.joining(","));
            pipeline.setOutboundCountry(outboundCountryCode);
        }

        //leadSource4Code
        if(EmptyUtils.isNotEmpty(syncOpportunity.getLeadSource4Code())){
            //根据source匹配
            String leadSource4Code = transferDict(DictConstants.TARGET_DICT_CODE_LEAD_SOURCE, syncOpportunity.getLeadSource4Code(), dictMap);
            pipeline.setLeadSource(leadSource4Code);
        }else if(EmptyUtils.isNotEmpty(syncOpportunity.getLeadSource3Code())){
            //根据source匹配
            String leadSource3Code = transferDict(DictConstants.TARGET_DICT_CODE_LEAD_SOURCE, syncOpportunity.getLeadSource3Code(), dictMap);
            pipeline.setLeadSource(leadSource3Code);
        }else if(EmptyUtils.isNotEmpty(syncOpportunity.getLeadSource2Code())) {
            //根据source匹配
            String leadSource2Code = transferDict(DictConstants.TARGET_DICT_CODE_LEAD_SOURCE, syncOpportunity.getLeadSource2Code(), dictMap);
            pipeline.setLeadSource(leadSource2Code);
        }else if(EmptyUtils.isNotEmpty(syncOpportunity.getLeadSource1Code())){
            //根据source匹配
            String leadSource1Code = transferDict(DictConstants.TARGET_DICT_CODE_LEAD_SOURCE, syncOpportunity.getLeadSource1Code(), dictMap);
            pipeline.setLeadSource(leadSource1Code);
        }

        //recurringCode
        XxlJobHelper.log("recurringCode: {}", syncOpportunity.getRecurringCode());
        if(EmptyUtils.isNotEmpty(syncOpportunity.getRecurringCode())){
            //根据recurring匹配
            String recurringCode = transferDict(DictConstants.TARGET_DICT_CODE_RECURRING, syncOpportunity.getRecurringCode(), dictMap);
            pipeline.setRecurring(recurringCode);
        }

        String confirmStatus = pipeline.getConfirmStatus();
        //如果状态是won，检查必填
        if (PipelineConstants.PIPELINE_STATUS_WON.equals(status)){
            List<String> nullProperties = CommonUtils.getNullProperties(pipeline, pipelineConfigList);
            if(EmptyUtils.isNotEmpty(nullProperties)){
                pipeline.setIsBlank(1);
            }
        }

        //分费
        List<SalesDelivery> salesDeliveryList = new ArrayList<>();
        if (PipelineConstants.PIPELINE_STATUS_WON.equals(status) &&
                (PipelineConstants.APPROVE_STATUS_PROCESSED.equals(confirmStatus) ||
                        PipelineConstants.APPROVE_STATUS_AMENDMENT_SUBMITTED.equals(confirmStatus) ||
                        PipelineConstants.APPROVE_STATUS_RETURNED.equals(confirmStatus))) {
            //EM
            SalesDelivery emSalesDelivery = new SalesDelivery();
            emSalesDelivery.setPipelineId(pipeline.getId());
            emSalesDelivery.setSalesDeliveryCreditAmount(amountRmb);
            emSalesDelivery.setSalesDeliveryCreditAmountUsd(amountUsd);
            emSalesDelivery.setSalesDeliveryCreditRatio(new BigDecimal(100));
            emSalesDelivery.setSalesDeliveryCredit(opprManagerUserAttribute.getUserId());
            emSalesDelivery.setSalesDeliveryCreditType(TYPE_INTEGER_EM);
            emSalesDelivery.setFiscalYear(pipeline.getWonFy());
            salesDeliveryList.add(emSalesDelivery);
            //EP
            SalesDelivery epSalesDelivery = new SalesDelivery();
            epSalesDelivery.setPipelineId(pipeline.getId());
            epSalesDelivery.setSalesDeliveryCreditAmount(amountRmb);
            epSalesDelivery.setSalesDeliveryCreditAmountUsd(amountUsd);
            epSalesDelivery.setSalesDeliveryCreditRatio(new BigDecimal(100));
            epSalesDelivery.setSalesDeliveryCredit(opprPartnerUserAttribute.getUserId());
            epSalesDelivery.setSalesDeliveryCreditType(TYPE_INTEGER_EP);
            epSalesDelivery.setFiscalYear(pipeline.getWonFy());
            salesDeliveryList.add(epSalesDelivery);

            for (String managetGpn : engagementManagerGpnMap.values()) {
                SalesDelivery salesDelivery = new SalesDelivery();
                salesDelivery.setPipelineId(pipeline.getId());
                BigDecimal salesDeliveryCreditRatio = null;
                if (managetGpn.equals(opprManagerUserAttribute.getGpn())) {
                    continue;
                } else {
                    salesDeliveryCreditRatio = BigDecimal.ZERO;
                    salesDelivery.setSalesDeliveryCreditAmount(BigDecimal.ZERO);
                    salesDelivery.setSalesDeliveryCreditAmountUsd(BigDecimal.ZERO);
                }
                UserAttribute managetUserAttribute = userAttributeMap.get(managetGpn);
                if (EmptyUtils.isNotEmpty(managetUserAttribute)) {
                    salesDelivery.setSalesDeliveryCredit(managetUserAttribute.getUserId());
                    salesDelivery.setSalesDeliveryCreditType(TYPE_INTEGER_EM);
                    salesDelivery.setSalesDeliveryCreditRatio(salesDeliveryCreditRatio);
                    salesDelivery.setFiscalYear(pipeline.getWonFy());
                    salesDeliveryList.add(salesDelivery);
                }
            }
            for (String partnerGpn : engagementPartnerGpnMap.values()) {
                SalesDelivery salesDelivery = new SalesDelivery();
                salesDelivery.setPipelineId(pipeline.getId());
                BigDecimal salesDeliveryCreditRatio = null;
                if (partnerGpn.equals(opprPartnerUserAttribute.getGpn())) {
                    continue;
                } else {
                    salesDeliveryCreditRatio = BigDecimal.ZERO;
                    salesDelivery.setSalesDeliveryCreditAmount(BigDecimal.ZERO);
                    salesDelivery.setSalesDeliveryCreditAmountUsd(BigDecimal.ZERO);
                }
                UserAttribute partnerUserAttribute = userAttributeMap.get(partnerGpn);
                if (EmptyUtils.isNotEmpty(partnerUserAttribute)) {
                    salesDelivery.setSalesDeliveryCredit(partnerUserAttribute.getUserId());
                    salesDelivery.setSalesDeliveryCreditType(TYPE_INTEGER_EP);
                    salesDelivery.setSalesDeliveryCreditRatio(salesDeliveryCreditRatio);
                    salesDelivery.setFiscalYear(pipeline.getWonFy());
                    salesDeliveryList.add(salesDelivery);
                }
            }
        } else {
            SalesDelivery emSalesDelivery = new SalesDelivery();
            emSalesDelivery.setPipelineId(pipeline.getId());
            emSalesDelivery.setSalesDeliveryCredit(opprManagerUserAttribute.getUserId());
            emSalesDelivery.setSalesDeliveryCreditType(TYPE_INTEGER_EM);
            emSalesDelivery.setSalesDeliveryCreditRatio(new BigDecimal(100));
            emSalesDelivery.setSalesDeliveryCreditAmount(amountRmb);
            emSalesDelivery.setFiscalYear(pipeline.getWonFy());
            emSalesDelivery.setSalesDeliveryCreditAmountUsd(amountUsd);
            salesDeliveryList.add(emSalesDelivery);
            SalesDelivery epSalesDelivery = new SalesDelivery();
            epSalesDelivery.setPipelineId(pipeline.getId());
            epSalesDelivery.setSalesDeliveryCredit(opprPartnerUserAttribute.getUserId());
            epSalesDelivery.setSalesDeliveryCreditType(TYPE_INTEGER_EP);
            epSalesDelivery.setSalesDeliveryCreditRatio(new BigDecimal(100));
            epSalesDelivery.setSalesDeliveryCreditAmount(amountRmb);

            epSalesDelivery.setFiscalYear(pipeline.getWonFy());
            epSalesDelivery.setSalesDeliveryCreditAmountUsd(amountUsd);
            salesDeliveryList.add(epSalesDelivery);
        }
        if(EmptyUtils.isNotEmpty(newEngagementList)){
            insertEngagement.addAll(newEngagementList);
        }
        return salesDeliveryList;
    }

    private List<String> objectToStringList(Object data) {
        // 空值检查
        if (EmptyUtils.isEmpty(data)) {
            return null;
        }

        // 情况0: 处理数组类型
        if (data.getClass().isArray()) {
            List<String> arrayList = new ArrayList<>();
            int length = Array.getLength(data);
            for (int i = 0; i < length; i++) {
                Object item = Array.get(data, i);
                if (item != null) {
                    arrayList.add(item.toString());
                }
            }
            return !arrayList.isEmpty() ? arrayList : null;
        }

        // 情况1: 如果已经是List类型，直接转换
        if (data instanceof List) {
            List<?> origList = (List<?>) data;
            List<String> newList = new ArrayList<>();
            for (Object item : origList) {
                if (item != null) {
                    newList.add(item.toString());
                }
            }
            return !newList.isEmpty() ? newList : null;
        }

        // 将对象转为字符串
        String strData = data.toString().trim();

        // 情况2: 尝试使用Jackson解析JSON
        try {
            List<String> jsonList = JSONObject.parseObject(strData, List.class);
            if (EmptyUtils.isNotEmpty(jsonList)) {
                List<String> newList = new ArrayList<>();
                for (Object item : jsonList) {
                    if (item != null && EmptyUtils.isNotEmpty(item.toString())) {
                        newList.add(item.toString());
                    }
                }
                return !newList.isEmpty() ? newList : null;
            }
        } catch (Exception e) {
            // Jackson解析失败，继续尝试下一种方法
        }

        // 情况3: 作为简单字符串"[xxx,yyy]"格式处理
        if (strData.startsWith("[") && strData.endsWith("]")) {
            String content = strData.substring(1, strData.length() - 1).trim();
            if (!content.isEmpty()) {
                List<String> result = new ArrayList<>();
                for (String item : content.split(",")) {
                    item = item.trim();
                    if (!item.isEmpty()) {
                        result.add(item);
                    }
                }
                return !result.isEmpty() ? result : null;
            }
        }

        // 情况4: 如果是单个值，直接返回包含这个值的列表
        if (!strData.isEmpty()) {
            List<String> singleItem = new ArrayList<>();
            singleItem.add(strData);
            return singleItem;
        }

        return null;
    }
    private static List<String> objectToStringLists(Object data) {
        if (EmptyUtils.isEmpty(data)) {
            return null;
        }
        String sGpn = data.toString();
        List<String> subList = JacksonUtils.readValue(sGpn, List.class);
        if (EmptyUtils.isEmpty(subList)) {
            return null;
        }
        List<String> newList = new ArrayList<>();
        for (String sub : subList) {
            if (EmptyUtils.isNotEmpty(sub)) {
                newList.add(sub);
            }
        }
        return newList;
    }

    public static void main(String[] args) {
        String s = "[A2, A2R7, A2R7L114, N0010]";
        List<String> list = objectToStringLists(s);
        System.out.println(list);
    }


    private UserAttribute getUserId(String gpn,List<Map> engInfoList,
                                    Map<String, UserAttribute> userAttributeMap,
                                    PipelineForTransfer pipeline, String type) {
        UserAttribute ua = null;
        if (EmptyUtils.isNotEmpty(gpn)) {
            ua = userAttributeMap.get(gpn);
        }
        if (EmptyUtils.isEmpty(ua)) {
            if (EmptyUtils.isNotEmpty(engInfoList)) {
                for (Map<String, Object> map : engInfoList) {
                    Object oEngCode = map.get("eng_code");
                    if (EmptyUtils.isEmpty(oEngCode)) {
                        Object oEngagementGpn = null;
                        if (TYPE_EM.equals(type)) {
                            oEngagementGpn = map.get("engagement_manager_gpn");
                        } else {
                            oEngagementGpn = map.get("engagement_partner_gpn");
                        }
                        if (EmptyUtils.isEmpty(oEngagementGpn)) {
                            ua = userAttributeMap.get(oEngagementGpn.toString());
                            break;
                        }
                    }
                }
            }
        }
        if (EmptyUtils.isEmpty(ua)) {
            if (TYPE_EM.equals(type)) {
                ua = defaultEmAttribute;
            } else {
                ua = defaultEpAttribute;
            }
            logger.error("pipeline: {} set {} user default", pipeline.getPipelineCode(), type);
            XxlJobHelper.log("pipeline: {} set {} user default", pipeline.getPipelineCode(), type);
        }
        return ua;
    }

    private void insertPipeline(List<PipelineForTransfer> insertPipeline, PipelineForTransfer pipeline,
                                List<SalesDelivery> insertSalesDelivery, List<SalesDelivery> newInsertSalesDelivery,
                                List<Engagement> insertEngagement) {
        insertPipeline.add(pipeline);
        insertSalesDelivery.addAll(newInsertSalesDelivery);
        if (insertPipeline.size() >= PAGE_SIZE) {
            XxlJobHelper.log("insert pipeline start count: {}", insertPipeline.size());
            pipelineForTransferService.save(insertPipeline);
            XxlJobHelper.log("insert pipeline end count: {}", insertPipeline.size());
            XxlJobHelper.log("insert sales start count: {}", insertSalesDelivery.size());
            salesDeliveryService.save(insertSalesDelivery);
            XxlJobHelper.log("insert sales end count: {}", insertSalesDelivery.size());
            XxlJobHelper.log("insert engagement start count: {}", insertEngagement.size());
            engagementService.save(insertEngagement);
            XxlJobHelper.log("insert engagement end count: {}", insertEngagement.size());
            insertPipeline.clear();
            insertSalesDelivery.clear();
            insertEngagement.clear();

        }
    }

    private void updatePipeline(List<PipelineForTransfer> updatePipeline, PipelineForTransfer pipeline,
                                List<SalesDelivery> insertSalesDelivery,
                                List<SalesDelivery> newInsertSalesDelivery,
                                List<String> deleteSalesDeliveryPipelineIds,
                                List<Engagement> insertEngagement) {
        updatePipeline.add(pipeline);
        if(EmptyUtils.isNotEmpty(newInsertSalesDelivery)){
            insertSalesDelivery.addAll(newInsertSalesDelivery);
        }
        if (updatePipeline.size() >= PAGE_SIZE) {
            XxlJobHelper.log("update pipeline start count: {}", updatePipeline.size());
            pipelineForTransferService.updateBatchByIds(updatePipeline);
            XxlJobHelper.log("update pipeline end count: {}", updatePipeline.size());
            XxlJobHelper.log("delete sales start count: {}", deleteSalesDeliveryPipelineIds.size());
            salesDeliveryService.realDeleteByPipelineIds(deleteSalesDeliveryPipelineIds);
            XxlJobHelper.log("delete sales end count: {}", deleteSalesDeliveryPipelineIds.size());
            //XxlJobHelper.log("delete engagement start count: {}", deleteSalesDeliveryPipelineIds.size());
            //engagementService.realDeleteByPipelineIds(deleteSalesDeliveryPipelineIds);
            //XxlJobHelper.log("delete engagement end count: {}", deleteSalesDeliveryPipelineIds.size());
            XxlJobHelper.log("insert sales start count: {}", insertSalesDelivery.size());
            if(EmptyUtils.isNotEmpty(insertSalesDelivery)){
                salesDeliveryService.save(insertSalesDelivery);
            }
            XxlJobHelper.log("insert sales end count: {}", insertSalesDelivery.size());
            XxlJobHelper.log("insert engagement start count: {}", insertEngagement.size());
            if(EmptyUtils.isNotEmpty(insertEngagement)){
                engagementService.save(insertEngagement);
            }
            XxlJobHelper.log("insert engagement end count: {}", insertEngagement.size());
            updatePipeline.clear();
            insertSalesDelivery.clear();
            deleteSalesDeliveryPipelineIds.clear();
            insertEngagement.clear();
        }
    }

    private void flush(List<PipelineForTransfer> insertPipeline,
                       List<PipelineForTransfer> updatePipeline,
                       List<SalesDelivery> insertSalesDelivery,
                       List<String> deleteSalesDeliveryPipelineIds,
                       List<Engagement> insertEngagement) {
        if (EmptyUtils.isNotEmpty(insertPipeline)) {
            XxlJobHelper.log("insert pipeline start count: {}", insertPipeline.size());
            pipelineForTransferService.save(insertPipeline);
            XxlJobHelper.log("insert pipeline end count: {}", insertPipeline.size());
        }
        if (EmptyUtils.isNotEmpty(updatePipeline)) {
            XxlJobHelper.log("update pipeline start count: {}", updatePipeline.size());
            pipelineForTransferService.updateBatchByIds(updatePipeline);
            XxlJobHelper.log("update pipeline end count: {}", updatePipeline.size());
        }
        if (EmptyUtils.isNotEmpty(deleteSalesDeliveryPipelineIds)) {
            XxlJobHelper.log("delete sales start count: {}", deleteSalesDeliveryPipelineIds.size());
            salesDeliveryService.realDeleteByPipelineIds(deleteSalesDeliveryPipelineIds);
            XxlJobHelper.log("delete sales end count: {}", deleteSalesDeliveryPipelineIds.size());
            //XxlJobHelper.log("delete engagement start count: {}", deleteSalesDeliveryPipelineIds.size());
            //engagementService.realDeleteByPipelineIds(deleteSalesDeliveryPipelineIds);
            //XxlJobHelper.log("delete engagement end count: {}", deleteSalesDeliveryPipelineIds.size());
        }
        if (EmptyUtils.isNotEmpty(insertSalesDelivery)) {
            XxlJobHelper.log("insert sales start count: {}", insertSalesDelivery.size());
            salesDeliveryService.save(insertSalesDelivery);
            XxlJobHelper.log("insert sales end count: {}", insertSalesDelivery.size());
        }
        XxlJobHelper.log("insert engagement start count: {}", insertEngagement.size());
        if(EmptyUtils.isNotEmpty(insertEngagement)){
            engagementService.save(insertEngagement);
        }
        XxlJobHelper.log("insert engagement end count: {}", insertEngagement.size());
    }

    private Map<String, SyncTaxClient> loadSyncTaxClient() {
        Map<String, SyncTaxClient> syncTaxClientMap = new HashMap<>();
        Search<SyncTaxClient> search = new Search<>();
        SyncTaxClient qSyncTaxClient = new SyncTaxClient();
        qSyncTaxClient.setClientId(clientId);
        search.setQueryParams(qSyncTaxClient);
        Page page = new Page();
        page.setCurrentPage(1);
        page.setPageSize(PAGE_SIZE);

        QueryExtension queryExtension = new QueryExtension();
        queryExtension.setPage(page);
        List<StringSort> sorts = new ArrayList<>();
        StringSort stringSort= new StringSort();
        stringSort.setProperty("client_id");
        stringSort.setDirection(Direction.ASC);
        sorts.add(stringSort);
        queryExtension.setSorts(sorts);
        search.setQueryExtension(queryExtension);

        Search<SyncTaxClient> rSearch = syncTaxClientService.queryPageByPara(search);
        if (EmptyUtils.isNotEmpty(rSearch.getEntities())) {
            for (SyncTaxClient syncTaxClient : rSearch.getEntities()) {
                syncTaxClientMap.put(syncTaxClient.getClientId(), syncTaxClient);
            }
        }
        while (hasNext(rSearch.getPage())) {
            page.setCurrentPage(page.getCurrentPage() + 1);
            rSearch = syncTaxClientService.queryPageByPara(search);
            if (EmptyUtils.isNotEmpty(rSearch.getEntities())) {
                for (SyncTaxClient syncTaxClient : rSearch.getEntities()) {
                    syncTaxClientMap.put(syncTaxClient.getClientId(), syncTaxClient);
                }
            }
        }

        return syncTaxClientMap;
    }

    private Map<String, PipelineForTransfer> loadLocalPipeline() {
        Map<String, PipelineForTransfer> pipelineMap = new HashMap<>();
        Search<PipelineForTransfer> search = new Search<>();
        PipelineForTransfer qPipeline = new PipelineForTransfer();
        qPipeline.setPipelineCode(pipelineCode);
        search.setQueryParams(qPipeline);
        Page page = new Page();
        page.setCurrentPage(1);
        page.setPageSize(PAGE_SIZE);

        QueryExtension queryExtension = new QueryExtension();
        queryExtension.setPage(page);
        List<StringSort> sorts = new ArrayList<>();
        StringSort stringSort= new StringSort();
        stringSort.setProperty("pipeline_code");
        stringSort.setDirection(Direction.ASC);
        sorts.add(stringSort);
        queryExtension.setSorts(sorts);
        search.setQueryExtension(queryExtension);

        int currentCount = 0;
        Search<PipelineForTransfer> rSearch = pipelineForTransferService.queryPageByPara(search);
        long totalCount = rSearch.getPage().getTotalCount();
        if (EmptyUtils.isNotEmpty(rSearch.getEntities())) {
            for (PipelineForTransfer pipeline : rSearch.getEntities()) {
                pipelineMap.put(pipeline.getPipelineCode(), pipeline);
            }
            currentCount = currentCount + rSearch.getEntities().size();
        }
        XxlJobHelper.log("load old pipeline: ["+currentCount + "/"+ totalCount +"]");
        while (hasNext(rSearch.getPage())) {
            page.setCurrentPage(page.getCurrentPage() + 1);
            rSearch = pipelineForTransferService.queryPageByPara(search);
            if (EmptyUtils.isNotEmpty(rSearch.getEntities())) {
                for (PipelineForTransfer pipeline : rSearch.getEntities()) {
                    pipelineMap.put(pipeline.getPipelineCode(), pipeline);
                }
                currentCount = currentCount + rSearch.getEntities().size();
            }
            XxlJobHelper.log("load old pipeline: ["+currentCount + "/"+ totalCount +"]");
        }

        return pipelineMap;
    }


     boolean hasNext(Page page) {
        long totalCount = page.getTotalCount();
        if (totalCount < 1L) {
            return false;
        } else {
            long pageSize = page.getPageSize();
            long currentPage = page.getCurrentPage();
            double pageCount = (double)totalCount / (double)pageSize;
            return pageCount > (double)currentPage;
        }
    }

    @Override
    public void clear() {
        if(EmptyUtils.isNotEmpty(currencyObjectMap)){
            currencyObjectMap.clear();
        }
        if(EmptyUtils.isNotEmpty(fiscalCalenderMap)){
            fiscalCalenderMap.clear();
        }
        errorCache.clear();
    }

    protected Map<String, String> getDictionaryItem(String dictCode){
        Dictionary dictionary = new Dictionary();
        dictionary.setDictCode(dictCode);
        dictionary = dictionaryService.queryOneByPara(dictionary);
        DictionaryItem qDictionaryItem = new DictionaryItem();
        qDictionaryItem.setDictId(dictionary.getId());
        List<DictionaryItem> items = dictionaryItemService.queryByPara(qDictionaryItem);
        Map<String, String> itemMap = new HashMap<>();
        for (DictionaryItem item : items) {
            itemMap.put(item.getItemKey(), item.getItemValue());
        }
        return itemMap;
    }
}
