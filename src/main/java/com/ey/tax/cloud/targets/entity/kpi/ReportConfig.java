package com.ey.tax.cloud.targets.entity.kpi;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ey.cn.tax.framework.mybatisplus.core.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-03-07 17:05:09
 *
 */
@Data
@TableName("target_report_config")
public class ReportConfig extends BaseEntity {
    /**
     * 财年 COLUMN:fiscal_year
     */
    private String fiscalYear;

    /**
     * 字段label COLUMN:label_name
     */
    private String labelName;

    /**
     * 功能名称 COLUMN:function_name
     */
    private String functionName;

    @TableField(exist = false)
    private List<String> functionNameList;

    /**
     * 是否显隐 COLUMN:is_show
     */
    private String isShow;

    /**
     * 是否用户控制 COLUMN:is_user_control
     */
    private String isUserControl;

    /**
     * 角色 COLUMN:role
     */
    //忽略为空
    @TableField(value = "role",whereStrategy = FieldStrategy.NOT_EMPTY)
    private String role;
    @TableField(exist = false)
    private List<String> roleList;

    private String columnCode;

    private Integer orderNum;

    private String textFormat;

    private String align;

    private String isSort;

    private Integer width;

    private String sortType;
}
