package com.ey.tax.cloud.targets.dto.pipeline;

import com.ey.cn.tax.framework.validator.QueryByIdGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-11-26 10:54:36
 * 
 */
@Data
@Schema(description = "根据id查询实体")
public class PipelineAmountRecordQueryByIdDTO {
    
    /**
     * 数据id
     */
    @Schema(description = "数据id")
    @NotBlank(message = "{PipelineAmountRecordDTO.id.NotBlank}", groups = {QueryByIdGroup.class})
    private String id;
}