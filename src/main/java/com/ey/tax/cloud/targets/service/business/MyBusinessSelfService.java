package com.ey.tax.cloud.targets.service.business;

import com.ey.cn.tax.framework.entity.Search;
import com.ey.cn.tax.framework.service.BaseService;
import com.ey.tax.cloud.targets.entity.business.MyBusinessScope;
import com.ey.tax.cloud.targets.entity.business.MyBusinessSelf;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-07-25 18:29:35
 *
 */
public interface MyBusinessSelfService extends BaseService<MyBusinessSelf> {

    Search<MyBusinessScope> queryPageByParaScope(Search<MyBusinessScope> search);
}
