package com.ey.tax.cloud.targets.dto.transfer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 字典简单数据DTO
 */
@Data
@Schema(description = "字典简单数据")
public class DictSimpleDataDTO {

    /**
     * 字典类型
     */
    @Schema(description = "字典类型")
    private String dictType;

    /**
     * ID
     */
    @Schema(description = "ID")
    private Integer id;

    /**
     * 字典标签
     */
    @Schema(description = "字典标签")
    private String label;

    /**
     * 父级ID
     */
    @Schema(description = "父级ID")
    private Integer pid;

    /**
     * 字典键值
     */
    @Schema(description = "字典键值")
    private String value;

}
