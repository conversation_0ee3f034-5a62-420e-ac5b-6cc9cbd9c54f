package com.ey.tax.cloud.targets.dto.pipeline;

import com.ey.cn.tax.framework.dto.BaseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-08-05 11:21:26
 *
 */
@Data
@Schema(description = "默认实体")
public class CoeUserDTO extends BaseDTO {
    /**
     * 地区 COLUMN:location
     */
    @Schema(description = "地区")
    private String location;

    /**
     * 财年 COLUMN:fiscal_year
     */
    @Schema(description = "财年")
    private String fiscalYear;

    /**
     * 用户id COLUMN:user_id
     */
    @Schema(description = "用户id")
    private String userId;

    /**
     * 状态 COLUMN:status
     */
    @Schema(description = "状态")
    private String status;
    @Schema(description = "备注")
    private String remark;
}
