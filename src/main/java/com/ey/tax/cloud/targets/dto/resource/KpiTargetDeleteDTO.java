package com.ey.tax.cloud.targets.dto.resource;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-03-18 11:03:51
 * 
 */
@Data
@Schema(description = "根据条件删除实体")
public class KpiTargetDeleteDTO {
    /**
     * COLUMN:user_id
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String userId;

    /**
     * COLUMN:fiscal_year
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String fiscalYear;

    /**
     * COLUMN:name
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String name;

    /**
     * COLUMN:gpn
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String gpn;

    /**
     * COLUMN:personal_revenue_target
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal personalRevenueTarget;

    /**
     * COLUMN:role_no
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer roleNo;

    /**
     * COLUMN:personal_target_coefficient
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal personalTargetCoefficient;

    /**
     * COLUMN:ssl_target
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal sslTarget;

    /**
     * COLUMN:ssl_target_coefficient
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal sslTargetCoefficient;

    /**
     * COLUMN:x_sl_x_ssl_referral_target
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal xSlXSslReferralTarget;

    /**
     * COLUMN:x_sl_x_ssl_referral_target_coefficient
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal xSlXSslReferralTargetCoefficient;

    /**
     * COLUMN:other_special_contribution
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String otherSpecialContribution;

    /**
     * COLUMN:other_special_contribution_coefficient
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal otherSpecialContributionCoefficient;

    /**
     * COLUMN:ratio_check
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal ratioCheck;

    /**
     * COLUMN:sales_deduction_from_target
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal salesDeductionFromTarget;

    /**
     * COLUMN:pipeline_deduction_from_target
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal pipelineDeductionFromTarget;

    /**
     * COLUMN:quality_effective_risk_management_deduction
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal qualityEffectiveRiskManagementDeduction;

    /**
     * COLUMN:exchange_rate_local_currency_to_rmb
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal exchangeRateLocalCurrencyToRmb;

    /**
     * 币种 COLUMN:currency
     */
    @Schema(description = "币种", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String currency;

    /**
     * COLUMN:smart_delivery_target
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal smartDeliveryTarget;

    /**
     * COLUMN:ut_target
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal utTarget;

    /**
     * COLUMN:margin_target
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal marginTarget;

    /**
     * COLUMN:average_revenue_day_target
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal averageRevenueDayTarget;

    /**
     * COLUMN:self_generated_target
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal selfGeneratedTarget;

    /**
     * COLUMN:quality_score_feedback
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal qualityScoreFeedback;

    /**
     * COLUMN:quality_score_product_pursuits
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal qualityScoreProductPursuits;

    /**
     * COLUMN:type
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String type;
}