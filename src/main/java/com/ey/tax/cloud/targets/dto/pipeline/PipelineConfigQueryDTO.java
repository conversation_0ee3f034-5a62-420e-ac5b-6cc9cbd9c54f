package com.ey.tax.cloud.targets.dto.pipeline;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-01-24 16:14:31
 *
 */
@Data
@Schema(description = "根据条件查询实体")
public class PipelineConfigQueryDTO {
    /**
     * 字段 COLUMN:column_name
     */
    @Schema(description = "字段", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String columnName;

    /**
     * 是否可编辑 COLUMN:is_edit
     */
    @Schema(description = "是否可编辑", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String isEdit;

    /**
     * 编辑后是否审批 COLUMN:is_edit_approve
     */
    @Schema(description = "编辑后是否审批", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String isEditApprove;

    /**
     * 颜色 COLUMN:color
     */
    @Schema(description = "颜色", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String color;

    /**
     * 表名 COLUMN:table_code
     */
    @Schema(description = "表名", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String tableCode;

    @Schema(description = "表名列表", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<String> tableCodeList;

    /**
     * pipeline页签 COLUMN:pipeline_tab
     */
    @Schema(description = "pipeline页签", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String pipelineTab;

    /**
     * pipeline分组 COLUMN:pipeline_group
     */
    @Schema(description = "pipeline分组", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String pipelineGroup;

    /**
     * 排序 COLUMN:order_num
     */
    @Schema(description = "排序", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer orderNum;

    /**
     * table名称 COLUMN:table_name
     */
    @Schema(description = "table名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String tableName;

    /**
     * 字段显示名称 COLUMN:column_display_name
     */
    @Schema(description = "字段显示名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String columnDisplayName;

    /**
     * 是否远程搜索 COLUMN:is_search
     */
    @Schema(description = "是否远程搜索", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer isSearch;

    /**
     * 提示信息 COLUMN:column_tips
     */
    @Schema(description = "提示信息", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String columnTips;

    /**
     * 字段控件类型 COLUMN:column_control_type
     */
    @Schema(description = "字段控件类型", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String columnControlType;

    @Schema(description = "角色", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String role;
    @Schema(description = "财年", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String fiscalYear;
    @Schema(description = "用户", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String userId;
}
