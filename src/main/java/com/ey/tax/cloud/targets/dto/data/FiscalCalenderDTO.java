package com.ey.tax.cloud.targets.dto.data;

import com.ey.cn.tax.framework.dto.BaseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-01-17 14:35:33
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "默认实体")
public class FiscalCalenderDTO extends BaseDTO {
    /**
     * COLUMN:fiscal_year
     */
    @Schema(description = "null")
    private String fiscalYear;

    /**
     * COLUMN:week
     */
    @Schema(description = "null")
    private Integer week;

    /**
     * COLUMN:calendar_date
     */
    @Schema(description = "null")
    private LocalDateTime calendarDate;

    /**
     * COLUMN:calendar_year
     */
    @Schema(description = "null")
    private Integer calendarYear;

    /**
     * COLUMN:calendar_quarter
     */
    @Schema(description = "null")
    private Integer calendarQuarter;

    /**
     * COLUMN:calendar_month
     */
    @Schema(description = "null")
    private Integer calendarMonth;

    /**
     * COLUMN:calendar_week
     */
    @Schema(description = "null")
    private Integer calendarWeek;

    /**
     * COLUMN:calendar_day
     */
    @Schema(description = "null")
    private Integer calendarDay;
}
