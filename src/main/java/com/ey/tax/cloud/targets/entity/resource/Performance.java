package com.ey.tax.cloud.targets.entity.resource;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ey.cn.tax.framework.mybatisplus.core.entity.BaseEntity;
import java.math.BigDecimal;
import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-01-17 14:35:35
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("target_resource_performance")
public class Performance extends BaseEntity {
    /**
     * COLUMN:user_id
     */
    private String userId;

    /**
     * COLUMN:level
     */
    private String level;

    /**
     * COLUMN:week
     */
    private Integer week;

    /**
     * COLUMN:ter_usd
     */
    private BigDecimal terUsd;

    /**
     * COLUMN:ner_usd
     */
    private BigDecimal nerUsd;

    /**
     * COLUMN:margin
     */
    private BigDecimal margin;

    /**
     * COLUMN:revenue_day
     */
    private Integer revenueDay;

    /**
     * COLUMN:ut
     */
    private BigDecimal ut;

    /**
     * COLUMN:gds_penetration
     */
    private BigDecimal gdsPenetration;

    /**
     * COLUMN:sales_usd
     */
    private BigDecimal salesUsd;

    /**
     * COLUMN:em_efforts_new_client
     */
    private BigDecimal emEffortsNewClient;

    /**
     * COLUMN:em_efforts_new_eng
     */
    private BigDecimal emEffortsNewEng;

    /**
     * COLUMN:pipelines_usd
     */
    private BigDecimal pipelinesUsd;

    /**
     * COLUMN:opportunity_type
     */
    private String opportunityType;


    private BigDecimal kpiScore;

    private String fiscalYear;

    private String city;
    private BigDecimal opportunityPartner;
    private BigDecimal opportunityPursuitLeader;

    private BigDecimal terRmb;

    private BigDecimal nerRmb;

    private BigDecimal weekTerRmb;

    private BigDecimal weekNerRmb;

    private String remark;


    @TableField(exist = false)
    private List<String> userIdList;

    @TableField(exist = false)
    private Integer type;

    @TableField(exist = false)
    private List<String> userIdsNotIn;
}
