package com.ey.tax.cloud.targets.entity.dashboard;

import lombok.Data;

import java.util.List;

@Data
public class Dashboard {

    /**
     * chart code
     */
    private String chartCode;

    /**
     * user type
     */
    private String userType;

    /**
     * data scope
     */
    private String dataScope;

    /**
     * 财年 COLUMN:FISCAL_YEAR
     */
    private String fiscalYear;


    /**
     * 子服务线 COLUMN:subserviceline
     */
    private List<String> subServiceLine;

    /**
     * competency
     */
    private List<String> competency;

    /**
     * 区域
     */
    private List<String> market;

    /**
     * 城市
     */
    private List<String> city;

    private String type;

    private String userId;

}
