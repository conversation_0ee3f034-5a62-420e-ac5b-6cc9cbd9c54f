select sum(sales.sales_delivery_credit_amount_usd) as amount, sales.city, sales.product_id as product
from tax_cloud_target.target_pipeline_sales_delivery_data sales
    left join tax_cloud_target.target_pipeline pipeline on sales.pipeline_id = pipeline.id
where sales.sales_delivery_credit = #{args.userId}
    and pipeline.won_fy = #{args.fiscalYear}
    and pipeline.status = 'Won'
    and pipeline.confirm_status in ('Processed','Amendment Submitted','Returned')
    and sales.product_id is not null
    and sales.is_del = 0
    and pipeline.is_del = 0
group by sales.city, sales.product_id order by amount desc;