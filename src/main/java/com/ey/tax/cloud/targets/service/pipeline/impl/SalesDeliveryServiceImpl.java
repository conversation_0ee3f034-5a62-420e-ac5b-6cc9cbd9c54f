package com.ey.tax.cloud.targets.service.pipeline.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ey.cn.tax.framework.constant.SystemConstants;
import com.ey.cn.tax.framework.entity.ICustomBaseEntity;
import com.ey.cn.tax.framework.mybatisplus.core.mapper.IEyBaseMapper;
import com.ey.cn.tax.framework.mybatisplus.core.query.EyQueryWrapper;
import com.ey.cn.tax.framework.mybatisplus.core.toolkit.EyWrappers;
import com.ey.cn.tax.framework.mybatisplus.extension.service.AbstractService;
import com.ey.cn.tax.framework.utils.ConvertUtils;
import com.ey.cn.tax.framework.utils.EmptyUtils;
import com.ey.tax.cloud.targets.constant.PipelineConstants;
import com.ey.tax.cloud.targets.entity.data.ExchangeRate;
import com.ey.tax.cloud.targets.entity.pipeline.Pipeline;
import com.ey.tax.cloud.targets.entity.pipeline.SalesDelivery;
import com.ey.tax.cloud.targets.entity.pipeline.SalesDeliveryApprove;
import com.ey.tax.cloud.targets.entity.pipeline.SalesDeliveryHis;
import com.ey.tax.cloud.targets.entity.user.UserAttribute;
import com.ey.tax.cloud.targets.mapper.pipeline.SalesDeliveryMapper;
import com.ey.tax.cloud.targets.service.data.ExchangeRateService;
import com.ey.tax.cloud.targets.service.data.FiscalCalenderService;
import com.ey.tax.cloud.targets.service.pipeline.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.collections.api.factory.Sets;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-01-16 15:29:01
 *
 */
@Service
public class SalesDeliveryServiceImpl extends AbstractService<SalesDeliveryMapper, SalesDelivery> implements SalesDeliveryService {


    @Autowired
    private PipelineService pipelineService;

    @Autowired
    private SalesDeliveryHisService salesDeliveryHisService;
    @Autowired
    private SalesDeliveryApproveService salesDeliveryApproveService;
    @Autowired
    private FiscalCalenderService fiscalCalenderService;

    /**
     *     a. 不涉及历史财年计算（当前财年项目）
     *     i. 计算时会根据kpi表fiscal_year和当前财年判断是否涉及历史财年。如果有值，则不涉及。将修改前的sales_delivery_credit_ratio、sales_delivery_credit_amount赋值到历史字段。通过新sales_delivery_credit_ratio计算sales_delivery_credit_amount并赋值。
     *     b. 涉及历史财年计算（跨财年项目）
     *     i. 如果fiscal_year和当前财年不相等，则将kpi数据转移到kip历史表中。之后根据历史财年的数据和新修改的数据的插值生成当前财年的数据插入kpi表。
     */
    @Override
    public List<SalesDelivery> submit(String pipelineId, String fiscalYear,String updateFy, List<SalesDelivery> list) {
        //获取pipeline
        Pipeline pipeline = pipelineService.queryById(pipelineId);
        //查询历史数据
        SalesDelivery salesDelivery = new SalesDelivery();
        salesDelivery.setPipelineId(pipelineId);
        List<SalesDelivery> oldList = queryByPara(salesDelivery);
        //判断是否涉及跨年
        if (oldList != null && oldList.size() > 0) {
            if ((!fiscalCalenderService.getCurrentFiscalCalender().equals(oldList.get(0).getFiscalYear()))) {
                //涉及跨年
                for (SalesDelivery delivery : list) {
                       //查找对应的历史数据
                        SalesDelivery oldDelivery = findDelivery(delivery, oldList);
                        if (oldDelivery != null) {
                            //有历史数据
                            //赋值历史数据
                            delivery.setSalesDeliveryCreditRatioLast(oldDelivery.getSalesDeliveryCreditRatio());
                            delivery.setSalesDeliveryCreditAmountLast(oldDelivery.getSalesDeliveryCreditAmount());
                            delivery.setSalesDeliveryCreditAmountUsdLast(oldDelivery.getSalesDeliveryCreditAmountUsd());
                        }
                        //计算新数据
                        BigDecimal salesDeliveryCreditRatio = delivery.getSalesDeliveryCreditRatio();
                        delivery.setSalesDeliveryCreditAmount(salesDeliveryCreditRatio.multiply(pipeline.getAmount()).multiply(new BigDecimal("0.01")));
                        delivery.setSalesDeliveryCreditAmountUsd(salesDeliveryCreditRatio.multiply(pipeline.getAmountUsd()).multiply(new BigDecimal("0.01")));
                }
            } else {
                //不涉及跨年
                for (SalesDelivery delivery : list) {
                    //查找对应的历史数据
                    SalesDelivery oldDelivery = findDelivery(delivery, oldList);
                    if (oldDelivery != null) {
                        //有历史数据
                        //赋值历史数据
                        delivery.setSalesDeliveryCreditRatioLast(oldDelivery.getSalesDeliveryCreditRatioLast());
                        delivery.setSalesDeliveryCreditAmountLast(oldDelivery.getSalesDeliveryCreditAmountLast());
                        delivery.setSalesDeliveryCreditAmountUsdLast(oldDelivery.getSalesDeliveryCreditAmountUsdLast());
                    }
                    //计算新数据
                    BigDecimal salesDeliveryCreditRatio = delivery.getSalesDeliveryCreditRatio();
                    delivery.setSalesDeliveryCreditAmount(salesDeliveryCreditRatio.multiply(pipeline.getAmount()).multiply(new BigDecimal("0.01")));
                    delivery.setSalesDeliveryCreditAmountUsd(salesDeliveryCreditRatio.multiply(pipeline.getAmountUsd()).multiply(new BigDecimal("0.01")));
                }
            }
        }
        //将oldList中的数据插入历史表
        List<SalesDeliveryHis> salesDeliveryHisList = ConvertUtils.convertEntityList2DTOList(oldList, SalesDeliveryHis.class);
        //去掉id
        salesDeliveryHisList.forEach(salesDeliveryHis -> salesDeliveryHis.setId(null));
        salesDeliveryHisService.save(salesDeliveryHisList);
        //删除oldList
        oldList.forEach(salesDelivery1 -> baseMapper.deleteById(salesDelivery1.getId()));
        //去掉list id
       list.forEach(salesDelivery1 -> salesDelivery1.setId(null));
        return list;
    }

    @Override
    public List<SalesDelivery> submit(Pipeline p, String pipelineId, String fiscalYear, String updateFy, List<SalesDelivery> list,Boolean approve) {

        //获取pipeline
        Pipeline pipeline = p;


        //查询历史数据
        SalesDelivery salesDelivery = new SalesDelivery();
        salesDelivery.setPipelineId(pipelineId);
        List<SalesDelivery> oldList = queryByPara(salesDelivery);
        //是否跨年
        Boolean isCrossYear = false;
        //判断是否涉及跨年
        if (oldList != null && oldList.size() > 0) {
            if ((!fiscalCalenderService.getCurrentFiscalCalender().equals(oldList.get(0).getFiscalYear()))) {
                isCrossYear = true;
                //涉及跨年
                for (SalesDelivery delivery : list) {
                    delivery.setFiscalYear(fiscalCalenderService.getCurrentFiscalCalender());
                    //查找对应的历史数据
                    SalesDelivery oldDelivery = findDelivery(delivery, oldList);
                    if (oldDelivery != null) {
                        //有历史数据
                        //赋值历史数据
                        delivery.setSalesDeliveryCreditRatioLast(oldDelivery.getSalesDeliveryCreditRatio());
                        delivery.setSalesDeliveryCreditAmountLast(oldDelivery.getSalesDeliveryCreditAmount());
                        delivery.setSalesDeliveryCreditAmountUsdLast(oldDelivery.getSalesDeliveryCreditAmountUsd());
                    }
                    //计算新数据
                    if(EmptyUtils.isEmpty(delivery.getUseAmount())||delivery.getUseAmount()==0){
                        BigDecimal salesDeliveryCreditRatio = delivery.getSalesDeliveryCreditRatio();
                        delivery.setSalesDeliveryCreditAmount(salesDeliveryCreditRatio.multiply(pipeline.getAmount()).multiply(new BigDecimal("0.01")));
                        delivery.setSalesDeliveryCreditAmountUsd(salesDeliveryCreditRatio.multiply(pipeline.getAmountUsd()).multiply(new BigDecimal("0.01")));
                    }else {
                        delivery.setSalesDeliveryCreditRatio( delivery.getSalesDeliveryCreditAmount().divide(pipeline.getAmount(),2,BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100")));
                        delivery.setSalesDeliveryCreditAmountUsd( delivery.getSalesDeliveryCreditRatio().multiply(pipeline.getAmountUsd()).divide(new BigDecimal("100"),2,BigDecimal.ROUND_HALF_UP));
                    }

                }
            } else {
                //不涉及跨年
                for (SalesDelivery delivery : list) {
                    delivery.setFiscalYear(fiscalCalenderService.getCurrentFiscalCalender());
                    //查找对应的历史数据
                    SalesDelivery oldDelivery = findDelivery(delivery, oldList);
                    if (oldDelivery != null) {
                        //有历史数据
                        //赋值历史数据
                        delivery.setSalesDeliveryCreditRatioLast(oldDelivery.getSalesDeliveryCreditRatioLast());
                        delivery.setSalesDeliveryCreditAmountLast(oldDelivery.getSalesDeliveryCreditAmountLast());
                        delivery.setSalesDeliveryCreditAmountUsdLast(oldDelivery.getSalesDeliveryCreditAmountUsdLast());
                    }
                    //计算新数据
                    if(EmptyUtils.isEmpty(delivery.getUseAmount())||delivery.getUseAmount()==0) {
                        BigDecimal salesDeliveryCreditRatio = delivery.getSalesDeliveryCreditRatio();
                        delivery.setSalesDeliveryCreditAmount(salesDeliveryCreditRatio.multiply(pipeline.getAmount()).multiply(new BigDecimal("0.01")));
                        delivery.setSalesDeliveryCreditAmountUsd(salesDeliveryCreditRatio.multiply(pipeline.getAmountUsd()).multiply(new BigDecimal("0.01")));
                    }else {
                        delivery.setSalesDeliveryCreditRatio( delivery.getSalesDeliveryCreditAmount().divide(pipeline.getAmount(),2,BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100")));
                        delivery.setSalesDeliveryCreditAmountUsd( delivery.getSalesDeliveryCreditRatio().multiply(pipeline.getAmountUsd()).divide(new BigDecimal("100"),2,BigDecimal.ROUND_HALF_UP));

                    }
                }
            }
        }else {
            //不涉及跨年
            for (SalesDelivery delivery : list) {
                delivery.setFiscalYear(fiscalCalenderService.getCurrentFiscalCalender());
                //计算新数据
                if(EmptyUtils.isEmpty(delivery.getUseAmount())) {
                    BigDecimal salesDeliveryCreditRatio = delivery.getSalesDeliveryCreditRatio();
                    delivery.setSalesDeliveryCreditAmount(salesDeliveryCreditRatio.multiply(pipeline.getAmount()).multiply(new BigDecimal("0.01")));
                    delivery.setSalesDeliveryCreditAmountUsd(salesDeliveryCreditRatio.multiply(pipeline.getAmountUsd()).multiply(new BigDecimal("0.01")));
                }else {
                    delivery.setSalesDeliveryCreditRatio( delivery.getSalesDeliveryCreditAmount().divide(pipeline.getAmount(),2,BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100")));
                    delivery.setSalesDeliveryCreditAmountUsd( delivery.getSalesDeliveryCreditRatio().multiply(pipeline.getAmountUsd()).divide(new BigDecimal("100"),2,BigDecimal.ROUND_HALF_UP));
                }
            }
        }
        //将oldList中的数据插入历史表

        if(approve){
            List<SalesDeliveryHis> salesDeliveryHisList = ConvertUtils.convertEntityList2DTOList(oldList, SalesDeliveryHis.class);
            //去掉id
            salesDeliveryHisList.forEach(salesDeliveryHis -> salesDeliveryHis.setId(null));
            salesDeliveryHisService.save(salesDeliveryHisList);
            //删除oldList
            if(isCrossYear){
                oldList.forEach(salesDelivery1 -> baseMapper.deleteById(salesDelivery1.getId()));
            }else {
                baseMapper.realDeleteBatchIds(oldList.stream().map(SalesDelivery::getId).collect(Collectors.toList()));
            }

        }

        //去掉list id
        list.forEach(salesDelivery1 -> salesDelivery1.setId(null));
        return list;
    }

    @Override
    public void apporve(List<SalesDelivery> list) {
        //生成数据到审批表
        List<SalesDeliveryApprove> salesDeliveryApproves = ConvertUtils.convertEntityList2DTOList(list, SalesDeliveryApprove.class);
        salesDeliveryApproveService.save(salesDeliveryApproves);

    }

    private SalesDelivery findDelivery(SalesDelivery delivery, List<SalesDelivery> oldList) {
        for (SalesDelivery oldDelivery : oldList) {
            if (oldDelivery.getSalesDeliveryCredit().equals(delivery.getSalesDeliveryCredit())) {
                return oldDelivery;
            }
        }
        return null;
    }


    @Override
    public List<Pipeline> queryByUser(String userId) {
        SalesDelivery salesDelivery = new SalesDelivery();
        salesDelivery.setSalesDeliveryCredit(userId);
        List<SalesDelivery> salesDeliveryList = queryByPara(salesDelivery);
        //获取pipelineId
        List<String> pipelineIdList = null;
        if(CollectionUtils.isNotEmpty(salesDeliveryList)){
            pipelineIdList = salesDeliveryList.stream().map(SalesDelivery::getPipelineId).collect(Collectors.toList());
            //查询pipeline，将sales占比写入pipeline中
            List<Pipeline> pipelineList = pipelineService.queryByIds(pipelineIdList);
            if(CollectionUtils.isNotEmpty(pipelineList)){
                for (Pipeline pipeline : pipelineList) {
                    for (SalesDelivery delivery : salesDeliveryList) {
                        if(pipeline.getId().equals(delivery.getPipelineId())){
                            pipeline.setSalesDeliveryCreditRatio(delivery.getSalesDeliveryCreditRatio());
                        }
                    }
                }
                return pipelineList;
            }
        }
        return null;
    }

    @Override
    public List<String> queryIdByUser(String userId) {
        SalesDelivery salesDelivery = new SalesDelivery();
        salesDelivery.setSalesDeliveryCredit(userId);
        List<SalesDelivery> salesDeliveryList = queryByPara(salesDelivery);
        //获取pipelineId
        List<String> pipelineIdList = null;
        if(CollectionUtils.isNotEmpty(salesDeliveryList)){
            pipelineIdList = salesDeliveryList.stream().map(SalesDelivery::getPipelineId).collect(Collectors.toList());
        }
        return pipelineIdList;
    }



    protected Collection<SalesDelivery> processUserName(Collection<SalesDelivery> entities) {
        if (CollectionUtils.isNotEmpty(entities)) {
            Set<String> ids = Sets.mutable.empty();
            for (SalesDelivery t : entities) {
                String createUid = t.getCreateUid();
                if (StringUtils.isNotBlank(createUid)) {
                    ids.add(createUid);
                }
                String updateUid = t.getUpdateUid();
                if (StringUtils.isNotBlank(updateUid)) {
                    ids.add(updateUid);
                }
                String salesDeliveryCredit = t.getSalesDeliveryCredit();
                if (StringUtils.isNotBlank(salesDeliveryCredit)) {
                    ids.add(salesDeliveryCredit);
                }
            }
            Map<String, String> userMap = getUserNamesByIds(ids);
            for (SalesDelivery t : entities) {
                String createUid = t.getCreateUid();
                if (StringUtils.isNotBlank(createUid)) {
                    if(EmptyUtils.isNotEmpty(userMap.get(createUid))){
                        t.setCreateUserName(userMap.get(createUid).split("@")[0]);
                    }

                }
                String updateUid = t.getUpdateUid();
                if (StringUtils.isNotBlank(updateUid)) {
                    if(EmptyUtils.isNotEmpty(userMap.get(updateUid))){
                        t.setUpdateUserName(userMap.get(updateUid).split("@")[0]);
                    }
                }
                String salesDeliveryCredit = t.getSalesDeliveryCredit();
                if (StringUtils.isNotBlank(salesDeliveryCredit)) {
                    if(EmptyUtils.isNotEmpty(userMap.get(salesDeliveryCredit))){
                        t.setSalesDeliveryCreditName(userMap.get(salesDeliveryCredit).split("@")[0]);
                    }
                }
            }
            return entities;
        } else {
            return Collections.emptyList();
        }
    }

    @Override
    public List<SalesDelivery> selectByPipelineIds(List<String> pipelineIds, String userId){
        logger.debug("selectByPipelineIds {}:{}", getEntitySimpleName(), pipelineIds);
        if (CollectionUtils.isEmpty(pipelineIds)) {
            return Collections.emptyList();
        }
        QueryWrapper<SalesDelivery> queryWrapper = Wrappers.query();
        queryWrapper.in("pipeline_id", pipelineIds);
        if (StringUtils.isNotBlank(userId)) {
            queryWrapper.eq("sales_delivery_credit", userId);
        }
        List<SalesDelivery> entities = baseMapper.selectList(queryWrapper);
        this.processUserName((Collection)entities);
        return entities;
    }

    @Transactional(readOnly = true)
    public List<SalesDelivery> queryByPara(SalesDelivery entity) {
        logger.debug("queryByPara {}:{}", getEntitySimpleName(), entity);
        QueryWrapper<SalesDelivery> queryWrapper = Wrappers.query(entity);
        queryWrapper.in(CollectionUtils.isNotEmpty(entity.getSalesDeliveryCreditList()), "sales_Delivery_Credit", entity.getSalesDeliveryCreditList());
        //salesDeliveryCreditRatiomin 大于等于
        queryWrapper.ge(entity.getSalesDeliveryCreditRatiomin() != null, "sales_delivery_credit_ratio", entity.getSalesDeliveryCreditRatiomin());
        List<SalesDelivery> entities = baseMapper.selectList(queryWrapper);
        this.processUserName((Collection)entities);
        return entities;
    }

    @Override
    public List<SalesDelivery> queryByPipelineAndUser(SalesDelivery salesDelivery) {
        QueryWrapper<SalesDelivery> queryWrapper = Wrappers.query();
        queryWrapper.in("pipeline_id", salesDelivery.getPipelineIds());
        queryWrapper.eq("sales_delivery_credit", salesDelivery.getSalesDeliveryCredit());
        return baseMapper.selectList(queryWrapper);
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void save(Collection<SalesDelivery> entities) {
        logger.debug("save {}:{}", getEntitySimpleName(), entities);

        if (CollectionUtils.isEmpty(entities)) {
            return;
        }
        for (List<SalesDelivery> partition : ListUtils.partition(entities.stream().toList(), 2000)) {
            baseMapper.insertBatch(partition);
        }
    }

    /**
     * 条件删除 物理删除
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void realDeleteByPipelineIds(List<String> pipelineIds) {
        logger.debug("realDeleteByPara {}:{}", getEntitySimpleName(), pipelineIds);
        if (pipelineIds == null) {
            return;
        }
        EyQueryWrapper<SalesDelivery> query = new EyQueryWrapper<>();
        query.in("pipeline_id", pipelineIds);
        baseMapper.realDelete(query);
    }

    @Override
    public List<SalesDelivery> selectIsDel() {
        return baseMapper.selectIsDel();
    }

    /**
     * 条件删除 物理删除
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void realDeleteByPipelineId(String pipelineId) {
        logger.debug("realDeleteByPara {}:{}", getEntitySimpleName(), pipelineId);
        if (pipelineId == null) {
            return;
        }
        EyQueryWrapper<SalesDelivery> query = new EyQueryWrapper<>();
        query.eq("pipeline_id", pipelineId);
        baseMapper.realDelete(query);
    }




}
