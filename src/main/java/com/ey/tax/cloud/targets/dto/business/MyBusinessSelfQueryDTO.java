package com.ey.tax.cloud.targets.dto.business;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-07-29 16:59:07
 *
 */
@Data
@Schema(description = "根据条件查询实体")
public class MyBusinessSelfQueryDTO {
    /**
     * COLUMN:fiscal_year
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String fiscalYear;

    /**
     * COLUMN:fiscal_week
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String fiscalWeek;

    /**
     * pipeline名称 COLUMN:pipeline_name
     */
    @Schema(description = "pipeline名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String pipelineName;

    /**
     * pipelineCode COLUMN:pipeline_code
     */
    @Schema(description = "pipelineCode", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String pipelineCode;

    /**
     * pace 0:灰色 1:黄色 2:绿色 COLUMN:pace
     */
    @Schema(description = "pace 0:灰色 1:黄色 2:绿色", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer pace;

    /**
     * billing COLUMN:billing
     */
    @Schema(description = "billing", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer billing;

    /**
     * billing_total_budget COLUMN:billing_total_budget
     */
    @Schema(description = "billing_total_budget", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal billingTotalBudget;

    /**
     * billing_billed COLUMN:billing_billed
     */
    @Schema(description = "billing_billed", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal billingBilled;

    /**
     * billing_unbilled COLUMN:billing_unbilled
     */
    @Schema(description = "billing_unbilled", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal billingUnbilled;

    /**
     * collection COLUMN:collection
     */
    @Schema(description = "collection", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer collection;

    /**
     * COLUMN:uncollected
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal uncollected;

    /**
     * completed COLUMN:completed
     */
    @Schema(description = "completed", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer completed;

    /**
     * 用户在Pipeline上的分配比例 COLUMN:percentage
     */
    @Schema(description = "用户在Pipeline上的分配比例", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal percentage;

    /**
     * pipeline的amount * 用户占比 COLUMN:allocated_value
     */
    @Schema(description = "pipeline的amount * 用户占比", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal allocatedValue;

    /**
     * ter COLUMN:ter
     */
    @Schema(description = "ter", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal ter;

    /**
     * ner COLUMN:ner
     */
    @Schema(description = "ner", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal ner;

    /**
     * revenue_day COLUMN:revenue_day
     */
    @Schema(description = "revenue_day", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal revenueDay;

    /**
     * wip COLUMN:wip
     */
    @Schema(description = "wip", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal wip;

    /**
     * billing_amount COLUMN:billing_amount
     */
    @Schema(description = "billing_amount", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal billingAmount;

    /**
     * Collection amount COLUMN:collection_amount
     */
    @Schema(description = "Collection amount", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal collectionAmount;

    /**
     * ar COLUMN:ar
     */
    @Schema(description = "ar", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal ar;

    /**
     * 关联用户id COLUMN:user_id
     */
    @Schema(description = "关联用户id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String userId;

    /**
     * tag 分为etd/ytd 查询用 COLUMN:tag
     */
    @Schema(description = "tag 分为etd/ytd 查询用", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String tag;

    /**
     * COLUMN:level
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String level;

    /**
     * COLUMN:ssl3
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String ssl3;

    /**
     * COLUMN:city
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String city;

    /**
     * COLUMN:region
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String region;

    /**
     * COLUMN:ssl2
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String ssl2;

    /**
     * COLUMN:sl1
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String sl1;

    /**
     * COLUMN:level_group
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String levelGroup;

    /**
     * COLUMN:group_id
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String groupId;

    /**
     * margin COLUMN:margin
     */
    @Schema(description = "margin", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal margin;

    /**
     * pipeline的id COLUMN:pipeline_id
     */
    @Schema(description = "pipeline的id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String pipelineId;
}
