package com.ey.tax.cloud.targets.service.pipeline.impl;

import com.ey.cn.tax.framework.mybatisplus.extension.service.AbstractService;
import com.ey.tax.cloud.targets.entity.pipeline.PipelineAmountRecord;
import com.ey.tax.cloud.targets.mapper.pipeline.PipelineAmountRecordMapper;
import com.ey.tax.cloud.targets.service.pipeline.PipelineAmountRecordService;
import org.springframework.stereotype.Service;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-11-26 10:54:36
 * 
 */
@Service
public class PipelineAmountRecordServiceImpl extends AbstractService<PipelineAmountRecordMapper, PipelineAmountRecord> implements PipelineAmountRecordService {
}