package com.ey.tax.cloud.targets.mapper.auth;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ey.cn.tax.framework.mybatisplus.core.mapper.IEyBaseMapper;
import com.ey.tax.cloud.targets.entity.auth.AuthGroup;

import java.util.List;
import java.util.Map;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2023-12-22 10:32:03
 *
 */
public interface AuthGroupMapper extends IEyBaseMapper<AuthGroup> {


     default List<AuthGroup> selectByMap(Map<String, Object> columnMap) {
         //添加查询条件partnerRoleList
        QueryWrapper<AuthGroup> qw = Wrappers.query();
        return this.selectList((Wrapper)qw.allEq(columnMap));
    }
}
