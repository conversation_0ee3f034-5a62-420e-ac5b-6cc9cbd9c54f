package com.ey.tax.cloud.targets.service.product;

import com.ey.cn.tax.framework.service.BaseService;
import com.ey.tax.cloud.targets.dto.data.DictionaryItemRepDTO;
import com.ey.tax.cloud.targets.entity.product.Product;
import com.ey.tax.cloud.targets.entity.product.ProductPath;

import java.util.List;
import java.util.Map;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-01-11 14:46:33
 *
 */
public interface ProductService extends BaseService<Product> {

    /**
     * 根据id查询名称
     */
    String queryNameById(String id, String fiscalYear);

    /**
     * 根据id从线程变量查询名称
     *
     * @return
     */
    String queryNameByIdFromThreadLocal(String id, String fiscalYear);

    List<DictionaryItemRepDTO> queryRepTree(String fiscalYear);

    Map<String, ProductPath> queryProductPath(String fiscalYear);

    void copyData(String fromYear,String toYear);

    List<Product> selectProductListByFiscalYear();

}
