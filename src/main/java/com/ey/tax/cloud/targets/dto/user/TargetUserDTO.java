package com.ey.tax.cloud.targets.dto.user;

import com.ey.cn.tax.framework.dto.BaseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2023-09-26 15:09:02
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "默认实体")
public class TargetUserDTO extends BaseDTO {
    /**
     * gpn COLUMN:gpn
     */
    @Schema(description = "gpn")
    private String gpn;

    /**
     * 用户账号 COLUMN:account
     */
    @Schema(description = "用户账号")
    private String account;

    /**
     * 用户昵称 COLUMN:nickname
     */
    @Schema(description = "用户昵称")
    private String nickname;

    /**
     * 用户电话号码 COLUMN:phone
     */
    @Schema(description = "用户电话号码")
    private String phone;

    /**
     * 用户邮箱地址 COLUMN:email
     */
    @Schema(description = "用户邮箱地址")
    private String email;

    /**
     * 用户组ID COLUMN:group_id
     */
    @Schema(description = "用户组ID")
    private Integer groupId;

    /**
     * 用户级别分组 COLUMN:level_group
     */
    @Schema(description = "用户级别分组")
    private String levelGroup;

    /**
     * 用户level COLUMN:level
     */
    @Schema(description = "用户level")
    private String level;

    /**
     * 服务线 COLUMN:serviceline
     */
    @Schema(description = "服务线")
    private String serviceline;

    /**
     * 服务线ID COLUMN:servicelineid
     */
    @Schema(description = "服务线ID")
    private Integer servicelineid;

    /**
     * 子服务线 COLUMN:subserviceline
     */
    @Schema(description = "子服务线")
    private String subserviceline;

    /**
     * 子服务线ID COLUMN:subservicelineid
     */
    @Schema(description = "子服务线ID")
    private Integer subservicelineid;

    /**
     * sl3 COLUMN:sl3
     */
    @Schema(description = "sl3")
    private String sl3;

    /**
     * sl3Id COLUMN:sl3id
     */
    @Schema(description = "sl3Id")
    private Integer sl3id;

    /**
     * domain COLUMN:domain
     */
    @Schema(description = "domain")
    private String domain;

    /**
     * domainId COLUMN:domainid
     */
    @Schema(description = "domainId")
    private Integer domainid;

    /**
     * 国家 COLUMN:country
     */
    @Schema(description = "国家")
    private String country;

    /**
     * 国家ID COLUMN:countryid
     */
    @Schema(description = "国家ID")
    private Integer countryid;

    /**
     * 区域 COLUMN:region
     */
    @Schema(description = "区域")
    private String region;

    /**
     * 区域ID COLUMN:regionid
     */
    @Schema(description = "区域ID")
    private Integer regionid;

    /**
     * 城市 COLUMN:city
     */
    @Schema(description = "城市")
    private String city;

    /**
     * 城市ID COLUMN:cityid
     */
    @Schema(description = "城市ID")
    private Integer cityid;

    /**
     * 用户状态 COLUMN:status
     */
    @Schema(description = "用户状态")
    private String status;

    /**
     * 用户类型 COLUMN:user_type
     */
    @Schema(description = "用户类型")
    private String userType;

    /**
     * 是否虚拟用户 COLUMN:is_virtual
     */
    @Schema(description = "是否虚拟用户")
    private Short isVirtual;
}
