package com.ey.tax.cloud.targets.dto.business;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-07-29 14:23:08
 *
 */
@Data
@Schema(description = "根据条件查询实体")
public class MyBusinessQueryDTO {
    /**
     * Fiscal year COLUMN:fiscal_year
     */
    @Schema(description = "Fiscal year(pipeline详情页不传)", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String fiscalYear;


    @Schema(description = "pipelineCode", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String pipelineCode;

    @Schema(description = "tag(pipeline详情页不传)", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String tag;

    @Schema(description = "type(pipeline详情页传self)", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String type;
    @Schema(description = "statusList(pipeline详情页不传)", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<String> statusList;

    @Schema(description = "engagementName(pipeline详情页不传)", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String engagementName;
    @Schema(description = "fiscalYearList(pipeline详情页不传)", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<String> fiscalYearList;
    @Schema(description = "engagementCode(pipeline详情页不传)", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String engCodeLike;




}
