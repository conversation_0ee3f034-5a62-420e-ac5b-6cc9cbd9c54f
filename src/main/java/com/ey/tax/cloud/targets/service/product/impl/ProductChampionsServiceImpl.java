package com.ey.tax.cloud.targets.service.product.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ey.cn.tax.framework.entity.Search;
import com.ey.cn.tax.framework.mybatisplus.core.mapper.IEyBaseMapper;
import com.ey.cn.tax.framework.mybatisplus.core.query.EyQueryWrapper;
import com.ey.cn.tax.framework.mybatisplus.core.toolkit.EyWrappers;
import com.ey.cn.tax.framework.mybatisplus.extension.service.AbstractService;
import com.ey.cn.tax.framework.utils.GroovyReflectionUtils;
import com.ey.tax.cloud.targets.entity.product.ProductChampions;
import com.ey.tax.cloud.targets.mapper.product.ProductChampionsMapper;
import com.ey.tax.cloud.targets.service.product.ProductChampionsService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2025-05-19 10:40:28
 *
 */
@Service
public class ProductChampionsServiceImpl extends AbstractService<ProductChampionsMapper, ProductChampions> implements ProductChampionsService {




    @Transactional(
            readOnly = true
    )
    public List<ProductChampions> queryByPara(ProductChampions entity) {
        this.logger.debug("queryByPara {}:{}", this.getEntitySimpleName(), entity);
        //如果没有status，则查询为1的
        if (entity.getStatus() == null) {
            entity.setStatus("1");
        }
        List<ProductChampions> entities = ((IEyBaseMapper)this.baseMapper).selectList(EyWrappers.query(entity));
        this.processUserName(entities);
        return entities;
    }

    @Transactional(
            readOnly = true
    )
    public Search<ProductChampions> queryPageByPara(Search<ProductChampions> search) {
        this.logger.debug("queryPageByPara {}:{}", this.getEntitySimpleName(), search);
        ProductChampions queryParams = (ProductChampions)(search.getQueryParams());
        EyQueryWrapper<ProductChampions> queryWrapper = EyWrappers.query(queryParams);
        if (queryParams.getQueryExtension() == null) {
            queryWrapper.extension(search.getQueryExtension());
        }
        //productList
        if (queryParams.getProductList() != null) {
            queryWrapper.in("product_id", queryParams.getProductList().split(","));
        }
        //productTypeList
        if (queryParams.getProductTypeList() != null) {
            queryWrapper.in("product_type", queryParams.getProductTypeList().split(","));
        }

        if (!queryWrapper.isOrdered()) {
            queryWrapper.orderByDesc("create_time");
        }



        IPage<ProductChampions> iPage = ((IEyBaseMapper)this.baseMapper).selectPage((Page) GroovyReflectionUtils.convertTo(Page.class, search.getPage(), true, new String[0]), queryWrapper);
        List<ProductChampions> records = iPage.getRecords();
        search.setRecords(records);
        if (CollectionUtils.isEmpty(records)) {
            search.getPage().setTotalCount(0L);
        } else {
            this.processUserName(records);
            search.getPage().setTotalCount(iPage.getTotal());
        }

        return search;
    }
}
