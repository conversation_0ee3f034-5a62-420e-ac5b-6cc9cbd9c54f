package com.ey.tax.cloud.targets.service.mail.impl;

import com.ey.cn.tax.framework.utils.EmptyUtils;
import com.ey.cn.tax.framework.utils.JacksonUtils;
import com.ey.tax.cloud.mail.dto.DetailSendRequest;
import com.ey.tax.cloud.mail.remote.MailFeignClient;
import com.ey.tax.cloud.targets.constant.BooleanConstants;
import com.ey.tax.cloud.targets.constant.MailConstants;
import com.ey.tax.cloud.targets.constant.PipelineConstants;
import com.ey.tax.cloud.targets.constant.UserConstants;
import com.ey.tax.cloud.targets.entity.pipeline.Pipeline;
import com.ey.tax.cloud.targets.entity.user.UserAttribute;
import com.ey.tax.cloud.targets.entity.user.UserGroup;
import com.ey.tax.cloud.targets.service.data.FiscalCalenderService;
import com.ey.tax.cloud.targets.service.mail.MailSendService;
import com.ey.tax.cloud.targets.service.pipeline.PipelineService;
import com.ey.tax.cloud.targets.service.user.UserAttributeService;
import com.ey.tax.cloud.targets.service.user.UserGroupService;
import com.xxl.job.core.context.XxlJobHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

@Service("emMailSend")
public class EmMailSendServiceImpl implements MailSendService {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private UserGroupService userGroupService;

    @Autowired
    private PipelineService pipelineService;

    @Autowired
    private FiscalCalenderService fiscalCalenderService;

    @Autowired
    private UserAttributeService userAttributeService;

    @Autowired
    private MailFeignClient mailFeignClient;

    @Value("${target.mail-type}")
    private String mailType;

    @Override
    public void send(String sendType) {
        //查询所有ep
        UserGroup qUserGroup = new UserGroup();
        qUserGroup.setType(UserConstants.TYPE_GROUP);
        qUserGroup.setCode(UserConstants.CODE_EM);
        UserGroup userGroup = userGroupService.queryOneByPara(qUserGroup);
        UserAttribute qUserAttribute = new UserAttribute();
        qUserAttribute.setGroupId(userGroup.getId());
        qUserAttribute.setFiscalYear(fiscalCalenderService.getCurrentFiscalCalender());
        qUserAttribute.setStatus(UserConstants.STATUS_ACTIVE);
        List<UserAttribute> userAttributeList = userAttributeService.queryByPara(qUserAttribute);
        for (UserAttribute userAttribute : userAttributeList) {
            // 只发送attr的SL2不等于FSO的,tax FSO
            String ssl2 = userAttribute.getSsl2();
            String userEmail = userAttribute.getUserEmail();
            if(UserConstants.SSL2_TAX_FSO.equals(ssl2)){
                XxlJobHelper.log("ignore fso email: " + userEmail);
                continue;
            }
            process(userAttribute, sendType, fiscalCalenderService.getCurrentFiscalCalender());
        }
    }

    private void process(UserAttribute userAttribute, String sendType, String fiscalYear) {
        DetailSendRequest param = new DetailSendRequest();
        param.setTemplateCode(MailConstants.MAIL_CODE_EM);

        String userEmail = userAttribute.getUserEmail();
        List<String> emails = new ArrayList<>();
        if(MailConstants.MAIL_TYPE_PROD.equals(mailType)){
            //生产环境
            emails.add(userEmail);
        }else{
            //其他环境
            emails.add("<EMAIL>");
        }
        /*if (userEmail.equalsIgnoreCase("<EMAIL>".toLowerCase()) ||
                userEmail.equalsIgnoreCase("<EMAIL>".toLowerCase()) ||
                userEmail.equalsIgnoreCase("<EMAIL>".toLowerCase())) {
            emails.add("<EMAIL>");
            emails.add("<EMAIL>");
            emails.add("<EMAIL>");
            emails.add("<EMAIL>");
            emails.add("<EMAIL>");
        }else {
            return;
        }*/

        XxlJobHelper.log("process em email: " + userEmail);

        Map<String, Object> businessData = new HashMap<>();
        businessData.put("name", userAttribute.getUserName());

        //User = 主EM，Status = won  and  confirm Status = processed
        Pipeline qPipeline = new Pipeline();
        qPipeline.setEngManagerId(userAttribute.getUserId());
        qPipeline.setConfirmStatus(PipelineConstants.APPROVE_STATUS_PROCESSED);
        qPipeline.setStatus(PipelineConstants.PIPELINE_STATUS_WON);
        qPipeline.setWonFy(fiscalYear);
        Long numOne = pipelineService.queryCountByPara(qPipeline);
        businessData.put("numOne", numOne);

        //User = 主EM，Status = Pursuit
        qPipeline = new Pipeline();
        qPipeline.setEngManagerId(userAttribute.getUserId());
        qPipeline.setStatus(PipelineConstants.PIPELINE_STATUS_PURSUIT);
        Long numTwo = pipelineService.queryCountByPara(qPipeline);

        if(EmptyUtils.isEmpty(numOne)){
            numOne = 0L;
        }
        if(EmptyUtils.isEmpty(numTwo)){
            numTwo = 0L;
        }
        long total = numOne + numTwo;
        if(total <= 0){
            return;
        }

        businessData.put("numTwo", numTwo);
        String jsonData = JacksonUtils.toJson(businessData);
        param.setBusinessData(Base64.getEncoder().encodeToString(jsonData.getBytes()));
        param.setTo(emails);
        param.setBusinessAccount("Target");
        param.setDelay(BooleanConstants.BOOLEAN_OFF);
        /*if(MailConstants.SEND_TYPE_NOW.equals(sendType)){
            param.setDelay(BooleanConstants.BOOLEAN_OFF);
        }else{
            param.setDelay(BooleanConstants.BOOLEAN_ON);
            //获取当前日期上午10点整
            LocalDateTime morning10 = LocalDateTime.now().withHour(10).withMinute(0).withSecond(0).withNano(0);
            param.setSendTime(morning10);
        }*/
        mailFeignClient.asyncSend(param);
        //XxlJobHelper.log("numOne: " + numOne + "numTwo: " + numTwo);
        try {
            Thread.sleep(3000);
        }catch (Exception e) {
            logger.info("send email error", e);
        }
    }
}
