package com.ey.tax.cloud.targets.dto.product;

import com.ey.cn.tax.framework.dto.BaseRepDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-01-11 14:46:33
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "查询返回实体")
public class ProductRepTreeDTO extends BaseRepDTO {

    @Schema(description = "id")
    private String id;
    @Schema(description = "子节点")
    private List<ProductRepTreeDTO> children;
    @Schema(description = "label")
    private String label;

    @Schema(description = "value")
    private String value;

    private Boolean show;

}
