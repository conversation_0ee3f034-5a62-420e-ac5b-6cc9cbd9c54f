package com.ey.tax.cloud.targets.dto.kpi;

import com.ey.cn.tax.framework.validator.DeleteByIdListGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-03-11 10:12:56
 *
 */
@Data
@Schema(description = "根据id列表删除实体")
public class KpiResultDeleteByIdListDTO {

    /**
     * 数据id列表
     */
    @Schema(description = "数据id列表")
    @NotNull(message = "{KpiResultDTO.ids.NotNull}", groups = {DeleteByIdListGroup.class})
    @Size(min = 1, message = "{KpiResultDTO.ids.Size}", groups = {DeleteByIdListGroup.class})
    private List<String> ids;
}
