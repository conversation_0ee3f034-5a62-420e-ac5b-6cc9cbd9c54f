package com.ey.tax.cloud.targets.dto.kpi;

import com.ey.cn.tax.framework.validator.UpdateByIdGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-03-07 17:05:09
 *
 */
@Data
@Schema(description = "根据id批量更新实体")
public class ReportConfigUpdateByIdDTO {

    /**
     * 数据id
     */
    @Schema(description = "数据id")
    @NotBlank(message = "{ReportConfigDTO.id.NotBlank}", groups = {UpdateByIdGroup.class})
    private String id;

    /**
     * 财年 COLUMN:fiscal_year
     */
    @Schema(description = "财年", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String fiscalYear;

    /**
     * 字段label COLUMN:label_name
     */
    @Schema(description = "字段label", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String labelName;

    /**
     * 功能名称 COLUMN:function_name
     */
    @Schema(description = "功能名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String functionName;

    /**
     * 是否显隐 COLUMN:is_show
     */
    @Schema(description = "是否显隐", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String isShow;

    /**
     * 是否用户控制 COLUMN:is_user_control
     */
    @Schema(description = "是否用户控制", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String isUserControl;

    /**
     * 角色 COLUMN:role
     */
    @Schema(description = "角色", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String role;

    @Schema(description = "字段code", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String columnCode;

    @Schema(description = "排序字段", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer orderNum;

    @Schema(description = "文本格式", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String textFormat;
    @Schema(description = "对齐方式", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String align;

    @Schema(description = "是否排序", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String isSort;

    @Schema(description = "宽度", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer width;

    @Schema(description = "排序类型", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String sortType;
}
