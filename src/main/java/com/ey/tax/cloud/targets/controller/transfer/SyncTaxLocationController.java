package com.ey.tax.cloud.targets.controller.transfer;

import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.dto.SearchDTO;
import com.ey.cn.tax.framework.validator.AddGroup;
import com.ey.cn.tax.framework.validator.DeleteByIdGroup;
import com.ey.cn.tax.framework.validator.DeleteByIdListGroup;
import com.ey.cn.tax.framework.validator.QueryByIdGroup;
import com.ey.cn.tax.framework.validator.QueryByIdListGroup;
import com.ey.cn.tax.framework.validator.QueryPageGroup;
import com.ey.cn.tax.framework.validator.UpdateByIdGroup;
import com.ey.cn.tax.framework.web.annotation.EyPostMapping;
import com.ey.cn.tax.framework.web.base.BaseController;
import com.ey.tax.cloud.targets.dto.transfer.SyncTaxLocationAddDTO;
import com.ey.tax.cloud.targets.dto.transfer.SyncTaxLocationBatchUpdateDTO;
import com.ey.tax.cloud.targets.dto.transfer.SyncTaxLocationDTO;
import com.ey.tax.cloud.targets.dto.transfer.SyncTaxLocationDeleteByIdDTO;
import com.ey.tax.cloud.targets.dto.transfer.SyncTaxLocationDeleteByIdListDTO;
import com.ey.tax.cloud.targets.dto.transfer.SyncTaxLocationQueryByIdDTO;
import com.ey.tax.cloud.targets.dto.transfer.SyncTaxLocationQueryByIdListDTO;
import com.ey.tax.cloud.targets.dto.transfer.SyncTaxLocationQueryDTO;
import com.ey.tax.cloud.targets.dto.transfer.SyncTaxLocationQueryPageDTO;
import com.ey.tax.cloud.targets.dto.transfer.SyncTaxLocationRepDTO;
import com.ey.tax.cloud.targets.dto.transfer.SyncTaxLocationUpdateByIdDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-04-17 16:03:29
 *
 */
@Tag(name="中台-TaxLocation")
public interface SyncTaxLocationController extends BaseController<SyncTaxLocationDTO> {
    /**
     * add SyncTaxLocation from http
     */
    @Operation(summary = "新增", description = "新增一条数据")
    @EyPostMapping(value = "/add.do")
    ResponseDTO<Void> add(@RequestBody @Validated({AddGroup.class}) SyncTaxLocationAddDTO dto);

    /**
     * addList SyncTaxLocation from http
     */
    @Operation(summary = "批量新增", description = "批量新增数据")
    @EyPostMapping(value = "/addList.do")
    ResponseDTO<Void> addList(@RequestBody List<SyncTaxLocationAddDTO> dtos);

    /**
     * delete SyncTaxLocation from http
     */
    @Operation(summary = "根据id删除", description = "根据id删除一条数据")
    @EyPostMapping(value = "/deleteById.do")
    ResponseDTO<Void> deleteById(@RequestBody @Validated({DeleteByIdGroup.class}) SyncTaxLocationDeleteByIdDTO dto);

    /**
     * deleteList SyncTaxLocation from http
     */
    @Operation(summary = "根据id列表删除", description = "根据id列表批量删除数据")
    @EyPostMapping(value = "/deleteByIdList.do")
    ResponseDTO<Void> deleteByIdList(@RequestBody @Validated({DeleteByIdListGroup.class}) SyncTaxLocationDeleteByIdListDTO dto);

    /**
     * update SyncTaxLocation from http
     */
    @Operation(summary = "根据id更新", description = "根据id更新一条数据")
    @EyPostMapping(value = "/updateById.do")
    ResponseDTO<Void> updateById(@RequestBody @Validated({UpdateByIdGroup.class}) SyncTaxLocationUpdateByIdDTO dto);

    /**
     * updateBatch SyncTaxLocation from http
     */
    @Operation(summary = "批量更新", description = "批量更新数据")
    @EyPostMapping(value = "/updateBatch.do")
    ResponseDTO<Void> updateBatch(@RequestBody List<SyncTaxLocationBatchUpdateDTO> dtos);

    /**
     * query SyncTaxLocation from http
     */
    @Operation(summary = "根据id查询", description = "根据id查询数据")
    @EyPostMapping(value = "/queryById.do")
    ResponseDTO<SyncTaxLocationRepDTO> queryById(@RequestBody @Validated({QueryByIdGroup.class}) SyncTaxLocationQueryByIdDTO dto);

    /**
     * queryByIdList SyncTaxLocation from http
     */
    @Operation(summary = "根据id列表查询", description = "根据id列表查询数据")
    @EyPostMapping(value = "/queryByIdList.do")
    ResponseDTO<List<SyncTaxLocationRepDTO>> queryByIdList(@RequestBody @Validated({QueryByIdListGroup.class}) SyncTaxLocationQueryByIdListDTO dto);

    /**
     * queryList SyncTaxLocation from http
     */
    @Operation(summary = "查询列表", description = "查询数据列表")
    @EyPostMapping(value = "/queryList.do")
    ResponseDTO<List<SyncTaxLocationRepDTO>> queryList(@RequestBody SyncTaxLocationQueryDTO dto);

    /**
     * Page SyncTaxLocation from http
     */
    @Operation(summary = "分页查询", description = "根据条件分页查询")
    @EyPostMapping(value = "/queryPage.do")
    ResponseDTO<SearchDTO<SyncTaxLocationRepDTO>> queryPage(@RequestBody @Validated({QueryPageGroup.class}) SearchDTO<SyncTaxLocationQueryPageDTO> searchDTO);

    /**
     * Count SyncTaxLocation from http
     */
    @Operation(summary = "查询数量", description = "根据条件查询数量")
    @EyPostMapping(value = "/queryCount.do")
    ResponseDTO<Long> queryCount(@RequestBody SyncTaxLocationQueryDTO syncTaxLocationDTO);

    /**
     * One SyncTaxLocation from http
     */
    @Operation(summary = "查询单个数据", description = "根据条件查询一条数据,如果查询到多条则返回异常.")
    @EyPostMapping(value = "/queryOne.do")
    ResponseDTO<SyncTaxLocationRepDTO> queryOne(@RequestBody SyncTaxLocationQueryDTO syncTaxLocationDTO);

    /**
     * exist SyncTaxLocation from http
     */
    @Operation(summary = "查询是否存在", description = "根据条件查询是否存在")
    @EyPostMapping(value = "/exist.do")
    ResponseDTO<Boolean> exist(@RequestBody SyncTaxLocationQueryDTO syncTaxLocationDTO);
}
