package com.ey.tax.cloud.targets.dto.resource;

import com.ey.cn.tax.framework.dto.BaseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-01-17 14:35:35
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "默认实体")
public class PerformanceDTO extends BaseDTO {
    /**
     * COLUMN:user_id
     */
    @Schema(description = "null")
    private String userId;

    /**
     * COLUMN:pipeline_id
     */
    @Schema(description = "null")
    private String pipelineId;

    /**
     * COLUMN:name
     */
    @Schema(description = "null")
    private String name;

    /**
     * COLUMN:level
     */
    @Schema(description = "null")
    private String level;

    /**
     * COLUMN:week
     */
    @Schema(description = "null")
    private Integer week;

    /**
     * COLUMN:ter_usd
     */
    @Schema(description = "null")
    private BigDecimal terUsd;

    /**
     * COLUMN:ner_usd
     */
    @Schema(description = "null")
    private BigDecimal nerUsd;

    /**
     * COLUMN:margin
     */
    @Schema(description = "null")
    private BigDecimal margin;

    /**
     * COLUMN:revenue_day
     */
    @Schema(description = "null")
    private Integer revenueDay;

    /**
     * COLUMN:ut
     */
    @Schema(description = "null")
    private BigDecimal ut;

    /**
     * COLUMN:gds_penetration
     */
    @Schema(description = "null")
    private BigDecimal gdsPenetration;

    /**
     * COLUMN:sales_usd
     */
    @Schema(description = "null")
    private BigDecimal salesUsd;

    /**
     * COLUMN:em_efforts_new_client
     */
    @Schema(description = "null")
    private BigDecimal emEffortsNewClient;

    /**
     * COLUMN:em_efforts_new_eng
     */
    @Schema(description = "null")
    private BigDecimal emEffortsNewEng;

    /**
     * COLUMN:pipelines_usd
     */
    @Schema(description = "null")
    private BigDecimal pipelinesUsd;

    /**
     * COLUMN:opportunity_type
     */
    @Schema(description = "null")
    private String opportunityType;
}
