package com.ey.tax.cloud.targets.dto.kpi;

import com.ey.cn.tax.framework.dto.BaseRepDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-03-15 10:34:04
 * 
 */
@Data
@Schema(description = "查询返回实体")
public class KpiSupplementaryHisRepDTO extends BaseRepDTO {
    /**
     * COLUMN:user_id
     */
    @Schema(description = "null")
    private String userId;

    /**
     * COLUMN:fiscal_year
     */
    @Schema(description = "null")
    private String fiscalYear;

    /**
     * COLUMN:people_score
     */
    @Schema(description = "null")
    private BigDecimal peopleScore;

    /**
     * COLUMN:knowledge_score
     */
    @Schema(description = "null")
    private BigDecimal knowledgeScore;

    /**
     * COLUMN:compliance_score
     */
    @Schema(description = "null")
    private BigDecimal complianceScore;
}