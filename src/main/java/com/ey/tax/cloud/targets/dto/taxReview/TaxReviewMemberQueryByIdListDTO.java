package com.ey.tax.cloud.targets.dto.taxReview;

import com.ey.cn.tax.framework.validator.QueryByIdListGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-02-26 11:15:06
 * 
 */
@Data
@Schema(description = "根据id列表查询实体")
public class TaxReviewMemberQueryByIdListDTO {
    
    /**
     * 数据id列表
     */
    @Schema(description = "数据id列表")
    @NotNull(message = "{TaxReviewMemberDTO.ids.NotNull}", groups = {QueryByIdListGroup.class})
    @Size(min = 1, message = "{TaxReviewMemberDTO.ids.Size}", groups = {QueryByIdListGroup.class})
    private List<String> ids;
}