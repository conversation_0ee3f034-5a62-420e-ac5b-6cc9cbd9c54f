package com.ey.tax.cloud.targets.controller.tigeroar;

import com.ey.cn.tax.framework.web.annotation.EyAuthorize;
import com.ey.cn.tax.framework.web.annotation.EyPostMapping;
import com.ey.cn.tax.framework.web.base.ScanInterController;
import com.ey.tax.cloud.targets.dto.transfer.CategoryRequestDTO;
import com.ey.tax.cloud.targets.dto.transfer.CategoryResponseDTO;
import com.ey.tax.cloud.targets.dto.transfer.DictListAllSimpleRequestDTO;
import com.ey.tax.cloud.targets.dto.transfer.DictMergeRequestDTO;
import com.ey.tax.cloud.targets.dto.transfer.TokenRequestDTO;
import com.ey.tax.cloud.targets.dto.transfer.TokenResponseDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.RequestBody;

@Tag(name="tigetoar")
public interface TigeroarController extends ScanInterController {

    @Operation(summary = "获取token", description = "获取token")
    @EyPostMapping(value = "/getToken.do")
    TokenResponseDTO getToken(@RequestBody TokenRequestDTO dto);

    @Operation(summary = "获取字典", description = "获取字典")
    @EyPostMapping(value = "/getCategoryData.do")
    CategoryResponseDTO getCategoryData(@RequestBody CategoryRequestDTO dto, HttpServletRequest request);

    @Operation(summary = "获取字典", description = "获取字典")
    @EyPostMapping(value = "/getCategoryData2.do")
    CategoryResponseDTO getCategoryData2(@RequestBody CategoryRequestDTO dto, HttpServletRequest request);

    @Operation(summary = "获取特殊字典", description = "获取字典")
    @EyPostMapping(value = "/getDictData.do")
    Object getDictData(@RequestBody CategoryRequestDTO dto, HttpServletRequest request);
    @Operation(summary = "获取特殊字典2", description = "获取字典")
    @EyPostMapping(value = "/getDictData2.do")
    Object getDictData2(@RequestBody CategoryRequestDTO dto, HttpServletRequest request);

    @Operation(summary = "获取特殊字典2", description = "获取字典")
    @EyPostMapping(value = "/getMiddle.do")
    CategoryResponseDTO getMiddle(@RequestBody CategoryRequestDTO categoryRequestDTO, HttpServletRequest request);

    @Operation(summary = "获取码表列表", description = "前端公共字典表接口")
    @EyPostMapping(value = "/list-all-simple-with-app.do")
    Object listAllSimpleWithApp(@RequestBody DictListAllSimpleRequestDTO dto, HttpServletRequest request);

    @Operation(summary = "合并获取独立字典码表列表", description = "公共独立字典表合并获取接口")
    @EyPostMapping(value = "/listDefaultCategoryDataByCategoryCodes.do")
    Object listDefaultCategoryDataByCategoryCodes(@RequestBody DictMergeRequestDTO dto, HttpServletRequest request);

}
