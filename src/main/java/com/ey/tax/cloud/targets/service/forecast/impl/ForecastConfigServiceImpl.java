package com.ey.tax.cloud.targets.service.forecast.impl;

import com.ey.cn.tax.framework.context.EyUserContextHolder;
import com.ey.cn.tax.framework.entity.Search;
import com.ey.cn.tax.framework.mybatisplus.core.query.EyQueryWrapper;
import com.ey.cn.tax.framework.mybatisplus.extension.service.AbstractService;
import com.ey.cn.tax.framework.query.StringSort;
import com.ey.cn.tax.framework.utils.EmptyUtils;
import com.ey.tax.cloud.targets.constant.BooleanConstants;
import com.ey.tax.cloud.targets.constant.ForecastConstants;
import com.ey.tax.cloud.targets.constant.PipelineConstants;
import com.ey.tax.cloud.targets.entity.forecast.ForecastConfig;
import com.ey.tax.cloud.targets.entity.pipeline.Pipeline;
import com.ey.tax.cloud.targets.entity.product.Product;
import com.ey.tax.cloud.targets.entity.user.UserAttribute;
import com.ey.tax.cloud.targets.mapper.forecast.ForecastConfigMapper;
import com.ey.tax.cloud.targets.service.data.FiscalCalenderService;
import com.ey.tax.cloud.targets.service.forecast.ForecastBatchService;
import com.ey.tax.cloud.targets.service.forecast.ForecastConfigService;
import com.ey.tax.cloud.targets.service.pipeline.PipelineService;
import com.ey.tax.cloud.targets.service.pipeline.SalesDeliveryService;
import com.ey.tax.cloud.targets.service.product.ProductService;
import com.ey.tax.cloud.targets.service.user.UserAttributeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.*;
import java.util.*;

/**
 * <AUTHOR>
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * @date 2024-01-17 14:35:34
 */
@Service
public class ForecastConfigServiceImpl extends AbstractService<ForecastConfigMapper, ForecastConfig> implements ForecastConfigService {

    @Autowired
    private ForecastBatchService forecastBatchService;

    @Autowired
    private PipelineService pipelineService;

    @Autowired
    private FiscalCalenderService fiscalCalenderService;

    @Autowired
    private ProductService productService;

    @Autowired
    private UserAttributeService userAttributeService;

    @Autowired
    private SalesDeliveryService salesDeliveryService;

    @Transactional(rollbackFor = {Throwable.class})
    public boolean save(ForecastConfig entity) {
        entity.setFiscalYear(fiscalCalenderService.getCurrentFiscalCalender());
        return super.save(entity);
    }

    @Transactional(rollbackFor = {Throwable.class})
    public void save(Collection<ForecastConfig> entities) {

        for (ForecastConfig entity : entities) {
            entity.setFiscalYear(fiscalCalenderService.getCurrentFiscalCalender());
        }
        super.save(entities);
    }

    @Transactional(rollbackFor = {Throwable.class})
    public boolean update(ForecastConfig entity) {
        return super.update(entity);
    }

    @Transactional(rollbackFor = {Throwable.class})
    public boolean updateBatchByIds(Collection<ForecastConfig> entities) {
        forecastBatchService.check();
        return super.updateBatchByIds(entities);
    }

    @Transactional(rollbackFor = {Throwable.class})
    public int updateByIds(ForecastConfig data, Collection<String> ids) {
        forecastBatchService.check();
        return super.updateByIds(data, ids);
    }

    @Transactional(rollbackFor = {Throwable.class})
    public int updateByPara(ForecastConfig query, ForecastConfig data) {
        forecastBatchService.check();
        return super.updateByPara(query, data);
    }

    @Transactional
    public boolean deleteById(ForecastConfig data) {
        forecastBatchService.check();
        return super.deleteById(data);
    }

    @Override
    public List<ForecastConfig> queryByPipelineIds(List<String> pipelines, String fiscalYear) {
        EyQueryWrapper<ForecastConfig> queryWrapper = new EyQueryWrapper<>();
        queryWrapper.in("pipeline_id", pipelines);
        queryWrapper.eq("fiscal_year", fiscalYear);
        return baseMapper.selectList(queryWrapper);
    }

    public void queryByPipelineCodes(List<Pipeline> pipelines, String currentYear) {
        if (EmptyUtils.isEmpty(pipelines)) {
            return;
        }
        //String lastYear = fiscalCalenderService.getLastFiscalCalender();
        //String currentYear = fiscalCalenderService.getCurrentFiscalCalender();
        List<String> pipelineIds = new ArrayList<>();
        List<String> productIds = new ArrayList<>();
        for (Pipeline pipeline : pipelines) {
            pipelineIds.add(pipeline.getForcastPipelineId());
            productIds.add(pipeline.getProductId());
        }
        List<ForecastConfig> oldForecastConfigList = this.queryByPipelineIds(pipelineIds, currentYear);
        Map<String, ForecastConfig> oldForecastConfigMap = new HashMap<>();
        if (EmptyUtils.isNotEmpty(oldForecastConfigList)) {
            for (ForecastConfig oldForecastConfig : oldForecastConfigList) {
                oldForecastConfigMap.put(oldForecastConfig.getPipelineId(), oldForecastConfig);
            }
        }
        Product p = new Product();
        p.setStatus(1);
        List<Product> productList = productService.queryByPara(p);
        Map<String, Product> productMap = new HashMap<>();
        for (Product product : productList) {
            productMap.put(product.getProductId()+product.getFiscalYear(), product);
        }
        for (Pipeline pipeline : pipelines) {
            String wonFy = pipeline.getWonFy();
            String pursuitFy = pipeline.getPursuitFy();
            String recurring = pipeline.getRecurring();
            String status = pipeline.getStatus();
            String confirmStatus = pipeline.getConfirmStatus();
            Integer winRate = pipeline.getWinRate();
            String emId = pipeline.getEngManagerId();
            String epId = pipeline.getOpprPartnerId();
            String currentUserId = EyUserContextHolder.get().getAuthUserId();
            ForecastConfig oldForecastConfig = oldForecastConfigMap.get(pipeline.getForcastPipelineId());
            Product product = productMap.get(pipeline.getProductId()+wonFy);
            /*if (ForecastConstants.PRODUCT_501.equals(pipeline.getProductId())) {
                continue;
            }*/
            /*if(lastYear.equals(wonFy) &&
                    (PipelineConstants.RECURRING_YES_NEW_WIN.equals(recurring) || PipelinseConstants.RECURRING_YES_CONTINUOUS.equals(recurring)) &&
                    PipelineConstants.PIPELINE_STATUS_WON.equals(status) &&
                    (PipelineConstants.APPROVE_STATUS_PROCESSED.equals(confirmStatus) || PipelineConstants.APPROVE_STATUS_AMENDMENT_SUBMITTED.equals(confirmStatus))){

                 * 1.上一财年(won的财年)
                 * 2.Pipeline的RECURRING的值为“Yes-NewWin、Yes-Continuous”
                 * 3.业务状态为“won”
                 * 4.审批状态为 “processed、amendment submitted”


                processLastYearConfig(newForecastConfig, oldForecastConfig, product, pipeline);
                pipeline.setConfigType(newForecastConfig.getConfigType());
                pipeline.setForecastStartDate(newForecastConfig.getForecastStartDate());
                pipeline.setForecastEndDate(newForecastConfig.getForecastEndDate());
                if(currentUserId.equals(emId) || currentUserId.equals(epId)){
                    pipeline.setIsConfigEdit(BooleanConstants.BOOLEAN_ON);
                }else {
                    pipeline.setIsConfigEdit(BooleanConstants.BOOLEAN_OFF);
                }
            }*/

            /**
             * 1.本财年新增的Pipeline(won的财年)
             * 2.业务状态为“won”
             * 3.审批状态为 “processed、amendment submitted”
             */
            if (currentYear.equals(wonFy) &&
                    PipelineConstants.PIPELINE_STATUS_WON.equals(status) &&
                    (PipelineConstants.APPROVE_STATUS_PROCESSED.equals(confirmStatus)
                            || PipelineConstants.APPROVE_STATUS_AMENDMENT_SUBMITTED.equals(confirmStatus)
                            || PipelineConstants.APPROVE_STATUS_RETURNED.equals(confirmStatus))) {
                ForecastConfig newForecastConfig = new ForecastConfig();
                processNewWonConfig(newForecastConfig, oldForecastConfig, pipeline);
                pipeline.setConfigType(newForecastConfig.getConfigType());
                pipeline.setForecastStartDate(newForecastConfig.getForecastStartDate());
                pipeline.setForecastEndDate(newForecastConfig.getForecastEndDate());
                if (currentUserId.equals(emId) || currentUserId.equals(epId)) {
                    pipeline.setIsConfigEdit(BooleanConstants.BOOLEAN_ON);
                } else {
                    pipeline.setIsConfigEdit(BooleanConstants.BOOLEAN_OFF);
                }
            }

            /**
             * 1.本财年潜在的Pipeline(创建财年)
             * 2.业务状态为“pursuit”
             * 3.“Possibility of win”大于等于50%的服务机会
             */
            if (currentYear.equals(pursuitFy) &&
                    PipelineConstants.PIPELINE_STATUS_PURSUIT.equals(status) &&
                    (EmptyUtils.isNotEmpty(winRate) && winRate >= 50)) {
                ForecastConfig newForecastConfig = new ForecastConfig();
                processPotentialConfig(newForecastConfig, oldForecastConfig, pipeline);
                pipeline.setConfigType(newForecastConfig.getConfigType());
                pipeline.setForecastStartDate(newForecastConfig.getForecastStartDate());
                pipeline.setForecastEndDate(newForecastConfig.getForecastEndDate());
                if (currentUserId.equals(emId) || currentUserId.equals(epId)) {
                    pipeline.setIsConfigEdit(BooleanConstants.BOOLEAN_ON);
                } else {
                    pipeline.setIsConfigEdit(BooleanConstants.BOOLEAN_OFF);
                }
            }

            //分摊期间不可编辑状态
            if ((PipelineConstants.PIPELINE_STATUS_LOST.equals(status) || PipelineConstants.PIPELINE_STATUS_DECLINED.equals(status))
                    || (PipelineConstants.PIPELINE_STATUS_WON.equals(status) && EmptyUtils.isEmpty(confirmStatus))
                    || (PipelineConstants.PIPELINE_STATUS_PURSUIT.equals(status) && (EmptyUtils.isNotEmpty(winRate) && winRate < 50))
                   ) {
                pipeline.setIsConfigEdit(BooleanConstants.BOOLEAN_OFF);
            }
        }
    }

    /**
     * @param type
     * @return
     */
    @Override
    public List<ForecastConfig> queryByType(String type, List<StringSort> stringSortList) {
        List<Pipeline> pipelineList = null;
        List<Pipeline> newWonPipelineList = null;
        List<ForecastConfig> newForecastConfigList = new ArrayList<>();
        if (ForecastConstants.CONFIG_TYPE_LAST_YEAR.equals(type)) {
            newWonPipelineList = queryNewWonPipeline(stringSortList);
            pipelineList = queryLastYearPipeline(stringSortList);
        } else if (ForecastConstants.CONFIG_TYPE_NEW_WIN.equals(type)) {
            pipelineList = queryNewWonPipeline(stringSortList);
        } else {
            pipelineList = queryPotentialPipeline(stringSortList);
        }
        if (EmptyUtils.isEmpty(pipelineList)) {
            return newForecastConfigList;
        }
        List<String> pipelineIds = new ArrayList<>();
        List<String> productIds = new ArrayList<>();
        List<String> userIds = new ArrayList<>();
        Map<String, Pipeline> pipelineMap = new HashMap<>();
        for (Pipeline pipeline : pipelineList) {

            pipelineIds.add(pipeline.getId());
            pipelineMap.put(pipeline.getId(), pipeline);
            productIds.add(pipeline.getProductId());
            userIds.add(pipeline.getEngManagerId());
            userIds.add(pipeline.getOpprPartnerId());
        }
        if (EmptyUtils.isEmpty(pipelineIds)) {
            return newForecastConfigList;
        }
        Product p = new Product();
        p.setStatus(1);
        List<Product> productList = productService.queryByPara(p);
        Map<String, Product> productMap = new HashMap<>();
        for (Product product : productList) {
            productMap.put(product.getProductId()+product.getFiscalYear(), product);
        }
        Map<String, UserAttribute> userAttributeMap = userAttributeService.queryUserAttributeByIds(userIds);
        Map<String, String> userMap = new HashMap<>();
        for (UserAttribute userAttribute : userAttributeMap.values()) {
            userMap.put(userAttribute.getUserId(), userAttribute.getUserName());
        }
        String currentFy = fiscalCalenderService.getCurrentFiscalCalender();
        List<ForecastConfig> forecastConfigList = this.queryByPipelineIds(pipelineIds, currentFy);
        Map<String, ForecastConfig> oldForecastConfigMap = new HashMap<>();
        for (ForecastConfig forecastConfig : forecastConfigList) {
            oldForecastConfigMap.put(forecastConfig.getPipelineId(), forecastConfig);
        }

        Map<String, Pipeline> newWonPipelineMap = new HashMap<>();
        if (EmptyUtils.isNotEmpty(newWonPipelineList)) {
            for (Pipeline newWonpipeline : newWonPipelineList) {
                String key = newWonpipeline.getClientCode() + newWonpipeline.getProductId();
                newWonPipelineMap.put(key, newWonpipeline);
            }
        }
        for (Pipeline pipeline : pipelineList) {

            String emId = pipeline.getEngManagerId();
            String epId = pipeline.getOpprPartnerId();
            ForecastConfig forecastConfig = new ForecastConfig();
            forecastConfig.setClientName(pipeline.getClientName());
            forecastConfig.setPipelineName(pipeline.getPipelineName());
            forecastConfig.setPipelineId(pipeline.getId());
            forecastConfig.setProductId(pipeline.getProductId());
            Product product = productMap.get(pipeline.getProductId()+pipeline.getWonFy());
            if (EmptyUtils.isNotEmpty(product)) {
                forecastConfig.setProductName(product.getProductName());
            }
            forecastConfig.setEngManagerId(emId);
            forecastConfig.setEngManagerName(userMap.get(emId));
            forecastConfig.setOpprPartnerId(epId);
            forecastConfig.setOpprPartnerName(userMap.get(epId));
            forecastConfig.setAmount(pipeline.getAmount());
            forecastConfig.setAmountUsd(pipeline.getAmountUsd());
            String currentUserId = EyUserContextHolder.get().getAuthUserId();
            if (currentUserId.equals(emId) || currentUserId.equals(epId)) {
                forecastConfig.setIsEdit(BooleanConstants.BOOLEAN_ON);
            } else {
                forecastConfig.setIsEdit(BooleanConstants.BOOLEAN_OFF);
            }
            ForecastConfig oldForecastConfig = oldForecastConfigMap.get(forecastConfig.getPipelineId());
            if (ForecastConstants.CONFIG_TYPE_LAST_YEAR.equals(type)) {
                String key = pipeline.getClientCode() + pipeline.getProductId();
                Pipeline newWonPipeline = newWonPipelineMap.get(key);
                processLastYearConfig(forecastConfig, oldForecastConfig, product, pipeline);
                if (EmptyUtils.isNotEmpty(newWonPipeline)&&EmptyUtils.isEmpty(forecastConfig.getUserId())) {
                    forecastConfig.setAmortize(BooleanConstants.BOOLEAN_OFF);
                }
                int currentFiscalYear = Integer.parseInt(currentFy);
                if(EmptyUtils.isEmpty(forecastConfig.getForecastEndDate())){
                    continue;
                }
                if (forecastConfig.getForecastEndDate().getYear() < currentFiscalYear) {
                    continue;
                }
            } else if (ForecastConstants.CONFIG_TYPE_NEW_WIN.equals(type)) {
                processNewWonConfig(forecastConfig, oldForecastConfig, pipeline);
                int currentFiscalYear = Integer.parseInt(currentFy);
                if (forecastConfig.getForecastEndDate().getYear() < currentFiscalYear) {
                    continue;
                }
            } else {
                processPotentialConfig(forecastConfig, oldForecastConfig, pipeline);
            }
            newForecastConfigList.add(forecastConfig);
            String status = pipeline.getStatus();
            String confirmStatus = pipeline.getConfirmStatus();
            Integer winRate = pipeline.getWinRate();
            //分摊期间不可编辑状态
            if ((PipelineConstants.PIPELINE_STATUS_LOST.equals(status) || PipelineConstants.PIPELINE_STATUS_DECLINED.equals(status))
                    || (PipelineConstants.PIPELINE_STATUS_WON.equals(status) && EmptyUtils.isEmpty(confirmStatus))
                    || (PipelineConstants.PIPELINE_STATUS_PURSUIT.equals(status) && (EmptyUtils.isNotEmpty(winRate) && winRate >= 50))
                   ) {
                pipeline.setIsConfigEdit(BooleanConstants.BOOLEAN_OFF);
            }
        }
        //保存
        List<ForecastConfig> insertForecastConfigList = new ArrayList<>();
        for(ForecastConfig newForecastConfig:newForecastConfigList){
            if(EmptyUtils.isEmpty(newForecastConfig.getId())){
                insertForecastConfigList.add(newForecastConfig);
            }
        }
        if(EmptyUtils.isNotEmpty(insertForecastConfigList)){
            this.save(insertForecastConfigList);
        }
        return newForecastConfigList;
    }

    private void processLastYearConfig(ForecastConfig newForecastConfig, ForecastConfig oldForecastConfig,
                                       Product product, Pipeline pipeline) {
        LocalDateTime forecastStartDate;
        LocalDateTime forecastEndDate;
        if (EmptyUtils.isNotEmpty(product) && BooleanConstants.BOOLEAN_ON.equals(product.getIsForward())) {
            //获取的 product.getShareDateStart()为月份，拼接当前年份转为LocalDateTime
            int shareDateStart = product.getShareDateStart();
            int shareDateEnd = product.getShareDateEnd();
            int startYear = LocalDate.now().getYear();
            int endYear = LocalDate.now().getYear();
            if (shareDateEnd < shareDateStart) {
                endYear = startYear + 1;
            }
            forecastStartDate = LocalDateTime.of(startYear, shareDateStart, 1, 0, 0);
            LocalDate forecastEnd = LocalDate.of(endYear, shareDateEnd, 1);
            forecastEndDate = LocalDateTime.of(endYear, shareDateEnd, forecastEnd.lengthOfMonth(), 23, 59);
        } else {
            forecastStartDate = pipeline.getKickoffWeek();
            forecastEndDate = pipeline.getClosingWeek();
            if (EmptyUtils.isEmpty(forecastEndDate)) {
                forecastEndDate = forecastStartDate.plusMonths(6);
            }

        }
        newForecastConfig.setConfigType(ForecastConstants.CONFIG_TYPE_LAST_YEAR);
        newForecastConfig.setFiscalYear(fiscalCalenderService.getCurrentFiscalCalender());
        if (EmptyUtils.isNotEmpty(oldForecastConfig)) {
            forecastStartDate = oldForecastConfig.getForecastStartDate();
            forecastEndDate = oldForecastConfig.getForecastEndDate();
            newForecastConfig.setAmortize(oldForecastConfig.getAmortize());
            newForecastConfig.setUserId(oldForecastConfig.getUserId());
            newForecastConfig.setId(oldForecastConfig.getId());
        } else {
            if(EmptyUtils.isEmpty(pipeline.getClosingWeek())){
                return;
            }
            if(EmptyUtils.isEmpty(pipeline.getKickoffWeek())){
                return;
            }
            if(pipeline.getKickoffWeek().plusMonths(13).isBefore(pipeline.getClosingWeek())){
                return;
            }
            newForecastConfig.setAmortize(BooleanConstants.BOOLEAN_ON);
        }
        newForecastConfig.setForecastStartDate(forecastStartDate);
        newForecastConfig.setForecastEndDate(forecastEndDate);

    }

    private void processNewWonConfig(ForecastConfig newForecastConfig, ForecastConfig oldForecastConfig, Pipeline pipeline) {
        LocalDateTime forecastStartDate = pipeline.getFirstConfirmDate();
        LocalDateTime forecastEndDate = forecastStartDate.plusMonths(3);
        if (EmptyUtils.isNotEmpty(oldForecastConfig)) {
            forecastStartDate = oldForecastConfig.getForecastStartDate();
            forecastEndDate = oldForecastConfig.getForecastEndDate();
            newForecastConfig.setId(oldForecastConfig.getId());
            pipeline.setForecastConfigId(oldForecastConfig.getId());
        }
        //计算项目预测分摊总天数：总天数 = (预测分摊截止日期 - 预测分摊起始日期) + 1
        Duration totalDuration = Duration.between(forecastStartDate, forecastEndDate);
        ;
        long totalDays = totalDuration.toDays() + 1;
        //计算项目预测完成天数：完成天数 = (当前日期 - 预测分摊起始日期) + 1
        Duration completeDuration = Duration.between(forecastStartDate, LocalDateTime.now());
        long completeDays = completeDuration.toDays() + 1;
        //Completion rate完成率 = (完成天数 / 总天数) * 100%
        BigDecimal percentage;
        if (completeDays >= totalDays) {
            percentage = new BigDecimal(100);
        } else if (completeDays <= 0) {
            percentage = BigDecimal.ZERO;
        } else {
            BigDecimal bCompleteDays = new BigDecimal(completeDays);
            BigDecimal bTotalDays = new BigDecimal(totalDays);
            percentage = bCompleteDays
                    .divide(bTotalDays, 2, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal(100))
                    .setScale(0, RoundingMode.HALF_UP);
        }
        newForecastConfig.setCompletionRate(percentage);
        newForecastConfig.setForecastStartDate(forecastStartDate);
        newForecastConfig.setForecastEndDate(forecastEndDate);
    }

    private void processPotentialConfig(ForecastConfig newForecastConfig, ForecastConfig oldForecastConfig, Pipeline pipeline) {
        LocalDateTime forecastStartDate = pipeline.getAnticipatedWinDate();
        if(EmptyUtils.isEmpty(forecastStartDate)){
            return;
        }
        LocalDateTime forecastEndDate = forecastStartDate.plusMonths(3);
        newForecastConfig.setWinPossibility(new BigDecimal(pipeline.getWinRate()));
        if (EmptyUtils.isNotEmpty(oldForecastConfig)) {
            forecastStartDate = oldForecastConfig.getForecastStartDate();
            forecastEndDate = oldForecastConfig.getForecastEndDate();
            newForecastConfig.setWinPossibility(oldForecastConfig.getWinPossibility());
            newForecastConfig.setId(oldForecastConfig.getId());
            pipeline.setForecastConfigId(oldForecastConfig.getId());
        }
        newForecastConfig.setForecastStartDate(forecastStartDate);
        newForecastConfig.setForecastEndDate(forecastEndDate);
    }

    /**
     * 1.上一财年(won的财年)
     * 2.Pipeline的RECURRING的值为“Yes-NewWin、Yes-Continuous”
     * 3.业务状态为“won”
     * 4.审批状态为 “processed、amendment submitted”
     *
     * @return
     */
    private List<Pipeline> queryLastYearPipeline(List<StringSort> stringSortList) {
        List<String> pipelineIds = salesDeliveryService.queryIdByUser(EyUserContextHolder.get().getAuthUserId());
        if (EmptyUtils.isEmpty(pipelineIds)) {
            pipelineIds = new ArrayList<>();
            pipelineIds.add("0");
        }
        String lastFy = fiscalCalenderService.getLastFiscalCalender();
        Pipeline qPipeline = new Pipeline();
        qPipeline.setWonFy(lastFy);
        List<String> recurrings = new ArrayList<>();
        recurrings.add(PipelineConstants.RECURRING_YES_NEW_WIN);
        recurrings.add(PipelineConstants.RECURRING_YES_CONTINUOUS);
        qPipeline.setRecurrings(recurrings);
        qPipeline.setStatus(PipelineConstants.PIPELINE_STATUS_WON);
        List<String> confirmStatuss = new ArrayList<>();
        confirmStatuss.add(PipelineConstants.APPROVE_STATUS_PROCESSED);
        confirmStatuss.add(PipelineConstants.APPROVE_STATUS_AMENDMENT_SUBMITTED);
        qPipeline.setConfirmStatuss(confirmStatuss);
        qPipeline.setIds(pipelineIds);
        return pipelineService.queryForForecast(qPipeline, stringSortList);
    }

    /**
     * 1.本财年新增的Pipeline(won的财年)
     * 2.业务状态为“won”
     * 3.审批状态为 “processed、amendment submitted”
     * <p>
     * 1.First_confirm_date在追溯日期之后的Pipeline
     * 2.该Pipeline的分摊日期跨到新财年
     *
     * @return
     */
    private List<Pipeline> queryNewWonPipeline(List<StringSort> stringSortList) {
        //查询当前用户参与的pipeline
        List<String> pipelineIds = salesDeliveryService.queryIdByUser(EyUserContextHolder.get().getAuthUserId());
        if (EmptyUtils.isEmpty(pipelineIds)) {
            pipelineIds = new ArrayList<>();
            pipelineIds.add("0");
        }
        //财年开始日 + 往前追溯6个月
        String currentFy = fiscalCalenderService.getCurrentFiscalCalender();
        LocalDateTime firstDayOfFiscal = fiscalCalenderService.getFirstDayOfFiscal(currentFy);
        int year = Year.from(firstDayOfFiscal).getValue();
        LocalDateTime firstDayOfYear = LocalDateTime.of(year, Month.JANUARY, 1, 0, 0);

        Pipeline qPipeline = new Pipeline();
        qPipeline.setWonFy(currentFy);
        qPipeline.setStatus(PipelineConstants.PIPELINE_STATUS_WON);
        List<String> confirmStatuss = new ArrayList<>();
        confirmStatuss.add(PipelineConstants.APPROVE_STATUS_PROCESSED);
        confirmStatuss.add(PipelineConstants.APPROVE_STATUS_AMENDMENT_SUBMITTED);
        confirmStatuss.add(PipelineConstants.APPROVE_STATUS_RETURNED);
        qPipeline.setConfirmStatuss(confirmStatuss);
        qPipeline.setIds(pipelineIds);
        List<Pipeline> defaultList = pipelineService.queryForForecast(qPipeline, stringSortList);
        if (EmptyUtils.isEmpty(defaultList)) {
            defaultList = new ArrayList<>();
        }

        Pipeline qDatePipeline = new Pipeline();
        qDatePipeline.setFirstConfirmDateForecast(firstDayOfYear);
        qDatePipeline.setIds(pipelineIds);
        List<Pipeline> datePipelineList = pipelineService.queryForForecast(qDatePipeline, stringSortList);
        for (Pipeline datePipeline : datePipelineList) {
            datePipeline.setProcessFlag(BooleanConstants.BOOLEAN_ON);
            datePipeline.setFirstConfirmDateForecast(firstDayOfYear);
        }
        defaultList.addAll(datePipelineList);
        return defaultList;
    }

    /**
     * 1.本财年新增的Pipeline(创建财年)
     * 2.业务状态为“pursuit”
     * 3.“Possibility of win”大于等于50%的服务机会
     *
     * @return
     */
    private List<Pipeline> queryPotentialPipeline(List<StringSort> stringSortList) {
        List<String> pipelineIds = salesDeliveryService.queryIdByUser(EyUserContextHolder.get().getAuthUserId());
        if (EmptyUtils.isEmpty(pipelineIds)) {
            pipelineIds = new ArrayList<>();
            pipelineIds.add("0");
        }
        //财年开始日 + 往前追溯6个月
        String currentFy = fiscalCalenderService.getCurrentFiscalCalender();
        LocalDateTime firstDayOfFiscal = fiscalCalenderService.getFirstDayOfFiscal(currentFy);
        int year = Year.from(firstDayOfFiscal).getValue();
        LocalDateTime firstDayOfYear = LocalDateTime.of(year, Month.JANUARY, 1, 0, 0);

        Pipeline qPipeline = new Pipeline();
        qPipeline.setPursuitFy(currentFy);
        qPipeline.setStatus(PipelineConstants.PIPELINE_STATUS_PURSUIT);
        qPipeline.setWinRate(50);
        qPipeline.setIds(pipelineIds);

        List<Pipeline> defaultList = pipelineService.queryForForecast(qPipeline, stringSortList);
        if (EmptyUtils.isEmpty(defaultList)) {
            defaultList = new ArrayList<>();
        }

        Pipeline qDatePipeline = new Pipeline();
        qDatePipeline.setAnticipatedWinDateForecast(firstDayOfYear);
        qDatePipeline.setIds(pipelineIds);
        List<Pipeline> datePipelineList = pipelineService.queryForForecast(qDatePipeline, stringSortList);
        for (Pipeline datePipeline : datePipelineList) {
            datePipeline.setProcessFlag(BooleanConstants.BOOLEAN_ON);
            datePipeline.setAnticipatedWinDateForecast(firstDayOfYear);
        }
        defaultList.addAll(datePipelineList);
        return defaultList;
    }

}
