package com.ey.tax.cloud.targets.controller.data;

import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.dto.SearchDTO;
import com.ey.cn.tax.framework.validator.AddGroup;
import com.ey.cn.tax.framework.validator.DeleteByIdGroup;
import com.ey.cn.tax.framework.validator.DeleteByIdListGroup;
import com.ey.cn.tax.framework.validator.QueryByIdGroup;
import com.ey.cn.tax.framework.validator.QueryByIdListGroup;
import com.ey.cn.tax.framework.validator.QueryPageGroup;
import com.ey.cn.tax.framework.validator.UpdateByIdGroup;
import com.ey.cn.tax.framework.web.annotation.EyPostMapping;
import com.ey.cn.tax.framework.web.base.BaseController;
import com.ey.tax.cloud.targets.dto.data.*;
import feign.Response;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import java.util.Map;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-02-19 11:47:19
 *
 */
@Tag(name="模板实现")
public interface TemplateInstanceController extends BaseController<TemplateInstanceDTO> {
    /**
     * add TemplateInstance from http
     */
    @Operation(summary = "新增", description = "新增一条数据")
    @EyPostMapping(value = "/add.do")
    ResponseDTO<Void> add(@RequestParam("file") MultipartFile file, @RequestParam("tempCode") String tempCode, @RequestParam("tempName") String tempName, @RequestParam("status") String status,  @RequestParam("fiscalYear") String fiscalYear,@RequestParam(value ="remark",required = false) String remark);

    /**
     * addList TemplateInstance from http
     */
    @Operation(summary = "批量新增", description = "批量新增数据")
    @EyPostMapping(value = "/addList.do")
    ResponseDTO<Void> addList(@RequestBody List<TemplateInstanceAddDTO> dtos);

    /**
     * delete TemplateInstance from http
     */
    @Operation(summary = "根据id删除", description = "根据id删除一条数据")
    @EyPostMapping(value = "/deleteById.do")
    ResponseDTO<Void> deleteById(@RequestBody @Validated({DeleteByIdGroup.class}) TemplateInstanceDeleteByIdDTO dto);

    /**
     * deleteList TemplateInstance from http
     */
    @Operation(summary = "根据id列表删除", description = "根据id列表批量删除数据")
    @EyPostMapping(value = "/deleteByIdList.do")
    ResponseDTO<Void> deleteByIdList(@RequestBody @Validated({DeleteByIdListGroup.class}) TemplateInstanceDeleteByIdListDTO dto);

    /**
     * update TemplateInstance from http
     */
    @Operation(summary = "根据id更新", description = "根据id更新一条数据")
    @EyPostMapping(value = "/updateById.do")
    ResponseDTO<Void> updateById(@RequestBody @Validated({UpdateByIdGroup.class}) TemplateInstanceUpdateByIdDTO dto);

    @Operation(summary = "更新模板实现", description = "新增一条数据")
    @EyPostMapping(value = "/update.do")
    ResponseDTO<Void> update(@RequestParam(value ="file", required = false) MultipartFile file, @RequestParam(value ="tempCode", required = false) String tempCode,@RequestParam("id") String id, @RequestParam(value ="tempName",required = false) String tempName, @RequestParam(value ="status",required = false) String status,  @RequestParam(value ="fiscalYear",required = false) String fiscalYear,@RequestParam(value ="remark",required = false) String remark);


    /**
     * updateBatch TemplateInstance from http
     */
    @Operation(summary = "批量更新", description = "批量更新数据")
    @EyPostMapping(value = "/updateBatch.do")
    ResponseDTO<Void> updateBatch(@RequestBody List<TemplateInstanceBatchUpdateDTO> dtos);

    /**
     * query TemplateInstance from http
     */
    @Operation(summary = "根据id查询", description = "根据id查询数据")
    @EyPostMapping(value = "/queryById.do")
    ResponseDTO<TemplateInstanceRepDTO> queryById(@RequestBody @Validated({QueryByIdGroup.class}) TemplateInstanceQueryByIdDTO dto);

    /**
     * queryByIdList TemplateInstance from http
     */
    @Operation(summary = "根据id列表查询", description = "根据id列表查询数据")
    @EyPostMapping(value = "/queryByIdList.do")
    ResponseDTO<List<TemplateInstanceRepDTO>> queryByIdList(@RequestBody @Validated({QueryByIdListGroup.class}) TemplateInstanceQueryByIdListDTO dto);

    /**
     * queryList TemplateInstance from http
     */
    @Operation(summary = "查询列表", description = "查询数据列表")
    @EyPostMapping(value = "/queryList.do")
    ResponseDTO<List<TemplateInstanceRepDTO>> queryList(@RequestBody TemplateInstanceQueryDTO dto);

    /**
     * Page TemplateInstance from http
     */
    @Operation(summary = "分页查询", description = "根据条件分页查询")
    @EyPostMapping(value = "/queryPage.do")
    ResponseDTO<SearchDTO<TemplateInstanceRepDTO>> queryPage(@RequestBody @Validated({QueryPageGroup.class}) SearchDTO<TemplateInstanceQueryPageDTO> searchDTO);

    /**
     * Count TemplateInstance from http
     */
    @Operation(summary = "查询数量", description = "根据条件查询数量")
    @EyPostMapping(value = "/queryCount.do")
    ResponseDTO<Long> queryCount(@RequestBody TemplateInstanceQueryDTO templateInstanceDTO);

    /**
     * One TemplateInstance from http
     */
    @Operation(summary = "查询单个数据", description = "根据条件查询一条数据,如果查询到多条则返回异常.")
    @EyPostMapping(value = "/queryOne.do")
    ResponseDTO<TemplateInstanceRepDTO> queryOne(@RequestBody TemplateInstanceQueryDTO templateInstanceDTO);

    /**
     * exist TemplateInstance from http
     */
    @Operation(summary = "查询是否存在", description = "根据条件查询是否存在")
    @EyPostMapping(value = "/exist.do")
    ResponseDTO<Boolean> exist(@RequestBody TemplateInstanceQueryDTO templateInstanceDTO);

    /**
     * 模板上传
     */
    @Operation(summary = "模板上传", description = "模板上传")
    @EyPostMapping(value = "/importTemp.do")
    ResponseDTO<Map<String,String>> importTemp(@RequestParam("file") MultipartFile file);

    @Operation(summary = "模板上传并更新", description = "模板上传并更新")
    @EyPostMapping(value = "/importAndUpdateTemp.do")
    ResponseDTO<Void> importAndUpdateTemp(MultipartFile file,String tempId);

    /**
     * 模板下载
     */
    @Operation(summary = "模板下载", description = "模板下载")
    @EyPostMapping(value = "/downloadTemp.do")
    void downloadTemp(@RequestBody TemplateInstanceQueryByIdDTO dto);


    /**
     * 根据数据集编码计算模板
     */
    @Operation(summary = "根据数据集编码计算模板", description = "根据数据集编码计算模板")
    @EyPostMapping(value = "/calTempByDataSet.do")
    void calTempByDataSet(String tempId,String dataSetCode,Object json);


    /**
     * 上传文件并导入
     */
    @Operation(summary = "上传文件并导入数据（上传页面用）", description = "上传文件并导入")
    @EyPostMapping(value = "/uploadAndImportFile.do")
    ResponseDTO<Void> uploadAndImportFile(@RequestParam("file") MultipartFile file, @RequestParam("type") @Schema(description = "类型 追加：add，覆盖：cover") String type, @RequestParam("code")@Schema(description = "表名：Tax Reviw User、Tax Review calculation...") String code,@RequestParam("fiscalYear")@Schema(description = "财年") String fiscalYear);


    /**
     * 下载模板数据
     */
    @Operation(summary = "下载模板数据（下载页面用）", description = "下载模板数据")
    @EyPostMapping(value = "/downloadTempData.do")
    void downloadTempData(@RequestBody TemplateInstanceDownloadDTO dto);
    @Operation(summary = "异步下载模板数据（下载页面用）", description = "下载模板数据")
    @EyPostMapping(value = "/downloadTempDataAsync.do")
    ResponseDTO downloadBatch(@RequestBody TemplateInstanceDownloadDTO dto);

    /**
     * 根据id下载文件
     * @return
     */
    @Operation(summary = "根据id下载文件", description = "根据id下载数据")
    @EyPostMapping(value = "/downloadById.do")
    void downloadById(@RequestBody TemplateInstanceQueryByIdDTO dto);
    @Operation(summary = "根据id下载异步文件", description = "根据id下载数据")
    @EyPostMapping(value = "/downloadBatchById.do")
    void downloadBatchById(@RequestBody TemplateInstanceQueryByIdDTO dto);

    @Operation(summary = "查询是否显示（上传页面用）", description = "上传文件并导入")
    @EyPostMapping(value = "/getIsShow.do")
    ResponseDTO<IsShowDTO> getIsShow();
    @Operation(summary = "设置是否显示（上传页面用）", description = "上传文件并导入")
    @EyPostMapping(value = "/setIsShow.do")
    ResponseDTO<Void> setIsShow(@RequestBody IsShowDTO isShowDTO);



    @Operation(summary = "根据模板编码计算KPI结果", description = "根据模板编码计算KPI结果")
    @EyPostMapping(value = "/calKpiResult.do")
    void calKpiResult(String tempCode, Object json, String fiscalYear);
}
