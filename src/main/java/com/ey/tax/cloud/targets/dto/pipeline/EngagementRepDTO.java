package com.ey.tax.cloud.targets.dto.pipeline;

import com.ey.cn.tax.framework.dto.BaseRepDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-05-20 14:23:44
 *
 */
@Data
@Schema(description = "查询返回实体")
public class EngagementRepDTO extends BaseRepDTO {
    /**
     * pipeline的id COLUMN:pipeline_id
     */
    @Schema(description = "pipeline的id")
    private String pipelineId;
    @Schema(description = "pipeline的code")
    private String pipelineCode;

    @Schema(description = "pipeline的code")
    private String engagementCode;
    /**
     * code COLUMN:eng_code
     */
    @Schema(description = "code")
    private String engCode;

    /**
     * code名称 COLUMN:engagement_name
     */
    @Schema(description = "code名称")
    private String engagementName;

    /**
     * code状态 COLUMN:engagement_status
     */
    @Schema(description = "code状态")
    private String engagementStatus;

    /**
     * code时间 COLUMN:effective_date
     */
    @Schema(description = "code时间")
    private LocalDateTime effectiveDate;

    /**
     * code创建时间 COLUMN:eng_create_date
     */
    @Schema(description = "code创建时间")
    private LocalDateTime engCreateDate;

    /**
     * engagement partner gpn (ep) COLUMN:engagement_partner_gpn
     */
    @Schema(description = "engagement partner gpn (ep)")
    private String engagementPartnerGpn;

    /**
     * engagement partner id (ep) COLUMN:engagement_partner_id
     */
    @Schema(description = "engagement partner id (ep)")
    private String engagementPartnerId;

    /**
     * engagement manager gpn (em) COLUMN:engagement_manager_gpn
     */
    @Schema(description = "engagement manager gpn (em)")
    private String engagementManagerGpn;

    /**
     * engagement manager id (em) COLUMN:engagement_manager_id
     */
    @Schema(description = "engagement manager id (em)")
    private String engagementManagerId;
}
