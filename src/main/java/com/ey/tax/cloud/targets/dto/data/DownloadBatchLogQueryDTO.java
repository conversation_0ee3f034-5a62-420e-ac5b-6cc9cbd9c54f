package com.ey.tax.cloud.targets.dto.data;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-12-09 14:48:33
 * 
 */
@Data
@Schema(description = "根据条件查询实体")
public class DownloadBatchLogQueryDTO {
    /**
     * 类型 COLUMN:type_name
     */
    @Schema(description = "类型", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String typeName;

    /**
     * 状态 0:等待,1:上传中,2:上传成功,3:上传失败 COLUMN:status
     */
    @Schema(description = "状态 0:等待,1:上传中,2:上传成功,3:上传失败", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String status;

    /**
     * 开始时间 COLUMN:start_time
     */
    @Schema(description = "开始时间", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDateTime startTime;

    /**
     * 结束时间 COLUMN:end_time
     */
    @Schema(description = "结束时间", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDateTime endTime;

    /**
     * 财年 COLUMN:fiscal_year
     */
    @Schema(description = "财年", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String fiscalYear;

    /**
     * 错误原因 COLUMN:reason
     */
    @Schema(description = "错误原因", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String reason;

    /**
     * 文件id COLUMN:file_id
     */
    @Schema(description = "文件id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String fileId;
}