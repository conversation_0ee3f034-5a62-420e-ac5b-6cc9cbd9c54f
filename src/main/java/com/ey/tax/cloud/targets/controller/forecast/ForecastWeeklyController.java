package com.ey.tax.cloud.targets.controller.forecast;

import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.dto.SearchDTO;
import com.ey.cn.tax.framework.validator.AddGroup;
import com.ey.cn.tax.framework.validator.DeleteByIdGroup;
import com.ey.cn.tax.framework.validator.DeleteByIdListGroup;
import com.ey.cn.tax.framework.validator.QueryByIdGroup;
import com.ey.cn.tax.framework.validator.QueryByIdListGroup;
import com.ey.cn.tax.framework.validator.QueryPageGroup;
import com.ey.cn.tax.framework.validator.UpdateByIdGroup;
import com.ey.cn.tax.framework.web.annotation.EyPostMapping;
import com.ey.cn.tax.framework.web.base.BaseController;
import com.ey.tax.cloud.targets.dto.forecast.ForecastWeeklyAddDTO;
import com.ey.tax.cloud.targets.dto.forecast.ForecastWeeklyBatchUpdateDTO;
import com.ey.tax.cloud.targets.dto.forecast.ForecastWeeklyDTO;
import com.ey.tax.cloud.targets.dto.forecast.ForecastWeeklyDeleteByIdDTO;
import com.ey.tax.cloud.targets.dto.forecast.ForecastWeeklyDeleteByIdListDTO;
import com.ey.tax.cloud.targets.dto.forecast.ForecastWeeklyQueryByIdDTO;
import com.ey.tax.cloud.targets.dto.forecast.ForecastWeeklyQueryByIdListDTO;
import com.ey.tax.cloud.targets.dto.forecast.ForecastWeeklyQueryDTO;
import com.ey.tax.cloud.targets.dto.forecast.ForecastWeeklyQueryPageDTO;
import com.ey.tax.cloud.targets.dto.forecast.ForecastWeeklyRepDTO;
import com.ey.tax.cloud.targets.dto.forecast.ForecastWeeklyUpdateByIdDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-01-17 14:35:35
 *
 */
@Tag(name="按周汇总")
public interface ForecastWeeklyController extends BaseController<ForecastWeeklyDTO> {
    /**
     * add ForecastWeekly from http
     */
    @Operation(summary = "新增", description = "新增一条数据")
    @EyPostMapping(value = "/add.do")
    ResponseDTO<Void> add(@RequestBody @Validated({AddGroup.class}) ForecastWeeklyAddDTO dto);

    /**
     * addList ForecastWeekly from http
     */
    @Operation(summary = "批量新增", description = "批量新增数据")
    @EyPostMapping(value = "/addList.do")
    ResponseDTO<Void> addList(@RequestBody List<ForecastWeeklyAddDTO> dtos);

    /**
     * delete ForecastWeekly from http
     */
    @Operation(summary = "根据id删除", description = "根据id删除一条数据")
    @EyPostMapping(value = "/deleteById.do")
    ResponseDTO<Void> deleteById(@RequestBody @Validated({DeleteByIdGroup.class}) ForecastWeeklyDeleteByIdDTO dto);

    /**
     * deleteList ForecastWeekly from http
     */
    @Operation(summary = "根据id列表删除", description = "根据id列表批量删除数据")
    @EyPostMapping(value = "/deleteByIdList.do")
    ResponseDTO<Void> deleteByIdList(@RequestBody @Validated({DeleteByIdListGroup.class}) ForecastWeeklyDeleteByIdListDTO dto);

    /**
     * update ForecastWeekly from http
     */
    @Operation(summary = "根据id更新", description = "根据id更新一条数据")
    @EyPostMapping(value = "/updateById.do")
    ResponseDTO<Void> updateById(@RequestBody @Validated({UpdateByIdGroup.class}) ForecastWeeklyUpdateByIdDTO dto);

    /**
     * updateBatch ForecastWeekly from http
     */
    @Operation(summary = "批量更新", description = "批量更新数据")
    @EyPostMapping(value = "/updateBatch.do")
    ResponseDTO<Void> updateBatch(@RequestBody List<ForecastWeeklyBatchUpdateDTO> dtos);

    /**
     * query ForecastWeekly from http
     */
    @Operation(summary = "根据id查询", description = "根据id查询数据")
    @EyPostMapping(value = "/queryById.do")
    ResponseDTO<ForecastWeeklyRepDTO> queryById(@RequestBody @Validated({QueryByIdGroup.class}) ForecastWeeklyQueryByIdDTO dto);

    /**
     * queryByIdList ForecastWeekly from http
     */
    @Operation(summary = "根据id列表查询", description = "根据id列表查询数据")
    @EyPostMapping(value = "/queryByIdList.do")
    ResponseDTO<List<ForecastWeeklyRepDTO>> queryByIdList(@RequestBody @Validated({QueryByIdListGroup.class}) ForecastWeeklyQueryByIdListDTO dto);

    /**
     * queryList ForecastWeekly from http
     */
    @Operation(summary = "查询列表", description = "查询数据列表")
    @EyPostMapping(value = "/queryList.do")
    ResponseDTO<List<ForecastWeeklyRepDTO>> queryList(@RequestBody ForecastWeeklyQueryDTO dto);

    /**
     * Page ForecastWeekly from http
     */
    @Operation(summary = "分页查询", description = "根据条件分页查询")
    @EyPostMapping(value = "/queryPage.do")
    ResponseDTO<SearchDTO<ForecastWeeklyRepDTO>> queryPage(@RequestBody @Validated({QueryPageGroup.class}) SearchDTO<ForecastWeeklyQueryPageDTO> searchDTO);

    /**
     * Count ForecastWeekly from http
     */
    @Operation(summary = "查询数量", description = "根据条件查询数量")
    @EyPostMapping(value = "/queryCount.do")
    ResponseDTO<Long> queryCount(@RequestBody ForecastWeeklyQueryDTO forecastWeeklyDTO);

    /**
     * One ForecastWeekly from http
     */
    @Operation(summary = "查询单个数据", description = "根据条件查询一条数据,如果查询到多条则返回异常.")
    @EyPostMapping(value = "/queryOne.do")
    ResponseDTO<ForecastWeeklyRepDTO> queryOne(@RequestBody ForecastWeeklyQueryDTO forecastWeeklyDTO);

    /**
     * exist ForecastWeekly from http
     */
    @Operation(summary = "查询是否存在", description = "根据条件查询是否存在")
    @EyPostMapping(value = "/exist.do")
    ResponseDTO<Boolean> exist(@RequestBody ForecastWeeklyQueryDTO forecastWeeklyDTO);
}
