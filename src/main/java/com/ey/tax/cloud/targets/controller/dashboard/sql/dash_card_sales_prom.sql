select sum(sales.sales_delivery_credit_amount_usd) as amount from tax_cloud_target.target_pipeline_sales_delivery_data sales
    left join tax_cloud_target.target_pipeline pipeline on sales.pipeline_id = pipeline.id
    left join tax_cloud_target.target_user_attribute attr on attr.user_id = sales.sales_delivery_credit
where pipeline.status = #{args.status}
    and ${args.authSql}
    and pipeline.confirm_status in
    <foreach collection="args.confirmStatus" index="index" item="item" open="(" separator="," close=")" >
    #{item}
    </foreach>
    and pipeline.won_fy = #{args.fiscalYear}
    and sales.is_del = 0
    and pipeline.is_del = 0
    and attr.is_del = 0
    and sales.sales_delivery_credit_type != 3
    and attr.fiscal_year = #{args.fiscalYear}
    <if test="args.userType != null" >
  and sales.sales_delivery_credit_type = #{args.userType}
    </if>
    <if test="args.subServiceLine != null and args.subServiceLine.size() > 0" >
  AND sales.ssl2 in
    <foreach collection="args.subServiceLine" index="index" item="item" open="(" separator="," close=")" >
    #{item}
    </foreach>
    </if>
    <if test="args.competency != null and args.competency.size() > 0" >
  AND sales.ssl3 in
    <foreach collection="args.competency" index="index" item="item" open="(" separator="," close=")" >
    #{item}
    </foreach>
    </if>
    <if test="args.market != null and args.market.size() > 0" >
  AND sales.region in
    <foreach collection="args.market" index="index" item="item" open="(" separator="," close=")" >
    #{item}
    </foreach>
    </if>
    <if test="args.city != null and args.city.size() > 0" >
  AND sales.city in
    <foreach collection="args.city" index="index" item="item" open="(" separator="," close=")" >
    #{item}
    </foreach>
    </if>;