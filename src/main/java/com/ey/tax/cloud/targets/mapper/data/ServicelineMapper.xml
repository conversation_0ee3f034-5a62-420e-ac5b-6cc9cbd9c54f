<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ey.tax.cloud.targets.mapper.data.ServicelineMapper" >
  
  <resultMap id="EyTaxResultMap" type="com.ey.tax.cloud.targets.entity.data.Serviceline" >
    <id column="id" property="id" jdbcType="VARCHAR" />
    <result column="create_uid" property="createUid" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_uid" property="updateUid" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="is_del" property="isDel" jdbcType="SMALLINT" />
    <result column="fiscal_year" property="fiscalYear" jdbcType="VARCHAR" />
    <result column="item_key" property="itemKey" jdbcType="VARCHAR" />
    <result column="item_value" property="itemValue" jdbcType="VARCHAR" />
    <result column="item_parent" property="itemParent" jdbcType="VARCHAR" />
    <result column="auto_test" property="autoTest" jdbcType="SMALLINT" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="ext_one" property="extOne" jdbcType="VARCHAR" />
    <result column="ext_two" property="extTwo" jdbcType="VARCHAR" />
    <result column="order_num" property="orderNum" jdbcType="INTEGER" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="tag" property="tag" jdbcType="VARCHAR" />
    <result column="layer" property="layer" jdbcType="INTEGER" />
  </resultMap>
  
</mapper>