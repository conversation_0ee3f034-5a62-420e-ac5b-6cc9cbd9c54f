package com.ey.tax.cloud.targets.dto.pipeline;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-01-30 14:57:08
 * 
 */
@Data
@Schema(description = "新增实体")
public class SalesDeliveryDataAddDTO {
    /**
     * pipelineid COLUMN:pipeline_id
     */
    @Schema(description = "pipelineid", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String pipelineId;

    /**
     * 销售交付人员id COLUMN:sales_delivery_credit
     */
    @Schema(description = "销售交付人员id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String salesDeliveryCredit;

    /**
     * 销售交付人员类型（p、m、tpc） COLUMN:sales_delivery_credit_type
     */
    @Schema(description = "销售交付人员类型（p、m、tpc）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer salesDeliveryCreditType;

    /**
     * 销售交付人员业绩金额 COLUMN:sales_delivery_credit_amount
     */
    @Schema(description = "销售交付人员业绩金额", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal salesDeliveryCreditAmount;

    /**
     * 上次销售交付人员业绩金额 COLUMN:sales_delivery_credit_amount_last
     */
    @Schema(description = "上次销售交付人员业绩金额", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal salesDeliveryCreditAmountLast;

    /**
     * 是否还在项目 COLUMN:is_part
     */
    @Schema(description = "是否还在项目", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer isPart;

    /**
     * 财年 COLUMN:fiscal_year
     */
    @Schema(description = "财年", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String fiscalYear;

    /**
     * 审批状态（0待提交，1审批中，2成功，3驳回） COLUMN:confirm_status
     */
    @Schema(description = "审批状态（0待提交，1审批中，2成功，3驳回）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer confirmStatus;

    /**
     * 销售交付人员业绩占比 COLUMN:sales_delivery_credit_ratio
     */
    @Schema(description = "销售交付人员业绩占比", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Short salesDeliveryCreditRatio;

    /**
     * 上次销售交付人员业绩占比 COLUMN:sales_delivery_credit_ratio_last
     */
    @Schema(description = "上次销售交付人员业绩占比", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Short salesDeliveryCreditRatioLast;

    /**
     * 级别 COLUMN:level
     */
    @Schema(description = "级别", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String level;

    /**
     * SSL3 COLUMN:ssl3
     */
    @Schema(description = "SSL3", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String ssl3;

    /**
     * 城市 COLUMN:city
     */
    @Schema(description = "城市", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String city;

    /**
     * 子行业部门 COLUMN:industry_sub_sector
     */
    @Schema(description = "子行业部门", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String industrySubSector;

    /**
     * 公司类型 COLUMN:company_type
     */
    @Schema(description = "公司类型", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Object companyType;

    /**
     * 总部所在地 COLUMN:head_office_location
     */
    @Schema(description = "总部所在地", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String headOfficeLocation;

    /**
     * 客户渠道 COLUMN:client_channel
     */
    @Schema(description = "客户渠道", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String clientChannel;
}