select sum(sales.sales_delivery_credit_amount_usd) as amount from tax_cloud_target.target_pipeline_sales_delivery_data sales
    left join tax_cloud_target.target_pipeline pipeline on sales.pipeline_id = pipeline.id
where sales.sales_delivery_credit = #{args.userId}
    and pipeline.status = #{args.status}
    and pipeline.won_fy = #{args.fiscalYear}
    and sales.is_del = 0
    and pipeline.is_del = 0
    and pipeline.confirm_status in
    <foreach collection="args.confirmStatus" index="index" item="item" open="(" separator="," close=")" >
        #{item}
    </foreach>