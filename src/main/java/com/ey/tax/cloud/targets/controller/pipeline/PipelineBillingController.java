package com.ey.tax.cloud.targets.controller.pipeline;

import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.dto.SearchDTO;
import com.ey.cn.tax.framework.validator.AddGroup;
import com.ey.cn.tax.framework.validator.DeleteByIdGroup;
import com.ey.cn.tax.framework.validator.DeleteByIdListGroup;
import com.ey.cn.tax.framework.validator.QueryByIdGroup;
import com.ey.cn.tax.framework.validator.QueryByIdListGroup;
import com.ey.cn.tax.framework.validator.QueryPageGroup;
import com.ey.cn.tax.framework.validator.UpdateByIdGroup;
import com.ey.cn.tax.framework.web.annotation.EyPostMapping;
import com.ey.cn.tax.framework.web.base.BaseController;
import com.ey.tax.cloud.targets.dto.pipeline.PipelineBillingAddDTO;
import com.ey.tax.cloud.targets.dto.pipeline.PipelineBillingBatchUpdateDTO;
import com.ey.tax.cloud.targets.dto.pipeline.PipelineBillingDTO;
import com.ey.tax.cloud.targets.dto.pipeline.PipelineBillingDeleteByIdDTO;
import com.ey.tax.cloud.targets.dto.pipeline.PipelineBillingDeleteByIdListDTO;
import com.ey.tax.cloud.targets.dto.pipeline.PipelineBillingQueryByIdDTO;
import com.ey.tax.cloud.targets.dto.pipeline.PipelineBillingQueryByIdListDTO;
import com.ey.tax.cloud.targets.dto.pipeline.PipelineBillingQueryDTO;
import com.ey.tax.cloud.targets.dto.pipeline.PipelineBillingQueryPageDTO;
import com.ey.tax.cloud.targets.dto.pipeline.PipelineBillingRepDTO;
import com.ey.tax.cloud.targets.dto.pipeline.PipelineBillingUpdateByIdDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2023-12-28 16:17:28
 *
 */
@Tag(name="Billing")
public interface PipelineBillingController extends BaseController<PipelineBillingDTO> {
    /**
     * add PipelineBilling from http
     */
    @Operation(summary = "新增", description = "新增一条数据")
    @EyPostMapping(value = "/add.do")
    ResponseDTO<Void> add(@RequestBody @Validated({AddGroup.class}) PipelineBillingAddDTO dto);

    /**
     * addList PipelineBilling from http
     */
    @Operation(summary = "批量新增", description = "批量新增数据")
    @EyPostMapping(value = "/addList.do")
    ResponseDTO<Void> addList(@RequestBody List<PipelineBillingAddDTO> dtos);

    /**
     * delete PipelineBilling from http
     */
    @Operation(summary = "根据id删除", description = "根据id删除一条数据")
    @EyPostMapping(value = "/deleteById.do")
    ResponseDTO<Void> deleteById(@RequestBody @Validated({DeleteByIdGroup.class}) PipelineBillingDeleteByIdDTO dto);

    /**
     * deleteList PipelineBilling from http
     */
    @Operation(summary = "根据id列表删除", description = "根据id列表批量删除数据")
    @EyPostMapping(value = "/deleteByIdList.do")
    ResponseDTO<Void> deleteByIdList(@RequestBody @Validated({DeleteByIdListGroup.class}) PipelineBillingDeleteByIdListDTO dto);

    /**
     * update PipelineBilling from http
     */
    @Operation(summary = "根据id更新", description = "根据id更新一条数据")
    @EyPostMapping(value = "/updateById.do")
    ResponseDTO<Void> updateById(@RequestBody @Validated({UpdateByIdGroup.class}) PipelineBillingUpdateByIdDTO dto);

    /**
     * updateBatch PipelineBilling from http
     */
    @Operation(summary = "批量更新", description = "批量更新数据")
    @EyPostMapping(value = "/updateBatch.do")
    ResponseDTO<Void> updateBatch(@RequestBody List<PipelineBillingBatchUpdateDTO> dtos);

    /**
     * query PipelineBilling from http
     */
    @Operation(summary = "根据id查询", description = "根据id查询数据")
    @EyPostMapping(value = "/queryById.do")
    ResponseDTO<PipelineBillingRepDTO> queryById(@RequestBody @Validated({QueryByIdGroup.class}) PipelineBillingQueryByIdDTO dto);

    /**
     * queryByIdList PipelineBilling from http
     */
    @Operation(summary = "根据id列表查询", description = "根据id列表查询数据")
    @EyPostMapping(value = "/queryByIdList.do")
    ResponseDTO<List<PipelineBillingRepDTO>> queryByIdList(@RequestBody @Validated({QueryByIdListGroup.class}) PipelineBillingQueryByIdListDTO dto);

    /**
     * queryList PipelineBilling from http
     */
    @Operation(summary = "查询列表", description = "查询数据列表")
    @EyPostMapping(value = "/queryList.do")
    ResponseDTO<List<PipelineBillingRepDTO>> queryList(@RequestBody PipelineBillingQueryDTO dto);

    /**
     * Page PipelineBilling from http
     */
    @Operation(summary = "分页查询", description = "根据条件分页查询")
    @EyPostMapping(value = "/queryPage.do")
    ResponseDTO<SearchDTO<PipelineBillingRepDTO>> queryPage(@RequestBody @Validated({QueryPageGroup.class}) SearchDTO<PipelineBillingQueryPageDTO> searchDTO);

    /**
     * Count PipelineBilling from http
     */
    @Operation(summary = "查询数量", description = "根据条件查询数量")
    @EyPostMapping(value = "/queryCount.do")
    ResponseDTO<Long> queryCount(@RequestBody PipelineBillingQueryDTO pipelineBillingDTO);

    /**
     * One PipelineBilling from http
     */
    @Operation(summary = "查询单个数据", description = "根据条件查询一条数据,如果查询到多条则返回异常.")
    @EyPostMapping(value = "/queryOne.do")
    ResponseDTO<PipelineBillingRepDTO> queryOne(@RequestBody PipelineBillingQueryDTO pipelineBillingDTO);

    /**
     * exist PipelineBilling from http
     */
    @Operation(summary = "查询是否存在", description = "根据条件查询是否存在")
    @EyPostMapping(value = "/exist.do")
    ResponseDTO<Boolean> exist(@RequestBody PipelineBillingQueryDTO pipelineBillingDTO);
}
