package com.ey.tax.cloud.targets.controller.pipeline;

import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.dto.SearchDTO;
import com.ey.cn.tax.framework.validator.AddGroup;
import com.ey.cn.tax.framework.validator.DeleteByIdGroup;
import com.ey.cn.tax.framework.validator.DeleteByIdListGroup;
import com.ey.cn.tax.framework.validator.QueryByIdGroup;
import com.ey.cn.tax.framework.validator.QueryByIdListGroup;
import com.ey.cn.tax.framework.validator.QueryPageGroup;
import com.ey.cn.tax.framework.validator.UpdateByIdGroup;
import com.ey.cn.tax.framework.web.annotation.EyPostMapping;
import com.ey.cn.tax.framework.web.base.BaseController;
import com.ey.tax.cloud.targets.dto.pipeline.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2023-12-28 16:17:27
 *
 */
@Tag(name="pipeline审批表、审批页面用")
public interface PipelineApproveController extends BaseController<PipelineApproveDTO> {
    /**
     * add PipelineApprove from http
     */
    @Operation(summary = "新增", description = "新增一条数据")
    @EyPostMapping(value = "/add.do")
    ResponseDTO<Void> add(@RequestBody @Validated({AddGroup.class}) PipelineApproveAddDTO dto);

    /**
     * 审批通过
     */
    @Operation(summary = "审批通过", description = "审批通过")
    @EyPostMapping(value = "/approve.do")
    ResponseDTO<Void> approve(@RequestBody PipelineApproveIdListDTO ids);

    /**
     * 审批通过
     */
    @Operation(summary = "审批撤回", description = "审批撤回")
    @EyPostMapping(value = "/rollback.do")
    ResponseDTO<Void> rollback(@RequestBody PipelineApproveIdListDTO ids);

    /**
     * 审批驳回
     */
    @Operation(summary = "审批驳回", description = "审批驳回")
    @EyPostMapping(value = "/reject.do")
    ResponseDTO<Void> reject(@RequestBody PipelineApproveIdListDTO ids);

    /**
     * addList PipelineApprove from http
     */
    @Operation(summary = "批量新增", description = "批量新增数据")
    @EyPostMapping(value = "/addList.do")
    ResponseDTO<Void> addList(@RequestBody List<PipelineApproveAddDTO> dtos);

    /**
     * delete PipelineApprove from http
     */
    @Operation(summary = "根据id删除", description = "根据id删除一条数据")
    @EyPostMapping(value = "/deleteById.do")
    ResponseDTO<Void> deleteById(@RequestBody @Validated({DeleteByIdGroup.class}) PipelineApproveDeleteByIdDTO dto);

    /**
     * deleteList PipelineApprove from http
     */
    @Operation(summary = "根据id列表删除", description = "根据id列表批量删除数据")
    @EyPostMapping(value = "/deleteByIdList.do")
    ResponseDTO<Void> deleteByIdList(@RequestBody @Validated({DeleteByIdListGroup.class}) PipelineApproveDeleteByIdListDTO dto);

    /**
     * update PipelineApprove from http
     */
    @Operation(summary = "根据id更新", description = "根据id更新一条数据")
    @EyPostMapping(value = "/updateById.do")
    ResponseDTO<Void> updateById(@RequestBody @Validated({UpdateByIdGroup.class}) PipelineApproveUpdateByIdDTO dto);
    @Operation(summary = "根据id更新 em用", description = "根据id更新一条数据")
    @EyPostMapping(value = "/updateByIdEm.do")
    ResponseDTO<Void> updateByIdEm(@RequestBody @Validated({UpdateByIdGroup.class}) PipelineApproveUpdateByIdDTO dto);

    /**
     * updateBatch PipelineApprove from http
     */
    @Operation(summary = "批量更新", description = "批量更新数据")
    @EyPostMapping(value = "/updateBatch.do")
    ResponseDTO<Void> updateBatch(@RequestBody List<PipelineApproveBatchUpdateDTO> dtos);

    /**
     * query PipelineApprove from http
     */
    @Operation(summary = "根据id查询pipeline和kpi", description = "根据id查询数据")
    @EyPostMapping(value = "/queryById.do")
    ResponseDTO<PipelineApproveRepDTO> queryById(@RequestBody @Validated({QueryByIdGroup.class}) PipelineApproveQueryByIdDTO dto);

    /**
     * queryByIdList PipelineApprove from http
     */
    @Operation(summary = "根据id列表查询", description = "根据id列表查询数据")
    @EyPostMapping(value = "/queryByIdList.do")
    ResponseDTO<List<PipelineApproveRepDTO>> queryByIdList(@RequestBody @Validated({QueryByIdListGroup.class}) PipelineApproveQueryByIdListDTO dto);

    /**
     * queryList PipelineApprove from http
     */
    @Operation(summary = "查询列表", description = "查询数据列表")
    @EyPostMapping(value = "/queryList.do")
    ResponseDTO<List<PipelineApproveRepDTO>> queryList(@RequestBody PipelineApproveQueryDTO dto);

    /**
     * Page PipelineApprove from http
     */
    @Operation(summary = "分页查询", description = "根据条件分页查询")
    @EyPostMapping(value = "/queryPage.do")
    ResponseDTO<SearchDTO<PipelineApproveRepDTO>> queryPage(@RequestBody @Validated({QueryPageGroup.class}) SearchDTO<PipelineApproveQueryPageDTO> searchDTO);

    /**
     * Count PipelineApprove from http
     */
    @Operation(summary = "查询数量", description = "根据条件查询数量")
    @EyPostMapping(value = "/queryCount.do")
    ResponseDTO<Long> queryCount(@RequestBody PipelineApproveQueryDTO pipelineApproveDTO);

    /**
     * One PipelineApprove from http
     */
    @Operation(summary = "查询单个数据", description = "根据条件查询一条数据,如果查询到多条则返回异常.")
    @EyPostMapping(value = "/queryOne.do")
    ResponseDTO<PipelineApproveRepDTO> queryOne(@RequestBody PipelineApproveQueryDTO pipelineApproveDTO);

    /**
     * exist PipelineApprove from http
     */
    @Operation(summary = "查询是否存在", description = "根据条件查询是否存在")
    @EyPostMapping(value = "/exist.do")
    ResponseDTO<Boolean> exist(@RequestBody PipelineApproveQueryDTO pipelineApproveDTO);
}
