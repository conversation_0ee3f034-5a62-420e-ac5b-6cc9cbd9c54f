package com.ey.tax.cloud.targets.controller.tigeroar.impl;

import com.alibaba.fastjson2.JSONObject;
import com.ey.cn.tax.framework.context.EyCommonContextHolder;
import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.utils.EmptyUtils;
import com.ey.cn.tax.framework.utils.JacksonUtils;
import com.ey.cn.tax.framework.web.annotation.EyRestController;
import com.ey.tax.cloud.targets.constant.*;
import com.ey.tax.cloud.targets.controller.tigeroar.TigeroarController;
import com.ey.tax.cloud.targets.dto.transfer.*;
import com.ey.tax.cloud.targets.entity.auth.AuthGroupDetail;
import com.ey.tax.cloud.targets.entity.data.Dictionary;
import com.ey.tax.cloud.targets.entity.data.DictionaryItem;
import com.ey.tax.cloud.targets.entity.pipeline.*;
import com.ey.tax.cloud.targets.entity.product.Product;
import com.ey.tax.cloud.targets.entity.transfer.SyncOpportunity;
import com.ey.tax.cloud.targets.entity.transfer.SyncOpportunityMiddle;
import com.ey.tax.cloud.targets.entity.transfer.SyncTaxClient;
import com.ey.tax.cloud.targets.entity.user.UserAttribute;
import com.ey.tax.cloud.targets.service.auth.AuthGroupService;
import com.ey.tax.cloud.targets.service.data.DictionaryItemService;
import com.ey.tax.cloud.targets.service.data.DictionaryService;
import com.ey.tax.cloud.targets.service.pipeline.*;
import com.ey.tax.cloud.targets.service.product.ProductService;
import com.ey.tax.cloud.targets.service.transfer.SyncOpportunityMiddleService;
import com.ey.tax.cloud.targets.service.transfer.SyncOpportunityService;
import com.ey.tax.cloud.targets.service.transfer.SyncTaxClientService;
import com.ey.tax.cloud.targets.service.user.UserAttributeService;
import com.ey.tax.cloud.targets.utils.CommonUtils;
import com.ey.tax.cloud.targets.utils.ExceptionUtils;
import com.xxl.job.core.context.XxlJobHelper;
import io.github.resilience4j.core.ResultUtils;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@EyRestController(path ="/v1/tigeroar")
public class TigeroarControllerImpl implements TigeroarController {
    protected final Logger logger = LoggerFactory.getLogger(TigeroarControllerImpl.class);
    @Qualifier("ignoreHttpsRestTemplate")
    @Autowired
    private RestTemplate restTemplate;

    @Value("${tigeroar.url}")
    private String url;

    @Value("${tigeroar.appId}")
    private String appId;

    @Value("${tigeroar.clientId}")
    private String clientId;

    @Value("${tigeroar.appSecret}")
    private String appSecret;

    @Value("${tigeroar.tokenPath}")
    private String tokenPath;

    @Value("${tigeroar.categoryPath}")
    private String categoryPath;

    @Value("${tigeroar.dictPath}")
    private String dictPath;
    @Autowired
    private SyncOpportunityMiddleService syncOpportunityMiddleService;
    @Autowired
    private SyncTaxClientService syncTaxClientService;
    @Autowired
    private PipelineService pipeline;
    @Autowired
    private DictionaryItemService dictionaryItemService;
    @Autowired
    private UserAttributeService userAttributeService;

    @Autowired
    private DictionaryService dictionaryService;

    @Autowired
    private ProductService productService;
    @Autowired
    private PipelineConfigService pipelineConfigService;

    @Autowired
    private PipelineForTransferService pipelineForTransferService;
    @Autowired
    private SalesDeliveryService salesDeliveryService;
    @Autowired
    private  AuthGroupService  authGroupService;

    @Autowired
    private SyncOpportunityService syncOpportunityService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;


    @Override
    public TokenResponseDTO getToken(TokenRequestDTO dto) {
        String tokenUrl = url + tokenPath;
        logger.info("开始请求获取Token，请求URL: {}", tokenUrl);

        TokenRequestDTO tokenRequestDTO = new TokenRequestDTO();
        tokenRequestDTO.setClientId(clientId);
        tokenRequestDTO.setAppId(appId);
        tokenRequestDTO.setAppSecret(appSecret);
        logger.info("Token请求参数: clientId={}, appId={}, appSecret={}", clientId, appId,
                appSecret);

        TokenResponseDTO tokenResponseDTO = null;
        try {
            logger.info("发送Token请求...");
            tokenResponseDTO = restTemplate.postForObject(tokenUrl, tokenRequestDTO, TokenResponseDTO.class);
            logger.info("Token请求成功，响应结果: {}", tokenResponseDTO);
        } catch (Exception e) {
            logger.error("Token请求失败: {}", e);
            logger.debug("异常详情: ", e);

            if(EmptyUtils.isEmpty(tokenResponseDTO)){
                tokenResponseDTO = new TokenResponseDTO();
            }
            e.printStackTrace();
            tokenResponseDTO.setErrorMessage(ExceptionUtils.getFullStackTrace(e));
            logger.info("返回包含错误信息的响应对象");
        }

        return tokenResponseDTO;
    }

    @Override
    public CategoryResponseDTO getCategoryData(CategoryRequestDTO categoryRequestDTO, HttpServletRequest request) {
        String tokenUrl = url + categoryPath;
        logger.info("开始请求获取分类数据，请求URL: {}", tokenUrl);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Authorization", request.getHeader("tiger"));
        headers.add("client-id", clientId);
        logger.info("请求头信息: Content-Type={}, Authorization={}, client-id={}",
                MediaType.APPLICATION_JSON, request.getHeader("tiger"), clientId);

        HttpEntity<CategoryRequestDTO> requestEntity = new HttpEntity<>(categoryRequestDTO, headers);
        logger.info("分类数据请求参数: {}", categoryRequestDTO);

        CategoryResponseDTO categoryResponseDTO = null;
        try {
            logger.info("发送分类数据请求...");
            categoryResponseDTO = restTemplate.postForObject(tokenUrl, requestEntity, CategoryResponseDTO.class);
            logger.info("分类数据请求成功，响应结果: {}", categoryResponseDTO);
        } catch (Exception e) {
            logger.error("分类数据请求失败: {}", e.getMessage());
            logger.debug("异常详情: ", e);

            categoryResponseDTO = new CategoryResponseDTO();
            e.printStackTrace();
            categoryResponseDTO.setErrorMessage(ExceptionUtils.getFullStackTrace(e));
            logger.info("返回包含错误信息的响应对象");
        }

        return categoryResponseDTO;
    }

    @Override
    public CategoryResponseDTO getMiddle(CategoryRequestDTO categoryRequestDTO, HttpServletRequest request) {
        List<SyncOpportunity> list = null;
        CategoryResponseDTO categoryResponseDTO = new CategoryResponseDTO();
        Object stru = null;
        try {
            stru = syncOpportunityService.executeSql(categoryRequestDTO.getSql());
            logger.info("getTableStructure stru: {}", stru);
        } catch (Exception e) {
            logger.error("getTableStructure error", e);
            categoryResponseDTO.setErrorMessage(ExceptionUtils.getFullStackTrace(e));
        }
        categoryResponseDTO.setCode(200);
        categoryResponseDTO.setMsg(JSONObject.toJSONString(list));
        categoryResponseDTO.setRes(JSONObject.toJSONString(stru));
        return categoryResponseDTO;
    }
    @Override
    public CategoryResponseDTO getCategoryData2(CategoryRequestDTO categoryRequestDTO, HttpServletRequest request) {
        String tokenUrl = url + categoryPath;
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Authorization", request.getHeader("tiger"));
        headers.add("client-id", clientId);
        HttpEntity<CategoryRequestDTO> requestEntity = new HttpEntity<>(categoryRequestDTO, headers);
        CategoryResponseDTO categoryResponseDTO = null;
        try {
            categoryResponseDTO = restTemplate.postForObject(tokenUrl, requestEntity, CategoryResponseDTO.class);
        }catch (Exception e){
            categoryResponseDTO = new CategoryResponseDTO();
            e.printStackTrace();
            categoryResponseDTO.setErrorMessage(ExceptionUtils.getFullStackTrace(e));
        }
        return categoryResponseDTO;


    }

    protected Map<String, String> getDictionaryItem(String dictCode){
        Dictionary dictionary = new Dictionary();
        dictionary.setDictCode(dictCode);
        dictionary = dictionaryService.queryOneByPara(dictionary);
        DictionaryItem qDictionaryItem = new DictionaryItem();
        qDictionaryItem.setDictId(dictionary.getId());
        List<DictionaryItem> items = dictionaryItemService.queryByPara(qDictionaryItem);
        Map<String, String> itemMap = new HashMap<>();
        for (DictionaryItem item : items) {
            itemMap.put(item.getItemKey(), item.getItemValue());
        }
        return itemMap;
    }
    protected Map<String, String> getDictionaryMapping(String dictCode){
        Dictionary dictionary = new Dictionary();
        dictionary.setDictCode(dictCode);
        dictionary = dictionaryService.queryOneByPara(dictionary);
        DictionaryItem qDictionaryItem = new DictionaryItem();
        qDictionaryItem.setDictId(dictionary.getId());
        List<DictionaryItem> items = dictionaryItemService.queryByPara(qDictionaryItem);
        Map<String, String> itemMap = new HashMap<>();
        for (DictionaryItem item : items) {
            itemMap.put(item.getTag(), item.getItemKey());
        }
        return itemMap;
    }

    private String transferDict(String dictCode, String key, Map<String, Map<String, String>> dictMap) {
        key = "tiger-" + key;
        Map<String, String> mapping = dictMap.get(dictCode);
        String result = mapping.get(key);
        if (EmptyUtils.isEmpty(result)) {
            XxlJobHelper.log("dictCode: {} key: {} not found", dictCode, key);
        }
        return result;
    }

    private List<String> objectToStringList(Object data) {
        if (EmptyUtils.isEmpty(data)) {
            return null;
        }
        String sGpn = data.toString();
        List<String> subList = JacksonUtils.readValue(sGpn, List.class);
        if (EmptyUtils.isEmpty(subList)) {
            return null;
        }
        List<String> newList = new ArrayList<>();
        for (String sub : subList) {
            if (EmptyUtils.isNotEmpty(sub)) {
                newList.add(sub);
            }
        }
        return newList;
    }

    @Override
    public Object getDictData(CategoryRequestDTO categoryRequestDTO, HttpServletRequest request) {
        String tokenUrl = url + dictPath;
        //将categoryCode作为param拼接到url上
        tokenUrl = tokenUrl + "?categoryCode=" + categoryRequestDTO.getCategoryCode();
        //拼接functionType=default
        tokenUrl = tokenUrl + "&functionType=default";
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Authorization", request.getHeader("tiger"));
        headers.add("client-id", clientId);
        HttpEntity<CategoryRequestDTO> requestEntity = new HttpEntity<>(headers);
        String categoryResponseDTO = null;
        try {
            categoryResponseDTO = restTemplate.postForObject(tokenUrl, requestEntity, String.class);
        }catch (Exception e){
            CategoryResponseDTO categoryResponseDTO1 = new CategoryResponseDTO();
            e.printStackTrace();
            categoryResponseDTO1.setErrorMessage(ExceptionUtils.getFullStackTrace(e));
            return categoryResponseDTO1;
        }
        return ResponseDTO.success(categoryResponseDTO);
    }

    @Override
    public Object getDictData2(CategoryRequestDTO categoryRequestDTO, HttpServletRequest request) {
        String tokenUrl = url + dictPath;
        //将categoryCode作为param拼接到url上
        tokenUrl = tokenUrl + "?categoryCode=" + categoryRequestDTO.getCategoryCode();
        //拼接functionType=default
        tokenUrl = tokenUrl + "&functionType=default";
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Authorization", request.getHeader("tiger"));
        headers.add("client-id", clientId);
        HttpEntity<CategoryRequestDTO> requestEntity = new HttpEntity<>(headers);
        DictResponseDTO categoryResponseDTO = null;
        try {
            categoryResponseDTO = restTemplate.postForObject(tokenUrl, requestEntity, DictResponseDTO.class);
        }catch (Exception e){
            CategoryResponseDTO categoryResponseDTO1 = new CategoryResponseDTO();
            e.printStackTrace();
            categoryResponseDTO1.setErrorMessage(ExceptionUtils.getFullStackTrace(e));
            return categoryResponseDTO1;
        }
        return ResponseDTO.success(categoryResponseDTO);

    }
}
