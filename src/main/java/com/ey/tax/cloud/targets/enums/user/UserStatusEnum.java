package com.ey.tax.cloud.targets.enums.user;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.ey.cn.tax.framework.exception.EyTaxSystemException;
import com.ey.cn.tax.framework.utils.I18nUtils;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * <p>
 * 用户状态
 * </p>
 *
 * <AUTHOR>
 * @date 17:41 2023/8/24
 */
public enum UserStatusEnum {
    ACTIVE("Active", "Active", "com.ey.tax.cloud.targets.enums.user.UserStatusEnum.ACTIVE"),
    DISMISSION("Dismission", "Dismission", "com.ey.tax.cloud.targets.enums.user.UserStatusEnum.DISMISSION"),
    ;

    @EnumValue
    private final String key;
    private final String name;
    private final String i18nCode;

    UserStatusEnum(final String key, final String name, final String i18nCode) {
        this.key = key;
        this.name = name;
        this.i18nCode = i18nCode;
    }

    @JsonValue
    public String getKey() {
        return this.key;
    }

    public String getName() {
        return I18nUtils.getMessage(this.i18nCode, this.name);
    }

    @JsonCreator
    public static UserStatusEnum getEnum(Integer value) {
        for (UserStatusEnum enums : UserStatusEnum.values()) {
            if (enums.getKey().equals(value)) {
                return enums;
            }
        }
        throw new EyTaxSystemException("Error: Invalid UserStatusEnum type value: " + value);
    }


}
