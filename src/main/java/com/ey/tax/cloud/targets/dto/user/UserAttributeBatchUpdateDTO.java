package com.ey.tax.cloud.targets.dto.user;

import com.ey.cn.tax.framework.validator.BatchUpdateGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-01-09 10:02:34
 * 
 */
@Data
@Schema(description = "根据id批量更新实体")
public class UserAttributeBatchUpdateDTO {
    
    /**
     * 数据id
     */
    @Schema(description = "数据id")
    @NotBlank(message = "{UserAttributeDTO.id.NotBlank}", groups = {BatchUpdateGroup.class})
    private String id;

    /**
     * 平台用户id COLUMN:user_id
     */
    @Schema(description = "平台用户id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String userId;

    /**
     * 用户名 COLUMN:user_name
     */
    @Schema(description = "用户名", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String userName;

    /**
     * 邮箱 COLUMN:user_email
     */
    @Schema(description = "邮箱", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String userEmail;

    /**
     * GPN COLUMN:gpn
     */
    @Schema(description = "GPN", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String gpn;

    /**
     * 级别组 COLUMN:level_group
     */
    @Schema(description = "级别组", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String levelGroup;

    /**
     * 级别 COLUMN:level
     */
    @Schema(description = "级别", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String level;

    /**
     * 级别 COLUMN:group_id
     */
    @Schema(description = "组", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String groupId;

    /**
     * SL1 COLUMN:sl1
     */
    @Schema(description = "SL1", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String sl1;

    /**
     * SSL2 COLUMN:ssl2
     */
    @Schema(description = "SSL2", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String ssl2;

    /**
     * SSL3 COLUMN:ssl3
     */
    @Schema(description = "SSL3", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String ssl3;

    /**
     * 地区 COLUMN:region
     */
    @Schema(description = "地区", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String region;

    /**
     * 城市 COLUMN:city
     */
    @Schema(description = "城市", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String city;

    /**
     * 伙伴 COLUMN:buddy_partner
     */
    @Schema(description = "伙伴", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String buddyPartner;

    /**
     * 是否伙伴 COLUMN:is_buddy_partner
     */
    @Schema(description = "是否伙伴", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer isBuddyPartner;

    /**
     * 是否领导 COLUMN:is_leader
     */
    @Schema(description = "是否领导", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer isLeader;

    /**
     * 财年 COLUMN:fiscal_year
     */
    @Schema(description = "财年", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String fiscalYear;

    /**
     * 状态 COLUMN:status
     */
    @Schema(description = "状态", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer status;

    /**
     * 伙伴邮箱 COLUMN:buddy_partner_email
     */
    @Schema(description = "伙伴邮箱", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String buddyPartnerEmail;
}