package com.ey.tax.cloud.targets.mapper.transfer;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ey.cn.tax.framework.mybatisplus.core.mapper.IEyBaseMapper;
import com.ey.tax.cloud.targets.entity.transfer.SyncOpportunity;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-04-18 15:34:34
 *
 */
public interface SyncOpportunityMapper extends IEyBaseMapper<SyncOpportunity> {

    @Select("select * from ad_infa_for_all_tax.v_rpt_dwd_fct_opp_oppr_opportunity_df_ad_infa_for_all_tax where oppr_id = #{opprId}")
    List<Map<String,Object>> selectSyncOpportunityList(String opprId);

    @Select("SELECT 'CREATE TABLE ' || quote_ident(c.relname) || ' (' ||\n" +
            "    array_to_string(\n" +
            "        array_agg(\n" +
            "            '    ' || quote_ident(a.attname) || ' ' ||\n" +
            "            pg_catalog.format_type(a.atttypid, a.atttypmod) ||\n" +
            "            CASE WHEN a.attnotnull THEN ' NOT NULL' ELSE '' END ||\n" +
            "            CASE WHEN a.atthasdef THEN ' DEFAULT ' || (SELECT pg_catalog.pg_get_expr(adbin, adrelid) FROM pg_catalog.pg_attrdef WHERE adrelid = a.attrelid AND adnum = a.attnum) ELSE '' END\n" +
            "        ),\n" +
            "        ',\n" +
            "'\n" +
            "    ) ||\n" +
            "    CASE WHEN p.contype = 'p' THEN ',\n" +
            "    CONSTRAINT ' || quote_ident(p.conname) || ' PRIMARY KEY (' ||\n" +
            "        array_to_string(array_agg(quote_ident(pk.attname) ORDER BY pk.attnum), ', ') || ')' ELSE '' END ||\n" +
            "')' as ddl\n" +
            "FROM pg_catalog.pg_class c\n" +
            "JOIN pg_catalog.pg_namespace n ON n.oid = c.relnamespace\n" +
            "JOIN pg_catalog.pg_attribute a ON a.attrelid = c.oid\n" +
            "LEFT JOIN pg_catalog.pg_constraint p ON p.conrelid = c.oid AND p.contype = 'p'\n" +
            "LEFT JOIN pg_catalog.pg_attribute pk ON pk.attrelid = c.oid AND pk.attnum = ANY(p.conkey)\n" +
            "WHERE c.relname = #{tableName} AND a.attnum > 0 AND NOT a.attisdropped\n" +
            "GROUP BY c.relname, p.contype, p.conname;  ")
    String getTableStructure(String tableName);

    //执行传入的sql
    @Select("${sql}")
    List<Map<String,Object>> ex(String sql);



}
