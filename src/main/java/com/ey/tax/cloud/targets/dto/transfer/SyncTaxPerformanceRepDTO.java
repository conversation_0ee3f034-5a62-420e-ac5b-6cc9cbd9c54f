package com.ey.tax.cloud.targets.dto.transfer;

import com.ey.cn.tax.framework.dto.BaseRepDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-04-23 14:08:20
 * 
 */
@Data
@Schema(description = "查询返回实体")
public class SyncTaxPerformanceRepDTO extends BaseRepDTO {
    /**
     * COLUMN:gpn
     */
    @Schema(description = "null")
    private String gpn;

    /**
     * COLUMN:name
     */
    @Schema(description = "null")
    private String name;

    /**
     * COLUMN:sub_service_line_name
     */
    @Schema(description = "null")
    private String subServiceLineName;

    /**
     * COLUMN:competency_name
     */
    @Schema(description = "null")
    private String competencyName;

    /**
     * COLUMN:market_segment_name
     */
    @Schema(description = "null")
    private String marketSegmentName;

    /**
     * COLUMN:dict_city_name
     */
    @Schema(description = "null")
    private String dictCityName;

    /**
     * COLUMN:rank_name
     */
    @Schema(description = "null")
    private String rankName;

    /**
     * COLUMN:fytd_ter_usd
     */
    @Schema(description = "null")
    private Short fytdTerUsd;

    /**
     * COLUMN:fytd_ner_usd
     */
    @Schema(description = "null")
    private Short fytdNerUsd;

    /**
     * COLUMN:fytd_margin_usd
     */
    @Schema(description = "null")
    private Short fytdMarginUsd;

    /**
     * COLUMN:fytd_margin_persent
     */
    @Schema(description = "null")
    private Short fytdMarginPersent;

    /**
     * COLUMN:revenue_day
     */
    @Schema(description = "null")
    private Short revenueDay;

    /**
     * COLUMN:ds
     */
    @Schema(description = "null")
    private String ds;

    /**
     * level COLUMN:fiscal_year
     */
    @Schema(description = "level")
    private String fiscalYear;

    /**
     * COLUMN:fiscal_week
     */
    @Schema(description = "null")
    private String fiscalWeek;

    /**
     * effective_utilization COLUMN:effective_utilization
     */
    @Schema(description = "effective_utilization")
    private Integer effectiveUtilization;
}