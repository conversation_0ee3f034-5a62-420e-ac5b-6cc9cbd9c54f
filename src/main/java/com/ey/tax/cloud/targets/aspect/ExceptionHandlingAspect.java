package com.ey.tax.cloud.targets.aspect;

import com.ey.cn.tax.framework.dto.BaseRepDTO;
import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.dto.SearchDTO;
import com.ey.cn.tax.framework.utils.ConvertUtils;
import com.ey.tax.cloud.targets.dto.pipeline.PipelineRepDTO;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import static com.ey.cn.tax.framework.utils.ExceptionUtils.throwBuildBusinessException;

@Aspect
@Component
public class ExceptionHandlingAspect {

    protected final org.slf4j.Logger logger = LoggerFactory.getLogger(this.getClass());

    @AfterThrowing(pointcut = "execution(* com.ey.tax.cloud.targets.controller.*.*Controller.queryPage(..))", throwing = "ex")
    public ResponseDTO<Object> handleQueryPageException(RuntimeException ex) {
        //打印异常信息
        logger.error("error",ex);
        // 处理异常，返回响应
       // throwBuildBusinessException(ErrorCodeConstans.QUERY_ERROR);
        return ResponseDTO.success();
    }
}
