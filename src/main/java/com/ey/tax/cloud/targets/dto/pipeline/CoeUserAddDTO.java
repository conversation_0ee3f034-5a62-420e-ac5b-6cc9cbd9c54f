package com.ey.tax.cloud.targets.dto.pipeline;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-08-05 11:21:26
 *
 */
@Data
@Schema(description = "新增实体")
public class CoeUserAddDTO {
    /**
     * 地区 COLUMN:location
     */
    @Schema(description = "地区", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String location;

    /**
     * 财年 COLUMN:fiscal_year
     */
    @Schema(description = "财年", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String fiscalYear;

    /**
     * 用户id COLUMN:user_id
     */
    @Schema(description = "用户id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String userId;

    /**
     * 状态 COLUMN:status
     */
    @Schema(description = "状态", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String status;
    @Schema(description = "备注", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String remark;
}
