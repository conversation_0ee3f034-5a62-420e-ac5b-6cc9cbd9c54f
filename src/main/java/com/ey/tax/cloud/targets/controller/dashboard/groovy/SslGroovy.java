package com.ey.tax.cloud.targets.controller.dashboard.groovy;

import com.ey.cn.tax.framework.execution.ExecutionCommand;
import com.ey.cn.tax.framework.execution.IExecutionContext;
import com.ey.cn.tax.framework.utils.EmptyUtils;
import com.ey.cn.tax.lite.api.domain.service.ILiteDatasetExecutionService;
import com.ey.cn.tax.lite.execution.dataset.IDatasetGroovyRunnable;
import com.ey.cn.tax.lite.impl.port.input.controller.LiteDatasetController;
import com.ey.tax.cloud.targets.constant.DashboardConstants;
import com.ey.cn.tax.lite.impl.port.input.dto.LiteDatasetExecutionDTO;
import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.tax.cloud.targets.dto.dashboard.SslResultDTO;
import com.ey.tax.cloud.targets.dto.dashboard.SslResultSubDTO;
import com.ey.tax.cloud.targets.entity.auth.AuthGroupDetail;
import com.ey.tax.cloud.targets.entity.data.Dictionary;
import com.ey.tax.cloud.targets.entity.data.DictionaryItem;
import com.ey.tax.cloud.targets.entity.data.Serviceline;
import com.ey.tax.cloud.targets.entity.product.Product;
import com.ey.tax.cloud.targets.entity.user.UserAttribute;
import com.ey.tax.cloud.targets.service.dashboard.DashboardService;
import com.ey.tax.cloud.targets.service.data.DictionaryItemService;
import com.ey.tax.cloud.targets.service.data.DictionaryService;
import com.ey.tax.cloud.targets.service.data.FiscalCalenderService;
import com.ey.tax.cloud.targets.service.data.ServicelineService;
import com.ey.tax.cloud.targets.service.product.ProductService;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

class SslGroovy implements IDatasetGroovyRunnable {

    DictionaryService dictionaryService;

    DictionaryItemService dictionaryItemService;

    ProductService productService;

    FiscalCalenderService fiscalCalenderService;

    DashboardService dashboardService;
     ServicelineService servicelineService;

    @Override
    public Object run(Object args, Map<String, Object> dependentDatasets, IExecutionContext executionContext) {
        Map<String, Object> argsMap = (Map<String, Object>) args;
        String dataScope = (String) argsMap.get("dataScope");
        boolean isSelf = (boolean)argsMap.get("isSelf");
        List<Map<String,Object>> dto = null;
        dictionaryService = executionContext.getBean(DictionaryService.class).get();
        dictionaryItemService = executionContext.getBean(DictionaryItemService.class).get();
        servicelineService = executionContext.getBean(ServicelineService.class).get();
        String fy = (String)argsMap.get("fiscalYear");
        Serviceline dictionary = new Serviceline();
        dictionary.setFiscalYear(fy);
        dictionary.setStatus(1);
        List<Serviceline> items = servicelineService.queryByPara(dictionary);
        Map<String, String> sslMap = new HashMap<>();
        List<List<SslResultDTO>> result = new ArrayList<>();
        for(Serviceline item:items){
            sslMap.put(item.getItemKey(), item.getItemValue());
        }

        productService = executionContext.getBean(ProductService.class).get();
        Product p = new Product();
        p.setStatus(1);
        p.setFiscalYear(argsMap.get("fiscalYear").toString());
        List<Product> productList = productService.queryByPara(p);
        Map<String, Product> productMap = new HashMap<>();
        for (Product product : productList) {
            productMap.put(product.getProductId(), product);
        }

        dashboardService = executionContext.getBean(DashboardService.class).get();
        fiscalCalenderService = executionContext.getBean(FiscalCalenderService.class).get();

        String currentYear = (String) argsMap.get("fiscalYear");
        int currentYearInt = Integer.parseInt(currentYear);
        String lastYear = (currentYearInt - 1) + "";


        String sslCode = "DASH_SSL_SELF";
        if (DashboardConstants.DATA_SCOPE_PROM.equals(dataScope)) {
            sslCode = "DASH_SSL_PROM";
            List<List<AuthGroupDetail>> authGroups = (List<List<AuthGroupDetail>>) argsMap.get("authGroups");
            UserAttribute userAttribute = (UserAttribute) argsMap.get("userAttribute");
            String authSql = dashboardService.buildAuthSql(isSelf, authGroups, userAttribute,
                    "sales.sl1", "sales.ssl2", "sales.ssl3",
                    "sales.city", "sales.region",
                    "sales.level", "sales.group_id", "sales.level_group",
                    "sales.head_office_location", "sales.company_type", "sales.industry_sector", "sales.client_channel",
                    "sales.sales_delivery_credit");
            argsMap.put("authSql", authSql);
        }
        ILiteDatasetExecutionService liteDatasetController = executionContext.getBean(ILiteDatasetExecutionService.class).get();
        LiteDatasetExecutionDTO liteDatasetExecutionDTO = new LiteDatasetExecutionDTO();
        liteDatasetExecutionDTO.setCode(sslCode);
        //处理ssl3参数
        List<String> ssl3 = new ArrayList<>();
        //PAS Mobility
        ssl3.add("4201");
        //PAS WFA
        ssl3.add("4202");
        //ECG
        ssl3.add("1032");
        //RCA
        ssl3.add("1133");
        //Global Trade
        ssl3.add("1031");
        //Indirect Tax Core
        ssl3.add("1030");
        //TP
        ssl3.add("1029");
        //ITS-CORE
        ssl3.add("1027");
        //TT
        ssl3.add("1028");
        //BTA
        ssl3.add("1024");
        //PCS
        ssl3.add("4530");
        //QS
        ssl3.add("1025");
        //GCR-CORE
        ssl3.add("1021");
        //ACR
        ssl3.add("1022");
        //Payroll
        ssl3.add("1023");
        argsMap.put("ssl3", ssl3);
        //处理year
        List<String> year = new ArrayList<>();
        year.add(lastYear);
        year.add(currentYear);
        argsMap.put("year", year);
        liteDatasetExecutionDTO.setArgs(args);
        ExecutionCommand cmd = ExecutionCommand.builder()
                .args(args)
                .build();
        dto = liteDatasetController.execute(liteDatasetExecutionDTO, cmd);
        if (EmptyUtils.isEmpty(dto)) {
            return new ArrayList<>();
        }
        //组装结构
        List<Map<String, Object>> sslResult = dto;
        Map<String, SslResultSubDTO> sslResultSubDTOMap = new HashMap<>();
        //合并数据
        for(Map<String, Object> subSslResult:sslResult){
            Object oAmount = subSslResult.get("amount");
            if(EmptyUtils.isEmpty(oAmount)){
                continue;
            }
            BigDecimal amount = (BigDecimal) oAmount;
            String itemSsl = (String)subSslResult.get("ssl3");
            String itemYear = (String)subSslResult.get("year");
            String otherYear;
            if(itemYear.equals(currentYear)){
                otherYear = lastYear;
            }else{
                otherYear = currentYear;
            }
            String product = (String)subSslResult.get("product");
            String key = itemSsl + itemYear;
            String otherKey = itemSsl + otherYear;
            SslResultSubDTO sslResultSubDTO = sslResultSubDTOMap.get(key);
            if(EmptyUtils.isEmpty(sslResultSubDTO)){
                sslResultSubDTO = new SslResultSubDTO();
                sslResultSubDTO.setSsl3(itemSsl);
                sslResultSubDTO.setName(sslMap.get(itemSsl));
                sslResultSubDTO.setValue(BigDecimal.ZERO);
                sslResultSubDTO.setProduct("");
                sslResultSubDTO.setProductValue(BigDecimal.ZERO);
                sslResultSubDTO.setYear(itemYear);
                sslResultSubDTOMap.put(key, sslResultSubDTO);

                SslResultSubDTO otherSslResultSubDTO = new SslResultSubDTO();
                otherSslResultSubDTO.setSsl3(itemSsl);
                otherSslResultSubDTO.setName(sslMap.get(itemSsl));
                otherSslResultSubDTO.setValue(BigDecimal.ZERO);
                otherSslResultSubDTO.setProduct("");
                otherSslResultSubDTO.setProductValue(BigDecimal.ZERO);
                otherSslResultSubDTO.setYear(otherYear);
                sslResultSubDTOMap.put(otherKey, otherSslResultSubDTO);
            }

            if(amount.compareTo(sslResultSubDTO.getProductValue()) > 0){
                sslResultSubDTO.setProduct(product);
                sslResultSubDTO.setProductValue(amount);
            }
            sslResultSubDTO.setValue(sslResultSubDTO.getValue().add(amount));
        }

        Map<String, List<SslResultSubDTO>> sslResultMap = new HashMap<>();
        for(SslResultSubDTO sslResultSubDTO:sslResultSubDTOMap.values()){
            String ssl3Key = sslResultSubDTO.getSsl3();
            List<SslResultSubDTO> sslList = sslResultMap.get(ssl3Key);
            if(EmptyUtils.isEmpty(sslList)){
                sslList = new ArrayList<>();
                sslResultMap.put(ssl3Key, sslList);
            }
            sslList.add(sslResultSubDTO);
        }
        List<SslResultDTO> btsLast = new ArrayList<>();
        List<SslResultDTO> btsCurrent = new ArrayList<>();
        List<SslResultDTO> gcrLast = new ArrayList<>();
        List<SslResultDTO> gcrCurrent = new ArrayList<>();
        List<SslResultDTO> one = new ArrayList<>();
        BigDecimal btsTotal = BigDecimal.ZERO;
        BigDecimal gcrTotal = BigDecimal.ZERO;
        BigDecimal btsQuantitativeService = processOne("Quantitative Service", "BTS", sslResultMap.get("1025"), productMap, btsLast, btsCurrent, currentYear, lastYear);
        btsTotal = btsTotal.add(btsQuantitativeService);
        BigDecimal btsPrivateTax = processOne("Private Tax", "BTS", sslResultMap.get("4530"), productMap, btsLast, btsCurrent, currentYear, lastYear);
        btsTotal = btsTotal.add(btsPrivateTax);
        BigDecimal btsBat = processOne("BTA", "BTS", sslResultMap.get("1024"), productMap, btsLast, btsCurrent, currentYear, lastYear);
        btsTotal = btsTotal.add(btsBat);
        BigDecimal gcrPayroll = processOne("Payroll", "GCR", sslResultMap.get("1023"), productMap, gcrLast, gcrCurrent, currentYear, lastYear);
        gcrTotal = gcrTotal.add(gcrPayroll);
        BigDecimal gcrAcr = processOne("ACR", "GCR", sslResultMap.get("1022"), productMap, gcrLast, gcrCurrent, currentYear, lastYear);
        gcrTotal = gcrTotal.add(gcrAcr);
        BigDecimal gcrGcrCore = processOne("GCR Core", "GCR", sslResultMap.get("1021"), productMap, gcrLast, gcrCurrent, currentYear, lastYear);
        gcrTotal = gcrTotal.add(gcrGcrCore);

        //GCR  ["1021", "1022", "1023"]
        //BTS  ["1025", "1024", "4530", "1026"]
        //判断是否勾选了GCR和BTS,勾选的情况下需要保证这2个节点的结构要完整
        Object oCompetency = argsMap.get("competency");
        boolean selectGcr = false;
        boolean selectBts = false;
        if(EmptyUtils.isNotEmpty(oCompetency)){
            List<String> competencyList = (List<String>)oCompetency;
            for(String competency:competencyList){
                if("1021".equals(competency) || "1022".equals(competency) || "1023".equals(competency)){
                    selectGcr = true;
                }
                if("1025".equals(competency) || "1024".equals(competency)
                        || "4530".equals(competency)|| "1026".equals(competency)){
                    selectBts = true;
                }
            }
        }

        List<SslResultSubDTO> sslResultSubDTOList = new ArrayList<>();
        SslResultSubDTO btsSslResultSubDTO = new SslResultSubDTO();
        btsSslResultSubDTO.setName("BTS");
        sslResultSubDTOList.add(btsSslResultSubDTO);
        SslResultSubDTO gcrSslResultSubDTO = new SslResultSubDTO();
        gcrSslResultSubDTO.setName("GCR");
        sslResultSubDTOList.add(gcrSslResultSubDTO);
        if(selectGcr && EmptyUtils.isEmpty(gcrLast)){
            buidlGcr(lastYear,sslResultSubDTOList, gcrLast);
            buidlGcr(currentYear,sslResultSubDTOList, gcrCurrent);
        }
        if(selectBts && EmptyUtils.isEmpty(btsLast)){
            buidlBts(lastYear,sslResultSubDTOList, btsLast);
            buidlBts(currentYear,sslResultSubDTOList, btsCurrent);
        }

        if(btsTotal.compareTo(BigDecimal.ZERO) > 0){
            one.addAll(btsLast);
            one.addAll(btsCurrent);
        }

        if(gcrTotal.compareTo(BigDecimal.ZERO) > 0){
            one.addAll(gcrLast);
            one.addAll(gcrCurrent);
        }

        result.add(one);

        Map<String, SslResultDTO> sslResultDTOs = new HashMap<>();

        SslResultDTO lastSslResultDTOLast = new SslResultDTO();
        String lastKey = "FY" + lastYear;
        lastSslResultDTOLast.setName(lastKey);
        sslResultDTOs.put(lastKey, lastSslResultDTOLast);

        SslResultDTO currentSslResultDTOLast = new SslResultDTO();
        String currentKey = "FY" + currentYear;
        currentSslResultDTOLast.setName(currentKey);
        sslResultDTOs.put(currentKey, currentSslResultDTOLast);

        processTwo("ITTS Advisory", new String[]{"1027","1028"}, sslResultMap, productMap, sslResultDTOs);
        processTwo("ITTS-TP", new String[]{"1029"}, sslResultMap, productMap, sslResultDTOs);
        processTwo("Indirect Tax Core", new String[]{"1030"}, sslResultMap, productMap, sslResultDTOs);
        processTwo("Indirect Tax-Global Trade", new String[]{"1031"}, sslResultMap, productMap, sslResultDTOs);
        processTwo("ECG&RAC", new String[]{"1032","1133"}, sslResultMap, productMap, sslResultDTOs);
        processTwo("PAS", new String[]{"4201","4202","1251"}, sslResultMap, productMap, sslResultDTOs);
        List<SslResultDTO> two = new ArrayList<>();
        two.add(lastSslResultDTOLast);
        two.add(currentSslResultDTOLast);
        result.add(two);
        return result;
    }

    void buidlGcr(String type, List<SslResultSubDTO> sslResultSubDTOList, List<SslResultDTO> gcr){
        //补全gcr Payroll lastYear
        SslResultDTO payrollsslResultDTO = new SslResultDTO();
        payrollsslResultDTO.setName("Payroll");
        payrollsslResultDTO.setType(type);
        payrollsslResultDTO.setValue(sslResultSubDTOList);
        gcr.add(payrollsslResultDTO);
        //补全gcr ACR
        SslResultDTO acrSslResultDTO = new SslResultDTO();
        acrSslResultDTO.setName("ACR");
        acrSslResultDTO.setType(type);
        acrSslResultDTO.setValue(sslResultSubDTOList);
        gcr.add(acrSslResultDTO);
        //补全gcr GCR Core
        SslResultDTO gcrCoresslResultDTO = new SslResultDTO();
        gcrCoresslResultDTO.setName("GCR Core");
        gcrCoresslResultDTO.setType(type);
        gcrCoresslResultDTO.setValue(sslResultSubDTOList);
        gcr.add(gcrCoresslResultDTO);
    }

    void buidlBts(String type, List<SslResultSubDTO> sslResultSubDTOList, List<SslResultDTO> bts){
        //补全bts Quantitative Service
        SslResultDTO payrollsslResultDTO = new SslResultDTO();
        payrollsslResultDTO.setName("Quantitative Service");
        payrollsslResultDTO.setType(type);
        payrollsslResultDTO.setValue(sslResultSubDTOList);
        bts.add(payrollsslResultDTO);
        //补全bts Private Tax
        SslResultDTO acrSslResultDTO = new SslResultDTO();
        acrSslResultDTO.setName("Private Tax");
        acrSslResultDTO.setType(type);
        acrSslResultDTO.setValue(sslResultSubDTOList);
        bts.add(acrSslResultDTO);
        //补全bts BTA
        SslResultDTO gcrCoresslResultDTO = new SslResultDTO();
        gcrCoresslResultDTO.setName("BTA");
        gcrCoresslResultDTO.setType(type);
        gcrCoresslResultDTO.setValue(sslResultSubDTOList);
        bts.add(gcrCoresslResultDTO);
    }

    void processTwo(String name, String[] codes, Map<String, List<SslResultSubDTO>> sslResultMap,
                    Map<String, Product> productMap, Map<String, SslResultDTO> sslResultDTOs) {
        Map<String, SslResultSubDTO> temp = new HashMap<>();
        for(String code:codes){
            List<SslResultSubDTO> dataList = sslResultMap.get(code);
            for(SslResultSubDTO data:dataList){
                String yearKey = "FY" + data.getYear();
                SslResultSubDTO sslResultSubDTO = temp.get(yearKey);
                if(EmptyUtils.isEmpty(sslResultSubDTO)){
                    sslResultSubDTO = new SslResultSubDTO();
                    sslResultSubDTO.setName(name);
                    sslResultSubDTO.setValue(BigDecimal.ZERO);
                    sslResultSubDTO.setProduct("");
                    sslResultSubDTO.setProductValue(BigDecimal.ZERO);
                    temp.put(yearKey, sslResultSubDTO);
                }
                BigDecimal oldAmount = sslResultSubDTO.getValue();
                BigDecimal amount = data.getValue();
                if(amount.compareTo(oldAmount) > 0){
                    sslResultSubDTO.setProduct(getProduct(data.getProduct(), productMap));
                    sslResultSubDTO.setProductValue(data.getProductValue());
                }
                BigDecimal value = sslResultSubDTO.getValue().add(amount);
                sslResultSubDTO.setValue(value);
            }
        }
        for(String key:temp.keySet()){
            SslResultDTO sslResultDTO = sslResultDTOs.get(key);
            if(EmptyUtils.isEmpty(sslResultDTO)){
                sslResultDTO = new SslResultDTO();
                sslResultDTO.setName(key);
                sslResultDTOs.put(key, sslResultDTO);
            }
            sslResultDTO.getValue().add(temp.get(key));
        }
    }

    BigDecimal processOne(String name, String type, List<SslResultSubDTO> dataList, Map<String, Product> productMap,
                          List<SslResultDTO> oneLast, List<SslResultDTO> oneCurrent, String currentYear, String lastYear) {
        BigDecimal total = BigDecimal.ZERO;
        SslResultDTO lastSslResultDTO = null;
        SslResultDTO currentSslResultDTO = null;
        if(EmptyUtils.isNotEmpty(dataList)){
            for(SslResultSubDTO sslData : dataList){
                SslResultDTO sslResultDTO = new SslResultDTO();
                sslResultDTO.setName(name);
                sslResultDTO.setType("FY"+sslData.getYear());
                SslResultSubDTO bts = new SslResultSubDTO();
                bts.setName("BTS");
                if("BTS".equals(type)){
                    bts.setProduct(getProduct(sslData.getProduct(), productMap));
                    bts.setProductValue(sslData.getProductValue());
                    bts.setValue(sslData.getValue());
                    sslResultDTO.getValue().add(bts);
                    SslResultSubDTO otherGcr = new SslResultSubDTO();
                    otherGcr.setName("GCR");
                    sslResultDTO.getValue().add(otherGcr);
                    if(EmptyUtils.isNotEmpty(sslData.getValue())){
                        total = total.add(sslData.getValue());
                    }
                }
                SslResultSubDTO gcr = new SslResultSubDTO();
                gcr.setName("GCR");
                if("GCR".equals(type)){
                    gcr.setProduct(getProduct(sslData.getProduct(), productMap));
                    gcr.setProductValue(sslData.getProductValue());
                    gcr.setValue(sslData.getValue());
                    SslResultSubDTO otherBts = new SslResultSubDTO();
                    otherBts.setName("BTS");
                    sslResultDTO.getValue().add(otherBts);
                    sslResultDTO.getValue().add(gcr);
                    if(EmptyUtils.isNotEmpty(sslData.getValue())){
                        total = total.add(sslData.getValue());
                    }
                }
                if(lastYear.equals(sslData.getYear())){
                    lastSslResultDTO = sslResultDTO;
                }else{
                    currentSslResultDTO = sslResultDTO;
                }
            }
        }
        if(EmptyUtils.isEmpty(lastSslResultDTO)){
            lastSslResultDTO = new SslResultDTO();
            lastSslResultDTO.setName(name);
            lastSslResultDTO.setType("FY"+lastYear);
            SslResultSubDTO sub = new SslResultSubDTO();
            sub.setName(type);
            if("BTS".equals(type)){
                lastSslResultDTO.getValue().add(sub);
                SslResultSubDTO otherSub = new SslResultSubDTO();
                otherSub.setName("GCR");
                lastSslResultDTO.getValue().add(otherSub);
            }else{
                SslResultSubDTO otherSub = new SslResultSubDTO();
                otherSub.setName("BTS");
                lastSslResultDTO.getValue().add(otherSub);
                lastSslResultDTO.getValue().add(sub);
            }
        }
        oneLast.add(lastSslResultDTO);
        if(EmptyUtils.isEmpty(currentSslResultDTO)){
            currentSslResultDTO = new SslResultDTO();
            currentSslResultDTO.setName(name);
            currentSslResultDTO.setType("FY"+currentYear);
            SslResultSubDTO sub = new SslResultSubDTO();
            sub.setName(type);
            if("BTS".equals(type)){
                currentSslResultDTO.getValue().add(sub);
                SslResultSubDTO otherSub = new SslResultSubDTO();
                otherSub.setName("GCR");
                currentSslResultDTO.getValue().add(otherSub);
            }else{
                SslResultSubDTO otherSub = new SslResultSubDTO();
                otherSub.setName("BTS");
                currentSslResultDTO.getValue().add(otherSub);
                currentSslResultDTO.getValue().add(sub);
            }
        }
        oneCurrent.add(currentSslResultDTO);
        return total;
    }

    String getProduct(String productCode, Map<String, Product> productMap) {
        if (EmptyUtils.isEmpty(productCode)) {
            return null;
        }
        Product product = productMap.get(productCode);
        if(EmptyUtils.isEmpty(product)){
            return productCode;
        }
        return product.getProductName();
    }
}


