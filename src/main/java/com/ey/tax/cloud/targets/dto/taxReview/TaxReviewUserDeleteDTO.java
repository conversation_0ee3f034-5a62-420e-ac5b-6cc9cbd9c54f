package com.ey.tax.cloud.targets.dto.taxReview;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-01-30 14:57:06
 * 
 */
@Data
@Schema(description = "根据条件删除实体")
public class TaxReviewUserDeleteDTO {
    /**
     * GPN COLUMN:gpn
     */
    @Schema(description = "GPN", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String gpn;

    /**
     * 姓名 COLUMN:name
     */
    @Schema(description = "姓名", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String name;

    /**
     * 邮箱 COLUMN:email
     */
    @Schema(description = "邮箱", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String email;

    /**
     * 服务线 COLUMN:service_line
     */
    @Schema(description = "服务线", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String serviceLine;

    /**
     * 级别 COLUMN:level
     */
    @Schema(description = "级别", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String level;
}