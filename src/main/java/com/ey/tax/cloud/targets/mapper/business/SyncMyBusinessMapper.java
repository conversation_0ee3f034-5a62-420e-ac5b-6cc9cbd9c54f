package com.ey.tax.cloud.targets.mapper.business;

import com.ey.cn.tax.framework.mybatisplus.core.mapper.IEyBaseMapper;
import com.ey.tax.cloud.targets.entity.business.SyncMyBusiness;
import org.apache.ibatis.annotations.Select;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-07-29 10:48:38
 *
 */
public interface SyncMyBusinessMapper extends IEyBaseMapper<SyncMyBusiness> {

    //查询财年最大周数
    @Select("SELECT MAX(fiscal_week) FROM sync_tax_my_business WHERE fiscal_year = #{fiscalYear} and is_del = 0")
    String selectMaxWeek(String fiscalYear);
}
