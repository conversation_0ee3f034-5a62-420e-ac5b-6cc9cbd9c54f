package com.ey.tax.cloud.targets.service.data.impl;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.ey.cn.tax.framework.context.EyCommonContextHolder;
import com.ey.cn.tax.framework.mybatisplus.extension.service.AbstractService;
import com.ey.tax.cloud.targets.entity.data.ExchangeRate;
import com.ey.tax.cloud.targets.mapper.data.ExchangeRateMapper;
import com.ey.tax.cloud.targets.service.data.ExchangeRateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.concurrent.TimeUnit;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-01-30 14:57:08
 *
 */
@Service
public class ExchangeRateServiceImpl extends AbstractService<ExchangeRateMapper, ExchangeRate> implements ExchangeRateService {
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Override
    @Cached(name = "exchange:",
            key = "#fiscalYear + ':' + #exchangeCurrency",
            expire = 1,syncLocal = true,
            timeUnit = TimeUnit.DAYS,
            cacheType = CacheType.BOTH)
    public BigDecimal getExchangeRate(String fiscalYear, String exchangeCurrency) {

        ExchangeRate exchangeRate = new ExchangeRate();
        exchangeRate.setFiscalYear(fiscalYear);
        exchangeRate.setExchangeCurrency(exchangeCurrency);
        exchangeRate = queryOneByPara(exchangeRate);
        if(exchangeRate!=null){
            return exchangeRate.getExchangeRate();
        }
        return BigDecimal.ZERO;
    }
}
