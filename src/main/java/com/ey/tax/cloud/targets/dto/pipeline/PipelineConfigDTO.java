package com.ey.tax.cloud.targets.dto.pipeline;

import com.ey.cn.tax.framework.dto.BaseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-01-24 16:14:31
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "默认实体")
public class PipelineConfigDTO extends BaseDTO {
    /**
     * 字段 COLUMN:column_name
     */
    @Schema(description = "字段")
    private String columnName;

    /**
     * 是否可编辑 COLUMN:is_edit
     */
    @Schema(description = "是否可编辑")
    private String isEdit;

    /**
     * 编辑后是否审批 COLUMN:is_edit_approve
     */
    @Schema(description = "编辑后是否审批")
    private String isEditApprove;

    /**
     * 颜色 COLUMN:color
     */
    @Schema(description = "颜色")
    private String color;

    /**
     * 表名 COLUMN:table_code
     */
    @Schema(description = "表名")
    private String tableCode;

    /**
     * pipeline页签 COLUMN:pipeline_tab
     */
    @Schema(description = "pipeline页签")
    private String pipelineTab;

    /**
     * pipeline分组 COLUMN:pipeline_group
     */
    @Schema(description = "pipeline分组")
    private String pipelineGroup;

    /**
     * 排序 COLUMN:order_num
     */
    @Schema(description = "排序")
    private Integer orderNum;

    /**
     * table名称 COLUMN:table_name
     */
    @Schema(description = "table名称")
    private String tableName;

    /**
     * 字段显示名称 COLUMN:column_display_name
     */
    @Schema(description = "字段显示名称")
    private String columnDisplayName;

    /**
     * 是否远程搜索 COLUMN:is_search
     */
    @Schema(description = "是否远程搜索")
    private Integer isSearch;

    /**
     * 提示信息 COLUMN:column_tips
     */
    @Schema(description = "提示信息")
    private String columnTips;

    /**
     * 字段控件类型 COLUMN:column_control_type
     */
    @Schema(description = "字段控件类型")
    private String columnControlType;



    /**
     * 宽度 COLUMN:span
     */
    @Schema(description = "宽度")
    private Integer span;

    /**
     * 输入框说明 COLUMN:placeholder
     */
    @Schema(description = "输入框说明")
    private String placeholder;

    /**
     * 对应后台接口 COLUMN:api
     */
    @Schema(description = "对应后台接口")
    private String api;

    /**
     * 规则 COLUMN:rules
     */
    @Schema(description = "财年")
    private String fiscalYear;

    /**
     * 是否可以新增 COLUMN:insertable
     */
    @Schema(description = "是否可以新增")
    private Integer insertable;

    /**
     * 字典code COLUMN:dict_code
     */
    @Schema(description = "字典code")
    private String dictCode;

    /**
     * 备注 COLUMN:remark
     */
    @Schema(description = "备注", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String remark;

    /**
     * 子名称 COLUMN:sub_name
     */
    @Schema(description = "子名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String subName;

    @Schema(description = "角色", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String role;

    @Schema(description = "是否为空", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String isBlank;
}
