package com.ey.tax.cloud.targets.dto.pipeline;

import com.ey.cn.tax.framework.validator.BatchUpdateGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import java.math.BigDecimal;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-01-16 15:29:02
 * 
 */
@Data
@Schema(description = "根据id批量更新实体")
public class SalesDeliveryApproveBatchUpdateDTO {
    
    /**
     * 数据id
     */
    @Schema(description = "数据id")
    @NotBlank(message = "{SalesDeliveryApproveDTO.id.NotBlank}", groups = {BatchUpdateGroup.class})
    private String id;

    /**
     * COLUMN:pipeline_id
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String pipelineId;

    /**
     * COLUMN:sales_delivery_credit
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String salesDeliveryCredit;

    /**
     * COLUMN:sales_delivery_credit_ratio
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal salesDeliveryCreditRatio;

    /**
     * COLUMN:sales_delivery_credit_ratio_last
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal salesDeliveryCreditRatioLast;

    /**
     * COLUMN:sales_delivery_credit_type
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer salesDeliveryCreditType;

    /**
     * COLUMN:sales_delivery_credit_amount
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal salesDeliveryCreditAmount;

    /**
     * COLUMN:sales_delivery_credit_amount_last
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal salesDeliveryCreditAmountLast;

    /**
     * COLUMN:is_part
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer isPart;

    /**
     * COLUMN:fiscal_year
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String fiscalYear;
}
