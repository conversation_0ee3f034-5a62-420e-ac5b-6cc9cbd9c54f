package com.ey.tax.cloud.targets.service.user.impl;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.ey.cn.tax.framework.context.EyCommonContextHolder;
import com.ey.cn.tax.framework.mybatisplus.core.mapper.IEyBaseMapper;
import com.ey.cn.tax.framework.mybatisplus.core.toolkit.EyWrappers;
import com.ey.cn.tax.framework.mybatisplus.extension.service.AbstractService;
import com.ey.tax.cloud.targets.entity.user.UserAttribute;
import com.ey.tax.cloud.targets.entity.user.UserGroup;
import com.ey.tax.cloud.targets.mapper.user.UserGroupMapper;
import com.ey.tax.cloud.targets.service.user.UserGroupService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-01-09 11:49:16
 *
 */
@Service
public class UserGroupServiceImpl extends AbstractService<UserGroupMapper, UserGroup> implements UserGroupService {
    @Override
    @Cached(name = "group:",
            key = "#id+'group'",
            expire = 30,syncLocal = true,
            timeUnit = TimeUnit.DAYS,
            cacheType = CacheType.BOTH)
    public String getNameById(String id) {
        UserGroup userGroup = queryById(id);
        if (userGroup == null) {
            return null;
        }
        return userGroup.getGroupName();
    }


    @Transactional(
            readOnly = true
    )
    @Cached(name = "group:",
            key = "#id",
            syncLocal = true,
            expire = 30,
            timeUnit = TimeUnit.DAYS,
            cacheType = CacheType.LOCAL)
    public UserGroup queryById(Serializable id) {
        this.logger.debug("queryById {}:{}", this.getEntitySimpleName(), id);
        UserGroup t = (UserGroup)(((IEyBaseMapper)this.baseMapper).selectById(id));
        this.processUserName(t);
        return t;
    }

    @Override
    @Cached(name = "group:",
            key = "#id",syncLocal = true,
            expire = 30,
            timeUnit = TimeUnit.DAYS,
            cacheType = CacheType.BOTH)
    public UserGroup getNameByIdInThreadLocal(String id) {
        //在线程变量中查询
        if(EyCommonContextHolder.get("userGroup").isPresent()){
          Map<String,UserGroup> userGroupMap = (Map<String,UserGroup>)EyCommonContextHolder.get("userGroup").get();
            return userGroupMap.get(id);
        }
        //在数据库中查询
        List<UserGroup> userGroup = queryByPara(new UserGroup());
        //生成map
        Map<String,UserGroup> userGroupMap = userGroup.stream().collect(Collectors.toMap(UserGroup::getId, Function.identity()));
        EyCommonContextHolder.put("userGroup",userGroupMap);
        return userGroupMap.get(id);
    }


    @Transactional(
            readOnly = true
    )
    @Cached(name = "group:",
            key = "#entity.type+ ':' +#entity.code+':'+ #entity.parentId",
            expire = 30, syncLocal = true,
            timeUnit = TimeUnit.DAYS,
            cacheType = CacheType.BOTH)
    public List<UserGroup> queryByPara(UserGroup entity) {
        this.logger.debug("queryByPara {}:{}", this.getEntitySimpleName(), entity);
        List<UserGroup> entities = ((IEyBaseMapper)this.baseMapper).selectList(EyWrappers.query(entity));
        this.processUserName(entities);
        return entities;
    }
}
