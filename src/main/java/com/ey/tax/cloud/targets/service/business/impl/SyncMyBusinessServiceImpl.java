package com.ey.tax.cloud.targets.service.business.impl;

import com.ey.cn.tax.framework.mybatisplus.core.mapper.IEyBaseMapper;
import com.ey.cn.tax.framework.mybatisplus.core.query.EyQueryWrapper;
import com.ey.cn.tax.framework.mybatisplus.core.toolkit.EyWrappers;
import com.ey.cn.tax.framework.mybatisplus.extension.service.AbstractService;
import com.ey.tax.cloud.targets.entity.business.MyBusiness;
import com.ey.tax.cloud.targets.entity.business.SyncMyBusiness;
import com.ey.tax.cloud.targets.mapper.business.SyncMyBusinessMapper;
import com.ey.tax.cloud.targets.service.business.SyncMyBusinessService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-07-29 10:48:38
 *
 */
@Service
public class SyncMyBusinessServiceImpl extends AbstractService<SyncMyBusinessMapper, SyncMyBusiness> implements SyncMyBusinessService {

    @Override
    public String selectMaxWeek(String fiscalYear) {
        return baseMapper.selectMaxWeek(fiscalYear);
    }

    @Transactional(
            readOnly = true
    )
    public List<SyncMyBusiness> queryList(SyncMyBusiness entity) {
        this.logger.debug("queryByPara {}:{}", this.getEntitySimpleName(), entity);
        EyQueryWrapper<SyncMyBusiness> query = EyWrappers.query(entity);
        //limit20
        query.last("limit 20");

        List<SyncMyBusiness> entities = ((IEyBaseMapper)this.baseMapper).selectList(query);
        this.processUserName((Collection)entities);
        return entities;
    }
}
