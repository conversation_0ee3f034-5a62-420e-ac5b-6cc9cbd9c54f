package com.ey.tax.cloud.targets.boot;

import com.ey.tax.cloud.targets.service.kpi.KpiCalculateService;
import com.ey.tax.cloud.targets.service.kpi.KpiConfigMonitor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

@Component
public class TargetInit implements ApplicationRunner {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private KpiConfigMonitor kpiConfigMonitor;

    @Autowired
    private KpiCalculateService kpiCalculateService;

    @Value("${target.kip-monitor:false}")
    private boolean kpiMontitor;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        logger.info("init target start");
        if(kpiMontitor){
            kpiCalculateService.reset();
            kpiConfigMonitor.init();
            Thread kpiMonitorThread = (Thread)kpiConfigMonitor;
            kpiMonitorThread.start();
        }
        logger.info("init target end");
    }

}
