package com.ey.tax.cloud.targets.controller.pipeline.impl;

import com.ey.cn.tax.framework.context.EyUserContextHolder;
import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.dto.SearchDTO;
import com.ey.cn.tax.framework.entity.Search;
import com.ey.cn.tax.framework.utils.ConvertUtils;
import com.ey.cn.tax.framework.web.annotation.EyRestController;
import com.ey.cn.tax.framework.web.base.AbstractController;
import com.ey.tax.cloud.targets.controller.pipeline.SalesDeliveryController;
import com.ey.tax.cloud.targets.dto.pipeline.*;
import com.ey.tax.cloud.targets.entity.pipeline.Pipeline;
import com.ey.tax.cloud.targets.entity.pipeline.SalesDelivery;
import com.ey.tax.cloud.targets.service.pipeline.PipelineService;
import com.ey.tax.cloud.targets.service.pipeline.SalesDeliveryService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-01-16 15:29:01
 *
 */
@EyRestController(path ="/v1/salesDelivery")
public class SalesDeliveryControllerImpl extends AbstractController<SalesDeliveryService, SalesDeliveryDTO, SalesDelivery> implements SalesDeliveryController {



    @Autowired
    private PipelineService pipelineService;


    /**
     * deleteList SalesDelivery from http
     */
    @Override
    public ResponseDTO<Void> deleteByIdList(SalesDeliveryDeleteByIdListDTO dto) {
        getService().deleteByIds(dto.getIds());
        return ResponseDTO.success();
    }



    /**
     * query SalesDelivery from http
     */
    @Override
    public ResponseDTO<SalesDeliveryRepDTO> queryById(SalesDeliveryQueryByIdDTO dto) {
        SalesDelivery entity = getService().queryById(dto.getId());
        SalesDeliveryRepDTO rDTO = ConvertUtils.convertEntity2DTO(entity, SalesDeliveryRepDTO.class);
        return ResponseDTO.success(rDTO);
    }


    /**
     * queryList SalesDelivery from http
     */
    @Override
    public ResponseDTO<List<SalesDeliveryRepDTO>> queryList(SalesDeliveryQueryDTO dto) {
        SalesDelivery entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        List<SalesDelivery> entities = getService().queryByPara(entity);
        return ResponseDTO.success(ConvertUtils.convertEntityList2DTOList(entities, SalesDeliveryRepDTO.class));
    }

    /**
     * Page SalesDelivery from http
     */
    @Override
    public ResponseDTO<SearchDTO<SalesDeliveryRepDTO>> queryPage(SearchDTO<SalesDeliveryQueryPageDTO> searchDTO) {
        Search<SalesDelivery> search = ConvertUtils.convertSearchDTO2Search(searchDTO, entityClass);
        getService().queryPageByPara(search);
        return ResponseDTO.success(ConvertUtils.convertSearch2SearchDTO(search, SalesDeliveryRepDTO.class));
    }

    /**
     * Count SalesDelivery from http
     */
    @Override
    public ResponseDTO<Long> queryCount(SalesDeliveryQueryDTO dto) {
        SalesDelivery entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        Long count = getService().queryCountByPara(entity);
        return ResponseDTO.success(count);
    }

    @Override
    public ResponseDTO submitUpdate(SalesDeliverySubmitDTO dto) {
        //查询对应pipeline
        String pipelineId = dto.getPipelineId();
        Pipeline pipeline = pipelineService.queryById(pipelineId);
        //获取当前登录人
        String usrId = EyUserContextHolder.get().getAuthUserId();
        if(usrId.equalsIgnoreCase(pipeline.getOpprPartnerId())){
            //合伙人不需要审批
            List<SalesDelivery> list = getService().submit(pipelineId, String.valueOf(pipeline.getWonFy()),pipeline.getUpdateFy(), ConvertUtils.convertDTOList2EntityList(dto.getList(), SalesDelivery.class));
            SalesDelivery salesDelivery = new SalesDelivery();
            salesDelivery.setPipelineId(pipelineId);
            getService().deleteByPara(salesDelivery);
            getService().save(list);
        }else if(usrId.equalsIgnoreCase(pipeline.getEngManagerId())) {
            //经理需要审批
            List<SalesDelivery> list = getService().submit(pipelineId, String.valueOf(pipeline.getWonFy()),pipeline.getUpdateFy(), ConvertUtils.convertDTOList2EntityList(dto.getList(), SalesDelivery.class));
            getService().apporve(list);
        }else{
            //其他人不允许修改
            return ResponseDTO.errorMessage("E0000060009", "user not allow to update");
        }
        return ResponseDTO.success();
    }

}
