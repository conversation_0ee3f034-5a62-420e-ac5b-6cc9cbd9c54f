package com.ey.tax.cloud.targets.dto.auth;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2023-12-22 10:32:03
 * 
 */
@Data
@Schema(description = "根据条件删除实体")
public class AuthGroupRangeDeleteDTO {
    /**
     * 范围类型 COLUMN:range_type
     */
    @Schema(description = "范围类型", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String rangeType;

    /**
     * 范围值 COLUMN:range_vale
     */
    @Schema(description = "范围值", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String rangeValue;

    /**
     * 权限组ID COLUMN:group_id
     */
    @Schema(description = "权限组ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String groupId;
}
