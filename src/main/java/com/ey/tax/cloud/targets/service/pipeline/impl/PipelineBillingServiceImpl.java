package com.ey.tax.cloud.targets.service.pipeline.impl;

import com.ey.cn.tax.framework.mybatisplus.extension.service.AbstractService;
import com.ey.tax.cloud.targets.entity.pipeline.PipelineBilling;
import com.ey.tax.cloud.targets.mapper.pipeline.PipelineBillingMapper;
import com.ey.tax.cloud.targets.service.pipeline.PipelineBillingService;
import org.springframework.stereotype.Service;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2023-12-28 16:17:28
 * 
 */
@Service
public class PipelineBillingServiceImpl extends AbstractService<PipelineBillingMapper, PipelineBilling> implements PipelineBillingService {
}