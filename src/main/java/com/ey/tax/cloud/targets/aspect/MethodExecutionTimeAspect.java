package com.ey.tax.cloud.targets.aspect;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Aspect
@Component
public class MethodExecutionTimeAspect {

/*

    private static final Logger logger = LoggerFactory.getLogger(MethodExecutionTimeAspect.class);

    // 监控Controller
    @Around("execution(* com.ey.tax.cloud.targets.controller..*.*(..))")
    public Object monitorControllerExecutionTime(ProceedingJoinPoint joinPoint) throws Throwable {
        return monitorExecutionTime(joinPoint, "Controller");
    }

    // 监控Service
    @Around("execution(* com.ey.tax.cloud.targets.service..*.*(..))")
    public Object monitorServiceExecutionTime(ProceedingJoinPoint joinPoint) throws Throwable {
        return monitorExecutionTime(joinPoint, "Service");
    }

    private Object monitorExecutionTime(ProceedingJoinPoint joinPoint, String type) throws Throwable {
        long startTime = System.currentTimeMillis();
        String className = joinPoint.getSignature().getDeclaringTypeName();
        String methodName = joinPoint.getSignature().getName();

        try {
            return joinPoint.proceed();
        } finally {
            long endTime = System.currentTimeMillis();
            long executionTime = endTime - startTime;
            // 可以添加阈值警告
            if (executionTime > 2000) { // 如果执行时间超过1秒
                logger.warn("{} Method: {}.{} execution time exceeded threshold: {} ms",
                        type, className, methodName, executionTime);
            }
        }
    }*/
}
