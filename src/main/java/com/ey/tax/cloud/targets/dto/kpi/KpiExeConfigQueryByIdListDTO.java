package com.ey.tax.cloud.targets.dto.kpi;

import com.ey.cn.tax.framework.validator.QueryByIdListGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-01-22 10:49:40
 * 
 */
@Data
@Schema(description = "根据id列表查询实体")
public class KpiExeConfigQueryByIdListDTO {
    
    /**
     * 数据id列表
     */
    @Schema(description = "数据id列表")
    @NotNull(message = "{KpiExeConfigDTO.ids.NotNull}", groups = {QueryByIdListGroup.class})
    @Size(min = 1, message = "{KpiExeConfigDTO.ids.Size}", groups = {QueryByIdListGroup.class})
    private List<String> ids;
}