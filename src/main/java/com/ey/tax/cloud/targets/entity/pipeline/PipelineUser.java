package com.ey.tax.cloud.targets.entity.pipeline;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ey.cn.tax.framework.mybatisplus.core.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2023-12-25 11:19:57
 *
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("target_pipeline_user")
public class PipelineUser extends BaseEntity {
    /**
     * pipelineID COLUMN:pipeline_id
     */
    private String pipelineId;

    /**
     * 用户ID COLUMN:user_id
     */
    private String userId;
}
