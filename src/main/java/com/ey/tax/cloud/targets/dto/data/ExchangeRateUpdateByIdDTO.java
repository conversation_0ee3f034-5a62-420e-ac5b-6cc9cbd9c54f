package com.ey.tax.cloud.targets.dto.data;

import com.ey.cn.tax.framework.validator.UpdateByIdGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import java.math.BigDecimal;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-01-30 14:57:08
 * 
 */
@Data
@Schema(description = "根据id批量更新实体")
public class ExchangeRateUpdateByIdDTO {
    
    /**
     * 数据id
     */
    @Schema(description = "数据id")
    @NotBlank(message = "{ExchangeRateDTO.id.NotBlank}", groups = {UpdateByIdGroup.class})
    private String id;

    /**
     * 财年 COLUMN:fiscal_year
     */
    @Schema(description = "财年", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String fiscalYear;

    /**
     * 来源:tiger:0,导入:1 COLUMN:exchange_source
     */
    @Schema(description = "来源:tiger:0,导入:1", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer exchangeSource;

    /**
     * 汇率4位小数 COLUMN:exchange_rate
     */
    @Schema(description = "汇率4位小数", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal exchangeRate;

    /**
     * 币种 COLUMN:exchange_currency
     */
    @Schema(description = "币种", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String exchangeCurrency;
}