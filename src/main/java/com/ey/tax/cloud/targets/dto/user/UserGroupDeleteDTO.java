package com.ey.tax.cloud.targets.dto.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-01-09 11:49:16
 * 
 */
@Data
@Schema(description = "根据条件删除实体")
public class UserGroupDeleteDTO {
    /**
     * 类型 COLUMN:type
     */
    @Schema(description = "类型", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String type;

    /**
     * 父级 COLUMN:parent_id
     */
    @Schema(description = "父级", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String parentId;

    /**
     * 名称 COLUMN:group_name
     */
    @Schema(description = "名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String groupName;

    /**
     * 关联角色 COLUMN:role_id
     */
    @Schema(description = "关联角色", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String roleId;

    /**
     * 关联邮件模板 COLUMN:mail_id
     */
    @Schema(description = "关联邮件模板", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String mailId;
}