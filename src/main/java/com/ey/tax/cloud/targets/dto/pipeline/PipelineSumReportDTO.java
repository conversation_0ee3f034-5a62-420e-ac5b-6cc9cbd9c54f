package com.ey.tax.cloud.targets.dto.pipeline;

import com.ey.cn.tax.framework.dto.BaseRepDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2023-12-28 16:43:30
 *
 */
@Data
@EqualsAndHashCode(callSuper=false)
@Schema(description = "查询返回实体")
public class PipelineSumReportDTO extends BaseRepDTO {


    @Schema(description = "tal XXXX.00")
    private BigDecimal tal;

    private BigDecimal pipelineTal;


    private BigDecimal salesTal;

}
