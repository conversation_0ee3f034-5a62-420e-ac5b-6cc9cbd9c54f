package com.ey.tax.cloud.targets.service.auth.impl;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ey.cn.tax.framework.context.EyCommonContextHolder;
import com.ey.cn.tax.framework.context.EyUserContextHolder;
import com.ey.cn.tax.framework.entity.Search;
import com.ey.cn.tax.framework.mybatisplus.extension.service.AbstractService;
import com.ey.cn.tax.framework.mybatisplus.utils.PageUtils;
import com.ey.cn.tax.framework.utils.EmptyUtils;
import com.ey.tax.cloud.targets.constant.TargetAuthConstants;
import com.ey.tax.cloud.targets.entity.auth.*;
import com.ey.tax.cloud.targets.entity.data.Dictionary;
import com.ey.tax.cloud.targets.entity.data.DictionaryItem;
import com.ey.tax.cloud.targets.entity.data.Serviceline;
import com.ey.tax.cloud.targets.entity.user.UserAttribute;
import com.ey.tax.cloud.targets.entity.user.UserGroup;
import com.ey.tax.cloud.targets.mapper.auth.AuthGroupMapper;
import com.ey.tax.cloud.targets.service.auth.AuthGroupDetailService;
import com.ey.tax.cloud.targets.service.auth.AuthGroupRangeService;
import com.ey.tax.cloud.targets.service.auth.AuthGroupService;
import com.ey.tax.cloud.targets.service.auth.AuthGroupUserService;
import com.ey.tax.cloud.targets.service.data.DictionaryItemService;
import com.ey.tax.cloud.targets.service.data.DictionaryService;
import com.ey.tax.cloud.targets.service.data.ServicelineService;
import com.ey.tax.cloud.targets.service.user.UserAttributeService;
import com.ey.tax.cloud.targets.service.user.UserGroupService;
import groovy.lang.Lazy;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2023-12-22 10:32:03
 *
 */
@Service
public class AuthGroupServiceImpl extends AbstractService<AuthGroupMapper, AuthGroup> implements AuthGroupService {

    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private UserAttributeService userAttributeService;
    @Autowired
    private UserGroupService userGroupService;

    private AuthGroupService getSelf() {
        return applicationContext.getBean(AuthGroupService.class);
    }

    @Autowired
    private AuthGroupRangeService authGroupRangeService;
    @Autowired
    private AuthGroupUserService authGroupUserService;
    @Autowired
    private AuthGroupDetailService authGroupDetailService;
    @Autowired
    private ServicelineService servicelineService;

    @Autowired
    private DictionaryItemService dictionaryItemService;

    private String fiscalYearCommon;



    @Autowired
    private DictionaryService dictionaryService;
    @Transactional(readOnly = true)
    @Override
    public Search<AuthGroup> queryPageByPara(Search<AuthGroup> search) {
        logger.debug("queryPageByPara {}:{}", getEntitySimpleName(), search);
        QueryWrapper<AuthGroup> queryWrapper = Wrappers.query(search.getQueryParams());
        queryWrapper.orderByDesc("create_time");
        queryWrapper.in(CollectionUtils.isNotEmpty(search.getQueryParams().getAuthTypes()), "auth_type", search.getQueryParams().getAuthTypes());
        queryWrapper.in(CollectionUtils.isNotEmpty(search.getQueryParams().getMenuCodes()), "menu_code", search.getQueryParams().getMenuCodes());
        IPage<AuthGroup> iPage = baseMapper.selectPage(PageUtils.convertPageToIPage(search.getPage()), queryWrapper);
        List<AuthGroup> records = iPage.getRecords();
        search.setEntities(records);
        if (CollectionUtils.isEmpty(records)) {
            search.getPage().setTotalCount(0L);
        } else {
            processUserName(records);
            search.getPage().setTotalCount(iPage.getTotal());
        }
        return search;
    }

    /**
     * query by para
     */
    @Transactional(readOnly = true)
    public List<AuthGroup> queryByPara(AuthGroup entity) {
        logger.debug("queryByPara {}:{}", getEntitySimpleName(), entity);
        QueryWrapper<AuthGroup> queryWrapper = Wrappers.query(entity);
        queryWrapper.in(CollectionUtils.isNotEmpty(entity.getAuthTypes()), "auth_type", entity.getAuthTypes());
        queryWrapper.in(CollectionUtils.isNotEmpty(entity.getMenuCodes()), "menu_code", entity.getMenuCodes());
        List<AuthGroup> entities = baseMapper.selectList(queryWrapper);
        processUserName(entities);
        return entities;
    }

    @Override
    @Cached(name = "authCache", expire = 30, timeUnit = TimeUnit.DAYS,
            key = "#menuCode + ':' + #fiscalYear + ':' + #usrId",syncLocal = true,
            cacheType = CacheType.BOTH
             )
    public AuthCacheResult getByUserIdAndMenuResult(String usrId, String menuCode,String fiscalYear) {
        fiscalYearCommon = fiscalYear;
        AuthCacheResult authCacheResult = new AuthCacheResult();
        List<List<AuthGroupDetail>> authGroupDetailList = new ArrayList<>();
        String targetAuth = menuCode;
        List<AuthGroup> sslLeader = new ArrayList<>();
        UserAttribute userAttributes = userAttributeService.queryCurrentUser();
        //获取用户的levelGroup
        String groupList = userAttributes.getGroupId();
        //根据targetAuth查询用户权限
        AuthGroup authGroup = new AuthGroup();
        authGroup.setMenuCode(targetAuth);
        authGroup.setFiscalYear(fiscalYear);
        List<AuthGroup> authGroups = queryByPara(authGroup);
        if(EmptyUtils.isEmpty(authGroups)) {
            return null;
        }
        //查询authGroupUser生成map
        AuthGroupUser authGroupUserQuery = new AuthGroupUser();
        authGroupUserQuery.setUserId(usrId);
        List<AuthGroupUser> authGroupUsers1 = authGroupUserService.queryByPara(authGroupUserQuery);
        //根据groupId生成map
        Map<String,List<AuthGroupUser>> groupUserMap = authGroupUsers1.stream().collect(Collectors.groupingBy(AuthGroupUser::getGroupId));
        //查询authGroupRange生成map
        AuthGroupRange authGroupRangeQuery = new AuthGroupRange();
        List<AuthGroupRange> authGroupRanges1 = authGroupRangeService.queryByPara(authGroupRangeQuery);
        //根据groupId生成map
        Map<String,List<AuthGroupRange>> groupRangeMap = authGroupRanges1.stream().collect(Collectors.groupingBy(AuthGroupRange::getGroupId));
        if (EmptyUtils.isNotEmpty(authGroups)) {
            //判断该用户适用哪些权限
            for (AuthGroup group : authGroups) {
                    //如果没有交集，跳过
                    if (!groupList.equals(group.getPartnerRole())) {
                        continue;
                    }
                if(EmptyUtils.isNotEmpty(group.getUserTag())){
                    if(EmptyUtils.isNotEmpty(userAttributes.getUserTag())){
                        if(!group.getUserTag().equals(userAttributes.getUserTag())){
                            continue;
                        }
                    }
                }
                String authType = group.getAuthType();
                String conferType = group.getConferType();
                //authType为1时，表示仅自己
                if (TargetAuthConstants.AUTH_GROUP_IS_SELF.equals(authType)) {
                    authCacheResult.setIsSelf(true);
                } else {
                    if (TargetAuthConstants.AUTH_GROUP_RANGE_USER.equals(conferType)) {
                        //AuthGroupUser指定用户
                        if (EmptyUtils.isNotEmpty(groupUserMap.get(group.getId()))) {
                            for (AuthGroupUser groupUser : groupUserMap.get(group.getId())) {
                                if(groupUser.getUserId().equals(usrId)){
                                    AuthGroupDetail authGroupDetail = new AuthGroupDetail();
                                    authGroupDetail.setGroupId(group.getId());
                                    setDetailMap(authGroupDetail,authGroupDetailList);
                                    if(EmptyUtils.isNotEmpty(group.getRemark())&&(group.getRemark().startsWith("SSL Leader")||group.getRemark().startsWith("SL Leader")||group.getRemark().startsWith("Target Admin"))){
                                        sslLeader.add(group);
                                    }
                                    break;
                                }
                            }
                        }
                    } else {

                        if (EmptyUtils.isNotEmpty(groupRangeMap.get(group.getId()))) {
                            //authGroupRanges按照rangeType分组判断是否满足、如果都满足则添加到authGroupDetailList
                            Map<String,List<AuthGroupRange>> groupByRangeType = groupRangeMap.get(group.getId()).stream().collect(Collectors.groupingBy(AuthGroupRange::getRangeType));
                            int flagCount = 0;//满足的分组数量
                            for (String s : groupByRangeType.keySet()) {
                                for (AuthGroupRange range : groupByRangeType.get(s)) {
                                    if (range.getRangeType().equals("1")) {
                                            if (range.getRangeValue().equals(userAttributes.getSsl3())) {
                                                flagCount++;
                                                break;
                                        }
                                    } else if (range.getRangeType().equals("2")) {
                                            if (range.getRangeValue().equals(userAttributes.getCity())) {
                                                flagCount++;
                                                break;
                                        }
                                    } else if (range.getRangeType().equals("3")) {
                                            if (range.getRangeValue().equals(userAttributes.getLevel())) {
                                                flagCount++;
                                                break;
                                            }
                                    } else if (range.getRangeType().equals("userTag")) {
                                        if(EmptyUtils.isNotEmpty(userAttributes.getUserTag())){
                                            if (range.getRangeValue().equals(userAttributes.getUserTag())) {
                                                flagCount++;
                                                break;
                                            }
                                        }
                                    }
                                }
                            }
                            if(flagCount==groupByRangeType.size()){
                                AuthGroupDetail authGroupDetail = new AuthGroupDetail();
                                authGroupDetail.setGroupId(group.getId());
                                //判断group的remark是否为空，同时以Target Admin、SL Leader、SSL Leader开头
                                if(EmptyUtils.isNotEmpty(group.getRemark())&&(group.getRemark().startsWith("SSL Leader")||group.getRemark().startsWith("SL Leader")||group.getRemark().startsWith("Target Admin"))){
                                    sslLeader.add(group);
                                }
                                setDetailMap(authGroupDetail,authGroupDetailList);
                            }
                        }
                    }
                }
            }

        }
        authCacheResult.setAuthGroupDetailList(authGroupDetailList);
        authCacheResult.setSslLeader(sslLeader);
        return authCacheResult;
    }

    private void setDetailMap(AuthGroupDetail authGroupDetail,List<List<AuthGroupDetail>> authGroupDetailList) {
        List<AuthGroupDetail> authGroupDetails = authGroupDetailService.queryByPara(authGroupDetail);
        //limitType为2（不限定）时，根据detailType分组，并将值放到detailValues字段中,xml里面的sql查询时对同一个limitType的进行循环用or连接
        List<AuthGroupDetail> groupDetails = authGroupDetails.stream().filter(authGroupDetail1 -> authGroupDetail1.getLimitType().equals("1")).collect(Collectors.toList());
        for (AuthGroupDetail groupDetail : groupDetails) {
            if (EmptyUtils.isNotEmpty(groupDetail.getDetailValue())) {
                if (groupDetail.getDetailType().equals("3")) {
                    //查询用户组
                    UserGroup userGroup = userGroupService.getNameByIdInThreadLocal(groupDetail.getDetailValue());
                    if (EmptyUtils.isNotEmpty(userGroup)) {
                        groupDetail.setDetailValue(userGroup.getType());
                    }
                } else if (groupDetail.getDetailType().equals("2")) {
                    String tag = dictionaryService.queryTagByCode(TargetAuthConstants.AUTH_GROUP_DICT_LOCATION, groupDetail.getDetailValue());
                    if(EmptyUtils.isNotEmpty(tag)) {
                        groupDetail.setDetailValue(tag);
                    }
                } else if (groupDetail.getDetailType().equals("1")) {
                    Serviceline serviceline = new Serviceline();
                    serviceline.setFiscalYear(fiscalYearCommon);
                    serviceline.setItemKey(groupDetail.getDetailValue());
                    serviceline = servicelineService.queryOneByPara(serviceline);
                    if(EmptyUtils.isNotEmpty(serviceline.getLayer())) {
                        groupDetail.setDetailValue(String.valueOf(serviceline.getLayer()));

                    }
                }
            }
        }
        Map<String, List<AuthGroupDetail>> map = authGroupDetails.stream().filter(authGroupDetail1 -> authGroupDetail1.getLimitType().equals("2")).filter(authGroupDetail1 -> EmptyUtils.isNotEmpty(authGroupDetail1.getDetailValue())).collect(Collectors.groupingBy(AuthGroupDetail::getDetailType));
        for (Map.Entry<String, List<AuthGroupDetail>> entry : map.entrySet()) {
            List<String> detailValues = entry.getValue().stream().map(AuthGroupDetail::getDetailValue).collect(Collectors.toList());
            entry.getValue().get(0).setDetailValues(detailValues);
            groupDetails.add(entry.getValue().get(0));
        }
        //groupDetails 按照detailType、limitType、detailValue去重
        Set<String> set = new HashSet<>();
        List<AuthGroupDetail> groupDetails1 = groupDetails.stream().filter(p -> set.add(p.getDetailType() + "|" + p.getLimitType() + "|" + p.getDetailValue())).collect(Collectors.toList());
        authGroupDetailList.add(groupDetails1);
    }

    @Override
    public List<List<AuthGroupDetail>> getByUserIdAndMenu(String userId, String menuCode, String fiscalYear) {
        AuthCacheResult authByUserIdAndMenuResult = getSelf().getByUserIdAndMenuResult(userId, menuCode, fiscalYear);
        if(EmptyUtils.isNotEmpty(authByUserIdAndMenuResult.getIsSelf())&&authByUserIdAndMenuResult.getIsSelf()){
            EyCommonContextHolder.put(TargetAuthConstants.KEY_TARGET_AUTH_IS_SLEF, true);
        }
        if (EmptyUtils.isNotEmpty(authByUserIdAndMenuResult.getAuthGroupDetailList())) {
            EyCommonContextHolder.put(TargetAuthConstants.KEY_TARGET_AUTH_RANGE_LIST, authByUserIdAndMenuResult.getAuthGroupDetailList());

        }
        //判断sslLeader是否为空，不为空则添加到线程变量EyCommonContextHolder
        if (EmptyUtils.isNotEmpty(authByUserIdAndMenuResult.getSslLeader())) {
            EyCommonContextHolder.put(TargetAuthConstants.KEY_TARGET_AUTH_SSL_LEADER, authByUserIdAndMenuResult.getSslLeader());
        }
        return authByUserIdAndMenuResult.getAuthGroupDetailList();

    }

    public List<AuthGroupDetail> setDetailMap(AuthGroupDetail authGroupDetail,String fiscalYear) {
        List<AuthGroupDetail> authGroupDetails = authGroupDetailService.queryByPara(authGroupDetail);
        //limitType为2（不限定）时，根据detailType分组，并将值放到detailValues字段中,xml里面的sql查询时对同一个limitType的进行循环用or连接
        List<AuthGroupDetail> groupDetails = authGroupDetails.stream().filter(authGroupDetail1 -> authGroupDetail1.getLimitType().equals("1")).collect(Collectors.toList());
        for (AuthGroupDetail groupDetail : groupDetails) {
            if (EmptyUtils.isNotEmpty(groupDetail.getDetailValue())) {
                if (groupDetail.getDetailType().equals("3")) {
                    //查询用户组
                    UserGroup userGroup = userGroupService.getNameByIdInThreadLocal(groupDetail.getDetailValue());
                    if (EmptyUtils.isNotEmpty(userGroup)) {
                        groupDetail.setDetailValue(userGroup.getType());
                    }
                } else if (groupDetail.getDetailType().equals("2")) {
                    String tag = dictionaryService.queryTagByCode(TargetAuthConstants.AUTH_GROUP_DICT_LOCATION, groupDetail.getDetailValue());
                    if(EmptyUtils.isNotEmpty(tag)) {
                        groupDetail.setDetailValue(tag);
                    }
                } else if (groupDetail.getDetailType().equals("1")) {
                    Serviceline serviceline = new Serviceline();
                    serviceline.setFiscalYear(fiscalYear);
                    serviceline.setItemKey(groupDetail.getDetailValue());
                    serviceline = servicelineService.queryOneByPara(serviceline);
                    if(EmptyUtils.isNotEmpty(serviceline.getLayer())) {
                        groupDetail.setDetailValue(String.valueOf(serviceline.getLayer()));

                    }
                }
            }
        }
        Map<String, List<AuthGroupDetail>> map = authGroupDetails.stream().filter(authGroupDetail1 -> authGroupDetail1.getLimitType().equals("2")).filter(authGroupDetail1 -> EmptyUtils.isNotEmpty(authGroupDetail1.getDetailValue())).collect(Collectors.groupingBy(AuthGroupDetail::getDetailType));
        for (Map.Entry<String, List<AuthGroupDetail>> entry : map.entrySet()) {
            List<String> detailValues = entry.getValue().stream().map(AuthGroupDetail::getDetailValue).collect(Collectors.toList());
            entry.getValue().get(0).setDetailValues(detailValues);
            groupDetails.add(entry.getValue().get(0));
        }
        //groupDetails 按照detailType、limitType、detailValue去重
        Set<String> set = new HashSet<>();
        List<AuthGroupDetail> groupDetails1 = groupDetails.stream().filter(p -> set.add(p.getDetailType() + "|" + p.getLimitType() + "|" + p.getDetailValue())).collect(Collectors.toList());
        return groupDetails1;
    }



}
