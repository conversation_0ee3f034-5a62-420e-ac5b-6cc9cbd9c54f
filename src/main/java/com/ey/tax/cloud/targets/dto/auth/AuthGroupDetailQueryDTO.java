package com.ey.tax.cloud.targets.dto.auth;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2023-12-22 10:32:04
 * 
 */
@Data
@Schema(description = "根据条件查询实体")
public class AuthGroupDetailQueryDTO {
    /**
     * 权限组ID COLUMN:group_id
     */
    @Schema(description = "权限组ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String groupId;

    /**
     * 详情类型 COLUMN:detail_type
     */
    @Schema(description = "详情类型", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String detailType;

    /**
     * 详情值 COLUMN:detail_value
     */
    @Schema(description = "详情值", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String detailValue;

    /**
     * 限定类型 COLUMN:limit_type
     */
    @Schema(description = "限定类型", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String limitType;
}