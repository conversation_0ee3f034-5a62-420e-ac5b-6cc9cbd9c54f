package com.ey.tax.cloud.targets.service.pipeline;

import com.ey.cn.tax.framework.service.BaseService;
import com.ey.cn.tax.lite.impl.port.input.controller.LiteDatasetController;
import com.ey.tax.cloud.targets.entity.pipeline.SalesDeliveryData;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-01-30 14:57:08
 *
 */
public interface SalesDeliveryDataService extends BaseService<SalesDeliveryData> {

    void syncData();
    void syncDataByPipeline( String pipelineId);
    void syncDataByUserAndYear( String userId);

    List<String> selectPipelineIdBySalesDeliveryCreditList(String wonFy,List<String> ssl3List,List<String> locationList,List<String> regionList);
    List<String> selectPipelineIdBySalesDeliveryCreditList(List<String> userId);



}
