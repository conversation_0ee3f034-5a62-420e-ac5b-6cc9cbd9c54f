package com.ey.tax.cloud.targets.dto.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-01-09 10:02:34
 *
 */
@Data
@Schema(description = "模糊查询")
public class UserSearchDTO {
    /**
     * 平台用户id COLUMN:user_id
     */
    @Schema(description = "查询字段", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String keyword;

    @Schema(description = "查询类型(EM、EP、TPC、buddyPartner)", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String type;
    @Schema(description = "财年", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String fy;

    @Schema(description = "产品", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String productType;


}
