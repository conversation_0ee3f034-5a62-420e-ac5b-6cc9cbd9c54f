package com.ey.tax.cloud.targets.dto.auth;

import com.ey.cn.tax.framework.dto.BaseRepDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2023-12-22 10:32:03
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "查询返回实体")
public class AuthGroupRepDTO extends BaseRepDTO {
    /**
     * 菜单/功能code COLUMN:menu_code
     */
    @Schema(description = "菜单/功能code")
    private String menuCode;

    private String menuCodeLabel;

    /**
     * 用户组编码 COLUMN:group_code
     */
    @Schema(description = "用户组编码")
    private String groupCode;

    /**
     * 用户组名称 COLUMN:group_name
     */
    @Schema(description = "用户组名称")
    private String groupName;

    /**
     * 财年 COLUMN:fiscal_year
     */
    @Schema(description = "财年")
    private String fiscalYear;

    /**
     * 权限类型 COLUMN:auth_type
     */
    @Schema(description = "权限类型")
    private String authType;

    /**
     * 授权类型 COLUMN:confer_type
     */
    @Schema(description = "授权类型")
    private String conferType;

    /**
     * 伙伴角色 COLUMN:partner_role
     */
    @Schema(description = "伙伴角色")
    private String partnerRole;

    private String authTypeLabel;

    private String conferTypeLabel;

    private String partnerRoleLabel;

    @Schema(description = "是否当前财年")
    private boolean isCurrentFiscalYear;
    @Schema(description = "备注")
    private String remark;

    @Schema(description = "用户标签")
    private String userTag;
}
