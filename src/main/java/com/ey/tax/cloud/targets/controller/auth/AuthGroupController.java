package com.ey.tax.cloud.targets.controller.auth;

import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.dto.SearchDTO;
import com.ey.cn.tax.framework.validator.AddGroup;
import com.ey.cn.tax.framework.validator.DeleteByIdGroup;
import com.ey.cn.tax.framework.validator.DeleteByIdListGroup;
import com.ey.cn.tax.framework.validator.QueryByIdGroup;
import com.ey.cn.tax.framework.validator.QueryByIdListGroup;
import com.ey.cn.tax.framework.validator.QueryPageGroup;
import com.ey.cn.tax.framework.validator.UpdateByIdGroup;
import com.ey.cn.tax.framework.web.annotation.EyPostMapping;
import com.ey.cn.tax.framework.web.base.BaseController;
import com.ey.tax.cloud.admin.remote.resource.TenResourceFeignClient;
import com.ey.tax.cloud.targets.dto.auth.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2023-12-22 10:32:03
 *
 */
@Tag(name="权限组",description = "权限组")
public interface AuthGroupController extends BaseController<AuthGroupDTO> {





    /**
     * add AuthGroup from http
     */
    @Operation(summary = "新增", description = "新增一条数据")
    @EyPostMapping(value = "/add.do")
    ResponseDTO<Void> add(@RequestBody @Validated({AddGroup.class}) AuthGroupAddDTO dto);

    /**
     * delete AuthGroup from http
     */
    @Operation(summary = "根据id删除", description = "根据id删除一条数据")
    @EyPostMapping(value = "/deleteById.do")
    ResponseDTO<Void> deleteById(@RequestBody @Validated({DeleteByIdGroup.class}) AuthGroupDeleteByIdDTO dto);

    /**
     * deleteList AuthGroup from http
     */
    @Operation(summary = "根据id列表删除", description = "根据id列表批量删除数据")
    @EyPostMapping(value = "/deleteByIdList.do")
    ResponseDTO<Void> deleteByIdList(@RequestBody @Validated({DeleteByIdListGroup.class}) AuthGroupDeleteByIdListDTO dto);

    /**
     * update AuthGroup from http
     */
    @Operation(summary = "根据id更新", description = "根据id更新一条数据")
    @EyPostMapping(value = "/updateById.do")
    ResponseDTO<Void> updateById(@RequestBody @Validated({UpdateByIdGroup.class}) AuthGroupUpdateByIdDTO dto);


    /**
     * query AuthGroup from http
     */
    @Operation(summary = "根据id查询", description = "根据id查询数据")
    @EyPostMapping(value = "/queryById.do")
    ResponseDTO<AuthGroupRepDTO> queryById(@RequestBody @Validated({QueryByIdGroup.class}) AuthGroupQueryByIdDTO dto);


    /**
     * queryList AuthGroup from http
     */
    @Operation(summary = "查询列表", description = "查询数据列表")
    @EyPostMapping(value = "/queryList.do")
    ResponseDTO<List<AuthGroupRepDTO>> queryList(@RequestBody AuthGroupQueryDTO dto);

    /**
     * Page AuthGroup from http
     */
    @Operation(summary = "分页查询", description = "根据条件分页查询")
    @EyPostMapping(value = "/queryPage.do")
    ResponseDTO<SearchDTO<AuthGroupRepDTO>> queryPage(@RequestBody @Validated({QueryPageGroup.class}) SearchDTO<AuthGroupQueryPageDTO> searchDTO);

    /**
     * Count AuthGroup from http
     */
    @Operation(summary = "查询数量", description = "根据条件查询数量")
    @EyPostMapping(value = "/queryCount.do")
    ResponseDTO<Long> queryCount(@RequestBody AuthGroupQueryDTO authGroupDTO);


    /**
     * 通过权限组ID插入整个权限
     */
    @Operation(summary = "通过权限组ID插入、更新整个权限", description = "通过权限组ID插入整个权限，最外层的groupid必传，内层groupId可不传")
    @EyPostMapping(value = "/addAuthGroup.do")
    ResponseDTO<Void> addAuthGroup(@RequestBody AuthGroupAddAllDTO dto);

    /**
     * 通过权限组ID查询整个权限
     */
    @Operation(summary = "通过权限组ID查询整个权限", description = "通过权限组ID查询整个权限")
    @EyPostMapping(value = "/queryAuthGroup.do")
    ResponseDTO<AuthGroupQueryAllRepDTO> queryAuthGroup(@RequestBody AuthGroupQueryAllDTO dto);

    /**
     * 复制权限组
     */
    @Operation(summary = "复制权限组", description = "复制权限组")
    @EyPostMapping(value = "/copyAuthGroup.do")
    ResponseDTO<Void> copyAuthGroup(@RequestBody AuthGroupCopyDTO dto);

    /**
     * 查询group的字典树
     */
    @Operation(summary = "查询group的字典树", description = "查询group的字典树")
    @EyPostMapping(value = "/queryGroupTree.do")
    ResponseDTO<List<AuthGroupTreeRepDTO>> queryGroupTree(@RequestBody AuthGroupTreeDTO dto);
}
