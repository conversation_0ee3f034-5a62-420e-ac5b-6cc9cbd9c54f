package com.ey.tax.cloud.targets.entity.data;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ey.cn.tax.framework.mybatisplus.core.entity.BaseEntity;

import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-01-17 14:35:33
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("target_fiscal_calender")
public class FiscalCalender extends BaseEntity {
    /**
     * COLUMN:fiscal_year
     */
    private String fiscalYear;

    /**
     * COLUMN:week
     */
    private Integer week;

    /**
     * COLUMN:calendar_date
     */
    @TableField(condition = "DATE(%s) = #{%s}")
    private LocalDate calendarDate;

    /**
     * COLUMN:calendar_year
     */
    //模糊查询

    private Integer calendarYear;

    /**
     * COLUMN:calendar_quarter
     */
    private Integer calendarQuarter;

    /**
     * COLUMN:calendar_month
     */
    private Integer calendarMonth;

    /**
     * COLUMN:calendar_week
     */
    private Integer calendarWeek;

    /**
     * COLUMN:calendar_day
     */
    private Integer calendarDay;
}
