package com.ey.tax.cloud.targets.dto.pipeline;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-01-16 15:29:02
 * 
 */
@Data
@Schema(description = "根据条件删除实体")
public class SalesDeliveryApproveDeleteDTO {
    /**
     * COLUMN:pipeline_id
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String pipelineId;

    /**
     * COLUMN:sales_delivery_credit
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String salesDeliveryCredit;

    /**
     * COLUMN:sales_delivery_credit_ratio
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal salesDeliveryCreditRatio;

    /**
     * COLUMN:sales_delivery_credit_ratio_last
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal salesDeliveryCreditRatioLast;

    /**
     * COLUMN:sales_delivery_credit_type
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer salesDeliveryCreditType;

    /**
     * COLUMN:sales_delivery_credit_amount
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal salesDeliveryCreditAmount;

    /**
     * COLUMN:sales_delivery_credit_amount_last
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal salesDeliveryCreditAmountLast;

    /**
     * COLUMN:is_part
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer isPart;

    /**
     * COLUMN:fiscal_year
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String fiscalYear;
}
