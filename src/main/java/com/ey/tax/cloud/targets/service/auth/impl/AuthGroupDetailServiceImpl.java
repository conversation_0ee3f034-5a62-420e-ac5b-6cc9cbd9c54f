package com.ey.tax.cloud.targets.service.auth.impl;

import com.ey.cn.tax.framework.mybatisplus.core.query.EyQueryWrapper;
import com.ey.cn.tax.framework.mybatisplus.extension.service.AbstractService;
import com.ey.tax.cloud.targets.entity.auth.AuthGroupDetail;
import com.ey.tax.cloud.targets.entity.auth.AuthGroupRange;
import com.ey.tax.cloud.targets.mapper.auth.AuthGroupDetailMapper;
import com.ey.tax.cloud.targets.service.auth.AuthGroupDetailService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2023-12-22 10:32:04
 * 
 */
@Service
public class AuthGroupDetailServiceImpl extends AbstractService<AuthGroupDetailMapper, AuthGroupDetail> implements AuthGroupDetailService {

    @Override
    public List<AuthGroupDetail> queryByAuthGroupIds(List<String> ids) {
        EyQueryWrapper<AuthGroupDetail> queryWrapper = new EyQueryWrapper();
        queryWrapper.in("group_id", ids);
        return baseMapper.selectList(queryWrapper);
    }

}