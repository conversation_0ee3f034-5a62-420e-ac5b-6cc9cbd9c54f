package com.ey.tax.cloud.targets.controller.transfer;

import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.dto.SearchDTO;
import com.ey.cn.tax.framework.validator.AddGroup;
import com.ey.cn.tax.framework.validator.DeleteByIdGroup;
import com.ey.cn.tax.framework.validator.DeleteByIdListGroup;
import com.ey.cn.tax.framework.validator.QueryByIdGroup;
import com.ey.cn.tax.framework.validator.QueryByIdListGroup;
import com.ey.cn.tax.framework.validator.QueryPageGroup;
import com.ey.cn.tax.framework.validator.UpdateByIdGroup;
import com.ey.cn.tax.framework.web.annotation.EyPostMapping;
import com.ey.cn.tax.framework.web.base.BaseController;
import com.ey.tax.cloud.targets.dto.transfer.SyncSmuMercuryAddDTO;
import com.ey.tax.cloud.targets.dto.transfer.SyncSmuMercuryBatchUpdateDTO;
import com.ey.tax.cloud.targets.dto.transfer.SyncSmuMercuryDTO;
import com.ey.tax.cloud.targets.dto.transfer.SyncSmuMercuryDeleteByIdDTO;
import com.ey.tax.cloud.targets.dto.transfer.SyncSmuMercuryDeleteByIdListDTO;
import com.ey.tax.cloud.targets.dto.transfer.SyncSmuMercuryQueryByIdDTO;
import com.ey.tax.cloud.targets.dto.transfer.SyncSmuMercuryQueryByIdListDTO;
import com.ey.tax.cloud.targets.dto.transfer.SyncSmuMercuryQueryDTO;
import com.ey.tax.cloud.targets.dto.transfer.SyncSmuMercuryQueryPageDTO;
import com.ey.tax.cloud.targets.dto.transfer.SyncSmuMercuryRepDTO;
import com.ey.tax.cloud.targets.dto.transfer.SyncSmuMercuryUpdateByIdDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-08-29 10:44:33
 *
 */
@Tag(name="中台-smu")
public interface SyncSmuMercuryController extends BaseController<SyncSmuMercuryDTO> {
    /**
     * add SyncSmuMercury from http
     */
    @Operation(summary = "新增", description = "新增一条数据")
    @EyPostMapping(value = "/add.do")
    ResponseDTO<Void> add(@RequestBody @Validated({AddGroup.class}) SyncSmuMercuryAddDTO dto);

    /**
     * addList SyncSmuMercury from http
     */
    @Operation(summary = "批量新增", description = "批量新增数据")
    @EyPostMapping(value = "/addList.do")
    ResponseDTO<Void> addList(@RequestBody List<SyncSmuMercuryAddDTO> dtos);

    /**
     * delete SyncSmuMercury from http
     */
    @Operation(summary = "根据id删除", description = "根据id删除一条数据")
    @EyPostMapping(value = "/deleteById.do")
    ResponseDTO<Void> deleteById(@RequestBody @Validated({DeleteByIdGroup.class}) SyncSmuMercuryDeleteByIdDTO dto);

    /**
     * deleteList SyncSmuMercury from http
     */
    @Operation(summary = "根据id列表删除", description = "根据id列表批量删除数据")
    @EyPostMapping(value = "/deleteByIdList.do")
    ResponseDTO<Void> deleteByIdList(@RequestBody @Validated({DeleteByIdListGroup.class}) SyncSmuMercuryDeleteByIdListDTO dto);

    /**
     * update SyncSmuMercury from http
     */
    @Operation(summary = "根据id更新", description = "根据id更新一条数据")
    @EyPostMapping(value = "/updateById.do")
    ResponseDTO<Void> updateById(@RequestBody @Validated({UpdateByIdGroup.class}) SyncSmuMercuryUpdateByIdDTO dto);

    /**
     * updateBatch SyncSmuMercury from http
     */
    @Operation(summary = "批量更新", description = "批量更新数据")
    @EyPostMapping(value = "/updateBatch.do")
    ResponseDTO<Void> updateBatch(@RequestBody List<SyncSmuMercuryBatchUpdateDTO> dtos);

    /**
     * query SyncSmuMercury from http
     */
    @Operation(summary = "根据id查询", description = "根据id查询数据")
    @EyPostMapping(value = "/queryById.do")
    ResponseDTO<SyncSmuMercuryRepDTO> queryById(@RequestBody @Validated({QueryByIdGroup.class}) SyncSmuMercuryQueryByIdDTO dto);

    /**
     * queryByIdList SyncSmuMercury from http
     */
    @Operation(summary = "根据id列表查询", description = "根据id列表查询数据")
    @EyPostMapping(value = "/queryByIdList.do")
    ResponseDTO<List<SyncSmuMercuryRepDTO>> queryByIdList(@RequestBody @Validated({QueryByIdListGroup.class}) SyncSmuMercuryQueryByIdListDTO dto);

    /**
     * queryList SyncSmuMercury from http
     */
    @Operation(summary = "查询列表", description = "查询数据列表")
    @EyPostMapping(value = "/queryList.do")
    ResponseDTO<List<SyncSmuMercuryRepDTO>> queryList(@RequestBody SyncSmuMercuryQueryDTO dto);

    /**
     * Page SyncSmuMercury from http
     */
    @Operation(summary = "分页查询", description = "根据条件分页查询")
    @EyPostMapping(value = "/queryPage.do")
    ResponseDTO<SearchDTO<SyncSmuMercuryRepDTO>> queryPage(@RequestBody @Validated({QueryPageGroup.class}) SearchDTO<SyncSmuMercuryQueryPageDTO> searchDTO);

    /**
     * Count SyncSmuMercury from http
     */
    @Operation(summary = "查询数量", description = "根据条件查询数量")
    @EyPostMapping(value = "/queryCount.do")
    ResponseDTO<Long> queryCount(@RequestBody SyncSmuMercuryQueryDTO syncSmuMercuryDTO);

    /**
     * One SyncSmuMercury from http
     */
    @Operation(summary = "查询单个数据", description = "根据条件查询一条数据,如果查询到多条则返回异常.")
    @EyPostMapping(value = "/queryOne.do")
    ResponseDTO<SyncSmuMercuryRepDTO> queryOne(@RequestBody SyncSmuMercuryQueryDTO syncSmuMercuryDTO);

    /**
     * exist SyncSmuMercury from http
     */
    @Operation(summary = "查询是否存在", description = "根据条件查询是否存在")
    @EyPostMapping(value = "/exist.do")
    ResponseDTO<Boolean> exist(@RequestBody SyncSmuMercuryQueryDTO syncSmuMercuryDTO);
}
