package com.ey.tax.cloud.targets.dto.auth;

import com.ey.cn.tax.framework.validator.BatchUpdateGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2023-12-22 10:32:03
 * 
 */
@Data
@Schema(description = "根据id批量更新实体")
public class AuthGroupBatchUpdateDTO {
    
    /**
     * 数据id
     */
    @Schema(description = "数据id")
    @NotBlank(message = "{AuthGroupDTO.id.NotBlank}", groups = {BatchUpdateGroup.class})
    private String id;

    /**
     * 菜单/功能code COLUMN:menu_code
     */
    @Schema(description = "菜单/功能code", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String menuCode;

    /**
     * 用户组编码 COLUMN:group_code
     */
    @Schema(description = "用户组编码", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String groupCode;

    /**
     * 用户组名称 COLUMN:group_name
     */
    @Schema(description = "用户组名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String groupName;

    /**
     * 财年 COLUMN:fiscal_year
     */
    @Schema(description = "财年", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String fiscalYear;

    /**
     * 权限类型 COLUMN:auth_type
     */
    @Schema(description = "权限类型", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String authType;

    /**
     * 授权类型 COLUMN:confer_type
     */
    @Schema(description = "授权类型", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String conferType;

    /**
     * 伙伴角色 COLUMN:partner_role
     */
    @Schema(description = "伙伴角色", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String partnerRole;
}