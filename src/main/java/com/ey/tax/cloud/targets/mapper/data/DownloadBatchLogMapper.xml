<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ey.tax.cloud.targets.mapper.data.DownloadBatchLogMapper" >
  
  <resultMap id="EyTaxResultMap" type="com.ey.tax.cloud.targets.entity.data.DownloadBatchLog" >
    <id column="id" property="id" jdbcType="VARCHAR" />
    <result column="create_uid" property="createUid" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_uid" property="updateUid" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="is_del" property="isDel" jdbcType="SMALLINT" />
    <result column="auto_test" property="autoTest" jdbcType="SMALLINT" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="type_name" property="typeName" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="VARCHAR" />
    <result column="start_time" property="startTime" jdbcType="TIMESTAMP" />
    <result column="end_time" property="endTime" jdbcType="TIMESTAMP" />
    <result column="fiscal_year" property="fiscalYear" jdbcType="VARCHAR" />
    <result column="reason" property="reason" jdbcType="VARCHAR" />
    <result column="file_id" property="fileId" jdbcType="VARCHAR" />
  </resultMap>
  
</mapper>