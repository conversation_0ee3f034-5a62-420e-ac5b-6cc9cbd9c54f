package com.ey.tax.cloud.targets.dto.resource;

import com.ey.cn.tax.framework.dto.BaseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-01-17 14:35:33
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "默认实体")
public class PartnerRoleDTO extends BaseDTO {
    /**
     * COLUMN:ssl
     */
    @Schema(description = "null")
    private String ssl;

    /**
     * COLUMN:user_id
     */
    @Schema(description = "null")
    private String userId;

    /**
     * COLUMN:fiscal_year
     */
    @Schema(description = "null")
    private String fiscalYear;

    /**
     * COLUMN:name
     */
    @Schema(description = "null")
    private String name;

    /**
     * COLUMN:gpn
     */
    @Schema(description = "null")
    private String gpn;

    /**
     * COLUMN:performance_year
     */
    @Schema(description = "null")
    private Integer performanceYear;

    /**
     * COLUMN:group_name
     */
    @Schema(description = "null")
    private String groupName;

    /**
     * COLUMN:role_name
     */
    @Schema(description = "null")
    private String roleName;

    /**
     * COLUMN:role_coefficient
     */
    @Schema(description = "null")
    private BigDecimal roleCoefficient;

    /**
     * COLUMN:role_target
     */
    @Schema(description = "null")
    private BigDecimal roleTarget;

    /**
     * COLUMN:role_actual
     */
    @Schema(description = "null")
    private BigDecimal roleActual;

    /**
     * COLUMN:completion
     */
    @Schema(description = "null")
    private BigDecimal completion;

    /**
     * COLUMN:score
     */
    @Schema(description = "null")
    private BigDecimal score;
}
