<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ey.tax.cloud.targets.mapper.auth.AuthGroupRangeMapper" >

  <resultMap id="EyTaxResultMap" type="com.ey.tax.cloud.targets.entity.auth.AuthGroupRange" >
    <id column="id" property="id" jdbcType="VARCHAR" />
    <result column="create_uid" property="createUid" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_uid" property="updateUid" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="is_del" property="isDel" jdbcType="SMALLINT" />
    <result column="auto_test" property="autoTest" jdbcType="SMALLINT" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="range_type" property="rangeType" jdbcType="VARCHAR" />
    <result column="range_value" property="rangeValue" jdbcType="VARCHAR" />
    <result column="group_id" property="groupId" jdbcType="VARCHAR" />
  </resultMap>

</mapper>
