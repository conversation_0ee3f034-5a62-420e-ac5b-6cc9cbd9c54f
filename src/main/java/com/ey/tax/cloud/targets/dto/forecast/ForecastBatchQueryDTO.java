package com.ey.tax.cloud.targets.dto.forecast;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-01-17 14:35:37
 * 
 */
@Data
@Schema(description = "根据条件查询实体")
public class ForecastBatchQueryDTO {
    /**
     * COLUMN:user_id
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String userId;

    /**
     * COLUMN:status
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer status;

    /**
     * COLUMN:log_text
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String logText;
}