package com.ey.tax.cloud.targets.dto.kpi;

import com.ey.cn.tax.framework.validator.BatchUpdateGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-03-14 09:47:48
 * 
 */
@Data
@Schema(description = "根据id批量更新实体")
public class KpiSupplementaryBatchUpdateDTO {
    
    /**
     * 数据id
     */
    @Schema(description = "数据id")
    @NotBlank(message = "{KpiSupplementaryDTO.id.NotBlank}", groups = {BatchUpdateGroup.class})
    private String id;

    /**
     * 用户id COLUMN:user_id
     */
    @Schema(description = "用户id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String userId;

    /**
     * 财年 COLUMN:fiscal_year
     */
    @Schema(description = "财年", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String fiscalYear;

    /**
     * 一级指标名称 COLUMN:first_level_name
     */
    @Schema(description = "一级指标名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String firstLevelName;

    /**
     * 二级指标名称 COLUMN:second_level_name
     */
    @Schema(description = "二级指标名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String secondLevelName;

    /**
     * 三级指标名称 COLUMN:third_level_name
     */
    @Schema(description = "三级指标名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String thirdLevelName;

    /**
     * 描述 COLUMN:description
     */
    @Schema(description = "描述", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String description;

    /**
     * 排序 COLUMN:order_num
     */
    @Schema(description = "排序", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer orderNum;
}