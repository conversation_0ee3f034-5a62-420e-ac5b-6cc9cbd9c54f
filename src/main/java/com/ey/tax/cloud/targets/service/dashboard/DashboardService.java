package com.ey.tax.cloud.targets.service.dashboard;

import com.ey.tax.cloud.targets.entity.auth.AuthGroupDetail;
import com.ey.tax.cloud.targets.entity.dashboard.Dashboard;
import com.ey.tax.cloud.targets.entity.user.UserAttribute;

import java.util.List;

public interface DashboardService {

    Object queryData(Dashboard dashboard, String authType);

    String buildAuthSql(boolean isSelf, List<List<AuthGroupDetail>> authGroups, UserAttribute userAttribute,
                        String ssl1Name, String ssl2Name, String ssl3Name,
                        String cityName, String regionName, String levelName,
                        String groupName, String levelGroupName, String clientHq,
                        String clientType, String sector, String clientChannel,
                        String userId);

}
