package com.ey.tax.cloud.targets.dto.pipeline;

import com.ey.cn.tax.framework.dto.BaseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-01-23 16:33:32
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "默认实体")
public class PipelineDTO extends BaseDTO {
    /**
     * 客户名称 COLUMN:client_name
     */
    @Schema(description = "客户名称")
    private String clientName;

    /**
     * 客户编码 COLUMN:client_code
     */
    @Schema(description = "客户编码")
    private String clientCode;

    /**
     * 行业部门 COLUMN:industry_sector
     */
    @Schema(description = "行业部门")
    private String industrySector;

    /**
     * 子行业部门 COLUMN:industry_sub_sector
     */
    @Schema(description = "子行业部门")
    private String industrySubSector;

    /**
     * 公司类型 COLUMN:company_type
     */
    @Schema(description = "公司类型")
    private Object companyType;

    /**
     * 上市信息 COLUMN:stock_exchange
     */
    @Schema(description = "上市信息")
    private Object stockExchange;

    /**
     * 证券交易委员会信息 COLUMN:is_sec
     */
    @Schema(description = "证券交易委员会信息")
    private Integer isSec;

    /**
     * 总部所在地 COLUMN:head_office_location
     */
    @Schema(description = "总部所在地")
    private String headOfficeLocation;

    /**
     * 客户渠道 COLUMN:client_channel
     */
    @Schema(description = "客户渠道")
    private String clientChannel;

    /**
     * 客户分类 COLUMN:customer_classification
     */
    @Schema(description = "客户分类")
    private String customerClassification;

    /**
     * pipeline编码 COLUMN:pipeline_code
     */
    @Schema(description = "pipeline编码")
    private String pipelineCode;

    /**
     * 机会名称 COLUMN:pipeline_name
     */
    @Schema(description = "机会名称")
    private String pipelineName;

    /**
     * 本地服务代码 COLUMN:local_service_code
     */
    @Schema(description = "本地服务代码")
    private String localServiceCode;

    /**
     * 产品id COLUMN:product_id
     */
    @Schema(description = "产品id")
    private String productId;

    /**
     * 预计赢率 COLUMN:win_rate
     */
    @Schema(description = "预计赢率")
    private Integer winRate;

    /**
     * 预计赢得日期 COLUMN:anticipated_win_date
     */
    @Schema(description = "预计赢得日期")
    private LocalDateTime anticipatedWinDate;

    /**
     * 是否为竞争性投标 COLUMN:lead_source
     */
    @Schema(description = "是否为竞争性投标")
    private String leadSource;

    /**
     * 原因 COLUMN:won_reason
     */
    @Schema(description = "原因")
    private String wonReason;

    /**
     * 备注 COLUMN:description
     */
    @Schema(description = "备注")
    private String description;

    /**
     * eic单人 COLUMN:eic_own_effort
     */
    @Schema(description = "eic单人")
    private Integer eicOwnEffort;

    /**
     * 描述 COLUMN:efforts_description
     */
    @Schema(description = "描述")
    private String effortsDescription;

    /**
     * 结果 COLUMN:status
     */
    @Schema(description = "结果")
    private String status;

    /**
     * 主要ey办公室 COLUMN:psm_prime_office
     */
    @Schema(description = "主要ey办公室")
    private String psmPrimeOffice;

    /**
     * 预计开始日期 COLUMN:kickoff_week
     */
    @Schema(description = "预计开始日期")
    private LocalDateTime kickoffWeek;

    /**
     * 预计关闭日期 COLUMN:closing_week
     */
    @Schema(description = "预计关闭日期")
    private LocalDateTime closingWeek;

    /**
     * 交付风险 COLUMN:delivery_risk
     */
    @Schema(description = "交付风险")
    private String deliveryRisk;

    /**
     * 领导gpn COLUMN:pursuit_leader_id
     */
    @Schema(description = "领导gpn")
    private String pursuitLeaderId;

    /**
     * 参与合伙人gpn COLUMN:oppr_partner_id
     */
    @Schema(description = "参与合伙人gpn")
    private String opprPartnerId;

    /**
     * 预期的团队经理gpn COLUMN:eng_manager_id
     */
    @Schema(description = "预期的团队经理gpn")
    private String engManagerId;

    /**
     * 金额（价值cny） COLUMN:amount
     */
    @Schema(description = "金额（价值cny）")
    private BigDecimal amount;

    @Schema(description = "金额（本位币）")
    private BigDecimal amountCurrency;;

    /**
     * 金额（价值usd） COLUMN:amount_usd
     */
    @Schema(description = "金额（价值usd）")
    private BigDecimal amountUsd;

    /**
     * 货币 COLUMN:currency
     */
    @Schema(description = "货币")
    private String currency;

    /**
     * 是否包括vat COLUMN:is_inclusive_vat
     */
    @Schema(description = "是否包括vat")
    private Integer isInclusiveVat;

    /**
     * pace状态 COLUMN:pace_status
     */
    @Schema(description = "pace状态")
    private String paceStatus;

    /**
     * 位置 COLUMN:location_code
     */
    @Schema(description = "位置")
    private String locationCode;

    /**
     * 事件状态 COLUMN:event
     */
    @Schema(description = "事件状态")
    private String event;

    /**
     * 子代码标识 COLUMN:is_subcode
     */
    @Schema(description = "子代码标识")
    private String isSubcode;

    /**
     * 合同 COLUMN:contract
     */
    @Schema(description = "合同")
    private String contract;

    /**
     * 是否关闭 COLUMN:closed
     */
    @Schema(description = "是否关闭")
    private Integer closed;

    /**
     * 使用承包商 COLUMN:contractor_used
     */
    @Schema(description = "使用承包商")
    private String contractorUsed;

    /**
     * 外部占比 COLUMN:outsource
     */
    @Schema(description = "外部占比")
    private BigDecimal outsource;

    /**
     * 审批状态 COLUMN:confirm_status
     */
    @Schema(description = "审批状态")
    private String confirmStatus;

    /**
     * 创建财年 COLUMN:pursuit_fy
     */
    @Schema(description = "创建财年")
    private String pursuitFy;

    /**
     * 赢单财年 COLUMN:won_fy
     */
    @Schema(description = "赢单财年")
    private String wonFy;

    /**
     * efforts状态 COLUMN:efforts_approve
     */
    @Schema(description = "efforts状态")
    private String effortsApprove;



    /**
     * 其他event、event选other时填写 COLUMN:other_event
     */
    @Schema(description = "其他event、event选other时填写")
    private String otherEvent;

    /**
     * COLUMN:crm
     */
    @Schema(description = "null")
    private String crm;

    /**
     * tiger:stock_exchange、is_sec 生成 COLUMN:listed
     */
    @Schema(description = "tiger:stock_exchange、is_sec 生成")
    private String listed;

    /**
     * 地区 COLUMN:location
     */
    @Schema(description = "地区")
    private String location;



    /**
     * 项目类型 COLUMN:project_type
     */
    @Schema(description = "项目类型")
    private String projectType;

    /**
     * 项目状态 COLUMN:engagemant_status
     */
    @Schema(description = "项目状态")
    private String engagemantStatus;

    /**
     * 遇到的争议类型 COLUMN:category_of_the_disputes
     */
    @Schema(description = "遇到的争议类型")
    private String categoryOfTheDisputes;

    /**
     * 具体的争议情况 COLUMN:description_of_the_disputes
     */
    @Schema(description = "具体的争议情况")
    private String descriptionOfTheDisputes;

    /**
     * 税务争议团队（老师）提供的协助 COLUMN:the_advisor_s_contuibution_to_this_case
     */
    @Schema(description = "税务争议团队（老师）提供的协助")
    private String theAdvisorSContuibutionToThisCase;

    /**
     * 税务机关资源_省/市 COLUMN:resources_province_city
     */
    @Schema(description = "税务机关资源_省/市")
    private String resourcesProvinceCity;

    /**
     * 税务机关资源_主管税务机关 COLUMN:resources_tax_authority
     */
    @Schema(description = "税务机关资源_主管税务机关")
    private String resourcesTaxAuthority;

    /**
     * 项目code COLUMN:eng_code
     */
    @Schema(description = "项目code")
    private String engCode;

    /**
     * code开放日期 COLUMN:first_confirm_date
     */
    @Schema(description = "code开放日期")
    private LocalDateTime firstConfirmDate;


    @Schema(description = "投标类型")
    private String bidding;

    /**
     * 是否结转
     */
    @Schema(description = "是否结转")
    private String recurring;
    @Schema(description = "是否结转")
    private String updateFy;

    @Schema(description = "备注")
    private String remark;

    private Integer isSame;
    //is_same_kpi
    private Integer isSameKpi;

    //is_blank
    private Integer isBlank;

    private String accountId;

}
