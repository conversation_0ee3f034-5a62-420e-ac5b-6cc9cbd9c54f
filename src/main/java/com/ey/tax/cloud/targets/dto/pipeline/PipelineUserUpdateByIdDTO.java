package com.ey.tax.cloud.targets.dto.pipeline;

import com.ey.cn.tax.framework.validator.UpdateByIdGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2023-12-25 11:19:57
 * 
 */
@Data
@Schema(description = "根据id批量更新实体")
public class PipelineUserUpdateByIdDTO {
    
    /**
     * 数据id
     */
    @Schema(description = "数据id")
    @NotBlank(message = "{PipelineUserDTO.id.NotBlank}", groups = {UpdateByIdGroup.class})
    private String id;

    /**
     * pipelineID COLUMN:pipeline_id
     */
    @Schema(description = "pipelineID", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String pipelineId;

    /**
     * 用户ID COLUMN:user_id
     */
    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String userId;
}