package com.ey.tax.cloud.targets.mapper.business;

import com.ey.cn.tax.framework.mybatisplus.core.mapper.IEyBaseMapper;
import com.ey.tax.cloud.targets.entity.business.MyBusinessScope;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.ey.cn.tax.framework.mybatisplus.core.mapper.IEyBaseMapper;
import com.ey.tax.cloud.targets.entity.auth.AuthGroupDetail;
import com.ey.tax.cloud.targets.entity.pipeline.Pipeline;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-07-25 18:29:35
 *
 */
public interface MyBusinessScopeMapper extends IEyBaseMapper<MyBusinessScope> {

    default <P extends IPage<MyBusinessScope>> P selectPageForVK(P page, @Param("ew") Wrapper<MyBusinessScope> queryWrapper
            ) {
        List<MyBusinessScope> result = this.selectListForVK(page, queryWrapper);
        page.setRecords(result);
        return page;
    }

    List<MyBusinessScope> selectListForVK(IPage<MyBusinessScope> page, @Param("ew") Wrapper<MyBusinessScope> queryWrapper
           );


    List<Map<String,Object>> findBusinessUnionAll(MyBusinessScope queryParams);
}
