package com.ey.tax.cloud.targets.controller.resource.impl;

import com.ey.cn.tax.framework.context.EyCommonContextHolder;
import com.ey.cn.tax.framework.context.EyUserContextHolder;
import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.dto.SearchDTO;
import com.ey.cn.tax.framework.entity.Search;
import com.ey.cn.tax.framework.utils.ConvertUtils;
import com.ey.cn.tax.framework.utils.EmptyUtils;
import com.ey.cn.tax.framework.web.annotation.EyRestController;
import com.ey.cn.tax.framework.web.base.AbstractController;
import com.ey.tax.cloud.targets.constant.CommonConstants;
import com.ey.tax.cloud.targets.constant.TargetAuthConstants;
import com.ey.tax.cloud.targets.controller.resource.MemoRevenueController;
import com.ey.tax.cloud.targets.dto.resource.MemoRevenueAddDTO;
import com.ey.tax.cloud.targets.dto.resource.MemoRevenueBatchUpdateDTO;
import com.ey.tax.cloud.targets.dto.resource.MemoRevenueDTO;
import com.ey.tax.cloud.targets.dto.resource.MemoRevenueDeleteByIdDTO;
import com.ey.tax.cloud.targets.dto.resource.MemoRevenueDeleteByIdListDTO;
import com.ey.tax.cloud.targets.dto.resource.MemoRevenueQueryByIdDTO;
import com.ey.tax.cloud.targets.dto.resource.MemoRevenueQueryByIdListDTO;
import com.ey.tax.cloud.targets.dto.resource.MemoRevenueQueryDTO;
import com.ey.tax.cloud.targets.dto.resource.MemoRevenueQueryPageDTO;
import com.ey.tax.cloud.targets.dto.resource.MemoRevenueRepDTO;
import com.ey.tax.cloud.targets.dto.resource.MemoRevenueUpdateByIdDTO;
import com.ey.tax.cloud.targets.entity.resource.MemoRevenue;
import com.ey.tax.cloud.targets.entity.resource.Performance;
import com.ey.tax.cloud.targets.entity.user.UserAttribute;
import com.ey.tax.cloud.targets.service.data.DictionaryService;
import com.ey.tax.cloud.targets.service.resource.MemoRevenueService;
import com.ey.tax.cloud.targets.service.user.UserAttributeService;
import com.ey.tax.cloud.targets.service.user.UserGroupService;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-01-17 14:35:34
 *
 */
@EyRestController(path ="/v1/memoRevenue")
public class MemoRevenueControllerImpl extends AbstractController<MemoRevenueService, MemoRevenueDTO, MemoRevenue> implements MemoRevenueController {

    @Autowired
    private UserAttributeService userAttributeService;
    @Autowired
    private UserGroupService userGroupService;
    @Autowired
    private DictionaryService dictionaryService;

    /**
     * add MemoRevenue from http
     */
    @Override
    public ResponseDTO<Void> add(MemoRevenueAddDTO dto) {
        MemoRevenue entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        service.save(entity);
        return ResponseDTO.success();
    }

    /**
     * addList MemoRevenue from http
     */
    @Override
    public ResponseDTO<Void> addList(List<MemoRevenueAddDTO> dtos) {
        List<MemoRevenue> entitys = ConvertUtils.convertDTOList2EntityList(dtos, entityClass);
        service.save(entitys);
        return ResponseDTO.success();
    }

    /**
     * delete MemoRevenue from http
     */
    @Override
    public ResponseDTO<Void> deleteById(MemoRevenueDeleteByIdDTO dto) {
        service.deleteById(dto.getId());
        return ResponseDTO.success();
    }

    /**
     * deleteList MemoRevenue from http
     */
    @Override
    public ResponseDTO<Void> deleteByIdList(MemoRevenueDeleteByIdListDTO dto) {
        getService().deleteByIds(dto.getIds());
        return ResponseDTO.success();
    }

    /**
     * update MemoRevenue from http
     */
    @Override
    public ResponseDTO<Void> updateById(MemoRevenueUpdateByIdDTO dto) {
        MemoRevenue entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        getService().update(entity);
        return ResponseDTO.success();
    }

    /**
     * updateBatch MemoRevenue from http
     */
    @Override
    public ResponseDTO<Void> updateBatch(List<MemoRevenueBatchUpdateDTO> dtos) {
        List<MemoRevenue> entities = ConvertUtils.convertDTOList2EntityList(dtos, entityClass);
        getService().updateBatchByIds(entities);
        return ResponseDTO.success();
    }

    /**
     * query MemoRevenue from http
     */
    @Override
    public ResponseDTO<MemoRevenueRepDTO> queryById(MemoRevenueQueryByIdDTO dto) {
        MemoRevenue entity = getService().queryById(dto.getId());
        MemoRevenueRepDTO rDTO = ConvertUtils.convertEntity2DTO(entity, MemoRevenueRepDTO.class);
        return ResponseDTO.success(rDTO);
    }

    /**
     * queryByIdList MemoRevenue from http
     */
    @Override
    public ResponseDTO<List<MemoRevenueRepDTO>> queryByIdList(MemoRevenueQueryByIdListDTO dto) {
        List<MemoRevenue> entities = getService().queryByIds(dto.getIds());
        return ResponseDTO.success(ConvertUtils.convertEntityList2DTOList(entities, MemoRevenueRepDTO.class));
    }

    /**
     * queryList MemoRevenue from http
     */
    @Override
    public ResponseDTO<List<MemoRevenueRepDTO>> queryList(MemoRevenueQueryDTO dto) {
        MemoRevenue entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        List<MemoRevenue> entities = getService().queryByPara(entity);
        return ResponseDTO.success(ConvertUtils.convertEntityList2DTOList(entities, MemoRevenueRepDTO.class));
    }

    /**
     * Page MemoRevenue from http
     */
    @Override
    public ResponseDTO<SearchDTO<MemoRevenueRepDTO>> queryPage(SearchDTO<MemoRevenueQueryPageDTO> searchDTO) {
        try {
            if(EmptyUtils.isNotEmpty(searchDTO.getQueryParams().getSearchType())){
                EyCommonContextHolder.put(TargetAuthConstants.KEY_TARGET_AUTH,searchDTO.getQueryParams().getSearchType());
            }

        Search<MemoRevenue> search = ConvertUtils.convertSearchDTO2Search(searchDTO, entityClass);
        if(EmptyUtils.isNotEmpty(searchDTO.getQueryParams().getFiscalYear())){
            EyCommonContextHolder.put(TargetAuthConstants.KEY_YEAR,search.getQueryParams().getFiscalYear());
        }
        //查询关联用户
        if(EmptyUtils.isNotEmpty(searchDTO.getQueryParams().getSubServiceLineList())){
            UserAttribute userAttributes = new UserAttribute();
            userAttributes.setFiscalYear(String.valueOf(searchDTO.getQueryParams().getFiscalYear()));
            if(EmptyUtils.isNotEmpty(searchDTO.getQueryParams().getSubServiceLineList())){
                userAttributes.setSsl2s(searchDTO.getQueryParams().getSubServiceLineList());
            }
            List<UserAttribute> userAttributeList = userAttributeService.queryAll(userAttributes);
            List<String> userIds = userAttributeList.stream().map(UserAttribute::getUserId).collect(Collectors.toList());
            search.getQueryParams().setUserIdList(userIds);
        }
        String usrId = EyUserContextHolder.get().getAuthUserId();
        UserAttribute user = new UserAttribute();
        user.setUserId(usrId);
        user.setFiscalYear(search.getQueryParams().getFiscalYear());
        user = userAttributeService.queryOneByPara(user);
        if(CommonConstants.MEMO_BOSS_NAME_LIST.contains(user.getGpn())){
            search.getQueryParams().setProducts("SOE");
            getService().queryPageByPara(search);
        }else {
            getService().queryPageByParaAuth(search);
        }
        SearchDTO<MemoRevenueRepDTO> rSearchDTO = ConvertUtils.convertSearch2SearchDTO(search, MemoRevenueRepDTO.class);
        //填充name
        if(EmptyUtils.isNotEmpty(rSearchDTO.getRecords())){
            UserAttribute qUserAttribute = new UserAttribute();
            List<UserAttribute> allUserAttribute = userAttributeService.queryAll(qUserAttribute);
            Map<String, String> allUserAttributeMap = new HashMap<>();
            for(UserAttribute userAttribute:allUserAttribute){
                allUserAttributeMap.put(userAttribute.getUserId(), userAttribute.getUserName());
            }
            for(MemoRevenueRepDTO memoRevenueRepDTO:rSearchDTO.getRecords()){
                memoRevenueRepDTO.setTalName(allUserAttributeMap.get(memoRevenueRepDTO.getTalId()));
                memoRevenueRepDTO.setEmName(allUserAttributeMap.get(memoRevenueRepDTO.getEmId()));
                memoRevenueRepDTO.setEpName(allUserAttributeMap.get(memoRevenueRepDTO.getEpId()));
            }


        }
            return ResponseDTO.success(rSearchDTO);
        } catch (Exception e) {
          logger.error("error",e);
            SearchDTO<MemoRevenueRepDTO> rSearchDTO = new SearchDTO<>();
            return ResponseDTO.success(rSearchDTO);
        }

    }

    /**
     * Count MemoRevenue from http
     */
    @Override
    public ResponseDTO<Long> queryCount(MemoRevenueQueryDTO dto) {
        MemoRevenue entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        Long count = getService().queryCountByPara(entity);
        return ResponseDTO.success(count);
    }

    /**
     * One MemoRevenue from http
     */
    @Override
    public ResponseDTO<MemoRevenueRepDTO> queryOne(MemoRevenueQueryDTO dto) {
        MemoRevenue qEntity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        MemoRevenue rEntity = getService().queryOneByPara(qEntity);
        MemoRevenueRepDTO rDTO = ConvertUtils.convertEntity2DTO(rEntity, MemoRevenueRepDTO.class);
        return ResponseDTO.success(rDTO);
    }

    /**
     * exist MemoRevenue from http
     */
    @Override
    public ResponseDTO<Boolean> exist(MemoRevenueQueryDTO dto) {
        boolean resullt = getService().existByPara(ConvertUtils.convertDTO2Entity(dto, entityClass));
        return ResponseDTO.success(resullt);
    }
}
