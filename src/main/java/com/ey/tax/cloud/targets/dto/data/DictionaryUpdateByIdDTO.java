package com.ey.tax.cloud.targets.dto.data;

import com.ey.cn.tax.framework.validator.UpdateByIdGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-01-26 15:17:22
 *
 */
@Data
@Schema(description = "根据id批量更新实体")
public class DictionaryUpdateByIdDTO {

    /**
     * 数据id
     */
    @Schema(description = "数据id")
    @NotBlank(message = "{DictionaryDTO.id.NotBlank}", groups = {UpdateByIdGroup.class})
    private String id;

    /**
     * 字典编码 COLUMN:dict_code
     */
    @Schema(description = "字典编码", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String dictCode;

    /**
     * 字典名称 COLUMN:dict_name
     */
    @Schema(description = "字典名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String dictName;

    /**
     * 字典类型(来自tiger,target自有) COLUMN:dict_type
     */
    @Schema(description = "字典类型(来自tiger,target自有)", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer dictType;

    /**
     * 对应tiger的code COLUMN:dict_tiger_code
     */
    @Schema(description = "对应tiger的code", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String dictTigerCode;

    /**
     * 状态 COLUMN:status
     */
    @Schema(description = "状态", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer status;

    /**
     * 备注 COLUMN:remark
     */
    @Schema(description = "备注")
    private String remark;

    @Schema(description = "标签")
    private String tag;
}
