package com.ey.tax.cloud.targets.controller.auth.impl;

import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.dto.SearchDTO;
import com.ey.cn.tax.framework.entity.Search;
import com.ey.cn.tax.framework.utils.ConvertUtils;
import com.ey.cn.tax.framework.web.annotation.EyRestController;
import com.ey.cn.tax.framework.web.base.AbstractController;
import com.ey.tax.cloud.targets.controller.auth.AuthGroupDetailController;
import com.ey.tax.cloud.targets.dto.auth.AuthGroupDetailAddDTO;
import com.ey.tax.cloud.targets.dto.auth.AuthGroupDetailBatchUpdateDTO;
import com.ey.tax.cloud.targets.dto.auth.AuthGroupDetailDTO;
import com.ey.tax.cloud.targets.dto.auth.AuthGroupDetailDeleteByIdDTO;
import com.ey.tax.cloud.targets.dto.auth.AuthGroupDetailDeleteByIdListDTO;
import com.ey.tax.cloud.targets.dto.auth.AuthGroupDetailQueryByIdDTO;
import com.ey.tax.cloud.targets.dto.auth.AuthGroupDetailQueryByIdListDTO;
import com.ey.tax.cloud.targets.dto.auth.AuthGroupDetailQueryDTO;
import com.ey.tax.cloud.targets.dto.auth.AuthGroupDetailQueryPageDTO;
import com.ey.tax.cloud.targets.dto.auth.AuthGroupDetailRepDTO;
import com.ey.tax.cloud.targets.dto.auth.AuthGroupDetailUpdateByIdDTO;
import com.ey.tax.cloud.targets.entity.auth.AuthGroupDetail;
import com.ey.tax.cloud.targets.service.auth.AuthGroupDetailService;
import java.util.List;
import java.util.Map;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2023-12-22 10:32:04
 * 
 */
@EyRestController(path ="/v1/authGroupDetail")
public class AuthGroupDetailControllerImpl extends AbstractController<AuthGroupDetailService, AuthGroupDetailDTO, AuthGroupDetail> implements AuthGroupDetailController {

    /**
     * add AuthGroupDetail from http
     */
    @Override
    public ResponseDTO<Void> add(AuthGroupDetailAddDTO dto) {
        AuthGroupDetail entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        service.save(entity);
        return ResponseDTO.success();
    }

    /**
     * addList AuthGroupDetail from http
     */
    @Override
    public ResponseDTO<Void> addList(List<AuthGroupDetailAddDTO> dtos) {
        List<AuthGroupDetail> entitys = ConvertUtils.convertDTOList2EntityList(dtos, entityClass);
        service.save(entitys);
        return ResponseDTO.success();
    }

    /**
     * delete AuthGroupDetail from http
     */
    @Override
    public ResponseDTO<Void> deleteById(AuthGroupDetailDeleteByIdDTO dto) {
        service.deleteById(dto.getId());
        return ResponseDTO.success();
    }

    /**
     * deleteList AuthGroupDetail from http
     */
    @Override
    public ResponseDTO<Void> deleteByIdList(AuthGroupDetailDeleteByIdListDTO dto) {
        getService().deleteByIds(dto.getIds());
        return ResponseDTO.success();
    }

    /**
     * update AuthGroupDetail from http
     */
    @Override
    public ResponseDTO<Void> updateById(AuthGroupDetailUpdateByIdDTO dto) {
        AuthGroupDetail entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        getService().update(entity);
        return ResponseDTO.success();
    }

    /**
     * updateBatch AuthGroupDetail from http
     */
    @Override
    public ResponseDTO<Void> updateBatch(List<AuthGroupDetailBatchUpdateDTO> dtos) {
        List<AuthGroupDetail> entities = ConvertUtils.convertDTOList2EntityList(dtos, entityClass);
        getService().updateBatchByIds(entities);
        return ResponseDTO.success();
    }

    /**
     * query AuthGroupDetail from http
     */
    @Override
    public ResponseDTO<AuthGroupDetailRepDTO> queryById(AuthGroupDetailQueryByIdDTO dto) {
        AuthGroupDetail entity = getService().queryById(dto.getId());
        AuthGroupDetailRepDTO rDTO = ConvertUtils.convertEntity2DTO(entity, AuthGroupDetailRepDTO.class);
        return ResponseDTO.success(rDTO);
    }

    /**
     * queryByIdList AuthGroupDetail from http
     */
    @Override
    public ResponseDTO<List<AuthGroupDetailRepDTO>> queryByIdList(AuthGroupDetailQueryByIdListDTO dto) {
        List<AuthGroupDetail> entities = getService().queryByIds(dto.getIds());
        return ResponseDTO.success(ConvertUtils.convertEntityList2DTOList(entities, AuthGroupDetailRepDTO.class));
    }

    /**
     * queryList AuthGroupDetail from http
     */
    @Override
    public ResponseDTO<List<AuthGroupDetailRepDTO>> queryList(AuthGroupDetailQueryDTO dto) {
        AuthGroupDetail entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        List<AuthGroupDetail> entities = getService().queryByPara(entity);
        return ResponseDTO.success(ConvertUtils.convertEntityList2DTOList(entities, AuthGroupDetailRepDTO.class));
    }

    /**
     * Page AuthGroupDetail from http
     */
    @Override
    public ResponseDTO<SearchDTO<AuthGroupDetailRepDTO>> queryPage(SearchDTO<AuthGroupDetailQueryPageDTO> searchDTO) {
        Search<AuthGroupDetail> search = ConvertUtils.convertSearchDTO2Search(searchDTO, entityClass);
        getService().queryPageByPara(search);
        return ResponseDTO.success(ConvertUtils.convertSearch2SearchDTO(search, AuthGroupDetailRepDTO.class));
    }

    /**
     * Count AuthGroupDetail from http
     */
    @Override
    public ResponseDTO<Long> queryCount(AuthGroupDetailQueryDTO dto) {
        AuthGroupDetail entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        Long count = getService().queryCountByPara(entity);
        return ResponseDTO.success(count);
    }

    /**
     * One AuthGroupDetail from http
     */
    @Override
    public ResponseDTO<AuthGroupDetailRepDTO> queryOne(AuthGroupDetailQueryDTO dto) {
        AuthGroupDetail qEntity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        AuthGroupDetail rEntity = getService().queryOneByPara(qEntity);
        AuthGroupDetailRepDTO rDTO = ConvertUtils.convertEntity2DTO(rEntity, AuthGroupDetailRepDTO.class);
        return ResponseDTO.success(rDTO);
    }

    /**
     * exist AuthGroupDetail from http
     */
    @Override
    public ResponseDTO<Boolean> exist(AuthGroupDetailQueryDTO dto) {
        boolean resullt = getService().existByPara(ConvertUtils.convertDTO2Entity(dto, entityClass));
        return ResponseDTO.success(resullt);
    }
}