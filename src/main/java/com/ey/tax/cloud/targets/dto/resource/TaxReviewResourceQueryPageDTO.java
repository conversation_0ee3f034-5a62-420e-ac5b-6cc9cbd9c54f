package com.ey.tax.cloud.targets.dto.resource;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-01-17 14:35:37
 * 
 */
@Data
@Schema(description = "根据条件分页查询实体")
public class TaxReviewResourceQueryPageDTO {
    /**
     * COLUMN:eng_code
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String engCode;

    /**
     * COLUMN:eng_client_name
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String engClientName;

    /**
     * COLUMN:person_gpn
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String personGpn;

    /**
     * COLUMN:person_name
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String personName;

    /**
     * COLUMN:user_id
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String userId;

    /**
     * COLUMN:person_sub_service_line
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String personSubServiceLine;

    /**
     * COLUMN:sum_of_net_fees_usd
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal sumOfNetFeesUsd;

    /**
     * COLUMN:sum_of_net_fees_cny
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal sumOfNetFeesCny;

    /**
     * COLUMN:fiscal_year
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String fiscalYear;
}