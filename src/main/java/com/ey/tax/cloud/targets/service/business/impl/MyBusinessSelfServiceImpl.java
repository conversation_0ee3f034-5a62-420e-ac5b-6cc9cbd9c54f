package com.ey.tax.cloud.targets.service.business.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ey.cn.tax.framework.entity.ICustomBaseEntity;
import com.ey.cn.tax.framework.entity.Search;
import com.ey.cn.tax.framework.mybatisplus.core.mapper.IEyBaseMapper;
import com.ey.cn.tax.framework.mybatisplus.core.query.EyQueryWrapper;
import com.ey.cn.tax.framework.mybatisplus.core.toolkit.EyWrappers;
import com.ey.cn.tax.framework.mybatisplus.extension.service.AbstractService;
import com.ey.cn.tax.framework.utils.EmptyUtils;
import com.ey.cn.tax.framework.utils.GroovyUtils;
import com.ey.cn.tax.framework.utils.ReflectUtils;
import com.ey.tax.cloud.targets.constant.CommonConstants;
import com.ey.tax.cloud.targets.entity.business.MyBusinessScope;
import com.ey.tax.cloud.targets.entity.business.MyBusinessSelf;
import com.ey.tax.cloud.targets.entity.pipeline.Pipeline;
import com.ey.tax.cloud.targets.entity.user.UserAttribute;
import com.ey.tax.cloud.targets.mapper.business.MyBusinessSelfMapper;
import com.ey.tax.cloud.targets.service.business.MyBusinessSelfService;
import com.ey.tax.cloud.targets.service.pipeline.PipelineService;
import com.ey.tax.cloud.targets.service.user.UserAttributeService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.nio.channels.Pipe;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-07-25 18:29:35
 *
 */
@Service
public class MyBusinessSelfServiceImpl extends AbstractService<MyBusinessSelfMapper, MyBusinessSelf> implements MyBusinessSelfService {


    @Autowired
    private UserAttributeService userAttributeService;
    @Autowired
    private PipelineService pipelineService;

    @Transactional(
            rollbackFor = {Throwable.class}
    )
    public void deleteByPara(MyBusinessSelf entity) {
        this.logger.debug("deleteByPara {}:{}", this.getEntitySimpleName(), entity);
        if (entity != null) {
            EyQueryWrapper<MyBusinessSelf> query = EyWrappers.query(entity);
            //pipelineCodeList
            if (entity.getPipelineCodeList() != null && !entity.getPipelineCodeList().isEmpty()) {
                query.in("pipeline_code", entity.getPipelineCodeList());
            }
            //pipelineIdList
            if (entity.getPipelineIdList() != null && !entity.getPipelineIdList().isEmpty()) {
                query.in("pipeline_id", entity.getPipelineIdList());
            }
            //fiscalYearList
            if (entity.getFiscalYearList() != null && !entity.getFiscalYearList().isEmpty()) {
                query.in("fiscal_year", entity.getFiscalYearList());
            }
            ((IEyBaseMapper)this.baseMapper).deleteWithFill((ICustomBaseEntity) ReflectUtils.instance(this.getEntityClass()), query);
        }
    }



    @Transactional(
            readOnly = true
    )
    public Search<MyBusinessScope> queryPageByParaScope(Search<MyBusinessScope> search) {
        this.logger.debug("queryPageByPara {}:{}", this.getEntitySimpleName(), search);
        MyBusinessScope queryParamsScope = (MyBusinessScope)search.getQueryParams();
        MyBusinessSelf queryParams = new MyBusinessSelf();
        BeanUtils.copyProperties(queryParamsScope, queryParams);
        EyQueryWrapper<MyBusinessSelf> queryWrapper = EyWrappers.query(queryParams);
        if (queryParams.getQueryExtension() == null) {
            queryWrapper.extension(search.getQueryExtension());
        }

        if (!queryWrapper.isOrdered()) {
            queryWrapper.orderByDesc("create_time");
        }
        //fiscalYearList
        if (queryParams.getFiscalYearList() != null && !queryParams.getFiscalYearList().isEmpty()) {
            queryWrapper.in("fiscal_year", queryParams.getFiscalYearList());
        }
        //clientName
        if (queryParams.getClientName() != null) {
            Pipeline pipeline = new Pipeline();
            pipeline.setClientName(queryParams.getClientName());
            List<Pipeline> pipelines = pipelineService.queryByPara(pipeline);
            if(EmptyUtils.isNotEmpty(pipelines)){
                List<String> ids = new ArrayList<>();
                pipelines.forEach(p -> ids.add(p.getId()));
                queryWrapper.in("pipeline_id", ids);
            }else {
                List<String> ids = new ArrayList<>();
                ids.add("0");
                queryWrapper.in("pipeline_id", ids);
            }
        }
        //查询当前用户
        UserAttribute userAttribute = userAttributeService.queryCurrentUser();
        IPage<MyBusinessSelf> iPage ;
        if(CommonConstants.BOSS_NAME_LIST.contains(userAttribute.getUserEmail())) {
            iPage=  baseMapper.selectPageForVK((Page) GroovyUtils.convertTo(Page.class, search.getPage(), true, new String[0]), queryWrapper );
        }else{
            iPage = ((IEyBaseMapper)this.baseMapper).selectPage((Page) GroovyUtils.convertTo(Page.class, search.getPage(), true, new String[0]), queryWrapper);

        }
        List<MyBusinessSelf> recordsSelf = iPage.getRecords();
        List<MyBusinessScope> records = new ArrayList<>();
        recordsSelf.forEach((record) -> {
            MyBusinessScope myBusinessScope = new MyBusinessScope();
            BeanUtils.copyProperties(record, myBusinessScope);
            records.add(myBusinessScope);
        });
        search.setEntities(records);
        if (CollectionUtils.isEmpty(records)) {
            search.getPage().setTotalCount(0L);
        } else {
            this.processUserName((Collection)records);
            search.getPage().setTotalCount(iPage.getTotal());
        }

        return search;
    }
}
