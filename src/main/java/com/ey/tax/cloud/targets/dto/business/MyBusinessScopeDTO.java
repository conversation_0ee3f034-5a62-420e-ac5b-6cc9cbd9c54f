package com.ey.tax.cloud.targets.dto.business;

import com.ey.cn.tax.framework.dto.BaseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-07-29 16:59:07
 *
 */
@Data
@Schema(description = "默认实体")
public class MyBusinessScopeDTO extends BaseDTO {
    /**
     * COLUMN:fiscal_year
     */
    @Schema(description = "null")
    private String fiscalYear;

    /**
     * COLUMN:fiscal_week
     */
    @Schema(description = "null")
    private String fiscalWeek;

    /**
     * pipeline名称 COLUMN:pipeline_name
     */
    @Schema(description = "pipeline名称")
    private String pipelineName;

    /**
     * pipelineCode COLUMN:pipeline_code
     */
    @Schema(description = "pipelineCode")
    private String pipelineCode;

    /**
     * pace 0:灰色 1:黄色 2:绿色 COLUMN:pace
     */
    @Schema(description = "pace 0:灰色 1:黄色 2:绿色")
    private Integer pace;

    /**
     * billing COLUMN:billing
     */
    @Schema(description = "billing")
    private Integer billing;

    /**
     * billing_total_budget COLUMN:billing_total_budget
     */
    @Schema(description = "billing_total_budget")
    private BigDecimal billingTotalBudget;

    /**
     * billing_billed COLUMN:billing_billed
     */
    @Schema(description = "billing_billed")
    private BigDecimal billingBilled;

    /**
     * billing_unbilled COLUMN:billing_unbilled
     */
    @Schema(description = "billing_unbilled")
    private BigDecimal billingUnbilled;

    /**
     * collection COLUMN:collection
     */
    @Schema(description = "collection")
    private Integer collection;

    /**
     * COLUMN:uncollected
     */
    @Schema(description = "null")
    private BigDecimal uncollected;

    /**
     * completed COLUMN:completed
     */
    @Schema(description = "completed")
    private Integer completed;

    /**
     * 用户在Pipeline上的分配比例 COLUMN:percentage
     */
    @Schema(description = "用户在Pipeline上的分配比例")
    private BigDecimal percentage;

    /**
     * pipeline的amount * 用户占比 COLUMN:allocated_value
     */
    @Schema(description = "pipeline的amount * 用户占比")
    private BigDecimal allocatedValue;

    /**
     * ter COLUMN:ter
     */
    @Schema(description = "ter")
    private BigDecimal ter;

    /**
     * ner COLUMN:ner
     */
    @Schema(description = "ner")
    private BigDecimal ner;

    /**
     * revenue_day COLUMN:revenue_day
     */
    @Schema(description = "revenue_day")
    private BigDecimal revenueDay;

    /**
     * wip COLUMN:wip
     */
    @Schema(description = "wip")
    private BigDecimal wip;

    /**
     * billing_amount COLUMN:billing_amount
     */
    @Schema(description = "billing_amount")
    private BigDecimal billingAmount;

    /**
     * Collection amount COLUMN:collection_amount
     */
    @Schema(description = "Collection amount")
    private BigDecimal collectionAmount;

    /**
     * ar COLUMN:ar
     */
    @Schema(description = "ar")
    private BigDecimal ar;

    /**
     * 关联用户id COLUMN:user_id
     */
    @Schema(description = "关联用户id")
    private String userId;

    /**
     * tag 分为etd/ytd 查询用 COLUMN:tag
     */
    @Schema(description = "tag 分为etd/ytd 查询用")
    private String tag;

    /**
     * COLUMN:level
     */
    @Schema(description = "null")
    private String level;

    /**
     * COLUMN:ssl3
     */
    @Schema(description = "null")
    private String ssl3;

    /**
     * COLUMN:city
     */
    @Schema(description = "null")
    private String city;

    /**
     * COLUMN:region
     */
    @Schema(description = "null")
    private String region;

    /**
     * COLUMN:ssl2
     */
    @Schema(description = "null")
    private String ssl2;

    /**
     * COLUMN:sl1
     */
    @Schema(description = "null")
    private String sl1;

    /**
     * COLUMN:level_group
     */
    @Schema(description = "null")
    private String levelGroup;

    /**
     * COLUMN:group_id
     */
    @Schema(description = "null")
    private String groupId;

    /**
     * margin COLUMN:margin
     */
    @Schema(description = "margin")
    private BigDecimal margin;

    /**
     * pipeline的id COLUMN:pipeline_id
     */
    @Schema(description = "pipeline的id")
    private String pipelineId;
}
