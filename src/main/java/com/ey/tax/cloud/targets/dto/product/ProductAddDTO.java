package com.ey.tax.cloud.targets.dto.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-01-11 14:46:33
 *
 */
@Data
@Schema(description = "新增实体")
public class ProductAddDTO {
    /**
     * 类型 COLUMN:type
     */
    @Schema(description = "类型 0为ssl，1为产品", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer type;

    /**
     * 产品名称 COLUMN:product_name
     */
    @Schema(description = "产品名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String productName;

    /**
     * ssl COLUMN:ssl
     */
    @Schema(description = "ssl 类型0时储存名称类型1时储存对应ssl的id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String ssl;

    /**
     * 是否热门产品 COLUMN:hot_product
     */
    @Schema(description = "是否热门产品 0 不是 1是", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Short hotProduct;

    /**
     * 排序 COLUMN:order_num
     */
    @Schema(description = "排序", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer orderNum;

    /**
     * 产品code COLUMN:product_code
     */
    @Schema(description = "产品code", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String productCode;

    /**
     * 状态 COLUMN:status
     */
    @Schema(description = "启用禁用", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer status;

    /**
     * 结转状态 COLUMN:IS_FORWARD
     */
    @Schema(description = "结转状态", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer isForward;

    /**
     * 默认分摊日期起 COLUMN:share_date_start
     */
    @Schema(description = "默认分摊日期起", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer shareDateStart;

    /**
     * 默认分摊日期止 COLUMN:share_date_end
     */
    @Schema(description = "默认分摊日期止", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer shareDateEnd;

    /**
     * 默认分摊日期止 COLUMN:share_date_end
     */
    @Schema(description = "默认分摊日期", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<LocalDate> shareDateRange;

    private String level0;
    private String level1;
    private String productId;
    private String fiscalYear;
}
