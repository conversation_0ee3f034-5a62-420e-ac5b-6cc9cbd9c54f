package com.ey.tax.cloud.targets.service.pipeline;

import com.ey.cn.tax.framework.entity.Search;
import com.ey.cn.tax.framework.service.BaseService;
import com.ey.tax.cloud.targets.entity.pipeline.Engagement;
import com.ey.tax.cloud.targets.entity.pipeline.Pipeline;

import java.util.List;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-05-20 14:23:44
 * 
 */
public interface EngagementService extends BaseService<Engagement> {

    void realDeleteByPipelineId(String pipelineId);

    void realDeleteByPipelineIds(List<String> pipelineIds);

    Search<Engagement> queryPageByParaForTransfer(Search<Engagement> search);

}