package com.ey.tax.cloud.targets.dto.auth;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2023-12-22 10:32:03
 *
 */
@Data
@Schema(description = "新增实体")
public class AuthGroupAddDTO {
    /**
     * 菜单/功能code COLUMN:menu_code
     */
    @Schema(description = "菜单/功能code", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String menuCode;

    /**
     * 用户组编码 COLUMN:group_code
     */
    @Schema(description = "用户组编码", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String groupCode;

    /**
     * 用户组名称 COLUMN:group_name
     */
    @Schema(description = "用户组名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String groupName;

    /**
     * 财年 COLUMN:fiscal_year
     */
    @Schema(description = "财年", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String fiscalYear;

    /**
     * 权限类型 COLUMN:auth_type
     */
    @Schema(description = "权限类型", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String authType;

    /**
     * 授权类型 COLUMN:confer_type
     */
    @Schema(description = "授权类型", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String conferType;

    /**
     * 伙伴角色 COLUMN:partner_role
     */
    @Schema(description = "伙伴角色", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String partnerRole;
    @Schema(description = "备注", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String remark;

    @Schema(description = "用户标签", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
     private String userTag;


}
