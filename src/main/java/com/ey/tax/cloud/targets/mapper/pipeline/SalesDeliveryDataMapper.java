package com.ey.tax.cloud.targets.mapper.pipeline;

import com.ey.cn.tax.framework.mybatisplus.core.mapper.IEyBaseMapper;
import com.ey.tax.cloud.targets.entity.pipeline.SalesDeliveryData;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-01-30 14:57:08
 *
 */
public interface SalesDeliveryDataMapper extends IEyBaseMapper<SalesDeliveryData> {

    /**
     * select pipeline_id from target_pipeline_sales_delivery_data where sales_delivery_credit in (#{salesDeliveryCreditList}) and won_fy = #{wonFy} and sales_delivery_credit_ratio>=50
     */
    @Select({
            "<script>",
            "SELECT pipeline_id",
            "FROM target_pipeline_sales_delivery_data",
            "WHERE 1=1",
            "AND sales_delivery_credit_ratio >= 50",
            "AND (won_fy = #{wonFy} OR pursuit_fy = #{wonFy})",
            "<if test='ssl3List != null and ssl3List.size() > 0'>",
            "AND ssl3 IN",
            "<foreach item='item' index='index' collection='ssl3List' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='locationList != null and locationList.size() > 0'>",
            "AND city IN",
            "<foreach item='item' index='index' collection='locationList' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='regionList != null and regionList.size() > 0'>",
            "AND region IN",
            "<foreach item='item' index='index' collection='regionList' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "</if>",
            "</script>"
    })
    List<String> selectPipelineIdBySalesDeliveryCreditList(
            @Param("wonFy") String wonFy,
            @Param("ssl3List") List<String> ssl3List,
            @Param("locationList") List<String> locationList,
            @Param("regionList") List<String> regionList
    );
    @Select({
            "<script>",
            "SELECT pipeline_id",
            "FROM target_pipeline_sales_delivery_data",
            "WHERE sales_delivery_credit IN",
            "<foreach item='item' index='index' collection='salesDeliveryCreditList' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "AND sales_delivery_credit_ratio >= 50 ",
            "</script>"
    })
    List<String> selectPipelineIdBySalesDeliveryCreditList(@Param("salesDeliveryCreditList") List<String> salesDeliveryCreditList);
}
