package com.ey.tax.cloud.targets.controller.data.impl;

import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.dto.SearchDTO;
import com.ey.cn.tax.framework.entity.Search;
import com.ey.cn.tax.framework.utils.ConvertUtils;
import com.ey.cn.tax.framework.utils.EmptyUtils;
import com.ey.cn.tax.framework.web.annotation.EyRestController;
import com.ey.cn.tax.framework.web.base.AbstractController;
import com.ey.tax.cloud.targets.controller.data.DownloadBatchLogController;
import com.ey.tax.cloud.targets.dto.data.*;
import com.ey.tax.cloud.targets.entity.data.DownloadBatchLog;
import com.ey.tax.cloud.targets.service.data.DictionaryService;
import com.ey.tax.cloud.targets.service.data.DownloadBatchLogService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-12-09 14:48:33
 *
 */
@EyRestController(path ="/v1/downloadBatchLog")
public class DownloadBatchLogControllerImpl extends AbstractController<DownloadBatchLogService, DownloadBatchLogDTO, DownloadBatchLog> implements DownloadBatchLogController {


    @Autowired
    private DictionaryService dictionaryService;

    /**
     * add DownloadBatchLog from http
     */
    @Override
    public ResponseDTO<Void> add(DownloadBatchLogAddDTO dto) {
        DownloadBatchLog entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        service.save(entity);
        return ResponseDTO.success();
    }

    /**
     * addList DownloadBatchLog from http
     */
    @Override
    public ResponseDTO<Void> addList(List<DownloadBatchLogAddDTO> dtos) {
        List<DownloadBatchLog> entitys = ConvertUtils.convertDTOList2EntityList(dtos, entityClass);
        service.save(entitys);
        return ResponseDTO.success();
    }

    /**
     * delete DownloadBatchLog from http
     */
    @Override
    public ResponseDTO<Void> deleteById(DownloadBatchLogDeleteByIdDTO dto) {
        service.deleteById(dto.getId());
        return ResponseDTO.success();
    }

    /**
     * deleteList DownloadBatchLog from http
     */
    @Override
    public ResponseDTO<Void> deleteByIdList(DownloadBatchLogDeleteByIdListDTO dto) {
        getService().deleteByIds(dto.getIds());
        return ResponseDTO.success();
    }

    /**
     * update DownloadBatchLog from http
     */
    @Override
    public ResponseDTO<Void> updateById(DownloadBatchLogUpdateByIdDTO dto) {
        DownloadBatchLog entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        getService().update(entity);
        return ResponseDTO.success();
    }

    /**
     * updateBatch DownloadBatchLog from http
     */
    @Override
    public ResponseDTO<Void> updateBatch(List<DownloadBatchLogBatchUpdateDTO> dtos) {
        List<DownloadBatchLog> entities = ConvertUtils.convertDTOList2EntityList(dtos, entityClass);
        getService().updateBatchByIds(entities);
        return ResponseDTO.success();
    }

    /**
     * query DownloadBatchLog from http
     */
    @Override
    public ResponseDTO<DownloadBatchLogRepDTO> queryById(DownloadBatchLogQueryByIdDTO dto) {
        DownloadBatchLog entity = getService().queryById(dto.getId());
        DownloadBatchLogRepDTO rDTO = ConvertUtils.convertEntity2DTO(entity, DownloadBatchLogRepDTO.class);
        return ResponseDTO.success(rDTO);
    }

    /**
     * queryByIdList DownloadBatchLog from http
     */
    @Override
    public ResponseDTO<List<DownloadBatchLogRepDTO>> queryByIdList(DownloadBatchLogQueryByIdListDTO dto) {
        List<DownloadBatchLog> entities = getService().queryByIds(dto.getIds());
        return ResponseDTO.success(ConvertUtils.convertEntityList2DTOList(entities, DownloadBatchLogRepDTO.class));
    }

    /**
     * queryList DownloadBatchLog from http
     */
    @Override
    public ResponseDTO<List<DownloadBatchLogRepDTO>> queryList(DownloadBatchLogQueryDTO dto) {
        DownloadBatchLog entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        List<DownloadBatchLog> entities = getService().queryByPara(entity);
        return ResponseDTO.success(ConvertUtils.convertEntityList2DTOList(entities, DownloadBatchLogRepDTO.class));
    }

    /**
     * Page DownloadBatchLog from http
     */
    @Override
    public ResponseDTO<SearchDTO<DownloadBatchLogRepDTO>> queryPage(SearchDTO<DownloadBatchLogQueryPageDTO> searchDTO) {
        Search<DownloadBatchLog> search = ConvertUtils.convertSearchDTO2Search(searchDTO, entityClass);
        getService().queryPageByPara(search);
        //字典 downloadStatut、downloadType
        SearchDTO<DownloadBatchLogRepDTO> uploadBatchLogRepDTOSearchDTO = ConvertUtils.convertSearch2SearchDTO(search, DownloadBatchLogRepDTO.class);
        for (DownloadBatchLogRepDTO record : uploadBatchLogRepDTOSearchDTO.getRecords()) {
            if(EmptyUtils.isNotEmpty(record.getStatus())) {
                record.setStatusLabel(dictionaryService.getDictName("downloadStatus", record.getStatus()));
            }
            if(EmptyUtils.isNotEmpty(record.getTypeName())) {
                record.setTypeLabel(dictionaryService.getDictName("downloadType", record.getTypeName()));
            }
        }

        return ResponseDTO.success(uploadBatchLogRepDTOSearchDTO);
    }

    /**
     * Count DownloadBatchLog from http
     */
    @Override
    public ResponseDTO<Long> queryCount(DownloadBatchLogQueryDTO dto) {
        DownloadBatchLog entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        Long count = getService().queryCountByPara(entity);
        return ResponseDTO.success(count);
    }

    /**
     * One DownloadBatchLog from http
     */
    @Override
    public ResponseDTO<DownloadBatchLogRepDTO> queryOne(DownloadBatchLogQueryDTO dto) {
        DownloadBatchLog qEntity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        DownloadBatchLog rEntity = getService().queryOneByPara(qEntity);
        DownloadBatchLogRepDTO rDTO = ConvertUtils.convertEntity2DTO(rEntity, DownloadBatchLogRepDTO.class);
        return ResponseDTO.success(rDTO);
    }

    /**
     * exist DownloadBatchLog from http
     */
    @Override
    public ResponseDTO<Boolean> exist(DownloadBatchLogQueryDTO dto) {
        boolean resullt = getService().existByPara(ConvertUtils.convertDTO2Entity(dto, entityClass));
        return ResponseDTO.success(resullt);
    }
}
