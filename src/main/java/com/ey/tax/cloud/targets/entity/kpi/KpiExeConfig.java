package com.ey.tax.cloud.targets.entity.kpi;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ey.cn.tax.framework.mybatisplus.core.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-01-22 10:49:40
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("target_kpi_exe_config")
public class KpiExeConfig extends BaseEntity {
    /**
     * 表名 COLUMN:table_name
     */
    private String tableName;

    /**
     * 字段名 COLUMN:field_name
     */
    private String fieldName;

    /**
     * 模版id COLUMN:temp_id
     */
    private String tempId;

    /**
     * 状态 COLUMN:status
     */
    private Integer status;
}
