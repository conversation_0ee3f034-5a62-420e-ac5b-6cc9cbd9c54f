package com.ey.tax.cloud.targets.entity.user;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ey.cn.tax.framework.mybatisplus.core.entity.BaseEntity;
import com.ey.tax.cloud.targets.enums.user.UserStatusEnum;
import groovy.transform.Field;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2023-09-26 15:09:02
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("TARGET_USER")
public class TargetUser extends BaseEntity {
    /**
     * 邮箱
     */
    private String email;

    /**
     * 职级
     */
    private String level;

    /**
     * 昵称
     */
    private String nickname;

    @TableField(value = "is_virtual")
    private Integer virtual;

    private UserStatusEnum status;
}
