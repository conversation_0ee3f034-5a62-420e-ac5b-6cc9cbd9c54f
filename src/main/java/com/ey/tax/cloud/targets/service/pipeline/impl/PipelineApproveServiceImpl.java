package com.ey.tax.cloud.targets.service.pipeline.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.entity.Search;
import com.ey.cn.tax.framework.mybatisplus.core.mapper.IEyBaseMapper;
import com.ey.cn.tax.framework.mybatisplus.core.query.EyQueryWrapper;
import com.ey.cn.tax.framework.mybatisplus.core.toolkit.EyWrappers;
import com.ey.cn.tax.framework.mybatisplus.extension.service.AbstractService;
import com.ey.cn.tax.framework.mybatisplus.utils.PageUtils;
import com.ey.cn.tax.framework.utils.ConvertUtils;
import com.ey.cn.tax.framework.utils.EmptyUtils;
import com.ey.tax.cloud.targets.constant.PipelineConstants;
import com.ey.tax.cloud.targets.dto.pipeline.PipelineApproveIdListDTO;
import com.ey.tax.cloud.targets.dto.pipeline.PipelineUpdateByIdDTO;
import com.ey.tax.cloud.targets.dto.pipeline.SalesDeliveryDTO;
import com.ey.tax.cloud.targets.entity.data.ExchangeRate;
import com.ey.tax.cloud.targets.entity.pipeline.*;
import com.ey.tax.cloud.targets.entity.user.UserAttribute;
import com.ey.tax.cloud.targets.mapper.pipeline.PipelineApproveMapper;
import com.ey.tax.cloud.targets.service.data.ExchangeRateService;
import com.ey.tax.cloud.targets.service.data.FiscalCalenderService;
import com.ey.tax.cloud.targets.service.pipeline.*;
import com.ey.tax.cloud.targets.utils.CommonUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.collections.api.factory.Sets;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2023-12-28 16:17:27
 *
 */
@Service
public class PipelineApproveServiceImpl extends AbstractService<PipelineApproveMapper, PipelineApprove> implements PipelineApproveService {

    @Autowired
    private SalesDeliveryApproveService salesDeliveryApproveService;
    @Autowired
    private SalesDeliveryService salesDeliveryService;
    @Autowired
    private FiscalCalenderService fiscalCalenderService;
    @Autowired
    private SalesDeliveryDataService salesDeliveryDataService;
    @Autowired
    private ExchangeRateService exchangeRateService;
    @Lazy
    @Autowired
    private PipelineService pipelineService;
    @Lazy
    @Autowired
    PipelineConfigService pipelineConfigService;
    protected PipelineApprove processUserName(PipelineApprove t) {
        if (t == null) {
            return t;
        }
        Set<String> ids = Sets.mutable.empty();
        String createUid = t.getCreateUid();
        String updateUid = t.getUpdateUid();
        String pursuitLeaderId = t.getPursuitLeaderId();
        String opprPartnerId = t.getOpprPartnerId();
        String engManagerId = t.getEngManagerId();
        if (StringUtils.isNotBlank(createUid)) {
            ids.add(createUid);
        }
        if (StringUtils.isNotBlank(updateUid)) {
            ids.add(updateUid);
        }
        if (StringUtils.isNotBlank(pursuitLeaderId)) {
            ids.add(pursuitLeaderId);
        }
        if (StringUtils.isNotBlank(opprPartnerId)) {
            ids.add(opprPartnerId);
        }
        if (StringUtils.isNotBlank(engManagerId)) {
            ids.add(engManagerId);
        }
        Map<String, String> userMap = getUserNamesByIds(ids);
        if (StringUtils.isNotBlank(createUid)) {
            if(EmptyUtils.isNotEmpty(userMap.get(createUid))){
                t.setCreateUserName(userMap.get(createUid).split("@")[0]);
            }
        }
        if (StringUtils.isNotBlank(updateUid)) {
            if(EmptyUtils.isNotEmpty(userMap.get(updateUid))){
                t.setUpdateUserName(userMap.get(updateUid).split("@")[0]);
            }
        }
        if (StringUtils.isNotBlank(pursuitLeaderId)) {
            if(EmptyUtils.isNotEmpty(userMap.get(pursuitLeaderId))){
                t.setPursuitLeaderName(userMap.get(pursuitLeaderId).split("@")[0]);
            }
        }
        if (StringUtils.isNotBlank(opprPartnerId)) {
            if(EmptyUtils.isNotEmpty(userMap.get(opprPartnerId))){
                t.setOpprPartnerName(userMap.get(opprPartnerId).split("@")[0]);
            }
        }
        if (StringUtils.isNotBlank(engManagerId)) {
            if(EmptyUtils.isNotEmpty(userMap.get(engManagerId))){
                t.setEngManagerName(userMap.get(engManagerId).split("@")[0]);
            }
        }
        return t;
    }

    @Transactional(
            rollbackFor = {Throwable.class}
    )
    @Override
    public boolean update(PipelineApprove entity) {
        this.logger.debug("update {}:{}", this.getEntitySimpleName(), entity);
        if (entity == null) {
            return false;
        } else if (this.getId(entity) == null) {
            return false;
        } else {
            entity.setCompanyType(null);
            return ((IEyBaseMapper)this.baseMapper).updateById(entity) > 0;
        }
    }

    @Transactional(readOnly = true)
    @Override
    public List<PipelineApprove> queryByPara(PipelineApprove entity) {
        logger.debug("queryByPara {}:{}", getEntitySimpleName(), entity);
        QueryWrapper<PipelineApprove> queryWrapper = Wrappers.query(entity);
        if(EmptyUtils.isNotEmpty(entity.getApproveUser())){
            queryWrapper.eq("oppr_partner_id", entity.getApproveUser())
                    .or()
                    .eq("eng_manager_id", entity.getApproveUser());
        }


        List<PipelineApprove> entities = baseMapper.selectList(queryWrapper);
        for (PipelineApprove pipelineApprove : entities) {
            processUserName(pipelineApprove);
        }
        return entities;
    }

    @Transactional(readOnly = true)
    @Override
    public Search<PipelineApprove> queryPageByPara(Search<PipelineApprove> search) {
        logger.debug("queryPageByPara {}:{}", getEntitySimpleName(), search);
        EyQueryWrapper<PipelineApprove> queryWrapper = EyWrappers.query(search.getQueryParams());
        if(EmptyUtils.isNotEmpty(search.getQueryParams().getApproveUser())){
            queryWrapper.eq("oppr_partner_id", search.getQueryParams().getApproveUser())
                    .or()
                    .eq("eng_manager_id", search.getQueryParams().getApproveUser());
        }
        if (search.getQueryParams().getQueryExtension() == null) {
            queryWrapper.extension(search.getQueryExtension());
        }

        if (!queryWrapper.isOrdered()) {
            queryWrapper.orderByDesc("create_time");
        }
        IPage<PipelineApprove> iPage = baseMapper.selectPage(PageUtils.convertPageToIPage(search.getPage()), queryWrapper);
        List<PipelineApprove> records = iPage.getRecords();
        search.setEntities(records);
        if (CollectionUtils.isEmpty(records)) {
            search.getPage().setTotalCount(0L);
        } else {
            for (PipelineApprove record : records) {
                processUserName(record);
            }
            search.getPage().setTotalCount(iPage.getTotal());
        }
        return search;
    }




    @Override
    @Transactional(rollbackFor = {Throwable.class},propagation = Propagation.REQUIRES_NEW)
    public void updateByIdForEm(PipelineUpdateByIdDTO pipeline) {
        PipelineApprove entity = ConvertUtils.convertDTO2Entity(pipeline, getEntityClass());
        entity.setConfirmStatus(PipelineConstants.APPROVE_STATUS_AMENDMENT_SUBMITTED);
        PipelineApprove mainPipeline = this.queryById(pipeline.getId());
        //如果金额改变
        if (entity.getAmountCurrency().compareTo(mainPipeline.getAmountCurrency()) != 0) {
            //获取汇率
            ExchangeRate exchangeRate2 = new ExchangeRate();
            exchangeRate2.setFiscalYear(String.valueOf(fiscalCalenderService.getCurrentFiscalCalender()));
            exchangeRate2.setExchangeCurrency(entity.getCurrency());
            List<ExchangeRate> exchangeRateList = exchangeRateService.queryByPara(exchangeRate2);
            if (EmptyUtils.isNotEmpty(exchangeRateList)) {
                exchangeRate2 = exchangeRateList.get(0);
                BigDecimal amount = entity.getAmountCurrency().divide(exchangeRate2.getExchangeRate(), PipelineConstants.AMOUNT_DECIMAL, BigDecimal.ROUND_HALF_UP);
                entity.setAmount(amount);
                //计算amountUsd
                //获取汇率
                ExchangeRate exchangeRate = new ExchangeRate();
                exchangeRate.setFiscalYear(String.valueOf(fiscalCalenderService.getCurrentFiscalCalender()));
                exchangeRate.setExchangeCurrency("USD");
                List<ExchangeRate> exchangeRate1 = exchangeRateService.queryByPara(exchangeRate);
                if (EmptyUtils.isNotEmpty(exchangeRate1)) {
                    exchangeRate = exchangeRate1.get(0);
                }
                BigDecimal amountUsd = entity.getAmount().divide(exchangeRate.getExchangeRate(), PipelineConstants.AMOUNT_DECIMAL, BigDecimal.ROUND_HALF_UP);
                entity.setAmountUsd(amountUsd);
            }
        }
        this.update(entity);
        PipelineConfig config = new PipelineConfig();
        config.setTableCode("targetPipelineDetail");
        config.setIsEdit("1");
        config.setFiscalYear(String.valueOf(pipeline.getPursuitFy()));
        List<PipelineConfig> configs = pipelineConfigService.queryByPara(config);
        List<String> columnNames = configs.stream().map(PipelineConfig::getColumnName).collect(Collectors.toList());
        Pipeline query = new Pipeline();
        query.setPipelineCode(pipeline.getPipelineCode());
        Pipeline oldEntity = pipelineService.queryOneByPara(query);
        Pipeline newPipeline = new Pipeline();
        BeanUtils.copyProperties(entity, newPipeline);
        Map<String, Object> map = CommonUtils.getDifferences(oldEntity, newPipeline, columnNames);
        if (map.containsKey("eicOwnEffort")) {
            //➢ Edit时，如果只改了 “EIC‘s own effort” 这一个字段，需要EP在 “EIC‘s own effort” 列表下确认。（审批状态不变）
            //如果“EIC‘s own effort”从Y改成N，则直接生效不审批。
            if (EmptyUtils.isEmpty(oldEntity.getEicOwnEffort())) {
                oldEntity.setEicOwnEffort(0);
            }
            if (((1 == oldEntity.getEicOwnEffort() || 2 == oldEntity.getEicOwnEffort()) && 0 == entity.getEicOwnEffort())) {
                oldEntity.setEicOwnEffort(entity.getEicOwnEffort());
            } else {
                oldEntity.setEffortsApprove("Pending Approval");
                oldEntity.setEicOwnEffort(entity.getEicOwnEffort());
            }
            if (oldEntity.getConfirmStatus().equals(PipelineConstants.APPROVE_STATUS_RETURNED)) {
                oldEntity.setConfirmStatus(PipelineConstants.APPROVE_STATUS_AMENDMENT_SUBMITTED);
            }
            pipelineService.update(oldEntity);
        } else {
            if (oldEntity.getConfirmStatus().equals(PipelineConstants.APPROVE_STATUS_RETURNED)) {
                oldEntity.setConfirmStatus(PipelineConstants.APPROVE_STATUS_AMENDMENT_SUBMITTED);
                pipelineService.update(oldEntity);
            }
        }

        //处理kpi
        if (EmptyUtils.isNotEmpty(pipeline.getKpiList())) {
            //合伙人不需要审批
            SalesDeliveryApprove salesDelivery = new SalesDeliveryApprove();
            salesDelivery.setPipelineId(pipeline.getId());
            List<SalesDeliveryApprove> salesDeliveries = salesDeliveryApproveService.queryByPara(salesDelivery);
            for (SalesDeliveryDTO delivery : pipeline.getKpiList()) {
                boolean has = false;
                for (SalesDeliveryApprove salesDelivery2 : salesDeliveries) {
                    if (delivery.getSalesDeliveryCredit().equals(salesDelivery2.getSalesDeliveryCredit())) {
                        has = true;
                        //比较theAdvisorSContuibutionToThisCase、resourcesProvinceCity、resourcesTaxAuthority
                        boolean same = true;
                        boolean tpcSame = true;
                        if (delivery.getSalesDeliveryCreditType() == 3 || delivery.getSalesDeliveryCreditType() == 8) {
                            if (EmptyUtils.isNotEmpty(delivery.getTheAdvisorSContuibutionToThisCase()) && !delivery.getTheAdvisorSContuibutionToThisCase().equals(salesDelivery2.getTheAdvisorSContuibutionToThisCase())) {
                                same = false;
                            }
                            if (EmptyUtils.isNotEmpty(delivery.getResourcesProvinceCity()) && !delivery.getResourcesProvinceCity().equals(salesDelivery2.getResourcesProvinceCity())) {
                                same = false;
                            }
                            if (EmptyUtils.isNotEmpty(delivery.getResourcesTaxAuthority()) && !delivery.getResourcesTaxAuthority().equals(salesDelivery2.getResourcesTaxAuthority())) {
                                same = false;
                            }
                        }
                        if (delivery.getSalesDeliveryCreditRatio().compareTo(salesDelivery2.getSalesDeliveryCreditRatio()) != 0 || !same) {
                            if (delivery.getSalesDeliveryCreditType() == PipelineConstants.SALES_DELIVERY_CREDIT_TYPE_COEEP || delivery.getSalesDeliveryCreditType() == PipelineConstants.SALES_DELIVERY_CREDIT_TYPE_COEEM) {
                                entity.setCoeApprove(PipelineConstants.APPROVE_STATUS_AMENDMENT_SUBMITTED);
                            }
                            if (delivery.getSalesDeliveryCreditType() == PipelineConstants.SALES_DELIVERY_CREDIT_TYPE_TPC || delivery.getSalesDeliveryCreditType() == PipelineConstants.SALES_DELIVERY_CREDIT_TYPE_TPC_EM) {
                                entity.setTpcApprove(PipelineConstants.APPROVE_STATUS_AMENDMENT_SUBMITTED);
                            }
                        }

                    }
                    if (!has) {
                        if (delivery.getSalesDeliveryCreditType() == PipelineConstants.SALES_DELIVERY_CREDIT_TYPE_COEEP || delivery.getSalesDeliveryCreditType() == PipelineConstants.SALES_DELIVERY_CREDIT_TYPE_COEEM) {
                            entity.setCoeApprove(PipelineConstants.APPROVE_STATUS_AMENDMENT_SUBMITTED);
                        }
                        if (delivery.getSalesDeliveryCreditType() == PipelineConstants.SALES_DELIVERY_CREDIT_TYPE_TPC || delivery.getSalesDeliveryCreditType() == PipelineConstants.SALES_DELIVERY_CREDIT_TYPE_TPC_EM) {
                            entity.setTpcApprove(PipelineConstants.APPROVE_STATUS_AMENDMENT_SUBMITTED);
                        }
                    }
                }
            }
                this.update(entity);


                if (EmptyUtils.isNotEmpty(salesDeliveries)) {
                    //删除原始数据
                    SalesDeliveryApprove salesDelivery1 = new SalesDeliveryApprove();
                    salesDelivery1.setPipelineId(pipeline.getId());
                    salesDeliveryApproveService.deleteByPara(salesDelivery1);
                    //插入新数据
                    for (SalesDeliveryDTO deliveryApprove : pipeline.getKpiList()) {
                        SalesDeliveryApprove salesDelivery2 = new SalesDeliveryApprove();
                        BeanUtils.copyProperties(deliveryApprove, salesDelivery2);
                        if (EmptyUtils.isEmpty(salesDelivery2.getUseAmount()) || salesDelivery2.getUseAmount() == 0) {
                            BigDecimal salesDeliveryCreditRatio = salesDelivery2.getSalesDeliveryCreditRatio();
                            salesDelivery2.setSalesDeliveryCreditAmount(salesDeliveryCreditRatio.multiply(pipeline.getAmount()).multiply(new BigDecimal("0.01")));
                            salesDelivery2.setSalesDeliveryCreditAmountUsd(salesDeliveryCreditRatio.multiply(pipeline.getAmountUsd()).multiply(new BigDecimal("0.01")));
                        } else {
                            salesDelivery2.setSalesDeliveryCreditRatio(salesDelivery2.getSalesDeliveryCreditAmount().divide(pipeline.getAmount(), 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100")));
                            salesDelivery2.setSalesDeliveryCreditAmountUsd(salesDelivery2.getSalesDeliveryCreditRatio().multiply(pipeline.getAmountUsd()).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP));
                        }
                        salesDelivery2.setId(null);
                        salesDelivery2.setPipelineId(pipeline.getId());
                        salesDeliveryApproveService.save(salesDelivery2);
                    }
                }
            }
        }

    @Override
    @Transactional(rollbackFor = {Throwable.class},propagation = Propagation.REQUIRES_NEW)
    public void updateByIdForEp(PipelineUpdateByIdDTO pipeline) {
        PipelineApprove entity = ConvertUtils.convertDTO2Entity(pipeline, getEntityClass());
        BeanUtils.copyProperties(pipeline,entity);
        entity.setConfirmStatus(PipelineConstants.APPROVE_STATUS_PROCESSED);
        this.update(entity);
        //处理kpi
        //处理kpi
        List<SalesDeliveryApprove> salesDeliveries = new ArrayList<>();
        List<SalesDeliveryApprove> salesDeliveries2 = new ArrayList<>();
        if(EmptyUtils.isNotEmpty(pipeline.getKpiList())){
            //合伙人不需要审批
            SalesDeliveryApprove salesDelivery = new SalesDeliveryApprove();
            salesDelivery.setPipelineId(pipeline.getId());
            salesDeliveries = salesDeliveryApproveService.queryByPara(salesDelivery);
            if(EmptyUtils.isNotEmpty(salesDeliveries)){
                //删除原始数据
                SalesDeliveryApprove salesDelivery1 = new SalesDeliveryApprove();
                salesDelivery1.setPipelineId(pipeline.getId());
                salesDeliveryApproveService.deleteByPara(salesDelivery1);
                //插入新数据
                for(SalesDeliveryDTO deliveryApprove : pipeline.getKpiList()){
                    SalesDeliveryApprove salesDelivery2 = new SalesDeliveryApprove();
                    BeanUtils.copyProperties(deliveryApprove,salesDelivery2);
                    salesDelivery2.setId(null);
                    salesDelivery2.setPipelineId(pipeline.getId());
                    salesDeliveries2.add(salesDelivery2);
                    salesDeliveryApproveService.save(salesDelivery2);
                }
            }
        }
        //审批通过
        String pipelineCode = pipeline.getPipelineCode();
        Pipeline mainPipeline = new Pipeline();
        mainPipeline.setPipelineCode(pipelineCode);
        mainPipeline = pipelineService.queryOneByPara(mainPipeline);
        String pId = mainPipeline.getId();
        //如果金额改变
        if(entity.getAmountCurrency().compareTo(mainPipeline.getAmountCurrency())!=0){
                //获取汇率
                ExchangeRate exchangeRate2 = new ExchangeRate();
                exchangeRate2.setFiscalYear(String.valueOf(fiscalCalenderService.getCurrentFiscalCalender()));
                exchangeRate2.setExchangeCurrency(entity.getCurrency());
                List<ExchangeRate> exchangeRateList = exchangeRateService.queryByPara(exchangeRate2);
                if(EmptyUtils.isNotEmpty(exchangeRateList)){
                    exchangeRate2 = exchangeRateList.get(0);
                    BigDecimal amount = entity.getAmountCurrency().divide(exchangeRate2.getExchangeRate(),PipelineConstants.AMOUNT_DECIMAL,BigDecimal.ROUND_HALF_UP);
                    entity.setAmount(amount);
                    //计算amountUsd
                    //获取汇率
                    ExchangeRate exchangeRate = new ExchangeRate();
                    exchangeRate.setFiscalYear(String.valueOf(fiscalCalenderService.getCurrentFiscalCalender()));
                    exchangeRate.setExchangeCurrency("USD");
                    List<ExchangeRate> exchangeRate1 = exchangeRateService.queryByPara(exchangeRate);
                    if(EmptyUtils.isNotEmpty(exchangeRate1)){
                        exchangeRate = exchangeRate1.get(0);
                    }
                    BigDecimal amountUsd = entity.getAmount().divide(exchangeRate.getExchangeRate(),PipelineConstants.AMOUNT_DECIMAL,BigDecimal.ROUND_HALF_UP);
                    entity.setAmountUsd(amountUsd);
                }
        }
        LocalDateTime createTime = mainPipeline.getCreateTime();
        String createUid = mainPipeline.getCreateUid();
        BeanUtils.copyProperties(entity,mainPipeline);
        mainPipeline.setId(pId);
        mainPipeline.setCreateTime(createTime);
        mainPipeline.setCreateUid(createUid);
        mainPipeline.setConfirmStatus(PipelineConstants.APPROVE_STATUS_PROCESSED);
        mainPipeline.setUpdateFy(fiscalCalenderService.getCurrentFiscalCalender());
        //如果pipeline的effortsApprove为Pending Approval，那么更新为Approved
        if("Pending Approval".equals(mainPipeline.getEffortsApprove())){
            mainPipeline.setEffortsApprove("Confirmed");
        }
        pipelineService.update(mainPipeline);

        //查询kpi
        SalesDeliveryApprove salesDelivery = new SalesDeliveryApprove();
        salesDelivery.setPipelineId(pipeline.getId());
        if(EmptyUtils.isNotEmpty(salesDeliveries2)){
            //删除原始数据
            SalesDelivery salesDelivery1 = new SalesDelivery();
            salesDelivery1.setPipelineId(pId);
            List<SalesDelivery> list = salesDeliveryService.queryByPara(salesDelivery1);
            if(list.get(0).getFiscalYear().equals(fiscalCalenderService.getCurrentFiscalCalender())){
                salesDeliveryService.realDeleteByIds(list.stream().map(SalesDelivery::getId).collect(Collectors.toList()));
            }else {
                salesDeliveryService.deleteByPara(salesDelivery1);
            }
            //插入新数据
            for(SalesDeliveryApprove deliveryApprove : salesDeliveries2){
                SalesDelivery salesDelivery2 = new SalesDelivery();
                BeanUtils.copyProperties(deliveryApprove,salesDelivery2);
                if(EmptyUtils.isEmpty(salesDelivery2.getUseAmount())||salesDelivery2.getUseAmount()==0){
                    BigDecimal salesDeliveryCreditRatio = salesDelivery2.getSalesDeliveryCreditRatio();
                    salesDelivery2.setSalesDeliveryCreditAmount(salesDeliveryCreditRatio.multiply(pipeline.getAmount()).multiply(new BigDecimal("0.01")));
                    salesDelivery2.setSalesDeliveryCreditAmountUsd(salesDeliveryCreditRatio.multiply(pipeline.getAmountUsd()).multiply(new BigDecimal("0.01")));
                }else {
                    salesDelivery2.setSalesDeliveryCreditRatio( salesDelivery2.getSalesDeliveryCreditAmount().divide(pipeline.getAmount(),2,BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100")));
                    salesDelivery2.setSalesDeliveryCreditAmountUsd( salesDelivery2.getSalesDeliveryCreditRatio().multiply(pipeline.getAmountUsd()).divide(new BigDecimal("100"),2,BigDecimal.ROUND_HALF_UP));
                }
                salesDelivery2.setId(null);
                salesDelivery2.setPipelineId(pId);
                salesDeliveryService.save(salesDelivery2);
            }
        }
        //同步data表
        salesDeliveryDataService.syncDataByPipeline(pId);
    }


    @Transactional(rollbackFor = {Throwable.class},propagation = Propagation.REQUIRES_NEW)
    public Boolean approve(PipelineApproveIdListDTO pipelineApproveIdListDTO) {
        for (String id : pipelineApproveIdListDTO.getIds()) {
            PipelineApprove entity = queryById(id);
            String pipelineCode = entity.getPipelineCode();
            Pipeline pipeline = new Pipeline();
            pipeline.setPipelineCode(pipelineCode);
            pipeline = pipelineService.queryOneByPara(pipeline);
            String pId = pipeline.getId();
            //判断amountCurrency是否不同
            if(EmptyUtils.isNotEmpty(pipeline.getAmountCurrency()) && EmptyUtils.isNotEmpty(entity.getAmountCurrency()) && pipeline.getAmountCurrency().compareTo(entity.getAmountCurrency())!=0){
                pipeline.setAmountCurrency(entity.getAmountCurrency());
            }
            //如果pipeline的effortsApprove为Pending Approval，那么更新为Approved
            if("Pending Approval".equals(pipeline.getEffortsApprove())){
                pipeline.setEffortsApprove("Confirmed");
                entity.setEffortsApprove("Confirmed");
            }
            BeanUtils.copyProperties(entity,pipeline);
            pipeline.setId(pId);
            pipeline.setConfirmStatus(PipelineConstants.APPROVE_STATUS_PROCESSED);
            pipeline.setUpdateFy(fiscalCalenderService.getCurrentFiscalCalender());
            pipelineService.update(pipeline);
            entity.setConfirmStatus(PipelineConstants.APPROVE_STATUS_PROCESSED);
            update(entity);
            //查询kpi
            SalesDeliveryApprove salesDelivery = new SalesDeliveryApprove();
            salesDelivery.setPipelineId(id);
            List<SalesDeliveryApprove> salesDeliveries = salesDeliveryApproveService.queryByPara(salesDelivery);
            if(EmptyUtils.isNotEmpty(salesDeliveries)){
                //删除原始数据
                SalesDelivery salesDelivery1 = new SalesDelivery();
                salesDelivery1.setPipelineId(pId);
                salesDeliveryService.deleteByPara(salesDelivery1);
                //插入新数据
                for(SalesDeliveryApprove deliveryApprove : salesDeliveries){
                    SalesDelivery salesDelivery2 = new SalesDelivery();
                    BeanUtils.copyProperties(deliveryApprove,salesDelivery2);
                    salesDelivery2.setId(null);
                    salesDelivery2.setPipelineId(pId);
                    salesDeliveryService.save(salesDelivery2);
                }
            }
            salesDeliveryDataService.syncDataByPipeline(pId);
        }
        return true;
    }

}
