package com.ey.tax.cloud.targets.controller.kpi.impl;

import com.ey.tax.cloud.targets.entity.kpi.KpiBatchLog;
import com.ey.tax.cloud.targets.service.kpi.KpiBatchLogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class KpiBatchTask implements Runnable {

    protected final Logger logger = LoggerFactory.getLogger(this.getClass());
    private final KpiBatchLog entity;
    private final KpiBatchLogService service;

    public KpiBatchTask(KpiBatchLog entity, KpiBatchLogService service) {
        this.entity = entity;
        this.service = service;
    }

    @Override
    public void run() {
        try {
            service.runKpiBatch(entity);
        } catch (Exception e) {
            // 处理异常，如记录日志
            logger.error("KpiBatchTask execute error:", e);
        }
    }
}
