package com.ey.tax.cloud.targets.controller.data.impl;

import com.ey.cn.tax.framework.context.EyUserContextHolder;
import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.dto.SearchDTO;
import com.ey.cn.tax.framework.entity.Search;
import com.ey.cn.tax.framework.utils.ConvertUtils;
import com.ey.cn.tax.framework.utils.EmptyUtils;
import com.ey.cn.tax.framework.web.annotation.EyRestController;
import com.ey.cn.tax.framework.web.base.AbstractController;
import com.ey.tax.cloud.file.dto.DownloadByStreamDto;
import com.ey.tax.cloud.file.dto.GenerateUrlDto;
import com.ey.tax.cloud.file.dto.UpLoadByMultipartFileDTO;
import com.ey.tax.cloud.file.remote.fileinfo.FileInfoFeignClient;
import com.ey.tax.cloud.targets.controller.data.TemplateController;
import com.ey.tax.cloud.targets.dto.data.*;
import com.ey.tax.cloud.targets.entity.data.Template;
import com.ey.tax.cloud.targets.entity.data.TemplateInstance;
import com.ey.tax.cloud.targets.service.data.DictionaryService;
import com.ey.tax.cloud.targets.service.data.TemplateService;
import com.ey.tax.cloud.targets.utils.CommonUtils;
import com.grapecity.documents.excel.Workbook;
import feign.FeignException;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-02-19 11:47:19
 *
 */
@EyRestController(path ="/v1/template")
public class TemplateControllerImpl extends AbstractController<TemplateService, TemplateDTO, Template> implements TemplateController {

    /**
     * add Template from http
     */
    @Autowired
    private FileInfoFeignClient fileInfoFeignClient;
    @Autowired
    private DictionaryService dictionaryService;

    @Override
    public ResponseDTO<Void> add(TemplateAddDTO dto) {
        Template entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        service.save(entity);
        return ResponseDTO.success();
    }

    /**
     * addList Template from http
     */
    @Override
    public ResponseDTO<Void> addList(List<TemplateAddDTO> dtos) {
        List<Template> entitys = ConvertUtils.convertDTOList2EntityList(dtos, entityClass);
        service.save(entitys);
        return ResponseDTO.success();
    }

    /**
     * delete Template from http
     */
    @Override
    public ResponseDTO<Void> deleteById(TemplateDeleteByIdDTO dto) {
        service.deleteById(dto.getId());
        return ResponseDTO.success();
    }

    /**
     * deleteList Template from http
     */
    @Override
    public ResponseDTO<Void> deleteByIdList(TemplateDeleteByIdListDTO dto) {
        getService().deleteByIds(dto.getIds());
        return ResponseDTO.success();
    }

    /**
     * update Template from http
     */
    @Override
    public ResponseDTO<Void> updateById(TemplateUpdateByIdDTO dto) {
        Template entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        getService().update(entity);
        return ResponseDTO.success();
    }

    @Override
    public ResponseDTO<Void> updateFileById(MultipartFile file, String id) {
        Template entity = getService().queryById(id);
        UpLoadByMultipartFileDTO dto1 = new UpLoadByMultipartFileDTO();
        dto1.setSystemId("tenantapi");
        dto1.setTenantId(EyUserContextHolder.get().getTenantId());
        String fId = fileInfoFeignClient.uploadByFile(dto1,file).getData();
        entity.setFileId(fId);
        getService().update(entity);
        return ResponseDTO.success();
    }

    /**
     * updateBatch Template from http
     */
    @Override
    public ResponseDTO<Void> updateBatch(List<TemplateBatchUpdateDTO> dtos) {
        List<Template> entities = ConvertUtils.convertDTOList2EntityList(dtos, entityClass);
        getService().updateBatchByIds(entities);
        return ResponseDTO.success();
    }

    /**
     * query Template from http
     */
    @Override
    public ResponseDTO<TemplateRepDTO> queryById(TemplateQueryByIdDTO dto) {
        Template entity = getService().queryById(dto.getId());
        TemplateRepDTO rDTO = ConvertUtils.convertEntity2DTO(entity, TemplateRepDTO.class);
        return ResponseDTO.success(rDTO);
    }

    /**
     * queryByIdList Template from http
     */
    @Override
    public ResponseDTO<List<TemplateRepDTO>> queryByIdList(TemplateQueryByIdListDTO dto) {
        List<Template> entities = getService().queryByIds(dto.getIds());
        return ResponseDTO.success(ConvertUtils.convertEntityList2DTOList(entities, TemplateRepDTO.class));
    }

    /**
     * queryList Template from http
     */
    @Override
    public ResponseDTO<List<TemplateRepDTO>> queryList(TemplateQueryDTO dto) {
        Template entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        List<Template> entities = getService().queryByPara(entity);
        return ResponseDTO.success(ConvertUtils.convertEntityList2DTOList(entities, TemplateRepDTO.class));
    }

    /**
     * Page Template from http
     */
    @Override
    public ResponseDTO<SearchDTO<TemplateRepDTO>> queryPage(SearchDTO<TemplateQueryPageDTO> searchDTO) {
        Search<Template> search = ConvertUtils.convertSearchDTO2Search(searchDTO, entityClass);
        getService().queryPageByPara(search);
        SearchDTO<TemplateRepDTO> searchDTO1 = ConvertUtils.convertSearch2SearchDTO(search, TemplateRepDTO.class);
        for (TemplateRepDTO record : searchDTO1.getRecords()) {
            record.setStatusLabel(dictionaryService.getDictName("status", record.getStatus()));
        }
        return ResponseDTO.success(searchDTO1);
    }

    /**
     * Count Template from http
     */
    @Override
    public ResponseDTO<Long> queryCount(TemplateQueryDTO dto) {
        Template entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        Long count = getService().queryCountByPara(entity);
        return ResponseDTO.success(count);
    }

    /**
     * One Template from http
     */
    @Override
    public ResponseDTO<TemplateRepDTO> queryOne(TemplateQueryDTO dto) {
        Template qEntity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        Template rEntity = getService().queryOneByPara(qEntity);
        TemplateRepDTO rDTO = ConvertUtils.convertEntity2DTO(rEntity, TemplateRepDTO.class);
        return ResponseDTO.success(rDTO);
    }

    /**
     * exist Template from http
     */
    @Override
    public ResponseDTO<Boolean> exist(TemplateQueryDTO dto) {
        boolean resullt = getService().existByPara(ConvertUtils.convertDTO2Entity(dto, entityClass));
        return ResponseDTO.success(resullt);
    }
    public HttpServletResponse getCurrentResponse() {
        ServletRequestAttributes attrs = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attrs != null) {
            return attrs.getResponse();
        } else {
            // 在某些情况下，RequestContextHolder中可能没有response，例如非请求线程中
            throw new IllegalStateException("No HttpServletResponse currently bound to the thread");
        }
    }
    @Override
    public void downloadTemp(TemplateInstanceQueryByIdDTO dto) {
        Template templateInstance = getService().queryById(dto.getId());
        String fileId = templateInstance.getFileId();
        DownloadByStreamDto downloadByStreamDto = new DownloadByStreamDto();
        downloadByStreamDto.setId(fileId);
        feign.Response feignResponse =  fileInfoFeignClient.downloadByStream(downloadByStreamDto);
        GenerateUrlDto generateUrlDto = new GenerateUrlDto();
        generateUrlDto.setId(fileId);
        String url = fileInfoFeignClient.generateUrl(generateUrlDto).getData();
        // 从响应中获取文件名（根据实际情况获取）
        HttpServletResponse response = getCurrentResponse();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String fileName = "Target "+ sdf.format(System.currentTimeMillis()) + ".xlsx";
        // 正则表达式匹配URL中的文件名
        Pattern pattern = Pattern.compile(".*/([^/?]+)\\?.*");
        Matcher matcher = pattern.matcher(url);

        if (matcher.find()) {
            // 获取匹配的第一个组（即文件名）
            fileName = matcher.group(1);
        }

        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");

        // 获取输入流
        try (InputStream inputStream = feignResponse.body().asInputStream();
             OutputStream outputStream = response.getOutputStream()) {

            // 将输入流中的数据写入输出流
            byte[] buffer = new byte[1024];
            int length;
            while ((length = inputStream.read(buffer)) > 0) {
                outputStream.write(buffer, 0, length);
            }
            outputStream.flush();
        } catch (FeignException | IOException e) {
            throw new RuntimeException("Failed to download file", e);
        }
    }


}
