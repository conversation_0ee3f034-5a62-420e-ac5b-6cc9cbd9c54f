package com.ey.tax.cloud.targets.dto.auth;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2023-12-22 10:32:03
 *
 */
@Data
@Schema(description = "新增实体")
public class AuthGroupCopyDTO {
    /**
     * 被复制的权限组id
     */
    @Schema(description = "被复制的权限组id")
    private String id;

    /**
     * 新权限组名称
     */
    @Schema(description = "新权限组名称")
    private String groupName;

    /**
     * 新权限组code
     */
    @Schema(description = "新权限组code")
    private String groupCode;

    @Schema(description = "菜单/功能code")
    private String menuCode;



}
