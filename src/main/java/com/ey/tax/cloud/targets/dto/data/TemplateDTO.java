package com.ey.tax.cloud.targets.dto.data;

import com.ey.cn.tax.framework.dto.BaseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-02-19 11:47:19
 * 
 */
@Data
@Schema(description = "默认实体")
public class TemplateDTO extends BaseDTO {
    /**
     * 模板编码 COLUMN:temp_code
     */
    @Schema(description = "模板编码")
    private String tempCode;

    /**
     * 模板名称 COLUMN:temp_name
     */
    @Schema(description = "模板名称")
    private String tempName;

    /**
     * 状态 COLUMN:status
     */
    @Schema(description = "状态")
    private String status;

    /**
     * 文件ID COLUMN:file_id
     */
    @Schema(description = "文件ID")
    private String fileId;
}