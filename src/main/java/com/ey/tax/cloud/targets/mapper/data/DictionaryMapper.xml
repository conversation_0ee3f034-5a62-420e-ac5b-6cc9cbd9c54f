<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ey.tax.cloud.targets.mapper.data.DictionaryMapper" >
  
  <resultMap id="EyTaxResultMap" type="com.ey.tax.cloud.targets.entity.data.Dictionary" >
    <id column="id" property="id" jdbcType="VARCHAR" />
    <result column="create_uid" property="createUid" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_uid" property="updateUid" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="is_del" property="isDel" jdbcType="INTEGER" />
    <result column="dict_code" property="dictCode" jdbcType="VARCHAR" />
    <result column="dict_name" property="dictName" jdbcType="VARCHAR" />
    <result column="dict_type" property="dictType" jdbcType="INTEGER" />
    <result column="dict_tiger_code" property="dictTigerCode" jdbcType="VARCHAR" />
  </resultMap>
  
</mapper>