package com.ey.tax.cloud.targets.controller.resource;

import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.dto.SearchDTO;
import com.ey.cn.tax.framework.validator.AddGroup;
import com.ey.cn.tax.framework.validator.DeleteByIdGroup;
import com.ey.cn.tax.framework.validator.DeleteByIdListGroup;
import com.ey.cn.tax.framework.validator.QueryByIdGroup;
import com.ey.cn.tax.framework.validator.QueryByIdListGroup;
import com.ey.cn.tax.framework.validator.QueryPageGroup;
import com.ey.cn.tax.framework.validator.UpdateByIdGroup;
import com.ey.cn.tax.framework.web.annotation.EyPostMapping;
import com.ey.cn.tax.framework.web.base.BaseController;
import com.ey.tax.cloud.targets.dto.resource.PerformanceAddDTO;
import com.ey.tax.cloud.targets.dto.resource.PerformanceBatchUpdateDTO;
import com.ey.tax.cloud.targets.dto.resource.PerformanceDTO;
import com.ey.tax.cloud.targets.dto.resource.PerformanceDeleteByIdDTO;
import com.ey.tax.cloud.targets.dto.resource.PerformanceDeleteByIdListDTO;
import com.ey.tax.cloud.targets.dto.resource.PerformanceQueryByIdDTO;
import com.ey.tax.cloud.targets.dto.resource.PerformanceQueryByIdListDTO;
import com.ey.tax.cloud.targets.dto.resource.PerformanceQueryDTO;
import com.ey.tax.cloud.targets.dto.resource.PerformanceQueryPageDTO;
import com.ey.tax.cloud.targets.dto.resource.PerformanceRepDTO;
import com.ey.tax.cloud.targets.dto.resource.PerformanceUpdateByIdDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-01-17 14:35:35
 *
 */
@Tag(name="Performance")
public interface PerformanceController extends BaseController<PerformanceDTO> {
    /**
     * add Performance from http
     */
    @Operation(summary = "新增", description = "新增一条数据")
    @EyPostMapping(value = "/add.do")
    ResponseDTO<Void> add(@RequestBody @Validated({AddGroup.class}) PerformanceAddDTO dto);

    /**
     * addList Performance from http
     */
    @Operation(summary = "批量新增", description = "批量新增数据")
    @EyPostMapping(value = "/addList.do")
    ResponseDTO<Void> addList(@RequestBody List<PerformanceAddDTO> dtos);

    /**
     * delete Performance from http
     */
    @Operation(summary = "根据id删除", description = "根据id删除一条数据")
    @EyPostMapping(value = "/deleteById.do")
    ResponseDTO<Void> deleteById(@RequestBody @Validated({DeleteByIdGroup.class}) PerformanceDeleteByIdDTO dto);

    /**
     * deleteList Performance from http
     */
    @Operation(summary = "根据id列表删除", description = "根据id列表批量删除数据")
    @EyPostMapping(value = "/deleteByIdList.do")
    ResponseDTO<Void> deleteByIdList(@RequestBody @Validated({DeleteByIdListGroup.class}) PerformanceDeleteByIdListDTO dto);

    /**
     * update Performance from http
     */
    @Operation(summary = "根据id更新", description = "根据id更新一条数据")
    @EyPostMapping(value = "/updateById.do")
    ResponseDTO<Void> updateById(@RequestBody @Validated({UpdateByIdGroup.class}) PerformanceUpdateByIdDTO dto);

    /**
     * updateBatch Performance from http
     */
    @Operation(summary = "批量更新", description = "批量更新数据")
    @EyPostMapping(value = "/updateBatch.do")
    ResponseDTO<Void> updateBatch(@RequestBody List<PerformanceBatchUpdateDTO> dtos);

    /**
     * query Performance from http
     */
    @Operation(summary = "根据id查询", description = "根据id查询数据")
    @EyPostMapping(value = "/queryById.do")
    ResponseDTO<PerformanceRepDTO> queryById(@RequestBody @Validated({QueryByIdGroup.class}) PerformanceQueryByIdDTO dto);

    /**
     * queryByIdList Performance from http
     */
    @Operation(summary = "根据id列表查询", description = "根据id列表查询数据")
    @EyPostMapping(value = "/queryByIdList.do")
    ResponseDTO<List<PerformanceRepDTO>> queryByIdList(@RequestBody @Validated({QueryByIdListGroup.class}) PerformanceQueryByIdListDTO dto);

    /**
     * queryList Performance from http
     */
    @Operation(summary = "查询列表", description = "查询数据列表")
    @EyPostMapping(value = "/queryList.do")
    ResponseDTO<List<PerformanceRepDTO>> queryList(@RequestBody PerformanceQueryDTO dto);

    /**
     * Page Performance from http
     */
    @Operation(summary = "分页查询", description = "根据条件分页查询")
    @EyPostMapping(value = "/queryPage.do")
    ResponseDTO<SearchDTO<PerformanceRepDTO>> queryPage(@RequestBody @Validated({QueryPageGroup.class}) SearchDTO<PerformanceQueryPageDTO> searchDTO);
    /**
     * Page Performance from http
     */
    @Operation(summary = "分页查询Execuitive", description = "根据条件分页查询")
    @EyPostMapping(value = "/queryPageExecuitive.do")
    ResponseDTO<SearchDTO<PerformanceRepDTO>> queryPageExecuitive(@RequestBody @Validated({QueryPageGroup.class}) SearchDTO<PerformanceQueryPageDTO> searchDTO);
    /**
     * Page Performance from http
     */
    @Operation(summary = "分页查询Report", description = "根据条件分页查询")
    @EyPostMapping(value = "/queryPageReport.do")
    ResponseDTO<SearchDTO<PerformanceRepDTO>> queryPageReport(@RequestBody @Validated({QueryPageGroup.class}) SearchDTO<PerformanceQueryPageDTO> searchDTO);
    @Operation(summary = "分页查询Tpc", description = "根据条件分页查询")
    @EyPostMapping(value = "/queryPageTpc.do")
    ResponseDTO<SearchDTO<PerformanceRepDTO>> queryPageTpc(@RequestBody @Validated({QueryPageGroup.class}) SearchDTO<PerformanceQueryPageDTO> searchDTO);

    /**
     * Count Performance from http
     */
    @Operation(summary = "查询数量", description = "根据条件查询数量")
    @EyPostMapping(value = "/queryCount.do")
    ResponseDTO<Long> queryCount(@RequestBody PerformanceQueryDTO performanceDTO);

    /**
     * One Performance from http
     */
    @Operation(summary = "查询单个数据", description = "根据条件查询一条数据,如果查询到多条则返回异常.")
    @EyPostMapping(value = "/queryOne.do")
    ResponseDTO<PerformanceRepDTO> queryOne(@RequestBody PerformanceQueryDTO performanceDTO);

    /**
     * exist Performance from http
     */
    @Operation(summary = "查询是否存在", description = "根据条件查询是否存在")
    @EyPostMapping(value = "/exist.do")
    ResponseDTO<Boolean> exist(@RequestBody PerformanceQueryDTO performanceDTO);

    /**
     * 根据财年计算kpitarget
     */
    @Operation(summary = "根据财年计算kpitarget", description = "根据财年计算kpitarget")
    @EyPostMapping(value = "/updatePerformanceKpi.do")
    ResponseDTO<Void> updatePerformanceKpi(@RequestBody String year);
}
