package com.ey.tax.cloud.targets.controller.pipeline.impl;

import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.dto.SearchDTO;
import com.ey.cn.tax.framework.entity.Search;
import com.ey.cn.tax.framework.utils.ConvertUtils;
import com.ey.cn.tax.framework.web.annotation.EyRestController;
import com.ey.cn.tax.framework.web.base.AbstractController;
import com.ey.tax.cloud.targets.controller.pipeline.SalesDeliveryApproveController;
import com.ey.tax.cloud.targets.dto.pipeline.SalesDeliveryApproveAddDTO;
import com.ey.tax.cloud.targets.dto.pipeline.SalesDeliveryApproveBatchUpdateDTO;
import com.ey.tax.cloud.targets.dto.pipeline.SalesDeliveryApproveDTO;
import com.ey.tax.cloud.targets.dto.pipeline.SalesDeliveryApproveDeleteByIdDTO;
import com.ey.tax.cloud.targets.dto.pipeline.SalesDeliveryApproveDeleteByIdListDTO;
import com.ey.tax.cloud.targets.dto.pipeline.SalesDeliveryApproveQueryByIdDTO;
import com.ey.tax.cloud.targets.dto.pipeline.SalesDeliveryApproveQueryByIdListDTO;
import com.ey.tax.cloud.targets.dto.pipeline.SalesDeliveryApproveQueryDTO;
import com.ey.tax.cloud.targets.dto.pipeline.SalesDeliveryApproveQueryPageDTO;
import com.ey.tax.cloud.targets.dto.pipeline.SalesDeliveryApproveRepDTO;
import com.ey.tax.cloud.targets.dto.pipeline.SalesDeliveryApproveUpdateByIdDTO;
import com.ey.tax.cloud.targets.entity.pipeline.SalesDeliveryApprove;
import com.ey.tax.cloud.targets.service.pipeline.SalesDeliveryApproveService;
import java.util.List;
import java.util.Map;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-01-16 15:29:02
 * 
 */
@EyRestController(path ="/v1/salesDeliveryApprove")
public class SalesDeliveryApproveControllerImpl extends AbstractController<SalesDeliveryApproveService, SalesDeliveryApproveDTO, SalesDeliveryApprove> implements SalesDeliveryApproveController {

    /**
     * add SalesDeliveryApprove from http
     */
    @Override
    public ResponseDTO<Void> add(SalesDeliveryApproveAddDTO dto) {
        SalesDeliveryApprove entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        service.save(entity);
        return ResponseDTO.success();
    }

    /**
     * addList SalesDeliveryApprove from http
     */
    @Override
    public ResponseDTO<Void> addList(List<SalesDeliveryApproveAddDTO> dtos) {
        List<SalesDeliveryApprove> entitys = ConvertUtils.convertDTOList2EntityList(dtos, entityClass);
        service.save(entitys);
        return ResponseDTO.success();
    }

    /**
     * delete SalesDeliveryApprove from http
     */
    @Override
    public ResponseDTO<Void> deleteById(SalesDeliveryApproveDeleteByIdDTO dto) {
        service.deleteById(dto.getId());
        return ResponseDTO.success();
    }

    /**
     * deleteList SalesDeliveryApprove from http
     */
    @Override
    public ResponseDTO<Void> deleteByIdList(SalesDeliveryApproveDeleteByIdListDTO dto) {
        getService().deleteByIds(dto.getIds());
        return ResponseDTO.success();
    }

    /**
     * update SalesDeliveryApprove from http
     */
    @Override
    public ResponseDTO<Void> updateById(SalesDeliveryApproveUpdateByIdDTO dto) {
        SalesDeliveryApprove entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        getService().update(entity);
        return ResponseDTO.success();
    }

    /**
     * updateBatch SalesDeliveryApprove from http
     */
    @Override
    public ResponseDTO<Void> updateBatch(List<SalesDeliveryApproveBatchUpdateDTO> dtos) {
        List<SalesDeliveryApprove> entities = ConvertUtils.convertDTOList2EntityList(dtos, entityClass);
        getService().updateBatchByIds(entities);
        return ResponseDTO.success();
    }

    /**
     * query SalesDeliveryApprove from http
     */
    @Override
    public ResponseDTO<SalesDeliveryApproveRepDTO> queryById(SalesDeliveryApproveQueryByIdDTO dto) {
        SalesDeliveryApprove entity = getService().queryById(dto.getId());
        SalesDeliveryApproveRepDTO rDTO = ConvertUtils.convertEntity2DTO(entity, SalesDeliveryApproveRepDTO.class);
        return ResponseDTO.success(rDTO);
    }

    /**
     * queryByIdList SalesDeliveryApprove from http
     */
    @Override
    public ResponseDTO<List<SalesDeliveryApproveRepDTO>> queryByIdList(SalesDeliveryApproveQueryByIdListDTO dto) {
        List<SalesDeliveryApprove> entities = getService().queryByIds(dto.getIds());
        return ResponseDTO.success(ConvertUtils.convertEntityList2DTOList(entities, SalesDeliveryApproveRepDTO.class));
    }

    /**
     * queryList SalesDeliveryApprove from http
     */
    @Override
    public ResponseDTO<List<SalesDeliveryApproveRepDTO>> queryList(SalesDeliveryApproveQueryDTO dto) {
        SalesDeliveryApprove entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        List<SalesDeliveryApprove> entities = getService().queryByPara(entity);
        return ResponseDTO.success(ConvertUtils.convertEntityList2DTOList(entities, SalesDeliveryApproveRepDTO.class));
    }

    /**
     * Page SalesDeliveryApprove from http
     */
    @Override
    public ResponseDTO<SearchDTO<SalesDeliveryApproveRepDTO>> queryPage(SearchDTO<SalesDeliveryApproveQueryPageDTO> searchDTO) {
        Search<SalesDeliveryApprove> search = ConvertUtils.convertSearchDTO2Search(searchDTO, entityClass);
        getService().queryPageByPara(search);
        return ResponseDTO.success(ConvertUtils.convertSearch2SearchDTO(search, SalesDeliveryApproveRepDTO.class));
    }

    /**
     * Count SalesDeliveryApprove from http
     */
    @Override
    public ResponseDTO<Long> queryCount(SalesDeliveryApproveQueryDTO dto) {
        SalesDeliveryApprove entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        Long count = getService().queryCountByPara(entity);
        return ResponseDTO.success(count);
    }

    /**
     * One SalesDeliveryApprove from http
     */
    @Override
    public ResponseDTO<SalesDeliveryApproveRepDTO> queryOne(SalesDeliveryApproveQueryDTO dto) {
        SalesDeliveryApprove qEntity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        SalesDeliveryApprove rEntity = getService().queryOneByPara(qEntity);
        SalesDeliveryApproveRepDTO rDTO = ConvertUtils.convertEntity2DTO(rEntity, SalesDeliveryApproveRepDTO.class);
        return ResponseDTO.success(rDTO);
    }

    /**
     * exist SalesDeliveryApprove from http
     */
    @Override
    public ResponseDTO<Boolean> exist(SalesDeliveryApproveQueryDTO dto) {
        boolean resullt = getService().existByPara(ConvertUtils.convertDTO2Entity(dto, entityClass));
        return ResponseDTO.success(resullt);
    }
}