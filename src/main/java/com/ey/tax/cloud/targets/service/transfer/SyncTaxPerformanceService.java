package com.ey.tax.cloud.targets.service.transfer;

import com.ey.cn.tax.framework.service.BaseService;
import com.ey.tax.cloud.targets.entity.transfer.SyncTaxPerformance;

import java.util.List;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-04-23 14:08:20
 * 
 */
public interface SyncTaxPerformanceService extends BaseService<SyncTaxPerformance> {

    List<SyncTaxPerformance> selectListForMaxWeek(SyncTaxPerformance syncTaxPerformance);

}