package com.ey.tax.cloud.targets.dto.pipeline;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2025-03-04 15:06:12
 * 
 */
@Data
@Schema(description = "根据条件查询实体")
public class BusinessDataQueryDTO {
    /**
     * pipelineid COLUMN:pipeline_id
     */
    @Schema(description = "pipelineid", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String pipelineId;

    /**
     * 财年 COLUMN:fiscal_year
     */
    @Schema(description = "财年", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String fiscalYear;

    /**
     * level COLUMN:level
     */
    @Schema(description = "level", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String level;

    /**
     * SSL3 COLUMN:ssl3
     */
    @Schema(description = "SSL3", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String ssl3;

    /**
     * 城市 COLUMN:city
     */
    @Schema(description = "城市", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String city;

    /**
     * 行业部门 COLUMN:industry_sector
     */
    @Schema(description = "行业部门", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String industrySector;

    /**
     * 公司类型 COLUMN:company_type
     */
    @Schema(description = "公司类型", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Object companyType;

    /**
     * 总部所在地 COLUMN:head_office_location
     */
    @Schema(description = "总部所在地", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String headOfficeLocation;

    /**
     * 客户渠道 COLUMN:client_channel
     */
    @Schema(description = "客户渠道", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String clientChannel;

    /**
     * 地区 COLUMN:region
     */
    @Schema(description = "地区", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String region;

    /**
     * COLUMN:product_id
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String productId;

    /**
     * COLUMN:customer_classification
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String customerClassification;

    /**
     * 服务线二级 COLUMN:ssl2
     */
    @Schema(description = "服务线二级", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String ssl2;

    /**
     * 服务线1级 COLUMN:sl1
     */
    @Schema(description = "服务线1级", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String sl1;

    /**
     * level_group COLUMN:level_group
     */
    @Schema(description = "level_group", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String levelGroup;

    /**
     * 用户rankId COLUMN:group_id
     */
    @Schema(description = "用户rankId", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String groupId;

    /**
     * COLUMN:ner
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal ner;

    /**
     * COLUMN:ter
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal ter;

    /**
     * COLUMN:tag
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String tag;

    /**
     * COLUMN:user_id
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String userId;
}