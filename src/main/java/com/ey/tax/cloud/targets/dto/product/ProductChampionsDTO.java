package com.ey.tax.cloud.targets.dto.product;

import com.ey.cn.tax.framework.dto.BaseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2025-05-19 10:40:28
 * 
 */
@Data
@Schema(description = "默认实体")
public class ProductChampionsDTO extends BaseDTO {
    /**
     * COLUMN:product_id
     */
    @Schema(description = "null")
    private String productId;

    /**
     * 产品类型 COLUMN:product_type
     */
    @Schema(description = "产品类型")
    private String productType;

    /**
     * 财年 COLUMN:fiscal_year
     */
    @Schema(description = "财年")
    private String fiscalYear;

    /**
     * 用户id COLUMN:user_id
     */
    @Schema(description = "用户id")
    private String userId;

    /**
     * 状态 COLUMN:status
     */
    @Schema(description = "状态")
    private String status;
}