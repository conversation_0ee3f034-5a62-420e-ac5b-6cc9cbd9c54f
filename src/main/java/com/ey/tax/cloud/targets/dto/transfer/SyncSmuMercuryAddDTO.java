package com.ey.tax.cloud.targets.dto.transfer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-08-29 10:44:33
 * 
 */
@Data
@Schema(description = "新增实体")
public class SyncSmuMercuryAddDTO {
    /**
     * Service Line Code COLUMN:sl_code
     */
    @Schema(description = "Service Line Code", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String slCode;

    /**
     * Service Line Name COLUMN:sl_name
     */
    @Schema(description = "Service Line Name", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String slName;

    /**
     * Sub Service Line Code COLUMN:ssl_code
     */
    @Schema(description = "Sub Service Line Code", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String sslCode;

    /**
     * Sub Service Line Name COLUMN:ssl_name
     */
    @Schema(description = "Sub Service Line Name", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String sslName;

    /**
     * SMU Code COLUMN:smu_code
     */
    @Schema(description = "SMU Code", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String smuCode;

    /**
     * SMU Name COLUMN:smu_name
     */
    @Schema(description = "SMU Name", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String smuName;

    /**
     * Mercury Competency Code COLUMN:mercury_competency_code
     */
    @Schema(description = "Mercury Competency Code", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String mercuryCompetencyCode;

    /**
     * Mercury Competency Name COLUMN:mercury_competency_name
     */
    @Schema(description = "Mercury Competency Name", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String mercuryCompetencyName;

    /**
     * Country Region Code COLUMN:country_region_code
     */
    @Schema(description = "Country Region Code", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String countryRegionCode;

    /**
     * Mercury Service Code COLUMN:mercury_service_code
     */
    @Schema(description = "Mercury Service Code", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String mercuryServiceCode;

    /**
     * Mercury Service Name COLUMN:mercury_service_name
     */
    @Schema(description = "Mercury Service Name", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String mercuryServiceName;

    /**
     * COLUMN:status
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String status;
}