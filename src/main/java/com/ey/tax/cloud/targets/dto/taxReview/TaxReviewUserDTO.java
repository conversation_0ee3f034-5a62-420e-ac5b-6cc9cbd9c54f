package com.ey.tax.cloud.targets.dto.taxReview;

import com.ey.cn.tax.framework.dto.BaseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-01-30 14:57:06
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "默认实体")
public class TaxReviewUserDTO extends BaseDTO {
    /**
     * GPN COLUMN:gpn
     */
    @Schema(description = "GPN")
    private String gpn;

    /**
     * 姓名 COLUMN:name
     */
    @Schema(description = "姓名")
    private String name;

    /**
     * 邮箱 COLUMN:email
     */
    @Schema(description = "邮箱")
    private String email;

    /**
     * 服务线 COLUMN:service_line
     */
    @Schema(description = "服务线")
    private String serviceLine;

    /**
     * 级别 COLUMN:level
     */
    @Schema(description = "级别")
    private String level;
}
