package com.ey.tax.cloud.targets.service.forecast.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.ey.cn.tax.framework.context.EyUserContextHolder;
import com.ey.cn.tax.framework.exception.EyTaxBusinessException;
import com.ey.cn.tax.framework.mybatisplus.extension.service.AbstractService;
import com.ey.cn.tax.framework.utils.EmptyUtils;
import com.ey.cn.tax.framework.utils.I18nUtils;
import com.ey.tax.cloud.base.dto.BusinessFaultAddDTO;
import com.ey.tax.cloud.base.remote.fault.BaseBusFaultFeignClient;
import com.ey.tax.cloud.targets.constant.*;
import com.ey.tax.cloud.targets.entity.data.FiscalCalender;
import com.ey.tax.cloud.targets.entity.forecast.ForecastBatch;
import com.ey.tax.cloud.targets.entity.forecast.ForecastConfig;
import com.ey.tax.cloud.targets.entity.forecast.ForecastDaily;
import com.ey.tax.cloud.targets.entity.forecast.ForecastWeekly;
import com.ey.tax.cloud.targets.entity.pipeline.Pipeline;
import com.ey.tax.cloud.targets.entity.pipeline.SalesDelivery;
import com.ey.tax.cloud.targets.entity.product.Product;
import com.ey.tax.cloud.targets.mapper.forecast.ForecastBatchMapper;
import com.ey.tax.cloud.targets.service.data.FiscalCalenderService;
import com.ey.tax.cloud.targets.service.forecast.ForecastBatchService;
import com.ey.tax.cloud.targets.service.forecast.ForecastConfigService;
import com.ey.tax.cloud.targets.service.forecast.ForecastDailyService;
import com.ey.tax.cloud.targets.service.forecast.ForecastWeeklyService;
import com.ey.tax.cloud.targets.service.pipeline.SalesDeliveryService;
import com.ey.tax.cloud.targets.service.product.ProductService;
import com.ey.tax.cloud.targets.utils.ExceptionUtils;
import com.ey.tax.cloud.targets.utils.LockUtils;
import com.xxl.job.core.context.XxlJobHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-01-17 14:35:37
 *
 */
@Service
public class ForecastBatchServiceImpl extends AbstractService<ForecastBatchMapper, ForecastBatch> implements ForecastBatchService {

    /**
     * 获取forecast计算批次锁,超时时间10秒
     */
    private static final int WAIT_TIME = 10;

    /**
     * 获取到forecast计算批次锁后超时时间为60秒
     */
    private static final int LEASE_TIME = 60;

    @Lazy
    @Autowired
    private ForecastConfigService forecastConfigService;

    @Autowired
    private SalesDeliveryService salesDeliveryService;

    @Autowired
    private ProductService productService;

    @Autowired
    private FiscalCalenderService fiscalCalenderService;

    @Autowired
    private ForecastDailyService forecastDailyService;

    @Autowired
    private ForecastWeeklyService forecastWeeklyService;

    @Autowired
    protected BaseBusFaultFeignClient baseBusFaultFeignClient;

    @Transactional(readOnly = true)
    @Override
    public ForecastBatch queryCurrentBatch(ForecastBatch entity) {
        QueryWrapper<ForecastBatch> queryWrapper = Wrappers.query(entity);
        queryWrapper.in("user_id", EyUserContextHolder.get().getAuthUserId());
        queryWrapper.orderByDesc("create_time");
        List<ForecastBatch> forecastBatchList = baseMapper.selectList(queryWrapper);
        ForecastBatch forecastBatch;
        if(EmptyUtils.isEmpty(forecastBatchList)){
            forecastBatch = new ForecastBatch();
        }else{
            forecastBatch = forecastBatchList.get(0);
            if(ForecastBatchLogConstants.RUNNING.equals(forecastBatch.getStatus())){
                forecastBatch.setExecuteStatus(ForecastBatchLogConstants.EXECUTE_RUNNING);
            }else{
                forecastBatch.setExecuteStatus(ForecastBatchLogConstants.EXECUTE_END);
            }
        }
        return forecastBatch;
    }

    @Transactional(readOnly = true)
    @Override
    public boolean hasBatchRunning() {
        ForecastBatch qForecastBatch = new ForecastBatch();
        qForecastBatch.setStatus(ForecastBatchLogConstants.RUNNING);
        qForecastBatch.setUserId(EyUserContextHolder.get().getAuthUserId());
        ForecastBatch forecastBatch = this.queryOneByPara(qForecastBatch);
        if(EmptyUtils.isNotEmpty(forecastBatch)){
            return true;
        }
        return false;
    }

    @Transactional(readOnly = true)
    @Override
    public void check(){
        //查询是否有在计算中的任务
        //1.获取分布式锁
        String lockKey = CacheKeyConstants.CACHE_KEY_FORECAST_BATCH_LOCK + EyUserContextHolder.get().getAuthUserId();
        try {
            boolean flag = LockUtils.tryLock(lockKey, WAIT_TIME, LEASE_TIME);
            if(!flag){
                logger.error("try lock forecast batch error");
                String errorCode = ErrorCodeConstans.LOCK_FORECAST_BATCH_ERROR;
                throw new EyTaxBusinessException(errorCode, I18nUtils.getMessage(errorCode));
            }
            //2.查询批次
            if(this.hasBatchRunning()){
                String errorCode = ErrorCodeConstans.RUN_FORECAST_BATCH_EXIST;
                throw new EyTaxBusinessException(errorCode, I18nUtils.getMessage(errorCode));
            }
        }catch (Exception e){
            logger.error("process forecast error", e);
            if(e instanceof EyTaxBusinessException){
                throw (EyTaxBusinessException)e;
            }else{
                String errorCode = ErrorCodeConstans.CHECK_FORECAST_BATCH_ERROR;
                throw new EyTaxBusinessException(errorCode, I18nUtils.getMessage(errorCode), e);
            }
        }finally {
            //最后要释放锁
            if(LockUtils.isLocked(lockKey)){
                LockUtils.unlock(lockKey);
            }
        }
    }

    protected void submitBusinessFault(String businessType, String content) {
        try {
            BusinessFaultAddDTO businessFaultAddDTO = new BusinessFaultAddDTO();
            businessFaultAddDTO.setBusinessType(businessType);
            businessFaultAddDTO.setFaultContent(content);
            //1打开,2关闭
            businessFaultAddDTO.setFaultStatus("1");
            businessFaultAddDTO.setSystemType("targetapi");
            baseBusFaultFeignClient.add(businessFaultAddDTO);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("submit businessFault error", e);
        }
    }

    @Transactional
    @Override
    public ForecastBatch executeForecast() {
        return executeForecast(EyUserContextHolder.get().getAuthUserId());
    }

    @Transactional(rollbackFor = Throwable.class, propagation = Propagation.REQUIRES_NEW)
    @Override
    public ForecastBatch executeForecast(String userId) {
        //查询是否有在计算中的任务
        //1.获取分布式锁
        ForecastBatchService forecastBatchService = (ForecastBatchService)currentProxy();
        ForecastBatch forecastBatch;
        String lockKey = CacheKeyConstants.CACHE_KEY_FORECAST_BATCH_LOCK + userId;
        forecastBatch = new ForecastBatch();
        try {
            boolean flag = LockUtils.tryLock(lockKey, WAIT_TIME, LEASE_TIME);
            if(!flag){
                logger.error("try lock forecast batch error");
                String errorCode = ErrorCodeConstans.LOCK_FORECAST_BATCH_ERROR;
                throw new EyTaxBusinessException(errorCode, I18nUtils.getMessage(errorCode));
            }
            //2.查询批次
            if(this.hasBatchRunning()){
                String errorCode = ErrorCodeConstans.RUN_FORECAST_BATCH_EXIST;
                throw new EyTaxBusinessException(errorCode, I18nUtils.getMessage(errorCode));
            }
            //3.创建批次并设置状态为运行中
            forecastBatch.setStatus(ForecastBatchLogConstants.RUNNING);
            forecastBatch.setUserId(userId);
            forecastBatch.setStartTime(LocalDateTime.now());
            forecastBatchService.saveNewTransaction(forecastBatch);
            this.processForecast(forecastBatch);
        }catch (Exception e){
            logger.error("process forecast error", e);
            forecastBatch.setStatus(ForecastBatchLogConstants.ERROR);
            forecastBatch.setEndTime(LocalDateTime.now());
            forecastBatch.setLogText(e.getMessage());
            forecastBatchService.updateNewTransaction(forecastBatch);
            String errorMessage = ExceptionUtils.getFullStackTrace(e);
            XxlJobHelper.log("send error to admin");
            XxlJobHelper.log(errorMessage);
            submitBusinessFault(BusFaultConstants.BUS_TYPE_FORECAST_EXECUTE, "forecat execute error:" + errorMessage);
            //4.修改批次状态为异常
            if(e instanceof EyTaxBusinessException){
                throw (EyTaxBusinessException)e;
            }else{
                String errorCode = ErrorCodeConstans.RUN_FORECAST_BATCH_ERROR;
                throw new EyTaxBusinessException(errorCode, I18nUtils.getMessage(errorCode), e);
            }
        }finally {
            //最后要释放锁
            if(LockUtils.isLocked(lockKey)){
                LockUtils.unlock(lockKey);
            }
        }
        return forecastBatch;
    }

    /**
     * 1.查询要处理的pipeline
     * 1.1.查询sales中userId等于参数中的userId的pipeline
     * 1.2.pipeline分类,上年结转,本年新增,本年潜在,分类的同时构建默认config
     * 1.3.根据pipeline的id获取sales数据的amount
     *
     * 2.查询该pipeline的config
     * 2.1.根据pipeline的id获取config的数据
     * 2.2.将获取的数据和上一步中获取到的config数据合并
     *
     * 3.拆分amount到天和周
     * 3.1.根据config的配置将数据平均拆分到天
     * 3.2.根据拆分到天的数据合计到周
     *
     * @param forecastBatch
     * @return
     */
    private boolean processForecast(ForecastBatch forecastBatch){
        //1.1.查询sales中userId等于参数中的userId的pipeline
        List<Pipeline> pipelineList = salesDeliveryService.queryByUser(forecastBatch.getUserId());
        if(EmptyUtils.isNotEmpty(pipelineList)){
            Map<String, Pipeline> pipelineMap = new HashMap<>();
            //1.2.pipeline分类,上年结转,本年新增,本年潜在,分类的同时构建默认config
            Map<String, ForecastConfig> defaultForecastConfigMap = new HashMap<>();
            String currentFy = fiscalCalenderService.getCurrentFiscalCalender();
            String lastFy = fiscalCalenderService.getLastFiscalCalender();
            Product p = new Product();
            p.setStatus(1);
            List<Product> productList = productService.queryByPara(p);
            Map<String, Product> productMap = new HashMap<>();
            for (Product product : productList) {
                productMap.put(product.getProductId()+product.getFiscalYear(), product);
            }
            //财年开始日 + 往前追溯6个月
            LocalDateTime firstDayOfFiscal = fiscalCalenderService.getFirstDayOfFiscal(currentFy);
            int year = Year.from(firstDayOfFiscal).getValue();
            LocalDateTime firstDayOfYear = LocalDateTime.of(year, Month.JANUARY, 1, 0, 0);

            List<String> pipelineIds = new ArrayList<>();
            Map<String, Pipeline> lastYearPipelineMap = new HashMap<>();
            Map<String, Pipeline> newWinPipelineMap = new HashMap<>();

            List<Pipeline> halfYearPipelines = new ArrayList<>();
            List<String> halfYearPipelineIds = new ArrayList<>();

            List<Pipeline> anticipatedPipelines = new ArrayList<>();
            List<String> anticipatedPipelineIds = new ArrayList<>();

            for(Pipeline pipeline:pipelineList){
                pipelineMap.put(pipeline.getId(), pipeline);
                String wonFy = pipeline.getWonFy() + "";
                String recurring = pipeline.getRecurring();
                String status = pipeline.getStatus();
                String confirmStatus = pipeline.getConfirmStatus();
                String productId = pipeline.getProductId();
                String pursuitFy = pipeline.getPursuitFy() + "";
                Integer winRate = pipeline.getWinRate();
                LocalDateTime firstConfirmDate = pipeline.getFirstConfirmDate();
                LocalDateTime anticipatedWinDate = pipeline.getAnticipatedWinDate();
                if(lastFy.equals(wonFy) &&
                        (PipelineConstants.RECURRING_YES_NEW_WIN.equals(recurring) || PipelineConstants.RECURRING_YES_CONTINUOUS.equals(recurring)) &&
                        PipelineConstants.PIPELINE_STATUS_WON.equals(status) &&
                        (PipelineConstants.APPROVE_STATUS_PROCESSED.equals(confirmStatus) || PipelineConstants.APPROVE_STATUS_AMENDMENT_SUBMITTED.equals(confirmStatus))){
                    //上年结转
                    ForecastConfig forecastConfig = new ForecastConfig();
                    LocalDateTime forecastStartDate;
                    LocalDateTime forecastEndDate;
                    if(EmptyUtils.isEmpty(productId)){
                        if(EmptyUtils.isEmpty(pipeline.getKickoffWeek())||EmptyUtils.isEmpty(pipeline.getClosingWeek())){
                            XxlJobHelper.log("pipeline"+pipeline.getPipelineCode()+" kickoffWeek or closingWeek is null");
                            continue;
                        }
                        forecastStartDate = pipeline.getKickoffWeek();
                        forecastEndDate = pipeline.getClosingWeek();
                    }else {
                        Product product = productMap.get(productId+pipeline.getWonFy());
                        if (BooleanConstants.BOOLEAN_ON.equals(product.getIsForward())) {
                            //获取的 product.getShareDateStart()为月份，拼接当前年份转为LocalDateTime
                            int shareDateStart = product.getShareDateStart();
                            int shareDateEnd = product.getShareDateEnd();
                            int startYear = LocalDate.now().getYear();
                            int endYear = LocalDate.now().getYear();
                            if (shareDateEnd < shareDateStart) {
                                endYear = startYear + 1;
                            }
                            forecastStartDate = LocalDateTime.of(startYear, shareDateStart, 1, 0, 0);
                            forecastEndDate = LocalDateTime.of(endYear, shareDateEnd, 1, 0, 0);
                        } else {
                            if(EmptyUtils.isEmpty(pipeline.getKickoffWeek())){
                                XxlJobHelper.log("pipeline"+pipeline.getPipelineCode()+" kickoffWeek or closingWeek is null");
                                continue;
                            }
                            forecastStartDate = pipeline.getKickoffWeek();
                            if(EmptyUtils.isEmpty(pipeline.getClosingWeek())){
                              //使用kickoffWeek+6个月
                                forecastEndDate = forecastStartDate.plusMonths(6);
                            }else{
                                //如果kickoffWeek-closingWeek大于13个月则跳过
                                if(forecastStartDate.plusMonths(13).isBefore(pipeline.getClosingWeek())){
                                    continue;
                                }
                                forecastEndDate = pipeline.getClosingWeek();
                            }
                        }
                    }
                    forecastConfig.setForecastStartDate(forecastStartDate);
                    forecastConfig.setForecastEndDate(forecastEndDate);
                    forecastConfig.setConfigType(ForecastConstants.CONFIG_TYPE_LAST_YEAR);
                    forecastConfig.setPipelineId(pipeline.getId());
                    forecastConfig.setAmortize(BooleanConstants.BOOLEAN_ON);
                    forecastConfig.setFiscalYear(currentFy);
                    defaultForecastConfigMap.put(pipeline.getId(), forecastConfig);
                    pipelineIds.add(pipeline.getId());
                    lastYearPipelineMap.put(pipeline.getId(), pipeline);
                }else if(currentFy.equals(wonFy) &&
                        PipelineConstants.PIPELINE_STATUS_WON.equals(status) &&
                        (PipelineConstants.APPROVE_STATUS_PROCESSED.equals(confirmStatus)
                                || PipelineConstants.APPROVE_STATUS_AMENDMENT_SUBMITTED.equals(confirmStatus)
                                ||PipelineConstants.APPROVE_STATUS_RETURNED.equals(confirmStatus))) {
                    //本年新增
                    LocalDateTime forecastStartDate = firstConfirmDate;
                    LocalDateTime forecastEndDate = forecastStartDate.plusMonths(3);
                    ForecastConfig forecastConfig = new ForecastConfig();
                    forecastConfig.setForecastStartDate(forecastStartDate);
                    forecastConfig.setForecastEndDate(forecastEndDate);
                    forecastConfig.setConfigType(ForecastConstants.CONFIG_TYPE_NEW_WIN);
                    forecastConfig.setPipelineId(pipeline.getId());
                    forecastConfig.setAmortize(BooleanConstants.BOOLEAN_ON);
                    forecastConfig.setFiscalYear(currentFy);
                    //Completion rate
                    //计算项目预测分摊总天数：总天数 = (预测分摊截止日期 - 预测分摊起始日期) + 1
                    Duration totalDuration = Duration.between(forecastStartDate, forecastEndDate);
                    long totalDays = totalDuration.toDays() + 1;
                    //计算项目预测完成天数：完成天数 = (当前日期 - 预测分摊起始日期) + 1
                    Duration completeDuration = Duration.between(forecastStartDate, LocalDateTime.now());
                    long completeDays = completeDuration.toDays() + 1;
                    BigDecimal percentage;
                    if(completeDays >=  totalDays) {
                        percentage = new BigDecimal(100);
                    }else if(completeDays <= 0){
                        percentage = BigDecimal.ZERO;
                    }else {
                        BigDecimal bCompleteDays = new BigDecimal(completeDays);
                        BigDecimal bTotalDays = new BigDecimal(totalDays);
                        percentage = bCompleteDays
                                .divide(bTotalDays, 2, RoundingMode.HALF_UP)
                                .multiply(new BigDecimal(100))
                                .setScale(0, RoundingMode.HALF_UP);
                    }
                    //Completion rate完成率 = (完成天数 / 总天数) * 100%
                    forecastConfig.setCompletionRate(percentage);
                    defaultForecastConfigMap.put(pipeline.getId(), forecastConfig);
                    pipelineIds.add(pipeline.getId());
                    String clientProduct = pipeline.getClientCode() + pipeline.getProductId();
                    newWinPipelineMap.put(clientProduct, pipeline);
                }else if (currentFy.equals(pursuitFy) &&
                        PipelineConstants.PIPELINE_STATUS_PURSUIT.equals(status) &&
                        (EmptyUtils.isNotEmpty(winRate) && winRate >= 50)) {
                    //本年潜在
                    LocalDateTime forecastStartDate = pipeline.getAnticipatedWinDate();
                    if(EmptyUtils.isEmpty(forecastStartDate)){
                        continue;
                    }
                    LocalDateTime forecastEndDate = forecastStartDate.plusMonths(3);
                    ForecastConfig forecastConfig = new ForecastConfig();
                    forecastConfig.setForecastStartDate(forecastStartDate);
                    forecastConfig.setForecastEndDate(forecastEndDate);
                    forecastConfig.setConfigType(ForecastConstants.CONFIG_TYPE_POTENTIAL);
                    forecastConfig.setPipelineId(pipeline.getId());
                    forecastConfig.setAmortize(BooleanConstants.BOOLEAN_ON);
                    forecastConfig.setWinPossibility(new BigDecimal(pipeline.getWinRate()));
                    forecastConfig.setFiscalYear(currentFy);
                    defaultForecastConfigMap.put(pipeline.getId(), forecastConfig);
                    pipelineIds.add(pipeline.getId());
                }else if(EmptyUtils.isNotEmpty(firstConfirmDate) && firstConfirmDate.isAfter(firstDayOfYear)){
                    halfYearPipelines.add(pipeline);
                    halfYearPipelineIds.add(pipeline.getId());
                }else if(EmptyUtils.isNotEmpty(anticipatedWinDate) && anticipatedWinDate.isAfter(firstDayOfYear)){
                    anticipatedPipelines.add(pipeline);
                    anticipatedPipelineIds.add(pipeline.getId());
                }
            }
            //First_confirm_date大于追溯日期的数据
            //该Pipeline的分摊日期跨到新财年
            if(EmptyUtils.isNotEmpty(halfYearPipelineIds)){
                List<ForecastConfig> halfYearForecastConfigs = forecastConfigService.queryByPipelineIds(halfYearPipelineIds, lastFy);
                Map<String, ForecastConfig> halfYearForecastConfigMap = new HashMap<>();
                for(ForecastConfig forecastConfig:halfYearForecastConfigs){
                    halfYearForecastConfigMap.put(forecastConfig.getPipelineId(), forecastConfig);
                }
                for(Pipeline pipeline:halfYearPipelines){
                    String pipelineId = pipeline.getId();
                    //如果和上年结转重复则丢弃
                    if(EmptyUtils.isNotEmpty(lastYearPipelineMap.get(pipelineId))){
                        continue;
                    }
                    ForecastConfig halfYearForecastConfig = halfYearForecastConfigMap.get(pipelineId);
                    LocalDateTime forecastStartDate = pipeline.getFirstConfirmDate();
                    LocalDateTime forecastEndDate = pipeline.getFirstConfirmDate().plusMonths(6);
                    if(EmptyUtils.isNotEmpty(halfYearForecastConfig)){
                        forecastEndDate = halfYearForecastConfig.getForecastEndDate();
                        forecastStartDate = halfYearForecastConfig.getForecastStartDate();
                    }
                    int currentFiscalYear = Integer.parseInt(currentFy);
                    if(forecastEndDate.getYear() >= currentFiscalYear){
                        defaultForecastConfigMap.put(pipeline.getId(), halfYearForecastConfig);
                        pipelineIds.add(pipeline.getId());
                        ForecastConfig forecastConfig = new ForecastConfig();
                        forecastConfig.setForecastStartDate(forecastStartDate);
                        forecastConfig.setForecastEndDate(forecastEndDate);
                        forecastConfig.setConfigType(ForecastConstants.CONFIG_TYPE_NEW_WIN);
                        forecastConfig.setPipelineId(pipeline.getId());
                        forecastConfig.setAmortize(BooleanConstants.BOOLEAN_ON);
                        forecastConfig.setFiscalYear(currentFy);
                        //Completion rate
                        //计算项目预测分摊总天数：总天数 = (预测分摊截止日期 - 预测分摊起始日期) + 1
                        Duration totalDuration = Duration.between(forecastStartDate, forecastEndDate);
                        long totalDays = totalDuration.toDays() + 1;
                        //计算项目预测完成天数：完成天数 = (当前日期 - 预测分摊起始日期) + 1
                        Duration completeDuration = Duration.between(forecastStartDate, LocalDateTime.now());
                        long completeDays = completeDuration.toDays() + 1;
                        BigDecimal percentage;
                        if(completeDays >=  totalDays) {
                            percentage = new BigDecimal(100);
                        }else if(completeDays <= 0){
                            percentage = BigDecimal.ZERO;
                        }else {
                            BigDecimal bCompleteDays = new BigDecimal(completeDays);
                            BigDecimal bTotalDays = new BigDecimal(totalDays);
                            percentage = bCompleteDays
                                    .divide(bTotalDays, 2, RoundingMode.HALF_UP)
                                    .multiply(new BigDecimal(100))
                                    .setScale(0, RoundingMode.HALF_UP);
                        }
                        //Completion rate完成率 = (完成天数 / 总天数) * 100%
                        forecastConfig.setCompletionRate(percentage);
                        defaultForecastConfigMap.put(pipeline.getId(), forecastConfig);
                        pipelineIds.add(pipeline.getId());
                        String clientProduct = pipeline.getClientCode() + pipeline.getProductId();
                        newWinPipelineMap.put(clientProduct, pipeline);
                    }
                }
            }

            //Anticipated win date在追溯日期之后的Pipeline
            //该Pipeline的分摊日期跨到新财年
            if(EmptyUtils.isNotEmpty(anticipatedPipelineIds)){
                List<ForecastConfig> anticipatedForecastConfigs = forecastConfigService.queryByPipelineIds(anticipatedPipelineIds, lastFy);
                Map<String, ForecastConfig> anticipatedForecastConfigMap = new HashMap<>();
                for(ForecastConfig forecastConfig:anticipatedForecastConfigs){
                    anticipatedForecastConfigMap.put(forecastConfig.getPipelineId(), forecastConfig);
                }
                for(Pipeline pipeline:halfYearPipelines){
                    String pipelineId = pipeline.getId();
                    ForecastConfig halfYearForecastConfig = anticipatedForecastConfigMap.get(pipelineId);
                    LocalDateTime forecastStartDate = pipeline.getAnticipatedWinDate();
                    LocalDateTime forecastEndDate = forecastStartDate.plusMonths(6);
                    if(EmptyUtils.isNotEmpty(halfYearForecastConfig)){
                        forecastEndDate = halfYearForecastConfig.getForecastEndDate();
                        forecastStartDate = halfYearForecastConfig.getForecastStartDate();
                    }
                    ForecastConfig forecastConfig = new ForecastConfig();
                    forecastConfig.setForecastStartDate(forecastStartDate);
                    forecastConfig.setForecastEndDate(forecastEndDate);
                    forecastConfig.setConfigType(ForecastConstants.CONFIG_TYPE_POTENTIAL);
                    forecastConfig.setPipelineId(pipeline.getId());
                    forecastConfig.setAmortize(BooleanConstants.BOOLEAN_ON);
                    forecastConfig.setWinPossibility(new BigDecimal(pipeline.getWinRate()));
                    forecastConfig.setFiscalYear(currentFy);
                    defaultForecastConfigMap.put(pipeline.getId(), forecastConfig);
                    pipelineIds.add(pipeline.getId());
                }
            }

            //自动关闭规则：通过Client code+Product，判断“上年结转的数据”与“本年新增” 是否重复，重复则关闭“上年结转的数据”的摊销开关，动态判断
            for(Pipeline lastYearPipeline: lastYearPipelineMap.values()){
                String clientProduct = lastYearPipeline.getClientCode() + lastYearPipeline.getProductId();
                Pipeline newWinPipeline = newWinPipelineMap.get(clientProduct);
                if(EmptyUtils.isEmpty(newWinPipeline)){
                    continue;
                }
                ForecastConfig defaultForecastConfig = defaultForecastConfigMap.get(lastYearPipeline.getId());
                if(EmptyUtils.isNotEmpty(defaultForecastConfig)){
                    defaultForecastConfig.setAmortize(BooleanConstants.BOOLEAN_OFF);
                }
            }
            //1.3.根据pipeline的id获取sales数据的amount
            SalesDelivery qSalesDelivery = new SalesDelivery();
            qSalesDelivery.setSalesDeliveryCredit(forecastBatch.getUserId());
            qSalesDelivery.setPipelineIds(pipelineIds);
            if(EmptyUtils.isEmpty(pipelineIds)){
                processEnd(forecastBatch);
                XxlJobHelper.log("user pipeline is null end process");
                return false;
            }
            List<SalesDelivery> salesDeliveryList = salesDeliveryService.queryByPipelineAndUser(qSalesDelivery);
            Map<String, SalesDelivery> salesDeliveryMap = new HashMap<>();
            for(SalesDelivery salesDelivery:salesDeliveryList){
                salesDeliveryMap.put(salesDelivery.getPipelineId(), salesDelivery);
            }
            //2.1.根据pipeline的id获取config的数据
            List<ForecastConfig> forecastConfigs = forecastConfigService.queryByPipelineIds(pipelineIds, currentFy);
            List<String> forecastConfigIds = new ArrayList<>();
            //2.2.将获取的数据和上一步中获取到的config数据合并
            for(ForecastConfig forecastConfig:forecastConfigs){
                forecastConfigIds.add(forecastConfig.getId());
                ForecastConfig defaultForecastConfig = defaultForecastConfigMap.get(forecastConfig.getPipelineId());
                LocalDateTime forecastStartDate = forecastConfig.getForecastStartDate();
                LocalDateTime forecastEndDate = forecastConfig.getForecastEndDate();
                if(EmptyUtils.isEmpty(defaultForecastConfig)){
                    continue;
                }
                String configType = forecastConfig.getConfigType();
                if(ForecastConstants.CONFIG_TYPE_LAST_YEAR.equals(configType)){
                    if(EmptyUtils.isNotEmpty(forecastConfig.getUserId())){
                        defaultForecastConfig.setUserId(forecastConfig.getUserId());
                        defaultForecastConfig.setAmortize(forecastConfig.getAmortize());
                    }
                }else if(ForecastConstants.CONFIG_TYPE_POTENTIAL.equals(configType)){
                    defaultForecastConfig.setWinPossibility(forecastConfig.getWinPossibility());
                }else if(ForecastConstants.CONFIG_TYPE_NEW_WIN.equals(configType)){
                    //Completion rate
                    //计算项目预测分摊总天数：总天数 = (预测分摊截止日期 - 预测分摊起始日期) + 1
                    Duration totalDuration = Duration.between(forecastStartDate, forecastEndDate);
                    long totalDays = totalDuration.toDays() + 1;
                    //计算项目预测完成天数：完成天数 = (当前日期 - 预测分摊起始日期) + 1
                    Duration completeDuration = Duration.between(forecastStartDate, LocalDateTime.now());
                    long completeDays = completeDuration.toDays() + 1;
                    BigDecimal percentage;
                    if(completeDays >=  totalDays) {
                        percentage = new BigDecimal(100);
                    }else if(completeDays <= 0){
                        percentage = BigDecimal.ZERO;
                    }else {
                        BigDecimal bCompleteDays = new BigDecimal(completeDays);
                        BigDecimal bTotalDays = new BigDecimal(totalDays);
                        percentage = bCompleteDays
                                .divide(bTotalDays, 2, RoundingMode.HALF_UP)
                                .multiply(new BigDecimal(100))
                                .setScale(0, RoundingMode.HALF_UP);
                    }
                    //Completion rate完成率 = (完成天数 / 总天数) * 100%
                    defaultForecastConfig.setCompletionRate(percentage);
                }
                defaultForecastConfig.setForecastStartDate(forecastStartDate);
                defaultForecastConfig.setForecastEndDate(forecastEndDate);
            }
            forecastConfigService.realDeleteByIds(forecastConfigIds);
            forecastConfigService.save(defaultForecastConfigMap.values());
            //3.1.根据config的配置将数据平均拆分到天
            List<ForecastDaily> allForecastDaily = new ArrayList<>();
            for(Map.Entry<String, ForecastConfig> entry:defaultForecastConfigMap.entrySet()){
                String pipelineId = entry.getKey();
                ForecastConfig forecastConfig = entry.getValue();
                if(!BooleanConstants.BOOLEAN_ON.equals(forecastConfig.getAmortize())){
                    continue;
                }
                SalesDelivery salesDelivery = salesDeliveryMap.get(pipelineId);
                if(EmptyUtils.isEmpty(salesDelivery.getSalesDeliveryCreditAmount()) || salesDelivery.getSalesDeliveryCreditAmount().equals(BigDecimal.ZERO)){
                    continue;
                }
                List<ForecastDaily> forecastDailyList = splitForDaily(forecastBatch, forecastConfig, salesDelivery, pipelineMap.get(pipelineId));
                allForecastDaily.addAll(forecastDailyList);
            }

            ForecastDaily dForecastDaily = new ForecastDaily();
            dForecastDaily.setFiscalYear(currentFy);
            dForecastDaily.setPipelineIds(pipelineIds);
            dForecastDaily.setUserId(forecastBatch.getUserId());
            forecastDailyService.realDeleteByPara(dForecastDaily);
            forecastDailyService.save(allForecastDaily);
            //按周合并
            Map<Integer, ForecastWeekly> forecastWeeklyMap = new HashMap<>();
            for(ForecastDaily forecastDaily:allForecastDaily){
                Integer week = forecastDaily.getForecastWeek();
                ForecastWeekly forecastWeekly = forecastWeeklyMap.get(week);
                if(EmptyUtils.isEmpty(forecastWeekly)){
                    forecastWeekly = new ForecastWeekly();
                    forecastWeekly.setForecastWeek(week);
                    forecastWeekly.setFiscalYear(currentFy);
                    forecastWeekly.setNer(BigDecimal.ZERO);
                    forecastWeekly.setTer(BigDecimal.ZERO);
                    forecastWeekly.setUserId(forecastBatch.getUserId());
                    forecastWeeklyMap.put(week, forecastWeekly);
                }
                forecastWeekly.setNer(forecastWeekly.getNer().add(forecastDaily.getNer()));
                forecastWeekly.setTer(forecastWeekly.getTer().add(forecastDaily.getTer()));
            }

            ForecastWeekly dForecastWeekly = new ForecastWeekly();
            dForecastWeekly.setFiscalYear(currentFy);
            dForecastWeekly.setUserId(forecastBatch.getUserId());
            forecastWeeklyService.realDeleteByPara(dForecastWeekly);
            forecastWeeklyService.save(forecastWeeklyMap.values());
        }
        XxlJobHelper.log("process end");
        processEnd(forecastBatch);
        return true;
    }

    private void processEnd(ForecastBatch forecastBatch){
        //获取分布式锁
        ForecastBatchService forecastBatchService = (ForecastBatchService)currentProxy();
        String lockKey = CacheKeyConstants.CACHE_KEY_FORECAST_BATCH_LOCK + forecastBatch.getUserId();
        try {
            boolean flag = LockUtils.tryLock(lockKey, WAIT_TIME, LEASE_TIME);
            if(!flag){
                logger.error("try lock forecast batch error");
                String errorCode = ErrorCodeConstans.LOCK_FORECAST_BATCH_ERROR;
                throw new EyTaxBusinessException(errorCode, I18nUtils.getMessage(errorCode));
            }
            //2.修改批次状态
            forecastBatch.setStatus(ForecastBatchLogConstants.END);
            forecastBatch.setEndTime(LocalDateTime.now());
            this.update(forecastBatch);
        }catch (Exception e){
            logger.error("process forecast error", e);
            forecastBatch.setStatus(ForecastBatchLogConstants.ERROR);
            forecastBatch.setEndTime(LocalDateTime.now());
            forecastBatch.setLogText(e.getMessage());
            forecastBatchService.updateNewTransaction(forecastBatch);
            String errorMessage = ExceptionUtils.getFullStackTrace(e);
            XxlJobHelper.log("send error to admin");
            XxlJobHelper.log(errorMessage);
            submitBusinessFault(BusFaultConstants.BUS_TYPE_FORECAST_EXECUTE, "forecat execute error:" + errorMessage);
            if(e instanceof EyTaxBusinessException){
                throw (EyTaxBusinessException)e;
            }else{
                String errorCode = ErrorCodeConstans.RUN_FORECAST_BATCH_ERROR;
                throw new EyTaxBusinessException(errorCode, I18nUtils.getMessage(errorCode), e);
            }
        }finally {
            //最后要释放锁
            if(LockUtils.isLocked(lockKey)){
                LockUtils.unlock(lockKey);
            }
        }
    }

    private List<ForecastDaily> splitForDaily(ForecastBatch forecastBatch, ForecastConfig forecastConfig, SalesDelivery salesDelivery, Pipeline pipeline){
        //根据起止日期获取所有日期
        List<FiscalCalender> fiscalCalenderList = fiscalCalenderService.queryFiscalCalenderBetweenForForecast(forecastConfig.getForecastStartDate(),
                forecastConfig.getForecastEndDate());
        int count = fiscalCalenderList.size();
        List<ForecastDaily> forecastDailyList = new ArrayList<>();
        BigDecimal amount = salesDelivery.getSalesDeliveryCreditAmount();
        if(EmptyUtils.isEmpty(amount)){
            amount = BigDecimal.ZERO;
        }
        BigDecimal dayAmount = amount.divide(new BigDecimal(count), 2, RoundingMode.HALF_UP);
        String configType = forecastConfig.getConfigType();
        //TER：Amount
        //NER：Amount*（1- outsource %）
        BigDecimal outsource = pipeline.getOutsource();
        if(EmptyUtils.isEmpty(outsource)){
            outsource = BigDecimal.ZERO;
        }
        BigDecimal exeParam = new BigDecimal(1).subtract(outsource);
        BigDecimal ner;
        BigDecimal ter;
        if(ForecastConstants.CONFIG_TYPE_LAST_YEAR.equals(configType)){
            ter = dayAmount;
            ner = dayAmount.multiply(exeParam);
        }else if(ForecastConstants.CONFIG_TYPE_NEW_WIN.equals(configType)){
            BigDecimal completionRate = forecastConfig.getCompletionRate();
            ter = dayAmount.multiply(completionRate).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
            ner = dayAmount.multiply(exeParam).multiply(completionRate).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
        }else{
            BigDecimal winPossibility = forecastConfig.getWinPossibility();
            ter = dayAmount.multiply(winPossibility).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
            ner = dayAmount.multiply(exeParam).multiply(winPossibility).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
        }
        for(FiscalCalender fiscalCalender:fiscalCalenderList){
            //没有落在本财年内的数据丢弃
            if(!fiscalCalender.getFiscalYear().equals(fiscalCalenderService.getCurrentFiscalCalender())){
                continue;
            }
            ForecastDaily forecastDaily = new ForecastDaily();
            forecastDaily.setPipelineId(salesDelivery.getPipelineId());
            forecastDaily.setFiscalYear(fiscalCalender.getFiscalYear());
            forecastDaily.setForecastDay(fiscalCalender.getCalendarDate().atStartOfDay());
            forecastDaily.setForecastWeek(fiscalCalender.getWeek());
            forecastDaily.setTer(ter.setScale(2, RoundingMode.HALF_UP));
            forecastDaily.setNer(ner.setScale(2, RoundingMode.HALF_UP));
            forecastDaily.setApportionAmount(dayAmount);
            forecastDaily.setUserId(forecastBatch.getUserId());
            forecastDailyList.add(forecastDaily);
        }
        return forecastDailyList;
    }

    @Transactional(rollbackFor = Throwable.class, propagation = Propagation.REQUIRES_NEW)
    @Override
    public boolean updateNewTransaction(ForecastBatch entity) {
        logger.debug("update {}:{}", getEntitySimpleName(), entity);
        if (entity == null) {
            return false;
        }
        if (getId(entity) == null) {
            return false;
        }
        return baseMapper.updateById(entity) > 0;
    }

    @Transactional(rollbackFor = Throwable.class, propagation = Propagation.REQUIRES_NEW)
    @Override
    public boolean saveNewTransaction(ForecastBatch entity) {
        logger.debug("save {}:{}", getEntitySimpleName(), entity);
        if (entity == null) {
            return false;
        }
        return SqlHelper.retBool(baseMapper.insert(entity));
    }

}
