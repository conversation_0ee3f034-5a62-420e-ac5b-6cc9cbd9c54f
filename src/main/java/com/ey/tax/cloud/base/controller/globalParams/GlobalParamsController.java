package com.ey.tax.cloud.base.controller.globalParams;

import com.ey.cn.tax.framework.dto.ComboBoxDTO;
import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.entity.Search;
import com.ey.cn.tax.framework.validator.*;
import com.ey.cn.tax.framework.web.annotation.EyGetMapping;
import com.ey.cn.tax.framework.web.annotation.EyPostMapping;
import com.ey.cn.tax.framework.web.base.BaseController;
import com.ey.cn.tax.lite.admin.dto.GlobalParamsDTO;
import com.ey.cn.tax.lite.admin.dto.GlobalParamsQueryRepDTO;
import com.ey.cn.tax.lite.admin.entity.BaseGlobalParamsEntity;
import com.ey.tax.cloud.base.dto.dynamic.DownloadDTO;
import com.ey.tax.cloud.base.dto.globalParams.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-07-03 16:08:45
 * 
 */
@Tag(name="全局参数")
public interface GlobalParamsController extends BaseController<GlobalParamsDTO> {

    /**
     * add GlobalParams from http
     */
    @Operation(summary = "全局参数-查询系统下拉列表", description = "全局参数-查询系统下拉列表")
    @EyGetMapping(value = "/sysOptions.do")
    ResponseDTO<List<ComboBoxDTO<String>>> querySysOptions();

    /**
     * add GlobalParams from http
     */
    @Operation(summary = "全局参数-查询系统下拉列表", description = "全局参数-查询系统下拉列表")
    @EyGetMapping(value = "/appOptions.do")
    ResponseDTO<Map<String,List<ComboBoxDTO<String>>>> queryAppOptions();

    /**
     * add GlobalParams from http
     */
    @Operation(summary = "全局参数-新增", description = "全局参数-新增一条数据")
    @EyPostMapping(value = "/add.do")
    ResponseDTO<Void> add(@RequestBody @Validated({AddGroup.class}) GlobalParamsAddDTO dto);

    /**
     * delete GlobalParams from http
     */
    @Operation(summary = "全局参数-根据id删除", description = "全局参数-根据id删除一条数据")
    @EyPostMapping(value = "/deleteById.do")
    ResponseDTO<Void> deleteById(@RequestBody @Validated({DeleteByIdGroup.class}) GlobalParamsDeleteByIdDTO dto);

    /**
     * update GlobalParams from http
     */
    @Operation(summary = "全局参数-根据id更新", description = "全局参数-根据id更新一条数据")
    @EyPostMapping(value = "/updateById.do")
    ResponseDTO<Void> updateById(@RequestBody @Validated({UpdateByIdGroup.class}) GlobalParamsUpdateByIdDTO dto);

    /**
     * query GlobalParams from http
     */
    @Operation(summary = "全局参数-根据id查询", description = "全局参数-根据id查询数据")
    @EyPostMapping(value = "/queryById.do")
    ResponseDTO<GlobalParamsQueryRepDTO> queryById(@RequestBody @Validated({QueryByIdGroup.class}) GlobalParamsQueryByIdDTO dto);

    /**
     * Page GlobalParams from http
     */
    @Operation(summary = "全局参数-分页查询", description = "全局参数-根据条件分页查询")
    @EyPostMapping(value = "/queryPage.do")
    ResponseDTO<Search<BaseGlobalParamsEntity>> queryPage(@RequestBody @Validated({QueryPageGroup.class}) Search<GlobalParamsQueryPageDTO> searchDTO) throws Exception;

    @Operation(summary = "全局参数-下载")
    @EyPostMapping("/download")
    void download(@RequestBody @Validated DownloadDTO dto, HttpServletResponse resp);

    @Operation(summary = "全局参数-上传")
    @EyPostMapping("/upload")
    void upload(@RequestPart("file") MultipartFile file);

    @Operation(summary = "查询全局参数")
    @EyPostMapping("/webBase")
    ResponseDTO<List<GlobalParamsQueryRepDTO>> queryGlobalParam(@RequestBody GlobalParamsQueryDTO dto);

    @Operation(summary = "查询全局参数(key:value)")
    @EyPostMapping("/webBaseMap")
    ResponseDTO<Map<String,Object>> queryGlobalParamMap(@RequestBody GlobalParamsQueryDTO dto);

    @Operation(summary = "查询全局参数下拉列表(Combox)")
    @EyPostMapping("/webBaseCombox")
    ResponseDTO<List<ComboBoxDTO<Object>>> queryGlobalParamCombox(@RequestBody GlobalParamsQueryDTO dto);

}
