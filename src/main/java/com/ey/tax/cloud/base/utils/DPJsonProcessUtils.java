package com.ey.tax.cloud.base.utils;

import com.ey.tax.cloud.base.constants.DPJsonProcessConstants;
import com.ey.tax.cloud.base.entity.dynamic.ButtonConfigEntity;
import com.ey.tax.cloud.base.entity.dynamic.ColumnConfigEntity;
import com.ey.tax.cloud.base.entity.dynamic.FormConfigEntity;
import com.ey.tax.cloud.base.entity.dynamic.SearchConfigEntity;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 动态页面json处理工具
 * @Author: Stone Zhang
 * @Date: 2025/3/20 14:46
 */
@Getter
public class DPJsonProcessUtils {
    private final static Logger logger = LoggerFactory.getLogger(DPJsonProcessUtils.class);

    private final List<SearchConfigEntity> searchConfig = new ArrayList<>();
    private final List<ColumnConfigEntity> columnConfig = new ArrayList<>();
    private final List<FormConfigEntity> formConfig = new ArrayList<>();
    private final List<ButtonConfigEntity> buttonConfig = new ArrayList<>();

    /**
     * 解析动态页面 json，提取出可供控制的配置
     * @param json
     */
    public void parse(String json) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            JsonNode root = mapper.readTree(json);

            // type=tab 主容器下是标签页
            JsonNode content = root.path("content");
            String type = content.path("type").asText();
            if ("container".equalsIgnoreCase(type)) {
                // type=container 主容器下是一个容器，暂不支持
                return;
            }

            // parse 表格
            if ("table".equalsIgnoreCase(type)) {
                parseTable(content, DPJsonProcessConstants.PARENT_TYPE_PAGE, null, null);
            }

            // parse 表单
            if ("form".equalsIgnoreCase(type)) {
                parseForm(content, DPJsonProcessConstants.PARENT_TYPE_DIALOG, null, null);
            }

            // parse 标签页
            if ("tab".equalsIgnoreCase(type)) {
                parseTab(content);
            }

            // parse from表单
            JsonNode childrenNode = root.path("dialogs");
            if (childrenNode.isArray()) {
                for (JsonNode child : childrenNode) {
                    parseDialog(child);
                }
            }
            System.out.println();
        } catch (JsonProcessingException e) {
            logger.error("parse json error", e);
        }
    }

    private void parseTab(JsonNode node) {
        String type = node.path("type").asText();
        if (!"tab".equalsIgnoreCase(type)) {
            return;
        }
        // children
        JsonNode panelChild = node.path("children");
        if (!panelChild.isArray()) {
            return;
        }
        for (JsonNode child : panelChild) {
            parsePanel(child);
        }
    }

    private void parsePanel(JsonNode node) {
        String type = node.path("type").asText();
        if (!"panel".equalsIgnoreCase(type)) {
            return;
        }
        String id = node.path("id").asText();
        String label = node.path("properties").path("props").path("label").asText();
        JsonNode children = node.path("children");
        if (!children.isArray()) {
            return;
        }
        for (JsonNode child : children) {
            String childType = child.path("type").asText();
            if ("table".equalsIgnoreCase(childType)) {
                parseTable(child, DPJsonProcessConstants.PARENT_TYPE_TAB, id, label);
            } else if ("form".equalsIgnoreCase(childType)) {
                JsonNode formChildrenNode = child.path("children");
                if (!formChildrenNode.isArray()) {
                    break;
                }
                for (JsonNode formChild : formChildrenNode) {
                    parseForm(formChild, DPJsonProcessConstants.PARENT_TYPE_DIALOG, id, label);
                }
            }
        }
    }

    private void parseTable(JsonNode node, String parentType, String parentId, String parentName) {
        String type = node.path("type").asText();
        if (!"table".equalsIgnoreCase(type)) {
            return;
        }

        // 定位到search节点
        JsonNode searchNode = node.path("search");
        parseSearch(searchNode, parentType, parentId, parentName);

        // 定位到column节点
        JsonNode childrenNode = node.path("children");
        if (childrenNode.isArray()) {
            for (JsonNode child : childrenNode) {
                parseColumnConfig(child, parentType, parentId, parentName);
            }
        }

        // 定位到tableTop区域button
        JsonNode buttonsNode = node.path("buttons");
        if (buttonsNode.isArray()) {
            for (JsonNode child : buttonsNode) {
                parseButtonConfig(child, parentType, DPJsonProcessConstants.POSITION_TABLE_TOP, parentId, parentName);
            }
        }
    }

    /**
     * @param node:
     * @param parentType:
     * @description: 解析 search节点
     * @author: Stone Zhang
     * @date: 2025/3/20 15:03
     */
    private void parseSearch(JsonNode node, String parentType, String parentId, String parentName) {
        String type = node.path("type").asText();
        if (!"search".equalsIgnoreCase(type)) {
            return;
        }
        JsonNode childrenNode = node.path("children");
        if (!childrenNode.isArray()) {
            return;
        }

        // 遍历所有子元素
        for (JsonNode child : childrenNode) {
            SearchConfigEntity searchConfigEntity = new SearchConfigEntity();
            JsonNode idNode = child.path("id");
            if (!idNode.isMissingNode() && idNode.isTextual()) {
                searchConfigEntity.setId(idNode.asText());
            }
            JsonNode typeNode = child.path("type");
            if (!typeNode.isMissingNode() && typeNode.isTextual()) {
                searchConfigEntity.setType(typeNode.asText());
            }
            JsonNode propsNode = child.path("properties").path("props");
            if (!propsNode.isMissingNode()) {
                JsonNode propNode = propsNode.path("prop");
                if (!propNode.isMissingNode() && propNode.isTextual()) {
                    searchConfigEntity.setProp(propNode.asText());
                }
                JsonNode labelNode = propsNode.path("label");
                if (!labelNode.isMissingNode() && labelNode.isTextual()) {
                    searchConfigEntity.setLabel(labelNode.asText());
                }
                JsonNode placeholderNode = propsNode.path("placeholder");
                if (!placeholderNode.isMissingNode() && placeholderNode.isTextual()) {
                    searchConfigEntity.setPlaceholder(placeholderNode.asText());
                }
            }
            searchConfigEntity.setParentType(parentType);
            searchConfigEntity.setParentId(parentId);
            searchConfigEntity.setParentName(parentName);
            searchConfig.add(searchConfigEntity);
        }
    }

    /**
     * @param node:
     * @param parentType:
     * @description: 解析 Column 节点
     * @author: Stone Zhang
     * @date: 2025/3/29 21:47
     */
    private void parseColumnConfig(JsonNode node, String parentType, String parentId, String parentName) {
        String type = node.path("type").asText();
        if (!"column".equalsIgnoreCase(type)) {
            return;
        }
        ColumnConfigEntity columnConfigEntity = new ColumnConfigEntity();
        JsonNode idNode = node.path("id");
        if (!idNode.isMissingNode() && idNode.isTextual()) {
            columnConfigEntity.setId(idNode.asText());
        }
        JsonNode propsNode = node.path("properties").path("props");
        if (!propsNode.isMissingNode()) {
            JsonNode typeNode = propsNode.path("type");
            if ("operate".equalsIgnoreCase(typeNode.asText())) {
                // 处理行 Button
                JsonNode buttonNode = node.path("children");
                if (buttonNode.isArray()) {
                    for (JsonNode buttonChild : buttonNode) {
                        parseButtonConfig(buttonChild, parentType, DPJsonProcessConstants.POSITION_TABLE_OPERATE, parentId, parentName);
                    }
                }
            } else {
                JsonNode propNode = propsNode.path("prop");
                if (!propNode.isMissingNode() && propNode.isTextual()) {
                    columnConfigEntity.setProp(propNode.asText());
                }
                JsonNode labelNode = propsNode.path("label");
                if (!labelNode.isMissingNode() && labelNode.isTextual()) {
                    columnConfigEntity.setLabel(labelNode.asText());
                }
            }
        }
        columnConfigEntity.setParentType(parentType);
        columnConfigEntity.setParentId(parentId);
        columnConfigEntity.setParentName(parentName);
        columnConfig.add(columnConfigEntity);
    }

    /**
     * @param node:
     * @param parentType:
     * @param position:
     * @description: 解析 button
     * @author: Stone Zhang
     * @date: 2025/3/29 22:15
     */
    private void parseButtonConfig(JsonNode node, String parentType, String position, String parentId, String parentName) {
        String type = node.path("type").asText();
        if (!"button".equalsIgnoreCase(type)) {
            return;
        }
        ButtonConfigEntity buttonConfigEntity = new ButtonConfigEntity();
        JsonNode id = node.path("id");
        if (!id.isMissingNode() && id.isTextual()) {
            buttonConfigEntity.setId(id.asText());
        }
        JsonNode label = node.path("properties").path("props").path("label");
        if (!label.isMissingNode() && label.isTextual()) {
            buttonConfigEntity.setLabel(label.asText());
        }
        buttonConfigEntity.setPosition(position);
        buttonConfigEntity.setParentId(parentId);
        buttonConfigEntity.setParentName(parentName);
        buttonConfigEntity.setParentType(parentType);
        buttonConfig.add(buttonConfigEntity);
    }

    private void parseDialog(JsonNode node) {
        String type = node.path("type").asText();
        if (!"dialog".equalsIgnoreCase(type)) {
            return;
        }
        String id = node.path("id").asText();
        String title = node.path("properties").path("props").path("title").asText();
        JsonNode childrenNode = node.path("children");
        if (!childrenNode.isArray()) {
            return;
        }
        for (JsonNode child : childrenNode) {
            String childType = child.path("type").asText();
            if ("form".equalsIgnoreCase(childType)) {
                JsonNode formChildrenNode = child.path("children");
                if (formChildrenNode.isArray()) {
                    for (JsonNode formChild : formChildrenNode) {
                        parseForm(formChild, DPJsonProcessConstants.PARENT_TYPE_DIALOG, id, title);
                    }
                }
                // 定位到tableTop区域button
                JsonNode buttonsNode = node.path("buttons");
                if (buttonsNode.isArray()) {
                    for (JsonNode button : buttonsNode) {
                        parseButtonConfig(button, DPJsonProcessConstants.PARENT_TYPE_DIALOG, DPJsonProcessConstants.POSITION_DIALOG, id, title);
                    }
                }
            } else if ("table".equalsIgnoreCase(childType)) {
                parseTable(child, DPJsonProcessConstants.PARENT_TYPE_DIALOG, id, title);
            }
        }
    }

    private void parseForm(JsonNode node, String parentType, String parentId, String parentName) {
        String type = node.path("type").asText();
        if ("table".equalsIgnoreCase(type)) {
            parseTable(node, parentType, parentId, parentName);
        } else if ("button".equalsIgnoreCase(type) ||"input".equalsIgnoreCase(type) || "cascader".equalsIgnoreCase(type)
                || "select".equalsIgnoreCase(type) || "checkbox".equalsIgnoreCase(type) || "radio".equalsIgnoreCase(type)
                || "time".equalsIgnoreCase(type) || "date".equalsIgnoreCase(type) || "download".equalsIgnoreCase(type)
                || "upload".equalsIgnoreCase(type) || "transfer".equalsIgnoreCase(type) || "switch".equalsIgnoreCase(type)
                || "link".equalsIgnoreCase(type) || "monaco".equalsIgnoreCase(type) || "icons".equalsIgnoreCase(type)
                || "richText".equalsIgnoreCase(type)) {
            String id = node.path("id").asText();
            JsonNode propsNode = node.path("properties").path("props");
            FormConfigEntity formConfigEntity = new FormConfigEntity();
            formConfigEntity.setParentId(parentId);
            formConfigEntity.setParentName(parentName);
            formConfigEntity.setParentType(parentType);
            formConfigEntity.setId(node.path("id").asText());
            formConfigEntity.setType(type);
            formConfigEntity.setLabel(propsNode.path("label").asText());
            formConfigEntity.setProp(propsNode.path("prop").asText());
            if ("input".equalsIgnoreCase(type)) {
                formConfigEntity.setPlaceholder(propsNode.path("placeholder").asText());
            }
            if (!"button".equalsIgnoreCase(type) && !"download".equalsIgnoreCase(type) && !"link".equalsIgnoreCase(type)) {
                formConfigEntity.setDefaultValue(propsNode.path("defaultValue").asText());
            }
            formConfig.add(formConfigEntity);
        }
    }
}













