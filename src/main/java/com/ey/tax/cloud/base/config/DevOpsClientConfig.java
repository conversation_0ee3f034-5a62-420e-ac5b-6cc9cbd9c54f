package com.ey.tax.cloud.base.config;

import com.aliyun.auth.credentials.Credential;
import com.aliyun.auth.credentials.provider.StaticCredentialProvider;
import com.aliyun.sdk.service.devops20210625.AsyncClient;
import darabonba.core.client.ClientOverrideConfiguration;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

@Configuration
@RequiredArgsConstructor
public class DevOpsClientConfig {

    private final DevOpsProperties props;

    @Bean(destroyMethod = "close")
    public AsyncClient devopsClient() {
        StaticCredentialProvider provider = StaticCredentialProvider.create(
                Credential.builder()
                        .accessKeyId(props.getAccessKeyId())
                        .accessKeySecret(props.getAccessKeySecret())
                        .build());

        return AsyncClient.builder()
                .region(props.getRegion())
                .credentialsProvider(provider)
                .overrideConfiguration(
                        ClientOverrideConfiguration.create()
                                .setEndpointOverride(props.getEndpoint())
                                .setConnectTimeout(Duration.ofSeconds(30))
                ).build();
    }
}