package com.ey.tax.cloud.base.entity.resource;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ey.cn.tax.framework.mybatisplus.core.entity.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2023-08-31 15:14:13
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("base_user_role_company")
public class UserRoleCompany extends TenantEntity {
    /**
     * 用户id COLUMN:user_id
     */
    private String userId;

    /**
     * 角色id COLUMN:role_id
     */
    private String roleId;

    /**
     * 公司id COLUMN:company_id
     */
    private String companyId;
}
