package com.ey.tax.cloud.base.dto.pdfconverter;

import com.ey.cn.tax.framework.validator.BatchUpdateGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-06-28 18:19:23
 * 
 */
@Data
@Schema(description = "根据id批量更新实体")
public class PDFConverterSourceFilesBatchUpdateDTO {
    
    /**
     * 数据id
     */
    @Schema(description = "数据id")
    @NotBlank(message = "{PDFConverterSourceFilesDTO.id.NotBlank}", groups = {BatchUpdateGroup.class})
    private String id;

    /**
     * 上传文件名或文件夹路径 COLUMN:path_name
     */
    @Schema(description = "上传文件名或文件夹路径", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String pathName;

    /**
     * 父级文件夹路径 COLUMN:parent_id
     */
    @Schema(description = "父级文件夹路径", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String parentId;

    /**
     * 区分文件或文件夹 COLUMN:type
     */
    @Schema(description = "区分文件或文件夹", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer type;

    /**
     * 是否生成过pdf COLUMN:isgenerated
     */
    @Schema(description = "是否生成过pdf", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
     private Integer generateStatus;

    /**
     * 生成pdf的密码 COLUMN:password
     */
    @Schema(description = "生成pdf的密码", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String password;

    /**
     * 关联File服务的id COLUMN:related_id
     */
    @Schema(description = "关联File服务的id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String relatedId;

    /**
     * 关联项目表id COLUMN:engagement_id
     */
    @Schema(description = "关联项目表id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String engagementId;
}