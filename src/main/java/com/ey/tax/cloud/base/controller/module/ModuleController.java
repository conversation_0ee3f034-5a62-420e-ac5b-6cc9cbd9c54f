package com.ey.tax.cloud.base.controller.module;

import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.dto.SearchDTO;
import com.ey.cn.tax.framework.validator.*;
import com.ey.cn.tax.framework.web.base.BaseController;
import com.ey.tax.cloud.base.dto.module.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-10-12 15:55:08
 * 
 */
@Tag(name="")
public interface ModuleController extends BaseController<ModuleDTO> {
    /**
     * add Module from http
     */
    @Operation(summary = "新增", description = "新增一条数据")
    //@EyPostMapping(value = "/add.do")
    ResponseDTO<Void> add(@RequestBody @Validated({AddGroup.class}) ModuleAddDTO dto);

    /**
     * addList Module from http
     */
    @Operation(summary = "批量新增", description = "批量新增数据")
    //@EyPostMapping(value = "/addList.do")
    ResponseDTO<Void> addList(@RequestBody List<ModuleAddDTO> dtos);

    /**
     * delete Module from http
     */
    @Operation(summary = "根据id删除", description = "根据id删除一条数据")
    //@EyPostMapping(value = "/deleteById.do")
    ResponseDTO<Void> deleteById(@RequestBody @Validated({DeleteByIdGroup.class}) ModuleDeleteByIdDTO dto);

    /**
     * deleteList Module from http
     */
    @Operation(summary = "根据id列表删除", description = "根据id列表批量删除数据")
    //@EyPostMapping(value = "/deleteByIdList.do")
    ResponseDTO<Void> deleteByIdList(@RequestBody @Validated({DeleteByIdListGroup.class}) ModuleDeleteByIdListDTO dto);

    /**
     * update Module from http
     */
    @Operation(summary = "根据id更新", description = "根据id更新一条数据")
    //@EyPostMapping(value = "/updateById.do")
    ResponseDTO<Void> updateById(@RequestBody @Validated({UpdateByIdGroup.class}) ModuleUpdateByIdDTO dto);

    /**
     * updateBatch Module from http
     */
    @Operation(summary = "批量更新", description = "批量更新数据")
    //@EyPostMapping(value = "/updateBatch.do")
    ResponseDTO<Void> updateBatch(@RequestBody List<ModuleBatchUpdateDTO> dtos);

    /**
     * query Module from http
     */
    @Operation(summary = "根据id查询", description = "根据id查询数据")
    //@EyPostMapping(value = "/queryById.do")
    ResponseDTO<ModuleRepDTO> queryById(@RequestBody @Validated({QueryByIdGroup.class}) ModuleQueryByIdDTO dto);

    /**
     * queryByIdList Module from http
     */
    @Operation(summary = "根据id列表查询", description = "根据id列表查询数据")
    //@EyPostMapping(value = "/queryByIdList.do")
    ResponseDTO<List<ModuleRepDTO>> queryByIdList(@RequestBody @Validated({QueryByIdListGroup.class}) ModuleQueryByIdListDTO dto);

    /**
     * queryList Module from http
     */
    @Operation(summary = "查询列表", description = "查询数据列表")
    //@EyPostMapping(value = "/queryList.do")
    ResponseDTO<List<ModuleRepDTO>> queryList(@RequestBody ModuleQueryDTO dto);

    /**
     * Page Module from http
     */
    @Operation(summary = "分页查询", description = "根据条件分页查询")
    //@EyPostMapping(value = "/queryPage.do")
    ResponseDTO<SearchDTO<ModuleRepDTO>> queryPage(@RequestBody @Validated({QueryPageGroup.class}) SearchDTO<ModuleQueryPageDTO> searchDTO);

    /**
     * Count Module from http
     */
    @Operation(summary = "查询数量", description = "根据条件查询数量")
    //@EyPostMapping(value = "/queryCount.do")
    ResponseDTO<Long> queryCount(@RequestBody ModuleQueryDTO moduleDTO);

    /**
     * One Module from http
     */
    @Operation(summary = "查询单个数据", description = "根据条件查询一条数据,如果查询到多条则返回异常.")
    //@EyPostMapping(value = "/queryOne.do")
    ResponseDTO<ModuleRepDTO> queryOne(@RequestBody ModuleQueryDTO moduleDTO);

    /**
     * exist Module from http
     */
    @Operation(summary = "查询是否存在", description = "根据条件查询是否存在")
    //@EyPostMapping(value = "/exist.do")
    ResponseDTO<Boolean> exist(@RequestBody ModuleQueryDTO moduleDTO);
}