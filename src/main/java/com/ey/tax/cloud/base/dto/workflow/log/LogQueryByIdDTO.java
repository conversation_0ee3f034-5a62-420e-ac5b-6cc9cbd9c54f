package com.ey.tax.cloud.base.dto.workflow.log;

import com.ey.cn.tax.framework.validator.QueryByIdGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2025-03-10 15:28:30
 * 
 */
@Data
@Schema(description = "根据id查询实体")
public class LogQueryByIdDTO {
    
    /**
     * 数据id
     */
    @Schema(description = "数据id")
    @NotBlank(message = "{LogDTO.id.NotBlank}", groups = {QueryByIdGroup.class})
    private String id;
}