package com.ey.tax.cloud.base.dto.dynamic;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2023-09-09 22:38:04
 * 
 */
@Data
@Schema(description = "根据条件分页查询实体")
public class DynamicPageOptionsQueryPageDTO {
    /**
     * 子应用 COLUMN:sub_app_code
     */
    @Schema(description = "子应用", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String subAppCode;

    /**
     * 数据名称 COLUMN:data_name
     */
    @Schema(description = "数据名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String dataName;

    /**
     * 数据类型，sql, json COLUMN:data_type
     */
    @Schema(description = "数据类型，sql, json", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String dataType;

    /**
     * 数据配置 COLUMN:data_config
     */
    @Schema(description = "数据配置", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String dataConfig;

    /**
     * 唯一标识 COLUMN:data_code
     */
    @Schema(description = "唯一标识", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String dataCode;

    /**
     * 系统编码 COLUMN:data_code
     */
    @Schema(description = "系统编码", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String sysCode;

    /**
     * 应用编码 COLUMN:data_code
     */
    @Schema(description = "应用编码", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String appCode;

}
