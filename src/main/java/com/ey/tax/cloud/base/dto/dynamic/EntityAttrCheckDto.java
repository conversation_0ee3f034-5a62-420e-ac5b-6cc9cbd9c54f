package com.ey.tax.cloud.base.dto.dynamic;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Auther: Dylan DY Li
 * @Date: 2024/3/18 3:08 PM
 * @description:
 */

@Data
@Schema(name = "VariableAttrCheckDto", description = "动态页面规则定义")
public class EntityAttrCheckDto {

    /**
     * 表字段
     */
    private String field;

    /**
     * 格式校验 校验表达式
     */
    private String checkExpression;

    /**
     * 非空校验
     */
    private Boolean emptyCheck;

    /**
     * 唯一性校验
     */
    private Boolean uniqueCheck;

    /**
     * 数据校验范围
     */
    private String dataSetCheck;

    /**
     * 自定义规则校验(Groovy脚本)
     */
    private String variableAttrCheckScript;

    /**
     * 格式校验 - 提示语
     */
    private String checkExpressionMsg;

    /**
     * 非空校验 - 提示语
     */
    private String emptyCheckMsg;

    /**
     * 唯一性校验 - 提示语
     */
    private String uniqueCheckMsg;

    /**
     * 数据校验范围 - 提示语
     */
    private String dataSetCheckMsg;
}
