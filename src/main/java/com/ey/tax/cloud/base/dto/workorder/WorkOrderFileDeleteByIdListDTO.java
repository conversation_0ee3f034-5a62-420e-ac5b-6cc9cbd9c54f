package com.ey.tax.cloud.base.dto.workorder;

import com.ey.cn.tax.framework.validator.DeleteByIdListGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-05-07 17:52:06
 * 
 */
@Data
@Schema(description = "根据id列表删除实体")
public class WorkOrderFileDeleteByIdListDTO {
    
    /**
     * 数据id列表
     */
    @Schema(description = "数据id列表")
    @NotNull(message = "{WorkOrderFileDTO.ids.NotNull}", groups = {DeleteByIdListGroup.class})
    @Size(min = 1, message = "{WorkOrderFileDTO.ids.Size}", groups = {DeleteByIdListGroup.class})
    private List<String> ids;
}