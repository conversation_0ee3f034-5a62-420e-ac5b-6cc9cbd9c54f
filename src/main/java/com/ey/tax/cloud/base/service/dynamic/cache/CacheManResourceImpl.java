package com.ey.tax.cloud.base.service.dynamic.cache;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ey.cn.tax.framework.utils.ConvertUtils;
import com.ey.tax.cloud.app.mapper.roleResource.AppRoleResourceMapper;
import com.ey.tax.cloud.base.dto.dynamic.ResourceDTO;
import com.ey.tax.cloud.base.dto.dynamic.RoleResourceDTO;
import com.ey.tax.cloud.base.dto.resource.ButtonResource;
import com.ey.tax.cloud.base.entity.resource.ManResource;
import com.ey.tax.cloud.base.mapper.resource.ManResourceMapper;
import com.ey.tax.cloud.base.mapper.resource.ManRoleResMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

@Component
public class CacheManResourceImpl implements CacheResourceInterface {

    @Autowired
    ManResourceMapper resourceMapper;

    @Autowired
    ManRoleResMapper roleResourceMapper;

    @Override
    public ButtonResource getResource(String sysCode,String appCode, Set<String> range) {

        if(range.contains(rang_man_sys)){
            // 租户端系统资源
            List<ResourceDTO> resources = resourceMapper.selectButtons();
            // 租户端角色资源
            List<RoleResourceDTO> roleResources = roleResourceMapper.selectRoleButtonResource();
            // 租户端角色资源
            return ButtonResource.builder().resources(resources).roleResources(roleResources).build();
        }
        return null;
    }
}
