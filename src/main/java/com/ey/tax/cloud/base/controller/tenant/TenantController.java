package com.ey.tax.cloud.base.controller.tenant;

import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.dto.TenantDTO;
import com.ey.cn.tax.framework.web.annotation.EyPostMapping;
import com.ey.cn.tax.framework.web.base.BaseController;
import com.ey.tax.cloud.base.dto.tenant.TenantQueryDictDTO;
import com.ey.tax.cloud.base.dto.tenant.TenantRepDictDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2023-09-07 12:50:52
 *
 */
@Tag(name="租户管理")
public interface TenantController extends BaseController<TenantDTO> {
    /**
     * 查询租户字典
     */
    @Operation(summary = "查询租户字典", description = "根据租户字典用于构建下拉菜单")
    @EyPostMapping(value = "/queryDictionary.do")
    ResponseDTO<List<TenantRepDictDTO>> queryDictionary(@RequestBody TenantQueryDictDTO tenantDTO);

}
