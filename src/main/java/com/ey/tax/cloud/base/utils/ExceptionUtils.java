package com.ey.tax.cloud.base.utils;

import com.ey.cn.tax.framework.utils.EmptyUtils;
import org.springframework.jdbc.UncategorizedSQLException;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * 格式化异常
 * <AUTHOR>
 *
 */
public class ExceptionUtils {

	/**
	 * 获得异常message,如果message为空则返回stackTrace
	 * @param throwable
	 * @return
	 */
	public static String getErrorMessage(Throwable throwable){
		String errorMessage = throwable.getMessage();
		if(errorMessage == null){
			errorMessage = getStackTrace(throwable);
		}
		return errorMessage;
	}

	/**
	 * 获得异常message,如果message为空则返回固定长度stackTrace
	 * @param throwable
	 * @return
	 */
	public static String getErrorMessage(Throwable throwable,int limit){
		String errorMessage = throwable.getMessage();
		if(errorMessage == null){
			errorMessage = getStackTrace(throwable,limit);
		}
		return errorMessage;
	}

	/**
	 * 获得异常栈信息
	 * @param throwable 异常
	 * @return String 异常堆栈
	 */
	public static String getStackTrace(Throwable throwable){
		StringWriter sw = new StringWriter();
		throwable.printStackTrace(new PrintWriter(sw));
		return sw.toString();
	}

	/**
	 * 获得异常栈信息
	 * @param throwables 异常
	 * @return String 异常堆栈
	 */
	public static List<String> getStackTrace(List<Throwable> throwables){
		List<String> stackTraces = new ArrayList<String>();
		if(throwables == null || throwables.size() <= 0){
			return null;
		}
		for(Throwable throwable:throwables){
			stackTraces.add(getStackTrace(throwable));
		}
		return stackTraces;
	}

	/**
	 * 获得异常栈信息
	 * @param throwable 异常
	 * @param limit 长度
	 * @return String 异常堆栈
	 */
	public static String getStackTrace(Throwable throwable,int limit){
		StringWriter sw = new StringWriter();
		throwable.printStackTrace(new PrintWriter(sw));
		String errorMessage = sw.toString();
		if(errorMessage != null && errorMessage.length() > limit){
			errorMessage = errorMessage.substring(0,limit);
		}
		return errorMessage;
	}

	/**
	 * 获得异常栈信息
	 * @param throwable 异常
	 * @return String 异常堆栈
	 */
	public static String getFullStackTrace(Throwable throwable){
		StringWriter sw = new StringWriter();
		throwable.printStackTrace(new PrintWriter(sw));
		String errorMessage = sw.toString();
		return errorMessage;
	}

	/**
	 * 获取SQLException Message
	 * @param throwable
	 * @return
	 */
	public static String getSimpleSQLExceptionErrorMessage(Throwable throwable){
		String errorMessage = null;
		if(throwable instanceof UncategorizedSQLException){
			UncategorizedSQLException uncategorizedSQLException = (UncategorizedSQLException)throwable;
			SQLException sqlException = uncategorizedSQLException.getSQLException();
			if(EmptyUtils.isNotEmpty(sqlException)){
				errorMessage = sqlException.getMessage();
			}
		}
		return errorMessage;
	}

}
