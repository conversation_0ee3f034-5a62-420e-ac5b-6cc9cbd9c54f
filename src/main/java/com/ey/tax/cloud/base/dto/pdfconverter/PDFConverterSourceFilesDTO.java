package com.ey.tax.cloud.base.dto.pdfconverter;

import com.ey.cn.tax.framework.dto.BaseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-06-28 18:19:23
 * 
 */
@Data
@Schema(description = "默认实体")
public class PDFConverterSourceFilesDTO extends BaseDTO {
    /**
     * 上传文件名或文件夹路径 COLUMN:path_name
     */
    @Schema(description = "上传文件名或文件夹路径")
    private String pathName;

    /**
     * 父级文件夹路径 COLUMN:parent_id
     */
    @Schema(description = "父级文件夹路径")
    private String parentId;

    /**
     * 区分文件或文件夹 COLUMN:type
     */
    @Schema(description = "区分文件或文件夹 ")
    private Integer type;

    /**
     * 是否生成过pdf COLUMN:isgenerated
     */
    @Schema(description = "是否生成过pdf")
     private Integer generateStatus;

    /**
     * 生成pdf的密码 COLUMN:password
     */
    @Schema(description = "生成pdf的密码")
    private String password;

    /**
     * 关联File服务的id COLUMN:related_id
     */
    @Schema(description = "关联File服务的id")
    private String relatedId;

    /**
     * 关联项目表id COLUMN:engagement_id
     */
    @Schema(description = "关联项目表id")
    private String engagementId;
}