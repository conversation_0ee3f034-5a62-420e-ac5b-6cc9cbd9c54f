package com.ey.tax.cloud.base.dto.workflow.item;

import com.ey.cn.tax.framework.dto.BaseRepDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2025-03-10 15:28:31
 * 
 */
@Data
@Schema(description = "查询返回实体")
public class ItemRepDTO extends BaseRepDTO {
    /**
     * 流程定义id COLUMN:flow_define_id
     */
    @Schema(description = "流程定义id")
    private String flowDefineId;

    /**
     * 流程模型id COLUMN:flow_model_id
     */
    @Schema(description = "流程模型id")
    private String flowModelId;

    /**
     * 来源引用 COLUMN:source_ref
     */
    @Schema(description = "来源引用")
    private String sourceRef;

    /**
     * 目标引用 COLUMN:target_ref
     */
    @Schema(description = "目标引用")
    private String targetRef;

    /**
     * item类型 COLUMN:item_type
     */
    @Schema(description = "item类型")
    private String itemType;

    /**
     * 节点属性 COLUMN:item_attribute
     */
    @Schema(description = "节点属性")
    private Object itemAttribute;

    /**
     * 组件id COLUMN:item_id
     */
    @Schema(description = "组件id")
    private String itemId;

    /**
     * 子类型 COLUMN:sub_type
     */
    @Schema(description = "子类型")
    private String subType;

    /**
     * 元素名称 COLUMN:item_name
     */
    @Schema(description = "元素名称")
    private String itemName;
}