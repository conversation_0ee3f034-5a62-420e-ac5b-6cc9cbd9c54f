package com.ey.tax.cloud.base.dto.workflow.log;

import com.ey.cn.tax.framework.dto.BaseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2025-03-10 15:28:30
 * 
 */
@Data
@Schema(description = "默认实体")
public class LogDTO extends BaseDTO {
    /**
     * 流程任务id COLUMN:flow_task_id
     */
    @Schema(description = "流程任务id")
    private String flowTaskId;

    /**
     * 流程实例id COLUMN:flow_instance_id
     */
    @Schema(description = "流程实例id")
    private String flowInstanceId;

    /**
     * 操作类型 COLUMN:operation_type
     */
    @Schema(description = "操作类型")
    private String operationType;

    /**
     * 元素id COLUMN:item_id
     */
    @Schema(description = "元素id")
    private String itemId;

    /**
     * 全局参数 COLUMN:global_variable
     */
    @Schema(description = "全局参数")
    private Object globalVariable;

    /**
     * 节点参数 COLUMN:item_variable
     */
    @Schema(description = "节点参数")
    private Object itemVariable;

    /**
     * 元素类型 COLUMN:item_type
     */
    @Schema(description = "元素类型")
    private String itemType;

    /**
     * 表达式 COLUMN:expression
     */
    @Schema(description = "表达式")
    private String expression;

    /**
     * 表达式执行结果 COLUMN:express_result
     */
    @Schema(description = "表达式执行结果")
    private String expressResult;

    /**
     * 执行状态 COLUMN:execute_status
     */
    @Schema(description = "执行状态")
    private String executeStatus;

    /**
     * 异常文本 COLUMN:exception_txt
     */
    @Schema(description = "异常文本")
    private String exceptionTxt;

    /**
     * 步骤序号 COLUMN:flow_step
     */
    @Schema(description = "步骤序号")
    private Integer flowStep;
}