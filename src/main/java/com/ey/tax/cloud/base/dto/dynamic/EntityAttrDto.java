package com.ey.tax.cloud.base.dto.dynamic;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @auther: <PERSON>
 * @date: 2022/2/13 11:34
 * @description: 动态页面变量属性
 */
@Data
@Schema(name = "VariableAttrDto", description = "动态页面数据集属性")
public class EntityAttrDto {
    /**
     * 表字段
     */
    private String field;
    /**
     * 属性名称
     */
    private String attrName;
    /**
     * 属性描述
     */
    private String attrDesc;
    /**
     * 属性类型
     */
    private String attrType;
    /**
     * 标头标志（导入到处文件字段头标识）
     */
    private String headerFlag;

    /**
     * 是否在table中显示
     */
    private Boolean isTableShow = true;
}
