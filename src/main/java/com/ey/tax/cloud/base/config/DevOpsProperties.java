package com.ey.tax.cloud.base.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties("aliyun.devops")
public class DevOpsProperties {

    private String region = "cn-hangzhou";
    /**
     * #Endpoint 请参考 https://api.aliyun.com/product/devops
     */
    private String endpoint = "devops.cn-hangzhou.aliyuncs.com";
    /**
     * AccessKey ID
     */
    private String accessKeyId;
    /**
     * AccessKey Secret
     */
    private String accessKeySecret;
    /**
     * 企业唯一标识
     */
    private String organizationId;
}