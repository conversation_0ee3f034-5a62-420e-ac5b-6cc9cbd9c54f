package com.ey.tax.cloud.base.service.app.clone;

import com.ey.tax.cloud.base.dto.AppBaseCloneFeignDTO;
import com.ey.tax.cloud.base.service.app.AppCloneService;

public interface BaseTableClone {

    void clone(AppBaseCloneFeignDTO dto);

    BaseTableClone getBaseTableClone() ;

    void setBaseTableClone(BaseTableClone baseTableClone);

    AppCloneService getAppCloneService();

    void setAppCloneService(AppCloneService appCloneService);
}
