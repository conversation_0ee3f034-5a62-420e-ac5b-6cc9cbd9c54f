package com.ey.tax.cloud.base.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.core.env.Environment;

/**
 * <p>
 * 工单文件存储路径配置
 * </p>
 *
 * <AUTHOR>
 * @date 12:04 2022/10/17
 */
@Data
@Component
@ConfigurationProperties(prefix = "ey.path")
public class PathConfig {

    /**
     * 工单上传附件路径
     */
    private String workorder;
    /**
     * 工单临时文件路径
     */
    private String temp;

    /**
     * pdf 存储路径
     */
    @Value("${nasData:/datas}")
    private String nasData;

    @Value("${ey.nas.register.enabled:/false}")
    private String nasSwitch;
}
