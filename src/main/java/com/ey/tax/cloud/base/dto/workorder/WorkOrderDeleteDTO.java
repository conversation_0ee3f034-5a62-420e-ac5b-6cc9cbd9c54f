package com.ey.tax.cloud.base.dto.workorder;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-02-24 20:13:47
 * 
 */
@Data
@Schema(description = "根据条件删除实体")
public class WorkOrderDeleteDTO {
    /**
     * 业务模块 COLUMN:business_module
     */
    @Schema(description = "业务模块", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String businessModule;

    /**
     * 标题 COLUMN:wo_title
     */
    @Schema(description = "标题", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String woTitle;

    /**
     * 描述 COLUMN:wo_describe
     */
    @Schema(description = "描述", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String woDescribe;

    /**
     * 提交人ID COLUMN:wo_submit_uid
     */
    @Schema(description = "提交人ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String woSubmitUid;

    /**
     * 提交人邮箱 COLUMN:wo_submit_email
     */
    @Schema(description = "提交人邮箱", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String woSubmitEmail;

    /**
     * 文件路径 COLUMN:wo_file_path
     */
    @Schema(description = "文件路径", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String woFilePath;

    /**
     * 文件名 COLUMN:wo_file_name
     */
    @Schema(description = "文件名", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String woFileName;

    /**
     * 文件类型 COLUMN:wo_file_type
     */
    @Schema(description = "文件类型", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String woFileType;

    /**
     * Submitted 已提交 Processing 处理中 ProcessingCompleted 处理完成 Close 关闭 COLUMN:wo_state
     */
    @Schema(description = "Submitted 已提交 Processing 处理中 ProcessingCompleted 处理完成 Close 关闭", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String woState;

    /**
     * 最后操作人 COLUMN:wo_last_operator_uid
     */
    @Schema(description = "最后操作人", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String woLastOperatorUid;

    /**
     * 最后操作人邮箱 COLUMN:wo_last_operator_email
     */
    @Schema(description = "最后操作人邮箱", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String woLastOperatorEmail;
}