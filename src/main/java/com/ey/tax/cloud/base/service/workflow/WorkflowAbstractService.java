package com.ey.tax.cloud.base.service.workflow;

import com.ey.cn.tax.framework.utils.EmptyUtils;
import com.ey.tax.cloud.base.constants.WorkflowConstants;
import com.ey.tax.cloud.base.entity.workflow.*;
import com.ey.tax.cloud.base.service.workflow.impl.WorkflowAsyncTask;
import com.ey.tax.cloud.base.utils.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.List;
import java.util.Map;

public abstract class WorkflowAbstractService<T extends WorkflowNode> {

    @Autowired
    protected LogService logService;

    @Autowired
    protected VariableService variableService;

    @Autowired
    protected InstanceService instanceService;

    @Autowired
    protected ItemService itemService;

    @Autowired
    protected TaskService taskService;

    @Autowired
    protected AttachmentService attachmentService;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    @Qualifier("WorkflowTaskExecutor")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    protected Map<String, Object> getInstanceParameters(String workflowInstanceId) {
        Variable qVariable = new Variable();
        qVariable.setFlowInstanceId(workflowInstanceId);
        Variable variable = variableService.queryOneByPara(qVariable);
        return variable.getVariableValue();
    }

    public List<T> createWorkflowNode(Item item, Instance instance, Map<String, Object> parameters) {
        instance.setFlowStep(instance.getFlowStep() + 1);
        instanceService.update(instance);
        List<T> tlist = create(item, instance, parameters);
        String executeType = (String)item.getItemAttribute().get(WorkflowConstants.SERVICE_TASK_PROPERTY_EXECUTE_MODE);
        for(T t : tlist){
            Log log = new Log();
            log.setFlowStep(instance.getFlowStep());
            log.setFlowInstanceId(instance.getId());
            log.setItemType(item.getItemType());
            log.setSubType(item.getSubType());
            log.setExecuteType(executeType);
            log.setLogType(WorkflowConstants.LOG_TYPE_CREATE);
            logService.save(log);
            t.setWorkflowInstanceId(instance.getId());
            if(EmptyUtils.isEmpty(t.getFlowStep())){
                t.setFlowStep(instance.getFlowStep());
            }
        }
        return tlist;
    }

    public abstract T build(Item item, Instance instance, Approve approve);

    public T buildWorkflowNode(Item item, Instance instance, Approve approve) {
        T t = build(item, instance, approve);
        t.setWorkflowInstanceId(instance.getId());
        return t;
    }

    public abstract List<T> create(Item item, Instance instance, Map<String, Object> parameters);

    public ExecuteResult executeWorkflowNode(Item item, Instance instance, T t, Approve approve) {
        ExecuteResult executeResult;
        try {
            executeResult = execute(item, instance, t, approve);
        }catch (Exception e){
            Log log = new Log();
            log.setFlowStep(instance.getFlowStep());
            log.setFlowInstanceId(t.getWorkflowInstanceId());
            log.setItemType(item.getItemType());
            log.setSubType(item.getSubType());
            log.setOperationType(approve.getApproveType());
            log.setGlobalVariable(approve.getParameters());
            log.setExceptionTxt(ExceptionUtils.getFullStackTrace(e));
            log.setLogType(WorkflowConstants.LOG_TYPE_ERROR);
            String executeType = (String)item.getItemAttribute().get(WorkflowConstants.SERVICE_TASK_PROPERTY_EXECUTE_MODE);
            log.setExecuteType(executeType);
            logService.saveLog(log);
            throw e;
        }
        List<Item> nextItemList = executeResult.getNextItemList();
        if(EmptyUtils.isEmpty(nextItemList)
                && !WorkflowConstants.ITEM_SUB_TYPE_END_EVENT.equals(item.getSubType())
                && WorkflowConstants.NEXT_NODE_SUCCESS.equals(executeResult.getExecuteStatus())){
            //多分支汇集到一个节点,需要判断是否要往下走
            //TODO
            nextItemList = itemService.queryNextByItemRef(item.getItemRef());
        }
        executeResult.setNextItemList(nextItemList);
        return executeResult;
    }

    public abstract ExecuteResult execute(Item item, Instance instance, T t, Approve approve);

    public void beforeWorkflowNode(Item item, Instance instance, T t, Approve approve) {
        Log log = new Log();
        log.setFlowStep(instance.getFlowStep());
        log.setFlowInstanceId(t.getWorkflowInstanceId());
        log.setItemType(item.getItemType());
        log.setSubType(item.getSubType());
        log.setGlobalVariable(approve.getParameters());
        log.setOperationType(approve.getApproveType());
        log.setGlobalVariable(approve.getParameters());
        log.setLogType(WorkflowConstants.LOG_TYPE_START);
        String executeType = (String)item.getItemAttribute().get(WorkflowConstants.SERVICE_TASK_PROPERTY_EXECUTE_MODE);
        log.setExecuteType(executeType);
        before(item, instance, t, log, approve);
        logService.save(log);
    }

    public void before(Item item, Instance instance, T t, Log log, Approve approve) {
        // sub class can override properties
    }

    public void afterWorkflowNode(Item item, Instance instance, T t, Approve approve) {
        Log log = new Log();
        log.setFlowStep(instance.getFlowStep());
        log.setFlowInstanceId(t.getWorkflowInstanceId());
        log.setItemType(item.getItemType());
        log.setSubType(item.getSubType());
        log.setGlobalVariable(approve.getParameters());
        log.setOperationType(approve.getApproveType());
        log.setLogType(WorkflowConstants.LOG_TYPE_END);
        String executeType = (String)item.getItemAttribute().get(WorkflowConstants.SERVICE_TASK_PROPERTY_EXECUTE_MODE);
        log.setExecuteType(executeType);
        after(item, instance, t, log, approve);
        logService.save(log);
    }

    public void after(Item item, Instance instance, T t, Log log, Approve approve) {
        // sub class can override properties
    }

    public void aSyncExecute(Item item, Instance instance, T t, Approve approve){
        //构建线程任务,通过线程池运行
        //线程任务回调流程引擎
        WorkflowAsyncTask workflowAsyncTask = new WorkflowAsyncTask(item, instance, t,
                approve, applicationContext);
        threadPoolTaskExecutor.execute(workflowAsyncTask);
    }

}
