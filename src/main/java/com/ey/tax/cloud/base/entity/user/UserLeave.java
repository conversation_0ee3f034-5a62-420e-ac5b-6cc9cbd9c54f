package com.ey.tax.cloud.base.entity.user;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ey.cn.tax.framework.mybatisplus.core.entity.BaseEntity;
import java.time.LocalDateTime;
import java.util.List;

import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-11-04 15:59:52
 * 
 */
@Data
@TableName("base_user_leave")
public class UserLeave extends BaseEntity {
    /**
     * 租户id COLUMN:tenant_id
     */
    private String tenantId;

    /**
     * 用户id COLUMN:user_id
     */
    private String userId;

    @TableField(exist = false)
    private List<String> userIds;

    /**
     * 开始时间 COLUMN:start_time
     */
    private LocalDateTime startTime;

    /**
     * 结束时间 COLUMN:end_time
     */
    private LocalDateTime endTime;

    /**
     * 离开类型 COLUMN:leave_type
     */
    private String leaveType;

    /**
     * 状态内容 COLUMN:remark
     */
    private String remark;
}