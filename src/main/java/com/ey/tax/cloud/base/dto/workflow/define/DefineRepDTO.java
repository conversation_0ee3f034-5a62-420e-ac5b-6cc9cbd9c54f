package com.ey.tax.cloud.base.dto.workflow.define;

import com.ey.cn.tax.framework.dto.BaseRepDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2025-03-10 15:28:32
 * 
 */
@Data
@Schema(description = "查询返回实体")
public class DefineRepDTO extends BaseRepDTO {
    /**
     * 子系统code COLUMN:sys_code
     */
    @Schema(description = "子系统code")
    private String sysCode;

    /**
     * 应用code COLUMN:app_code
     */
    @Schema(description = "应用code")
    private String appCode;

    /**
     * 流程状态,未部署,已部署 COLUMN:flow_status
     */
    @Schema(description = "流程状态,未部署,已部署")
    private String flowStatus;

    /**
     * 流程名称 COLUMN:flow_name
     */
    @Schema(description = "流程名称")
    private String flowName;

    /**
     * 工作流定义code COLUMN:flow_code
     */
    @Schema(description = "工作流定义code")
    private String flowCode;

    /**
     * 流程类型 COLUMN:flow_type
     */
    @Schema(description = "流程类型")
    private String flowType;

    /**
     * 备注 COLUMN:remark
     */
    @Schema(description = "备注", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String remark;

    /**
     * 备注 COLUMN:enable
     */
    @Schema(description = "是否禁用", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String enable;
}