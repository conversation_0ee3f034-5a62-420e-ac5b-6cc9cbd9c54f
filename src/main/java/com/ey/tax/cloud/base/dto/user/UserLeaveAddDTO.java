package com.ey.tax.cloud.base.dto.user;

import com.ey.cn.tax.framework.validator.AddGroup;
import com.ey.cn.tax.framework.validator.DeleteByIdGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-11-04 15:59:52
 * 
 */
@Data
@Schema(description = "新增实体")
public class UserLeaveAddDTO {

    /**
     * 数据id
     */
    @Schema(description = "数据id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @NotBlank(message = "{UserLeaveDTO.id.NotBlank}", groups = {DeleteByIdGroup.class})
    private String id;

    /**
     * 租户id COLUMN:tenant_id
     */
    @Schema(description = "租户id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String tenantId;

    /**
     * 用户id COLUMN:user_id
     */
    @Schema(description = "用户id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String userId;

    /**
     * 开始时间 COLUMN:start_time
     */
    @Schema(description = "开始时间", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @NotNull(message = "{UserLeave.startTime.NotNull}", groups = {AddGroup.class})
    private LocalDateTime startTime;

    /**
     * 结束时间 COLUMN:end_time
     */
    @Schema(description = "结束时间", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @NotNull(message = "{UserLeave.endTime.NotNull}", groups = {AddGroup.class})
    private LocalDateTime endTime;

    /**
     * 结束时间 COLUMN:end_time
     */
    @Schema(description = "状态内容", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @NotNull(message = "{UserLeave.remark.NotBlank}", groups = {AddGroup.class})
    private String remark;

    /**
     * 离开类型 COLUMN:leave_type
     */
    @Schema(description = "离开类型", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String leaveType;
}