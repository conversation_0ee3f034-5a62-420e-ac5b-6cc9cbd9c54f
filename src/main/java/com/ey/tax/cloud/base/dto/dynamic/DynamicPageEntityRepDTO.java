package com.ey.tax.cloud.base.dto.dynamic;

import com.ey.cn.tax.framework.dto.BaseRepDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-07-08 09:45:40
 * 
 */
@Data
@Schema(description = "查询返回实体")
public class DynamicPageEntityRepDTO extends BaseRepDTO {
    /**
     * 系统编码（none/ifs/tpd） COLUMN:sys_code
     */
    @Schema(description = "系统编码（none/ifs/tpd）")
    private String sysCode;

    /**
     * 应用编码(none/app_yizh/app_uuzy) COLUMN:app_code
     */
    @Schema(description = "应用编码(none/app_yizh/app_uuzy)")
    private String appCode;

    /**
     * 编码 COLUMN:entity_code
     */
    @Schema(description = "编码")
    private String entityCode;

    /**
     * 名称 COLUMN:entity_name
     */
    @Schema(description = "名称")
    private String entityName;

    /**
     * Read 只读 ReadWrite 读写 COLUMN:purpose
     */
    @Schema(description = "Read 只读 ReadWrite 读写")
    private String purpose;

    /**
     * SQL配置 COLUMN:config
     */
    @Schema(description = "SQL配置")
    private String config;

    /**
     * 表名 COLUMN:table_name
     */
    @Schema(description = "表名")
    private String tableName;

    /**
     * 唯一校验规则 COLUMN:unique_rule
     */
    @Schema(description = "唯一校验规则")
    private String uniqueRule;

    /**
     * 聚合sql配置 COLUMN:aggregation_cfg
     */
    @Schema(description = "聚合sql配置")
    private List<FunctionSqlDto> aggregationCfg;

    /**
     * 校验规则 COLUMN:variable_attr_check_rule
     */
    @Schema(description = "校验规则")
    private List<EntityAttrCheckDto> variableAttrCheckRule;

    /**
     * 校验规则 - 自定义规则校验脚本 COLUMN:variable_attr_check_script
     */
    @Schema(description = "校验规则 - 自定义规则校验脚本")
    private String variableAttrCheckScript;

    /**
     * 处理规则 COLUMN:variable_attr_deal_rule
     */
    @Schema(description = "处理规则")
    private List<EntityAttrDealDto> variableAttrDealRule;

    /**
     * 校验规则 - 自定义规则处理脚本 COLUMN:variable_attr_deal_script
     */
    @Schema(description = "校验规则 - 自定义规则处理脚本")
    private String variableAttrDealScript;

}