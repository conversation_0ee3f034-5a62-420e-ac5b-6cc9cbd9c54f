package com.ey.tax.cloud.base.service.dashboard.impl;

import com.ey.cn.tax.framework.mybatisplus.extension.service.AbstractService;
import com.ey.tax.cloud.base.entity.dashboard.DashboardProject;
import com.ey.tax.cloud.base.mapper.dashboard.DashboardProjectMapper;
import com.ey.tax.cloud.base.service.dashboard.DashboardProjectService;
import org.springframework.stereotype.Service;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-11-12 15:36:07
 * 
 */
@Service
public class DashboardProjectServiceImpl extends AbstractService<DashboardProjectMapper, DashboardProject> implements DashboardProjectService {
}