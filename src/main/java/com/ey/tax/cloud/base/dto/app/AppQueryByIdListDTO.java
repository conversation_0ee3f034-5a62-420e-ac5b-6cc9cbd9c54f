package com.ey.tax.cloud.base.dto.app;

import com.ey.cn.tax.framework.validator.QueryByIdListGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-07-04 14:57:19
 * 
 */
@Data
@Schema(description = "根据id列表查询实体")
public class AppQueryByIdListDTO {
    
    /**
     * 数据id列表
     */
    @Schema(description = "数据id列表")
    @NotNull(message = "{AppDTO.ids.NotNull}", groups = {QueryByIdListGroup.class})
    @Size(min = 1, message = "{AppDTO.ids.Size}", groups = {QueryByIdListGroup.class})
    private List<String> ids;
}
