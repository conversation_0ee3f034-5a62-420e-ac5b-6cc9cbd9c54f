package com.ey.tax.cloud.base.dto.workflow.item;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @ProjectName: ey-tax-cloud-base
 * @ClassName: WorkFlowNode
 * @Description: Node
 * @Author: <PERSON>na
 * @Date: 2025/3/21
 */
@Data
public class ItemWorkflowNode {

    String nodeId;


    List<ItemWorkflowNode> nextNodes = new ArrayList<>();

    public ItemWorkflowNode(String id) {
        this.nodeId = id;
    }


    public void connectTo(ItemWorkflowNode node) {
        nextNodes.add(node);
    }


}
