package com.ey.tax.cloud.base.dto.workflow.instance;

import com.ey.cn.tax.framework.dto.BaseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2025-03-10 15:28:31
 * 
 */
@Data
@Schema(description = "默认实体")
public class InstanceDTO extends BaseDTO {
    /**
     * 流程定义id COLUMN:flow_define_id
     */
    @Schema(description = "流程定义id")
    private String flowDefineId;

    /**
     * 流程模型id COLUMN:flow_model_id
     */
    @Schema(description = "流程模型id")
    private String flowModelId;

    /**
     * 任务序列 COLUMN:flow_step
     */
    @Schema(description = "任务序列")
    private Integer flowStep;

    /**
     * 流程状态 COLUMN:flow_status
     */
    @Schema(description = "流程状态")
    private String flowStatus;

    /**
     * 流程code COLUMN:flow_code
     */
    @Schema(description = "流程code")
    private String flowCode;
}