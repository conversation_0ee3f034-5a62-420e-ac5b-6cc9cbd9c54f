package com.ey.tax.cloud.base.groovy

import com.ey.cn.tax.framework.context.EyUserContextHolder
import com.ey.cn.tax.framework.execution.IExecutionContext
import com.ey.cn.tax.framework.mybatisplus.core.query.EyQueryWrapper
import com.ey.cn.tax.framework.mybatisplus.core.toolkit.EyWrappers
import com.ey.cn.tax.framework.utils.ConvertUtils
import com.ey.cn.tax.lite.execution.dataset.IDatasetGroovyRunnable
import com.ey.tax.cloud.app.constant.AppResourceConstants
import com.ey.tax.cloud.app.dto.resource.AppResTreeDTO
import com.ey.tax.cloud.app.entity.resource.AppResource
import com.ey.tax.cloud.app.mapper.resource.AppResourceMapper
import org.apache.commons.collections4.CollectionUtils
import org.apache.commons.lang3.StringUtils

class AppResurceTree implements IDatasetGroovyRunnable {

    AppResourceMapper appResourceMapper;

    @Override
    public Object run(Object args, Map<String, Object> dependentDatasets, IExecutionContext executionContext) {

        appResourceMapper = executionContext.getBean(AppResourceMapper.class).get();

        String sysCode = EyUserContextHolder.get().getSyscode();
        String appCode = EyUserContextHolder.get().getAppcode();

//        if(StringUtils.isEmpty(sysCode) || StringUtils.isEmpty(appCode)){
//            log.info("Syscode或Appcode为空")
//            return null;
//        }

        EyQueryWrapper wrapper = EyWrappers.query(AppResource.class).in("resource_type",new Integer[]{0,1});
        if(StringUtils.isNotBlank(sysCode) && StringUtils.isNotBlank(appCode)){
            wrapper.and(qw1->{
                qw1.and(qw2->{qw2.eq("parent_id",AppResourceConstants.SOURCE_ROOT_PARENT_ID).eq(StringUtils.isNotBlank(sysCode),"sys_code",sysCode)})
                        .or(qw3->qw3.eq(StringUtils.isNotBlank(sysCode),"sys_code",sysCode).eq(StringUtils.isNotBlank(appCode),"app_code",appCode))}
            );
        }
        List<AppResource> appResources = appResourceMapper.selectList(wrapper);

        if(CollectionUtils.isNotEmpty(appResources)){
            TreeSet<AppResTreeDTO> set = new TreeSet<>();
            Map<String,AppResTreeDTO> resourceMap = new HashMap<>();
            for( AppResource appResource: appResources){
                AppResTreeDTO dto = ConvertUtils.convertEntity2DTO(appResource,AppResTreeDTO.class);
                dto.setLabel(appResource.getResourceName());
                resourceMap.put(dto.getId(),dto);
                if(AppResourceConstants.SOURCE_ROOT_PARENT_ID.equalsIgnoreCase(dto.getParentId())){
                    set.add(dto);
                }
            }
            for( AppResTreeDTO appResource: resourceMap.values()){
                if(resourceMap.containsKey(appResource.getParentId())){
                    resourceMap.get(appResource.getParentId()).getChildren().add(appResource)
                }
            }
            return set;
        }

        return new TreeSet<>();
    }
}

