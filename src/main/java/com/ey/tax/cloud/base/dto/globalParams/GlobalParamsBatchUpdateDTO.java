package com.ey.tax.cloud.base.dto.globalParams;

import com.ey.cn.tax.framework.validator.BatchUpdateGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-07-03 16:08:45
 * 
 */
@Data
@Schema(description = "根据id批量更新实体")
public class GlobalParamsBatchUpdateDTO {
    
    /**
     * 数据id
     */
    @Schema(description = "数据id")
    @NotBlank(message = "{GlobalParamsDTO.id.NotBlank}", groups = {BatchUpdateGroup.class})
    private String id;

    /**
     * 参数编码 COLUMN:param_code
     */
    @Schema(description = "参数编码", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String paramCode;

    /**
     * 参数名称 COLUMN:param_name
     */
    @Schema(description = "参数名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String paramName;

    /**
     * 参数类型(NUMBER/STRING/BOOLEAN/ARRAY/MAP/JSON) COLUMN:param_type
     */
    @Schema(description = "参数类型(NUMBER/STRING/BOOLEAN/ARRAY/MAP/JSON)", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String paramType;

    /**
     * 参数值 COLUMN:param_value
     */
    @Schema(description = "参数值", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String paramValue;

    /**
     * 标签1 COLUMN:label1
     */
    @Schema(description = "标签1", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String label1;

    /**
     * 标签2 COLUMN:label2
     */
    @Schema(description = "标签2", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String label2;

    /**
     * 标签3 COLUMN:label3
     */
    @Schema(description = "标签3", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String label3;

    /**
     * 系统编码（none/ifs/tpd） COLUMN:sys_code
     */
    @Schema(description = "系统编码（none/ifs/tpd）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String sysCode;

    /**
     * 应用编码(none/app_yizh/app_uuzy) COLUMN:app_code
     */
    @Schema(description = "应用编码(none/app_yizh/app_uuzy)", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String appCode;
}
