package com.ey.tax.cloud.base.dto.database;

import com.ey.cn.tax.framework.validator.BatchUpdateGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-08-13 15:02:17
 * 
 */
@Data
@Schema(description = "根据id批量更新实体")
public class DbConfigBatchUpdateDTO {
    
    /**
     * 数据id
     */
    @Schema(description = "数据id")
    @NotBlank(message = "{DbConfigDTO.id.NotBlank}", groups = {BatchUpdateGroup.class})
    private String id;

    /**
     * 数据库IP COLUMN:db_ip
     */
    @Schema(description = "数据库IP", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String dbIp;

    /**
     * 数据库端口 COLUMN:db_port
     */
    @Schema(description = "数据库端口", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String dbPort;

    /**
     * 数据库编码 COLUMN:db_code
     */
    @Schema(description = "数据库编码", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String dbCode;

    /**
     * 用户名 COLUMN:db_username
     */
    @Schema(description = "用户名", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String dbUsername;

    /**
     * 密码 COLUMN:db_password
     */
    @Schema(description = "密码", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String dbPassword;
}