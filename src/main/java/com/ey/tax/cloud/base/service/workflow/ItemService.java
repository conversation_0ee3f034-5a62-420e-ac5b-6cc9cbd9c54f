package com.ey.tax.cloud.base.service.workflow;

import com.ey.cn.tax.framework.service.BaseService;
import com.ey.tax.cloud.base.dto.workflow.item.ItemParseFlowDTO;
import com.ey.tax.cloud.base.dto.workflow.item.ItemParseFlowResultDTO;
import com.ey.tax.cloud.base.entity.workflow.Item;

import java.util.List;

import java.util.Map;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2025-03-10 15:28:31
 * 
 */
public interface ItemService extends BaseService<Item> {

    ItemAttributeService attributeToDTO(Item item);

    Boolean parseFlow(Map<String,Object> params);

    List<Item> queryModelItemList(String modelId);

    Item queryStartEvent(String modelId);

    List<Item> queryNextByItemRef(String itemRef);

    List<Item> queryBeforeByItemRef(String itemRef);

    List<Item> queryAfterSequence(String itemRef);

    String checkFlowChart(String modelId,List<Item> itemList);


    ItemParseFlowResultDTO parseFlowToItem(Map<String,Object> params,String modelId);

    boolean deleteByModelId(String modelId,List<Item> itemList);
}