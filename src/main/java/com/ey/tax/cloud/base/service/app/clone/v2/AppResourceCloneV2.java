package com.ey.tax.cloud.base.service.app.clone.v2;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ey.cn.tax.framework.exception.EyTaxSystemException;
import com.ey.cn.tax.framework.utils.JacksonUtils;
import com.ey.tax.cloud.app.entity.resource.AppResource;
import com.ey.tax.cloud.app.mapper.resource.AppResourceMapper;
import com.ey.tax.cloud.base.dto.AppCloneAllFeignDTO;
import com.ey.tax.cloud.base.dto.app.AppCloneAllResDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@Order(2)
public class AppResourceCloneV2 extends AppCloneCommon {

    @Autowired
    AppResourceMapper mapper;

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    @Override
    public void clone(AppCloneAllFeignDTO dto, AppCloneAllResDTO result) {
        log.info("base table clone : AppResourceCloneV2");
        AppResource root = mapper.selectOne(Wrappers.lambdaQuery(AppResource.class).eq(AppResource::getSysCode, dto.getOriginalSysCode()).eq(AppResource::getParentId, "SOURCE_ROOT_PARENT_ID"));
        if (Objects.isNull(root)) {
            log.info("克隆资源时没有发现根节点！");
            throw new EyTaxSystemException("Error: 克隆资源时没有发现根节点");
        }

        List<AppResource> originals = mapper.selectList(Wrappers.lambdaQuery(AppResource.class).eq(AppResource::getSysCode, dto.getOriginalSysCode()).eq(AppResource::getAppCode, dto.getOriginalAppCode()));
        List<AppResource> targets = originals.stream().map(e -> newTarget(e, dto.getTargetSysCode(), dto.getTargetAppCode(), root.getId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(targets)) {
            mapper.insertOrUpdate(targets);
        }
        log.info("base table clone : AppResourceCloneV2 end~~~~~~~~");
    }


    private AppResource newTarget(AppResource original, String targetSysCode, String targetAppCode, String rootAppResourceId) {
        AppResource target = JacksonUtils.deepCopy(AppResource.class, original);
        target.setId(targetAppCode + "_" + compressTo32Char(original.getId()));
        if (!rootAppResourceId.equals(original.getParentId())) {
            target.setParentId(targetAppCode + "_" + compressTo32Char(original.getParentId()));
        } else {
            target.setResourceName(targetAppCode);
            target.setResourceCode(targetSysCode + "_" + targetAppCode);
        }
        target.setSysCode(targetSysCode);
        target.setAppCode(targetAppCode);
        target.setResourceSource("clone");
        target.setRemark("全量克隆");
        return target;
    }
}
