package com.ey.tax.cloud.base.mapper.resource;

import com.ey.cn.tax.framework.mybatisplus.core.mapper.IEyBaseMapper;
import com.ey.tax.cloud.base.entity.resource.ManUserRole;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Set;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2023-08-18 15:21:13
 * 
 */
public interface ManUserRoleMapper extends IEyBaseMapper<ManUserRole> {

    @Select("""
            
            select
            	burc.role_id
            from
            	base_man_user_role burc
            inner join base_man_role br on
            	burc.role_id = br.id
            where
            	burc.is_del = 0
            	and br.is_del =0
            	and burc.user_id = #{userId}
            """)
    Set<String> selectRole( @Param("userId")String userId );

}