package com.ey.tax.cloud.base.dto.workflow.attachment;

import com.ey.cn.tax.framework.validator.BatchUpdateGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2025-03-10 15:28:30
 * 
 */
@Data
@Schema(description = "根据id批量更新实体")
public class AttachmentBatchUpdateDTO {
    
    /**
     * 数据id
     */
    @Schema(description = "数据id")
    @NotBlank(message = "{AttachmentDTO.id.NotBlank}", groups = {BatchUpdateGroup.class})
    private String id;

    /**
     * 流程任务id COLUMN:flow_task_id
     */
    @Schema(description = "流程任务id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String flowTaskId;

    /**
     * 流程实例id COLUMN:flow_instance_id
     */
    @Schema(description = "流程实例id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String flowInstanceId;

    /**
     * 附件类型 COLUMN:attachment_type
     */
    @Schema(description = "附件类型", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String attachmentType;

    /**
     * 文件id COLUMN:file_id
     */
    @Schema(description = "文件id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String fileId;

    /**
     * 文件路径 COLUMN:file_path
     */
    @Schema(description = "文件路径", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String filePath;
}