package com.ey.tax.cloud.base.service.app.clone;

import com.ey.tax.cloud.base.dto.AppBaseCloneFeignDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class DynamicPageEntityClone extends AbstractBaseTableClone {

    @Override
    public void clone(AppBaseCloneFeignDTO dto) {
        log.info("base table clone : DynamicPageEntity");
        getAppCloneService().removeDynamicPageEntity(dto);
        getAppCloneService().cloneDynamicPageEntity(dto);
        if(baseTableClone != null){
            baseTableClone.setAppCloneService(getAppCloneService());
            baseTableClone.clone(dto);
        }
    }
}
