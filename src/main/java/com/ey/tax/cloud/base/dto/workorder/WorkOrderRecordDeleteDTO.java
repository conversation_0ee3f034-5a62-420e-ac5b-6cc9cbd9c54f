package com.ey.tax.cloud.base.dto.workorder;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-02-24 20:13:47
 * 
 */
@Data
@Schema(description = "根据条件删除实体")
public class WorkOrderRecordDeleteDTO {
    /**
     * 工单表ID COLUMN:wo_id
     */
    @Schema(description = "工单表ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String woId;

    /**
     * 工单提交内容，处理意见等 COLUMN:record_describe
     */
    @Schema(description = "工单提交内容，处理意见等", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String recordDescribe;

    /**
     * 操作后的最新工单状态 COLUMN:snapshot_state
     */
    @Schema(description = "操作后的最新工单状态", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String snapshotState;

    /**
     * 操作人 COLUMN:operator_uid
     */
    @Schema(description = "操作人", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String operatorUid;

    /**
     * 操作人邮箱 COLUMN:operator_email
     */
    @Schema(description = "操作人邮箱", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String operatorEmail;
}