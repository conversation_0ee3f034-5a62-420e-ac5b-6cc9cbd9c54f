package com.ey.tax.cloud.base.dto.workflow.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2025-03-10 15:28:33
 * 
 */
@Data
@Schema(description = "根据条件查询实体")
public class ModelQueryDTO {
    /**
     * 流程定义id COLUMN:flow_define_id
     */
    @Schema(description = "流程定义id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String flowDefineId;

    /**
     * 模型状态 COLUMN:model_status
     */
    @Schema(description = "模型状态", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String modelStatus;

    /**
     * 模型内容 COLUMN:model_content
     */
    @Schema(description = "模型内容", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String modelContent;
}