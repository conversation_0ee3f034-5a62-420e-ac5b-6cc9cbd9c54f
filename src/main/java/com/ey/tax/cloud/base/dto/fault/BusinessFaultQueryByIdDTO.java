package com.ey.tax.cloud.base.dto.fault;

import com.ey.cn.tax.framework.validator.QueryByIdGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-05-20 10:36:04
 * 
 */
@Data
@Schema(description = "根据id查询实体")
public class BusinessFaultQueryByIdDTO {
    
    /**
     * 数据id
     */
    @Schema(description = "数据id")
    @NotBlank(message = "{BusinessFaultDTO.id.NotBlank}", groups = {QueryByIdGroup.class})
    private String id;
}