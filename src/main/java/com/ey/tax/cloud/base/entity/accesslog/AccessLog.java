package com.ey.tax.cloud.base.entity.accesslog;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ey.cn.tax.framework.mybatisplus.core.entity.BaseEntity;
import com.ey.tax.cloud.base.handler.JSONTypeHandlerPgMap;
import lombok.Data;

import java.util.Map;

@Data
@TableName("base_access_log")
public class AccessLog extends BaseEntity {

    //请求方法名
    private String requestMethod;

    //访问地址
    private String requestUrl;

    //执行时长，单位：毫秒
    private long duration;

    // 预留字段
    @TableField(value = "datas",typeHandler = JSONTypeHandlerPgMap.class)
    private Map<String,Object> datas;

    // 备注
    private String remark;

}
