package com.ey.tax.cloud.base.entity.workorder;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ey.cn.tax.framework.mybatisplus.core.entity.BaseEntity;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-05-07 17:52:06
 * 
 */
@Data
@TableName("base_work_order_file")
public class WorkOrderFile extends BaseEntity {
    /**
     * COLUMN:file_path
     */
    private String filePath;

    /**
     * COLUMN:file_name
     */
    private String fileName;

    /**
     * COLUMN:file_type
     */
    private String fileType;

    /**
     * COLUMN:work_order_id
     */
    private String workOrderId;
}