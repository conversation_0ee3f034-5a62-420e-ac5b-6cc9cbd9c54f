package com.ey.tax.cloud.base.groovy


import com.ey.cn.tax.framework.execution.IExecutionContext
import com.ey.cn.tax.framework.mybatisplus.core.toolkit.EyWrappers
import com.ey.cn.tax.lite.execution.dataset.IDatasetGroovyRunnable
import com.ey.tax.cloud.app.entity.resource.AppResource
import com.ey.tax.cloud.app.mapper.resource.AppResourceMapper
import com.ey.tax.cloud.base.entity.app.App
import com.ey.tax.cloud.base.mapper.app.AppMapper
import org.apache.commons.collections.map.HashedMap

import java.util.stream.Collectors

class AppHaveInitResource implements IDatasetGroovyRunnable {

    AppMapper appMapper;

    AppResourceMapper appResourceMapper;

    @Override
    public Object run(Object args, Map<String, Object> dependentDatasets, IExecutionContext executionContext) {

        appResourceMapper = executionContext.getBean(AppResourceMapper.class).get();
        appMapper = executionContext.getBean(AppMapper.class).get();

        Map<String,Object> argM = (Map<String,Object>)args;

        String id = argM.get("id");
        App app = appMapper.selectById(id);

        String prifix = app.getSysCode()+"_"+app.getAppCode()+"_";

        List<AppResource> appResources = appResourceMapper.selectList(EyWrappers.query().likeRight("id",prifix))

        List<String> resourceIds = appResources.stream().map(e->{
            String key = e.getId();
            return key.substring(prifix.length(),key.lastIndexOf("_"));
        }).collect(Collectors.toList());

        Map<String,Object> res = new HashedMap()
        res.put("resourceIds",resourceIds);
        return res;
    }
}
