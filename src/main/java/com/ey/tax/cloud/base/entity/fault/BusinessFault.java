package com.ey.tax.cloud.base.entity.fault;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ey.cn.tax.framework.mybatisplus.core.entity.BaseEntity;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-05-20 10:36:04
 * 
 */
@Data
@TableName("base_business_fault")
public class BusinessFault extends BaseEntity {
    /**
     * 子系统 COLUMN:system_type
     */
    private String systemType;

    /**
     * 业务类型,关联数据字典,在数据字典的扩展字段里,加通知邮箱,邮箱可以配置多个,通过逗号分隔 COLUMN:business_type
     */
    private String businessType;

    /**
     * 异常内容 COLUMN:fault_content
     */
    private String faultContent;

    /**
     * 状态（打开,关闭） COLUMN:fault_status
     */
    private String faultStatus;
}