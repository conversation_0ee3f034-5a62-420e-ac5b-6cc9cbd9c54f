package com.ey.tax.cloud.base.dto.dynamic;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Created by yutao
 * @Date 2023/12/22 15:17
 * @Description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FunctionSqlDto {

    /**
     * 数据表属性
     */
    private String tableAttr;

    /**
     * 函数配置
     */
    private String functionConfig;

    /**
     * 别名
     */
    private String alias;

    /**
     * 标签名称
     */
    private String labelName;

}
