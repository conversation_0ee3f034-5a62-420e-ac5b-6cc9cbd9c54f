package com.ey.tax.cloud.base.service.app;

import com.ey.cn.tax.framework.dto.ComboBoxDTO;
import com.ey.cn.tax.framework.entity.Search;
import com.ey.cn.tax.framework.service.BaseService;
import com.ey.tax.cloud.base.dto.AppBaseCloneFeignDTO;
import com.ey.tax.cloud.base.dto.AppCloneAllFeignDTO;
import com.ey.tax.cloud.base.dto.AppInitResourceFeignDTO;
import com.ey.tax.cloud.base.dto.app.*;
import com.ey.tax.cloud.base.entity.app.App;
import com.ey.tax.cloud.base.remote.app.AppCloneFeignService;

import java.util.List;
import java.util.Map;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-07-04 14:57:19
 * 
 */
public interface AppService extends BaseService<App> {
    Map<String,List<ComboBoxDTO<String>>> getComBoxBySystem(AppDTO dto,boolean isContainNone);

    Search<AppRepDTO> queryPage(Search<App> search) throws Exception;

    List<ComboBoxDTO<String>> buildNone();

    List<AppDTO> queryListBySyscode();

    List<ComboBoxDTO<String>> queryUser(AppTenUserDTO param, boolean isKeyMail);

    AppDTO queryByAppCode(String appCode);

    void initResource(AppInitResourceDTO dto);

    void initResourceCallBack(AppInitResourceFeignDTO dto);

    void cloneApp(AppCloneDTO dto) ;

    void cloneAppAll(AppCloneAllFeignDTO dto) ;

    void cloneBaseTable(AppBaseCloneFeignDTO dto) ;

    AppCloneFeignService getServiceFeign(String sysCode);
}
