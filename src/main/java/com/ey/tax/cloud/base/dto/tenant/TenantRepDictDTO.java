package com.ey.tax.cloud.base.dto.tenant;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2023-09-07 12:50:52
 *
 */
@Data
@Schema(description = "字典返回实体")
public class TenantRepDictDTO {

    /**
     * 租户名称 COLUMN:lessee_name
     */
    @Schema(description = "租户名称")
    private String label;

    /**
     * 租户编码 COLUMN:lessee_code
     */
    @Schema(description = "租户id")
    private String value;

}
