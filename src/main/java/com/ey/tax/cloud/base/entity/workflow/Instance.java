package com.ey.tax.cloud.base.entity.workflow;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ey.cn.tax.framework.mybatisplus.core.entity.BaseEntity;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2025-03-10 15:28:31
 * 
 */
@Data
@TableName("tax_flow_instance")
public class Instance extends BaseEntity {
    /**
     * 流程定义id COLUMN:flow_define_id
     */
    private String flowDefineId;

    /**
     * 流程模型id COLUMN:flow_model_id
     */
    private String flowModelId;

    /**
     * 任务序列 COLUMN:flow_step
     */
    private Integer flowStep;

    /**
     * 流程状态 COLUMN:flow_status
     */
    private String flowStatus;

    /**
     * 流程code COLUMN:flow_code
     */
    private String flowCode;

    /**
     * 子系统code COLUMN:sys_code
     */
    private String sysCode;

    /**
     * 应用code COLUMN:app_code
     */
    private String appCode;

}