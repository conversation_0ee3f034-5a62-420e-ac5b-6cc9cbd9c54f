package com.ey.tax.cloud.base.entity.dynamic;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ey.cn.tax.framework.mybatisplus.core.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2023-09-09 22:38:04
 * 
 */
@Data
@TableName("sys_dynamic_page_options")
public class DynamicPageOptions extends BaseEntity {
    /**
     * 子应用 COLUMN:sub_app_code
     */
    private String subAppCode;

    /**
     * 数据名称 COLUMN:data_name
     */
    private String dataName;

    /**
     * 数据类型，sql, json COLUMN:data_type
     */
    private String dataType;

    /**
     * 数据配置 COLUMN:data_config
     */
    private String dataConfig;

    /**
     * 唯一标识 COLUMN:data_code
     */
    private String dataCode;

    /**
     * 系统编码 COLUMN:data_code
     */
    private String sysCode;

    /**
     * 应用编码 COLUMN:data_code
     */
    private String appCode;

}
