package com.ey.tax.cloud.base.utils;

import org.apache.commons.lang.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Description PlaceholderUtils
 * @Date 2025/3/20
 */
public class PlaceholderUtils {

    private static final Pattern PLACEHOLDER_PATTERN = Pattern.compile("\\$\\{([a-zA-Z]\\w*)\\}");


    public static String getKey(String str){
        Matcher matcher = PLACEHOLDER_PATTERN.matcher(str);
        if (matcher.find()){
            return matcher.group(1);
        } else {
            return StringUtils.EMPTY;
        }
    }

    public static void main(String[] args) {
        System.out.println(getKey("${123}"));
    }
}
