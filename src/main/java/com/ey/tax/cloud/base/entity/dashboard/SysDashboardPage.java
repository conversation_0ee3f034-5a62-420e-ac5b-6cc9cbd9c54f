package com.ey.tax.cloud.base.entity.dashboard;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ey.cn.tax.framework.mybatisplus.core.entity.BaseEntity;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-04-22 23:07:32
 * 
 */
@Data
@TableName("sys_dashboard_page")
public class SysDashboardPage extends BaseEntity {
    /**
     * 名称 COLUMN:dashboard_name
     */
    private String dashboardName;

    /**
     * 配置 COLUMN:dashboard_config
     */
    private String dashboardConfig;

    /**
     * 子应用 COLUMN:sub_app_code
     */
    private String subAppCode;

    /**
     * 编码 COLUMN:dashboard_code
     */
    private String dashboardCode;

    /**
     * 系统编码 COLUMN:dashboard_code
     */
    private String sysCode;
}
