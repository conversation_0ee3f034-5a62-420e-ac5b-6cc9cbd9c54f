package com.ey.tax.cloud.base.dto.module;

import com.ey.cn.tax.framework.validator.BatchUpdateGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-10-12 15:55:08
 * 
 */
@Data
@Schema(description = "根据id批量更新实体")
public class ModuleBatchUpdateDTO {
    
    /**
     * 数据id
     */
    @Schema(description = "数据id")
    @NotBlank(message = "{ModuleDTO.id.NotBlank}", groups = {BatchUpdateGroup.class})
    private String id;

    /**
     * 租户id COLUMN:tenant_id
     */
    @Schema(description = "租户id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String tenantId;

    /**
     * 系统编码 COLUMN:sys_code
     */
    @Schema(description = "系统编码", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String sysCode;

    /**
     * 应用code COLUMN:app_code
     */
    @Schema(description = "应用code", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String appCode;

    /**
     * 模块名称 COLUMN:module_name
     */
    @Schema(description = "模块名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String moduleName;

    /**
     * 模块编码 COLUMN:module_code
     */
    @Schema(description = "模块编码", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String moduleCode;

    /**
     * 模块排序 COLUMN:module_order
     */
    @Schema(description = "模块排序", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer moduleOrder;

    /**
     * 克隆数据集 COLUMN:clone_dataset
     */
    @Schema(description = "克隆数据集", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String cloneDataset;

    /**
     * 初始化数据集 COLUMN:init_dataset
     */
    @Schema(description = "初始化数据集", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String initDataset;

    /**
     * 删除数据集 COLUMN:remove_dataset
     */
    @Schema(description = "删除数据集", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String removeDataset;
}