package com.ey.tax.cloud.base.enums.theme;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.ey.cn.tax.framework.exception.EyTaxSystemException;
import com.ey.cn.tax.framework.utils.I18nUtils;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * <p>
 * 系统主题, 是否有效
 * </p>
 *
 * <AUTHOR>
 * @date 17:41 2023/8/24
 */
public enum ThemeIsValidEnum {
    INVALID(0, "无效的", "com.ey.tax.cloud.base.enums.theme.ThemeIsValidEnum.INVALID"),
    VALID(1, "有效的", "com.ey.tax.cloud.base.enums.theme.ThemeIsValidEnum.VALID"),
    ;

    @EnumValue
    private final Integer key;
    private final String name;
    private final String i18nCode;

    ThemeIsValidEnum(final Integer key, final String name, final String i18nCode) {
        this.key = key;
        this.name = name;
        this.i18nCode = i18nCode;
    }

    @JsonValue
    public Integer getKey() {
        return this.key;
    }

    public String getName() {
        return I18nUtils.getMessage(this.i18nCode, this.name);
    }

    @JsonCreator
    public static ThemeIsValidEnum getEnum(Integer value) {
        for (ThemeIsValidEnum enums : ThemeIsValidEnum.values()) {
            if (enums.getKey().equals(value)) {
                return enums;
            }
        }
        throw new EyTaxSystemException("Error: Invalid ThemeIsValidEnum type value: " + value);
    }


}
