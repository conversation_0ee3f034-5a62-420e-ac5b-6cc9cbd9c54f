package com.ey.tax.cloud.base.service.workflow.impl;

import com.ey.cn.tax.framework.context.EyUserContextHolder;
import com.ey.cn.tax.framework.mybatisplus.core.entity.BaseEntity;
import com.ey.cn.tax.framework.utils.EmptyUtils;
import com.ey.cn.tax.framework.utils.ExceptionUtils;
import com.ey.tax.cloud.base.constants.CommonConstant;
import com.ey.tax.cloud.base.constants.WorkflowConstants;
import com.ey.tax.cloud.base.dto.workflow.*;
import com.ey.tax.cloud.base.entity.workflow.*;
import com.ey.tax.cloud.base.service.workflow.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service("WorkflowEngineService")
public class WorkflowEngineServiceImpl implements WorkflowEngineService {

    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    private static final int MAX_DEEP = 20;

    @Autowired
    private DefineService defineService;

    @Autowired
    private ModelService modelService;

    @Autowired
    private ItemService itemService;

    @Autowired
    private LogService logService;

    @Autowired
    private AttachmentService attachmentService;

    @Autowired
    private InstanceService instanceService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private VariableService variableService;

    @Transactional
    @Override
    public String startFlow(String sysCode,String appCode,
                            String workflowCode, Map<String, Object> parameters) {
        //根据code查询当前发布的模型
        //1.获取defineId
        logger.info("start workflow {}", workflowCode);
        logger.info("parameters {}", parameters);
        if (StringUtils.isEmpty(sysCode) && StringUtils.isEmpty(appCode)){
            sysCode = EyUserContextHolder.get().getSyscode();
            appCode = EyUserContextHolder.get().getAppcode();
        }
        Define qDefine = new Define();
        qDefine.setSysCode(sysCode);
        qDefine.setAppCode(appCode);
        qDefine.setFlowCode(workflowCode);
        qDefine.setEnable(WorkflowConstants.DEFINE_FLAG_ENABLE);
        Define define = defineService.queryOneByPara(qDefine);
        //app 查不到从 sys查
        if(EmptyUtils.isEmpty(define) && !CommonConstant.SystemCode.NONE.equals(appCode)){
            qDefine.setAppCode(CommonConstant.SystemCode.NONE);
            define = defineService.queryOneByPara(qDefine);
        }
        if (EmptyUtils.isEmpty(define)) {
            ExceptionUtils.throwBuildSystemException("WORKFLOW_CODE_NOT_EXIST", workflowCode);
        }
        String defineStatus = define.getFlowStatus();
        if(EmptyUtils.isEmpty(defineStatus) || WorkflowConstants.MODEL_STATUS_UNDEPLOYED.equals(defineStatus)){
            ExceptionUtils.throwBuildSystemException("WORKFLOW_NOT_DEPLOYED", workflowCode);
        }
        String workflowDefineId = define.getId();
        //2.获取模型id
        Model model = modelService.queryDeployedModel(workflowDefineId);
        String workflowModelId = model.getId();
        if (EmptyUtils.isEmpty(model)) {
            ExceptionUtils.throwBuildSystemException("WORKFLOW_MODEL_UNDEPLOYED");
        }
        //3.根据模型id获取模型itemList
        Item startEvent = itemService.queryStartEvent(workflowModelId);
        //4.创建流程实例
        Instance instance = new Instance();
        instance.setFlowCode(workflowCode);
        instance.setFlowDefineId(workflowDefineId);
        instance.setFlowModelId(workflowModelId);
        instance.setSysCode(define.getSysCode());
        instance.setAppCode(appCode);
        instance.setFlowStep(0);
        instance.setFlowStatus(WorkflowConstants.INSTANCE_STATUS_RUNNING);
        instanceService.save(instance);
        //5.保存流程参数
        String workflowInstanceId = instance.getId();
        Variable variable = new Variable();
        variable.setFlowInstanceId(workflowInstanceId);
        variable.setVariableType(WorkflowConstants.VARIABLE_TYPE_SYSTEM);
        Map<String, Object> variableValue = new HashMap<>();
        variableValue.put(WorkflowConstants.SYSTEM_VARIABLE_KEY_WORKFLOW_START_TIME, new Date());
        variableValue.put(WorkflowConstants.SYSTEM_VARIABLE_KEY_WORKFLOW_CODE, workflowCode);
        variableValue.put(WorkflowConstants.SYSTEM_VARIABLE_KEY_WORKFLOW_MODEL_VERSION, model.getVersion());
        variableValue.put(WorkflowConstants.SYSTEM_VARIABLE_KEY_WORKFLOW_INSTANCE_ID, instance.getId());
        if (EmptyUtils.isNotEmpty(parameters)) {
            variableValue.putAll(parameters);
        }
        variable.setVariableValue(variableValue);
        variableService.save(variable);
        //6.创建startEvent节点
        WorkflowNodeService<WorkflowNode> workflowNodeService = this.getRealBeanByType(WorkflowConstants.SERVICE_START_EVENT);
        List<WorkflowNode> workflowNodeList = workflowNodeService.createWorkflowNode(startEvent, instance, variableValue);
        //7.进入节点迭代
        Approve approve = new Approve();
        approve.setParameters(variableValue);
        approve.setApproveType(WorkflowConstants.TASK_OPERATE_TYPE_APPROVE);
        processItem(startEvent, workflowNodeList.get(0), instance, approve, 1);
        return workflowInstanceId;
    }

    private void processItem(Item item, WorkflowNode workflowNode,
                             Instance instance, Approve approve, int deep) {
        //最大递归深度为20,防止死循环
        if (deep > MAX_DEEP) {
            ExceptionUtils.throwBuildSystemException("WORKFLOW_OUT_OF_DEEP");
        }
        //1.获取item的subType,拼接spring serviceId
        //startEvent -> WorkflowStartEventService
        String springBeanName = WorkflowConstants.SPRING_BEAN_ID_PREFIX + upperFirstLetter(item.getSubType()) + WorkflowConstants.SPRING_BEAN_ID_SUFFIX;
        WorkflowNodeService<WorkflowNode> workflowNodeService = this.getRealBeanByType(springBeanName);
        //2.执行item的execute方法
        //保存节点启动log
        workflowNodeService.beforeWorkflowNode(item, instance, workflowNode, approve);
        //获取variable
        Variable variable = variableService.queryByInstanceId(instance.getId());
        if (EmptyUtils.isEmpty(approve)) {
            approve = new Approve();
            approve.setParameters(new HashMap<>());
        }
        variable.setFlowInstanceId(instance.getId());
        if (EmptyUtils.isNotEmpty(approve.getParameters())) {
            variable.getVariableValue().putAll(approve.getParameters());
        }
        approve.setParameters(variable.getVariableValue());
        variableService.update(variable);
        ExecuteResult executeResult = workflowNodeService.executeWorkflowNode(item, instance, workflowNode, approve);
        processExecuteResult(executeResult, item, workflowNode, instance, approve, deep);
    }

    @Transactional
    public void processExecuteResult(ExecuteResult executeResult, Item item, WorkflowNode workflowNode,
                                     Instance instance, Approve approve, int deep) {
        approve.getParameters().put(WorkflowConstants.SYSTEM_VARIABLE_KEY_WORKFLOW_LAST_EXECUTE_TIME, new Date());
        String springBeanName = WorkflowConstants.SPRING_BEAN_ID_PREFIX + upperFirstLetter(item.getSubType()) + WorkflowConstants.SPRING_BEAN_ID_SUFFIX;
        WorkflowNodeService<WorkflowNode> workflowNodeService = this.getRealBeanByType(springBeanName);
        //保存节点结束log
        if(!WorkflowConstants.NEXT_NODE_ASYNC.equals(executeResult.getExecuteStatus())){
            workflowNodeService.afterWorkflowNode(item, instance, workflowNode, approve);
        }
        if (WorkflowConstants.NEXT_NODE_NORMAL.equals(executeResult.getExecuteStatus()) ||
                WorkflowConstants.NEXT_NODE_ASYNC.equals(executeResult.getExecuteStatus())) {
            return;
        } else if (WorkflowConstants.NEXT_NODE_SUSPEND.equals(executeResult.getExecuteStatus())) {
            //流程挂起
            instance.setFlowStatus(WorkflowConstants.INSTANCE_STATUS_SUSPEND);
            instanceService.update(instance);
            return;
        } else if (WorkflowConstants.NEXT_NODE_END.equals(executeResult.getExecuteStatus())) {
            instance.setFlowStatus(WorkflowConstants.INSTANCE_STATUS_FINISH);
            instanceService.update(instance);
            return;
        }
        //3.创建下一个节点
        nextFlow(executeResult.getNextItemList(), approve, instance, deep);
    }

    @Override
    @Transactional
    public void nextFlow(List<Item> nextItemList, Approve approve, Instance instance, int deep) {
        for (Item nextItem : nextItemList) {
            String nextSpringBeanName = WorkflowConstants.SPRING_BEAN_ID_PREFIX + upperFirstLetter(nextItem.getSubType()) + WorkflowConstants.SPRING_BEAN_ID_SUFFIX;
            WorkflowNodeService<WorkflowNode> nextWorkflowNodeService = this.getRealBeanByType(nextSpringBeanName);
            List<WorkflowNode> nextWorkflowNodeList = nextWorkflowNodeService.createWorkflowNode(nextItem, instance, approve.getParameters());
            int runTimes = 0;
            for (WorkflowNode nextWorkflowNode : nextWorkflowNodeList) {
                //userTask直接返回,需要人工触发
                if (WorkflowConstants.ITEM_SUB_TYPE_USER_TASK.equals(nextItem.getSubType())) {
                    Object oAutoApprove = nextItem.getItemAttribute().get(WorkflowConstants.USER_TASK_PROPERTY_AUTO_APPROVE);
                    if (WorkflowConstants.USER_TASK_PROPERTY_AUTO_APPROVE_YES.equals(oAutoApprove)) {
                        if(runTimes >= 1){
                            continue;
                        }
                    }else{
                        continue;
                    }
                }
                //4.递归
                //如果节点为自动审批,则WorkflowNode只需要运行一个
                approve.setApproveType(WorkflowConstants.TASK_OPERATE_TYPE_APPROVE);
                processItem(nextItem, nextWorkflowNode, instance, approve, deep);
                runTimes = runTimes + 1;
            }
        }
    }

    @Override
    @Transactional
    public void endFlow(String workflowInstanceId) {
        //cancel流程,流程实例设置为cancel,task设置为cancel
        Instance instance = instanceService.queryById(workflowInstanceId);
        String flowStatus = instance.getFlowStatus();
        if (WorkflowConstants.INSTANCE_STATUS_FINISH.equals(flowStatus)) {
            ExceptionUtils.throwBuildSystemException("WORKFLOW_ALREADY_FINISHED", workflowInstanceId);
        }
        if (WorkflowConstants.INSTANCE_STATUS_CANCEL.equals(flowStatus)) {
            ExceptionUtils.throwBuildSystemException("WORKFLOW_ALREADY_CANCELLED", workflowInstanceId);
        }
        Task qTask = new Task();
        qTask.setFlowInstanceId(workflowInstanceId);
        List<Task> taskList = taskService.queryByPara(qTask);
        List<Task> updateTaskList = new ArrayList<>();
        for (Task task : taskList) {
            if (WorkflowConstants.TASK_STATUS_FINISH.equals(task.getTaskStatus()) ||
                    WorkflowConstants.TASK_STATUS_CANCEL.equals(task.getTaskStatus())) {
                continue;
            }
            task.setTaskStatus(WorkflowConstants.TASK_STATUS_CANCEL);
            updateTaskList.add(task);
        }
        taskService.updateBatchByIds(updateTaskList);
        instance.setFlowStatus(WorkflowConstants.INSTANCE_STATUS_CANCEL);
        instanceService.update(instance);
        //记录log
        Log log = new Log();
        log.setFlowStep(instance.getFlowStep() + 1);
        log.setFlowInstanceId(workflowInstanceId);
        log.setItemType("workflowCancel");
        logService.save(log);
    }

    @Override
    @Transactional
    public void recover(String workflowInstanceId, Map<String, Object> variableValue) {
        //查询当前挂起的节点
        Task qTask = new Task();
        qTask.setFlowInstanceId(workflowInstanceId);
        qTask.setTaskStatus(WorkflowConstants.TASK_STATUS_SUSPEND);
        List<Task> tasks = taskService.queryByPara(qTask);
        if(EmptyUtils.isEmpty(tasks)){
            ExceptionUtils.throwBuildSystemException("WORKFLOW_TASK_NOT_NOT_SUSPEND");
        }
        Instance instance = instanceService.queryById(workflowInstanceId);
        instance.setFlowStatus(WorkflowConstants.INSTANCE_STATUS_RUNNING);
        instanceService.update(instance);
        for (Task task : tasks) {
            Item item = itemService.queryById(task.getItemId());
            Approve approve = new Approve();
            approve.setApproveType(WorkflowConstants.TASK_OPERATE_TYPE_APPROVE);
            approve.setParameters(variableValue);
            approve.setWorkflowTaskId(task.getId());
            String springBeanName = WorkflowConstants.SPRING_BEAN_ID_PREFIX + upperFirstLetter(item.getSubType()) + WorkflowConstants.SPRING_BEAN_ID_SUFFIX;
            WorkflowNodeService<WorkflowNode> workflowNodeService = this.getRealBeanByType(springBeanName);
            WorkflowNode workflowNode = workflowNodeService.buildWorkflowNode(item, instance, approve);
            processItem(item, workflowNode, instance, approve, 1);
        }
    }

    @Override
    @Transactional
    public void approve(Approve approve) {
        Task task = taskService.queryById(approve.getWorkflowTaskId());
        if(EmptyUtils.isEmpty(task)){
            ExceptionUtils.throwBuildSystemException("WORKFLOW_TASK_NOT_EXIST");
        }
        if(WorkflowConstants.TASK_STATUS_FINISH.equals(task.getTaskStatus())){
            ExceptionUtils.throwBuildSystemException("WORKFLOW_TASK_ALREADY_FINISH");
        }
        if(WorkflowConstants.TASK_STATUS_SUSPEND.equals(task.getTaskStatus())){
            ExceptionUtils.throwBuildSystemException("WORKFLOW_TASK_ALREADY_SUSPEND");
        }
        if (EmptyUtils.isEmpty(approve.getParameters())) {
            approve.setParameters(new HashMap<>());
        }
        approve.getParameters().put(WorkflowConstants.SYSTEM_VARIABLE_KEY_WORKFLOW_APPROVE_TIME, new Date());
        approve.getParameters().put(WorkflowConstants.SYSTEM_VARIABLE_KEY_WORKFLOW_APPROVE_TYPE, approve.getApproveType());
        String workflowInstanceId = task.getFlowInstanceId();
        Instance instance = instanceService.queryById(workflowInstanceId);
        Item item = itemService.queryById(task.getItemId());
        String springBeanName = WorkflowConstants.SPRING_BEAN_ID_PREFIX + upperFirstLetter(item.getSubType()) + WorkflowConstants.SPRING_BEAN_ID_SUFFIX;
        WorkflowNodeService<WorkflowNode> workflowNodeService = this.getRealBeanByType(springBeanName);
        WorkflowNode workflowNode = workflowNodeService.buildWorkflowNode(item, instance, approve);
        processItem(item, workflowNode, instance, approve, 1);
    }

    @Override
    @Transactional
    public void addVariable(String workflowInstanceId, Map<String, Object> parameters) {
        Variable qVariable = new Variable();
        qVariable.setFlowInstanceId(workflowInstanceId);
        Variable variable = variableService.queryOneByPara(qVariable);
        Map<String, Object> variableValue = variable.getVariableValue();
        if (EmptyUtils.isEmpty(variableValue)) {
            variableValue = new HashMap<>();
        }
        variableValue.putAll(parameters);
        variable.setVariableValue(variableValue);
        variableService.update(variable);
    }

    @Override
    @Transactional
    public void attachment(String workflowInstanceId, String workflowTaskId, List<String> attachmentFileIds) {
        if (EmptyUtils.isEmpty(attachmentFileIds)) {
            return;
        }
        List<Attachment> attachmentList = new ArrayList<>();
        for (String attachmentFileId : attachmentFileIds) {
            Attachment attachment = new Attachment();
            attachment.setFlowInstanceId(workflowInstanceId);
            attachment.setFileId(attachmentFileId);
            attachment.setFlowTaskId(workflowTaskId);
            attachmentList.add(attachment);
        }
        attachmentService.save(attachmentList);
    }

    private WorkflowNodeService<WorkflowNode> getRealBeanByType(String springBeanName) {
        return (WorkflowNodeService) applicationContext.getBean(springBeanName);
    }

    private String upperFirstLetter(String subType) {
        return subType.substring(0, 1).toUpperCase() + subType.substring(1);
    }

    @Override
    public List<WorkflowTaskResponseDTO> queryTask(WorkflowTaskQueryDTO workflowTaskQueryDTO) {
        Task task = new Task();
        task.setTaskTransactor(workflowTaskQueryDTO.getTransactor());
        task.setSubType(WorkflowConstants.ITEM_SUB_TYPE_USER_TASK);
        //待办的状态取哪个?
        List<Task> taskList = taskService.queryByPara(task);
        List<WorkflowTaskResponseDTO> dtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(taskList)) {
            List<String> defineIds = taskList.stream().map(Task::getFlowDefineId).collect(Collectors.toList());
            List<Define> defineList = defineService.queryByIds(defineIds);
            Map<String, String> defineMap = defineList.stream().collect(Collectors.toMap(BaseEntity::getId, Define::getFlowName));
            taskList.forEach(t -> {
                WorkflowTaskResponseDTO dto = new WorkflowTaskResponseDTO();
                dto.setAssignor(t.getTaskAssignor());
                dto.setTransactor(t.getTaskTransactor());
                dto.setWorkflowInstanceId(t.getFlowInstanceId());
                dto.setItemName(t.getItemName());
                dto.setFlowName(defineMap.get(t.getFlowDefineId()));
                dto.setBatchId(t.getBatchId());
                dto.setWorkFlowTaskId(t.getId());
                dtos.add(dto);
            });
        }
        return dtos;
    }

    @Override
    public List<WorkflowAttachmentResponseDTO> queryAttachment(WorkflowAttachmentQueryDTO workflowAttachmentQueryDTO) {
        Attachment attachment = new Attachment();
        attachment.setFlowInstanceId(workflowAttachmentQueryDTO.getWorkflowInstanceId());
        List<Attachment> attachmentList = attachmentService.queryByPara(attachment);
        List<WorkflowAttachmentResponseDTO> attachmentResponseDTOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(attachmentList)) {
            attachmentList.forEach(a -> {
                WorkflowAttachmentResponseDTO dto = new WorkflowAttachmentResponseDTO();
                dto.setFileId(a.getFileId());
                dto.setWorkflowTaskId(a.getFlowTaskId());
                dto.setWorkflowInstanceId(attachment.getFlowInstanceId());
                attachmentResponseDTOList.add(dto);
            });
        }
        return attachmentResponseDTOList;
    }


    @Override
    public List<WorkflowVariableResponseDTO> queryVariable(WorkflowVariableQueryDTO workflowVariableQueryDTO) {
        Variable variable = new Variable();
        variable.setFlowInstanceId(workflowVariableQueryDTO.getWorkflowInstanceId());
        List<Variable> variablesList = variableService.queryByPara(variable);
        List<WorkflowVariableResponseDTO> dtoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(variablesList)) {
            variablesList.forEach(v -> {
                WorkflowVariableResponseDTO dto = new WorkflowVariableResponseDTO();
                dto.setWorkflowInstanceId(v.getFlowInstanceId());
                dto.setParameters(v.getVariableValue());
                dtoList.add(dto);
            });
        }
        return dtoList;
    }

    @Override
    public Integer queryInstanceCount(WorkflowInstanceCountQueryDTO workflowInstanceCountQueryDTO) {
        Instance instance = new Instance();
        instance.setFlowCode(workflowInstanceCountQueryDTO.getWorkflowCode());

        return instanceService.queryCountByPara(instance).intValue();
    }
}
