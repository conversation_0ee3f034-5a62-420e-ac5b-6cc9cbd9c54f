package com.ey.tax.cloud.base.dto.maintain;

import com.ey.cn.tax.framework.validator.UpdateByIdGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2023-11-28 16:10:22
 * 
 */
@Data
@Schema(description = "根据id批量更新实体")
public class MaintainNoticeUpdateByIdDTO {
    
    /**
     * 数据id
     */
    @Schema(description = "数据id")
    @NotBlank(message = "{MaintainNoticeDTO.id.NotBlank}", groups = {UpdateByIdGroup.class})
    private String id;

    /**
     * 通知标题 COLUMN:title
     */
    @Schema(description = "通知标题", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String title;

    /**
     * 通知内容 COLUMN:content
     */
    @Schema(description = "通知内容", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String content;

    /**
     * 通知开始时间 COLUMN:notice_start_time
     */
    @Schema(description = "通知开始时间", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDateTime noticeStartTime;

    /**
     * 通知结束时间 COLUMN:notice_end_time
     */
    @Schema(description = "通知结束时间", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDateTime noticeEndTime;

    /**
     * 维护开始时间 COLUMN:maintain_start_time
     */
    @Schema(description = "维护开始时间", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDateTime maintainStartTime;

    /**
     * 维护结束时间 COLUMN:maintain_end_time
     */
    @Schema(description = "维护结束时间", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDateTime maintainEndTime;

    /**
     * 作用类型（1：管理端，2:租户端，3:管理端和租户端） COLUMN:type
     */
    @Schema(description = "作用类型（1：管理端，2:租户端，3:管理端和租户端）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Short type;
}