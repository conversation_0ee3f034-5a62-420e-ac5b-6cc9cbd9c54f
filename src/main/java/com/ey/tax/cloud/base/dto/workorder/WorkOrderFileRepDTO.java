package com.ey.tax.cloud.base.dto.workorder;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-05-07 17:52:06
 * 
 */
@Data
@Schema(description = "查询返回实体")
public class WorkOrderFileRepDTO {
    @Schema(description = "主键")
    private String id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;
    /**
     * COLUMN:file_path
     */
    @Schema(description = "文件路径")
    private String filePath;

    /**
     * COLUMN:file_name
     */
    @Schema(description = "文件名称")
    private String fileName;

    /**
     * COLUMN:file_type
     */
    @Schema(description = "文件类型")
    private String fileType;

    /**
     * COLUMN:work_order_id
     */
    @Schema(description = "工单ID")
    private String workOrderId;
}