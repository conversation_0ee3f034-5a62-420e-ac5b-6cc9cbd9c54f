package com.ey.tax.cloud.base.service.module.impl;

import com.ey.cn.tax.framework.mybatisplus.extension.service.AbstractService;
import com.ey.tax.cloud.base.entity.module.Module;
import com.ey.tax.cloud.base.mapper.module.ModuleMapper;
import com.ey.tax.cloud.base.service.module.ModuleService;
import org.springframework.stereotype.Service;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-10-12 15:55:08
 * 
 */
@Service
public class ModuleServiceImpl extends AbstractService<ModuleMapper, Module> implements ModuleService {
}