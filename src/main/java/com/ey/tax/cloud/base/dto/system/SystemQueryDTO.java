package com.ey.tax.cloud.base.dto.system;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-07-04 11:08:10
 * 
 */
@Data
@Schema(description = "根据条件查询实体")
public class SystemQueryDTO {
    /**
     * 系统编码，全局唯一 COLUMN:sys_code
     */
    @Schema(description = "系统编码，全局唯一", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String sysCode;

    /**
     * 系统名称 COLUMN:sys_name
     */
    @Schema(description = "系统名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String sysName;

    /**
     * 状态 启用 enable 禁用 disabled COLUMN:sys_state
     */
    @Schema(description = "状态 启用 enable 禁用 disabled", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String sysState;

    /**
     * 系统来源，平台内部 inside 平台外部 external COLUMN:sys_source
     */
    @Schema(description = "系统来源，平台内部 inside 平台外部 external", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String sysSource;

    /**
     * 显示顺序，1 2 3 COLUMN:sort_index
     */
    @Schema(description = "显示顺序，1 2 3", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer sortIndex;

    /**
     * 路由 COLUMN:route
     */
    @Schema(description = "路由", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String route;

    @Schema(description = "父系统编码")
    private String parentCode;

}
