package com.ey.tax.cloud.base.service.lang.impl;

import com.ey.cn.tax.framework.mybatisplus.extension.service.AbstractService;
import com.ey.tax.cloud.base.entity.lang.Lang;
import com.ey.tax.cloud.base.mapper.lang.LangMapper;
import com.ey.tax.cloud.service.lang.LangService;
import org.springframework.stereotype.Service;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2023-09-09 15:50:11
 * 
 */
@Service
public class LangServiceImpl extends AbstractService<LangMapper, Lang> implements LangService {
}