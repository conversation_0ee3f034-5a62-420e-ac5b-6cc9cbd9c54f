package com.ey.tax.cloud.base.config;

import com.ey.cn.tax.framework.rabbit.postprocessor.AfterReceiveMessagePostProcessor;
import com.ey.cn.tax.framework.utils.DataUtils;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.annotation.EnableRabbit;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

@Configuration
@EnableRabbit
public class RabbitMQConfig {

    @Value("${ey.rabbit.props.exchange-names.datasource}")
    private String exchangeName;

    @Bean
    public FanoutExchange exchange() {
        return new FanoutExchange(exchangeName);
    }

    //@Bean
    public Queue queue() {
        Map<String, Object> queueArguments = new HashMap<>();
        return new Queue("datasource-test", false, true, true, queueArguments);
    }

    //@Bean
    public Binding binding() {
        return BindingBuilder.bind(queue()).to(exchange());
    }

    @Bean
    public SimpleRabbitListenerContainerFactory consumerBatchContainerFactory(ConnectionFactory connectionFactory, ObjectProvider<AfterReceiveMessagePostProcessor> afterReceivePostProcessors) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);
        factory.setBatchListener(true);
        //每次接收条数
        factory.setBatchSize(10);
        //十秒内没有数据再入队列，也执行
        factory.setReceiveTimeout(1000L * 10);
        factory.setConsumerBatchEnabled(true);
        factory.setAfterReceivePostProcessors(afterReceivePostProcessors.orderedStream().toArray(MessagePostProcessor[]::new));
        return factory;
    }

}
