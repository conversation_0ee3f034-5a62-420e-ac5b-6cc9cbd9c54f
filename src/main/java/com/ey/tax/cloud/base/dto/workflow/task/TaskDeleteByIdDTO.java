package com.ey.tax.cloud.base.dto.workflow.task;

import com.ey.cn.tax.framework.validator.DeleteByIdGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2025-03-10 15:28:32
 * 
 */
@Data
@Schema(description = "根据id删除实体")
public class TaskDeleteByIdDTO {
    
    /**
     * 数据id
     */
    @Schema(description = "数据id")
    @NotBlank(message = "{TaskDTO.id.NotBlank}", groups = {DeleteByIdGroup.class})
    private String id;
}