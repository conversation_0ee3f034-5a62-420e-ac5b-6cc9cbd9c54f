package com.ey.tax.cloud.base.entity.resource;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ey.cn.tax.framework.mybatisplus.core.entity.BaseEntity;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2023-08-18 15:21:14
 *
 */
@Data
@TableName("base_man_resource")
public class ManResource extends BaseEntity {

    /**
     * 父节点 COLUMN:parent_id
     */
    private String parentId;

    /**
     * 资源类型 COLUMN:resource_type
     */
    private Integer resourceType;

    /**
     * 菜单路由 COLUMN:resource_route
     */
    private String resourceRoute;

    /**
     * 资源编码 COLUMN:resource_code
     */
    private String resourceCode;

    /**
     * 资源名称 COLUMN:resource_name
     */
    private String resourceName;

    /**
     * 图标 COLUMN:resource_icon
     */
    private String resourceIcon;

    /**
     * 排序 COLUMN:resource_order
     */
    private Integer resourceOrder;

    /**
     * 国际化code COLUMN:i18n_code
     */
    private String i18nCode;

    /**
     * 导航显示 COLUMN:navigation_view
     */
    private Integer navigationView;


    private String remark;

}
