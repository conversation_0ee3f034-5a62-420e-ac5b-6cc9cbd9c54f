package com.ey.tax.cloud.base.dto.dictionary;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-04-12 21:58:32
 */
@Data
@Schema(description = "查询返回实体")
public class DictionaryRepLayerDTO implements Serializable {
    /**
     * COLUMN:dict_code
     */
    @Schema(description = "字典编码")
    private String dictCode;

    /**
     * COLUMN:property1
     */
    @Schema(description = "属性 1")
    private String property1;

    /**
     * COLUMN:property2
     */
    @Schema(description = " 属性 2")
    private String property2;

    /**
     * COLUMN:property3
     */
    @Schema(description = "属性 3")
    private String property3;

    /**
     * COLUMN:sort_index
     */
    @Schema(description = "排序")
    private Integer sortIndex;

    /**
     * COLUMN:parent_code
     */
    @Schema(description = "父字典编码")
    private String parentCode;

    /**
     * COLUMN:dict_name
     */
    @Schema(description = "字典名称")
    private String dictName;

    /**
     * COLUMN:sys_code
     */
    @Schema(description = "系统编码")
    private String sysCode;

    /**
     * COLUMN:app_code
     */
    @Schema(description = "应用编码")
    private String appCode;

    @Schema(description = "子节点")
    private List<DictionaryRepLayerDTO> children = new ArrayList<>();
}