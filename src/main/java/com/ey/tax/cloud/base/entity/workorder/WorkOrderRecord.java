package com.ey.tax.cloud.base.entity.workorder;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ey.cn.tax.framework.mybatisplus.core.entity.BaseEntity;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-02-24 20:13:47
 * 
 */
@Data
@TableName("base_work_order_record")
public class WorkOrderRecord extends BaseEntity {
    /**
     * 工单表ID COLUMN:wo_id
     */
    private String woId;

    /**
     * 工单提交内容，处理意见等 COLUMN:record_describe
     */
    private String recordDescribe;

    /**
     * 操作后的最新工单状态 COLUMN:snapshot_state
     */
    private String snapshotState;

    /**
     * 操作人 COLUMN:operator_uid
     */
    private String operatorUid;

    /**
     * 操作人邮箱 COLUMN:operator_email
     */
    private String operatorEmail;
}