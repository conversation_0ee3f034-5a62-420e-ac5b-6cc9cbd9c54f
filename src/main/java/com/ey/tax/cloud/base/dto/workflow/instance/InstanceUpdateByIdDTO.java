package com.ey.tax.cloud.base.dto.workflow.instance;

import com.ey.cn.tax.framework.validator.UpdateByIdGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2025-03-10 15:28:31
 * 
 */
@Data
@Schema(description = "根据id批量更新实体")
public class InstanceUpdateByIdDTO {
    
    /**
     * 数据id
     */
    @Schema(description = "数据id")
    @NotBlank(message = "{InstanceDTO.id.NotBlank}", groups = {UpdateByIdGroup.class})
    private String id;

    /**
     * 流程定义id COLUMN:flow_define_id
     */
    @Schema(description = "流程定义id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String flowDefineId;

    /**
     * 流程模型id COLUMN:flow_model_id
     */
    @Schema(description = "流程模型id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String flowModelId;

    /**
     * 任务序列 COLUMN:flow_step
     */
    @Schema(description = "任务序列", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer flowStep;

    /**
     * 流程状态 COLUMN:flow_status
     */
    @Schema(description = "流程状态", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String flowStatus;

    /**
     * 流程code COLUMN:flow_code
     */
    @Schema(description = "流程code", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String flowCode;
}