package com.ey.tax.cloud.base.groovy

import com.ey.cn.tax.framework.context.EyUserContextHolder
import com.ey.cn.tax.framework.dto.ResponseDTO
import com.ey.cn.tax.framework.execution.IExecutionContext
import com.ey.cn.tax.framework.mybatisplus.core.toolkit.EyWrappers
import com.ey.cn.tax.lite.execution.dataset.IDatasetGroovyRunnable
import com.ey.tax.cloud.app.entity.userGroup.AppUserGroup
import com.ey.tax.cloud.app.mapper.userGroup.AppUserGroupMapper
import org.apache.commons.collections4.CollectionUtils
import org.apache.commons.lang3.StringUtils

import java.util.stream.Collectors

class AppGroupUserBinding implements IDatasetGroovyRunnable {

    AppUserGroupMapper appUserGroupMapper;

    @Override
    public Object run(Object args, Map<String, Object> dependentDatasets, IExecutionContext executionContext) {

        appUserGroupMapper = executionContext.getBean(AppUserGroupMapper.class).get();

        String tenantId = EyUserContextHolder.get().getTenantId();
        String sysCode = EyUserContextHolder.get().getSyscode();
        String appCode = EyUserContextHolder.get().getAppcode();

        Map<String,Object> arg = (Map<String,Object>)args;

//        if(StringUtils.isEmpty(sysCode) || StringUtils.isEmpty(appCode)){
//            log.info("Syscode或Appcode为空")
//            return null;
//        }

        String groupId = arg.get("groupId");
        List<String> userIds = arg.get("userIds");
        List<AppUserGroup> haveList = appUserGroupMapper.selectList(EyWrappers.query(AppUserGroup.class)
                .eq(StringUtils.isNotBlank(sysCode),"sys_code",sysCode)
                .eq(StringUtils.isNotBlank(appCode),"app_code",appCode)
                .eq("group_id",groupId))
        Set<String> haveUserIds = haveList.stream().map(e->{ return e.getUserId()}).collect(Collectors.toSet());
        if(StringUtils.isNotBlank(groupId) && CollectionUtils.isNotEmpty(userIds)){
            List<AppUserGroup> grs =  userIds.stream()
                    .filter (e-> !haveUserIds.contains(e))
                    .map (e->{
                        AppUserGroup entity = new AppUserGroup();
                        entity.setSysCode(sysCode);
                        entity.setAppCode(appCode);
                        entity.setGroupId(groupId);
                        entity.setUserId(e);
                        return entity;
                    }).collect(Collectors.toList());
//            appUserGroupMapper.delete(EyWrappers.query(AppUserGroup.class)
//                    .eq(StringUtils.isNotBlank(sysCode),"sys_code",sysCode)
//                    .eq(StringUtils.isNotBlank(appCode),"app_code",appCode)
//                    .eq("group_id",groupId))
            if(CollectionUtils.isNotEmpty(grs)){
                appUserGroupMapper.insertBatch(grs);
            }
        }
        return ResponseDTO.success();
    }
}

