package com.ey.tax.cloud.base.groovy

import com.ey.cn.tax.framework.context.EyUserContextHolder
import com.ey.cn.tax.framework.execution.IExecutionContext
import com.ey.cn.tax.framework.mybatisplus.core.toolkit.EyWrappers
import com.ey.cn.tax.lite.execution.dataset.IDatasetGroovyRunnable
import com.ey.tax.cloud.app.entity.resourceInterface.AppResourceInterface
import com.ey.tax.cloud.app.mapper.resourceInterface.AppResourceInterfaceMapper
import org.apache.commons.collections4.CollectionUtils
import org.apache.commons.lang3.StringUtils

class AppResourceInterfaceDetail implements IDatasetGroovyRunnable {

    AppResourceInterfaceMapper appResourceInterfaceMapper;

    @Override
    public Object run(Object args, Map<String, Object> dependentDatasets, IExecutionContext executionContext) {

        appResourceInterfaceMapper = executionContext.getBean(AppResourceInterfaceMapper.class).get();

        String tenantId = EyUserContextHolder.get().getTenantId();
        String sysCode = EyUserContextHolder.get().getSyscode();
        String appCode = EyUserContextHolder.get().getAppcode();

        Map<String,Object> arg = (Map<String,Object>)args;

//        if(StringUtils.isEmpty(sysCode) || StringUtils.isEmpty(appCode)){
//            log.info("Syscode或Appcode为空")
//            return null;
//        }

        Map<String,Object> result = new HashMap<>();
        List<String> resourceIds = arg.get("ids");
        if(CollectionUtils.isNotEmpty(resourceIds)){
            String resourceId = resourceIds.get(0);
            List<AppResourceInterface> list = appResourceInterfaceMapper.selectList(EyWrappers.query(AppResourceInterface.class)
                    .eq(StringUtils.isNotBlank(sysCode),"sys_code",sysCode)
                    .eq(StringUtils.isNotBlank(appCode),"app_code",appCode)
                    .eq("resource_id",resourceId));
            result.put("interfaceList",list)
        }
        return result;
    }
}