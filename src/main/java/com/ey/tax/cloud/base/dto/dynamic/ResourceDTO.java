package com.ey.tax.cloud.base.dto.dynamic;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ey.cn.tax.framework.mybatisplus.core.entity.BaseEntity;
import lombok.Data;


@Data
public class ResourceDTO extends BaseEntity {
    /**
     * 父节点 COLUMN:parent_id
     */
    private String parentId;

    /**
     * 资源类型 COLUMN:resource_type
     */
    private Integer resourceType;

    /**
     * 路由 COLUMN:resource_route
     */
    private String resourceRoute;

    /**
     * 资源编码 COLUMN:resource_code
     */
    private String resourceCode;

    /**
     * 资源名称 COLUMN:resource_name
     */
    private String resourceName;

    /**
     * 资源图标 COLUMN:resource_icon
     */
    private String resourceIcon;

    /**
     * 排序 COLUMN:resource_order
     */
    private Integer resourceOrder;

    /**
     * 国际化code COLUMN:i18n_code
     */
    private String i18nCode;

    /**
     * 导航显示 COLUMN:navigation_view
     */
    private Integer navigationView;


    private String subAppCode;

    /**
     * 系统编码
     */
    private String sysCode;

    /**
     * 应用编码
     */
    private String appCode;
}
