package com.ey.tax.cloud.base.dto.maintain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 查询单个维护通知实体
 */
@Data
@Schema(description = "查询单个维护通知实体")
public class MaintainNoticeQueryNoticeDTO {
    /**
     * 通知id COLUMN:id
     */
    @Schema(description = "通知id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String id;

    /**
     * 作用类型（1：管理端，2:租户端，3:管理端和租户端） COLUMN:type
     */
    @Schema(description = "作用类型（1：管理端，2:租户端，3:管理端和租户端）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Short type;
}