package com.ey.tax.cloud.base.service.app.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ey.cn.tax.framework.constant.SystemConstants;
import com.ey.cn.tax.framework.context.EyUserContextHolder;
import com.ey.cn.tax.framework.dto.ComboBoxDTO;
import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.entity.Search;
import com.ey.cn.tax.framework.mybatisplus.core.query.EyQueryWrapper;
import com.ey.cn.tax.framework.mybatisplus.core.toolkit.EyWrappers;
import com.ey.cn.tax.framework.mybatisplus.extension.service.AbstractService;
import com.ey.cn.tax.framework.utils.ConvertUtils;
import com.ey.cn.tax.framework.utils.GroovyReflectionUtils;
import com.ey.cn.tax.framework.utils.JacksonUtils;
import com.ey.cn.tax.lite.admin.entity.BaseGlobalParamsEntity;
import com.ey.cn.tax.lite.admin.entity.BaseSystemEntity;
import com.ey.cn.tax.lite.admin.entity.SysDatasetEntity;
import com.ey.cn.tax.lite.admin.mapper.BaseGlobalParamsMapper;
import com.ey.cn.tax.lite.admin.mapper.SysDatasetMapper;
import com.ey.cn.tax.lite.api.domain.repository.ISystemRepository;
import com.ey.cn.tax.lite.api.domain.service.IDefaultSystem;
import com.ey.tax.cloud.admin.dto.resource.ManResDTO;
import com.ey.tax.cloud.admin.dto.resource.ManResInterfResDTO;
import com.ey.tax.cloud.admin.dto.resource.ManResParamDTO;
import com.ey.tax.cloud.admin.dto.resource.ManResourceResDTO;
import com.ey.tax.cloud.admin.remote.resource.ManResourceFeignClient;
import com.ey.tax.cloud.app.dto.resourceInterface.AppResInterDTO;
import com.ey.tax.cloud.app.entity.resource.AppResource;
import com.ey.tax.cloud.app.entity.resourceInterface.AppResourceInterface;
import com.ey.tax.cloud.app.mapper.resource.AppResourceMapper;
import com.ey.tax.cloud.app.service.resource.AppResourceService;
import com.ey.tax.cloud.base.constants.CommonConstant;
import com.ey.tax.cloud.base.constants.ErrorCodeConstans;
import com.ey.tax.cloud.base.dto.*;
import com.ey.tax.cloud.base.dto.app.AppDTO;
import com.ey.tax.cloud.base.dto.app.*;
import com.ey.tax.cloud.base.entity.app.App;
import com.ey.tax.cloud.base.entity.module.Module;
import com.ey.tax.cloud.base.entity.user.TenUser;
import com.ey.tax.cloud.base.enums.system.SysStateEnum;
import com.ey.tax.cloud.base.handler.AppCloneFeignFactory;
import com.ey.tax.cloud.base.mapper.app.AppMapper;
import com.ey.tax.cloud.base.mapper.module.ModuleMapper;
import com.ey.tax.cloud.base.mapper.user.TenUserMapper;
import com.ey.tax.cloud.base.remote.app.AppCloneFeignService;
import com.ey.tax.cloud.base.service.app.AppCloneService;
import com.ey.tax.cloud.base.service.app.AppService;
import com.ey.tax.cloud.base.service.app.clone.BaseTabelCloneContext;
import com.ey.tax.cloud.base.service.app.clone.BaseTableClone;
import com.ey.tax.cloud.base.service.app.clone.v2.BaseTableCloneV2;
import com.ey.tax.cloud.base.service.module.ModuleService;
import com.ey.tax.cloud.base.service.system.SystemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.collections.api.factory.Lists;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * @date 2024-07-04 14:57:19
 */
@Slf4j
@Service
public class AppServiceImpl extends AbstractService<AppMapper, App> implements AppService, IDefaultSystem {

    @Autowired
    private SystemService systemService;

    @Autowired
    private ISystemRepository systemRepository;

    @Autowired
    ModuleService moduleService;

    @Autowired
    ModuleMapper moduleMapper;


    @Autowired
    private AppResourceService appResourceService;

    @Autowired
    private ManResourceFeignClient manResourceFeignClient;

    @Autowired
    private AppResourceMapper appResourceMapper;

    @Autowired
    private TenUserMapper tenUserMapper;

    @Autowired
    private BaseGlobalParamsMapper globalParamsMapper;


    @Autowired
    ObjectFactory<HttpMessageConverters> messageConverters;

    @Autowired
    BaseTabelCloneContext baseTabelCloneContext;

    @Autowired
    AppCloneService appCloneService;

    final String SERVICE_ADRESS = "ServiceAdress";

    @Autowired
    private SysDatasetMapper sysDatasetMapper;

    @Autowired
    private List<BaseTableCloneV2> clones;


    @Override
    public Map<String, List<ComboBoxDTO<String>>> getComBoxBySystem(AppDTO dto, boolean isContainNone) {
        Map<String, List<ComboBoxDTO<String>>> res = new HashMap<>();

        String contextSysCode = EyUserContextHolder.get().getSyscode();
        if (isContainNone && EyUserContextHolder.get().isNoneSys()) {
            res.put(SystemConstants.NONE, buildNone());
            // 构建中心
            res.put(SystemConstants.SYS, buildNone());
            // 平台管理
            res.put(SystemConstants.ADMIN, buildNone());

            BaseSystemEntity system = new BaseSystemEntity();
            system.setSysState(SysStateEnum.ENABLE.getKey());
            List<BaseSystemEntity> systems = systemService.queryByPara(system);
            for (BaseSystemEntity sys : systems) {
                res.put(sys.getSysCode(), buildNone());
            }
        } else if (isContainNone) {
            res.put(contextSysCode, buildNone());
        }

        App param = ConvertUtils.convertDTO2Entity(dto, App.class);
        List<App> apps = this.queryByPara(param);
        if (CollectionUtils.isNotEmpty(apps)) {
            for (App app : apps) {
                String appCode = app.getAppCode();
                String sysCode = app.getSysCode();
                // 如何head有sysCode数据，只返回对应的数据
                if (!EyUserContextHolder.get().isNoneSys() && !sysCode.equalsIgnoreCase(contextSysCode)) {
                    continue;

                }

                res.putIfAbsent(sysCode, Lists.mutable.empty());
                res.get(sysCode).add(new ComboBoxDTO<>(appCode, appCode));
            }

        }
        return res;
    }

    @Override
    public boolean save(App entity) {
        App param = new App();
        param.setSysCode(entity.getSysCode());
        param.setAppCode(entity.getAppCode());
        List<App> List = this.queryByPara(param);
        if (CollectionUtils.isNotEmpty(List)) {
            throwBuildBusinessException(ErrorCodeConstans.APP_EXIST_ERROR);
        }
        return super.save(entity);
    }

    @Override
    public Search<AppRepDTO> queryPage(Search<App> search) throws Exception {
        this.logger.debug("queryPageByPara {}:{}", this.getEntitySimpleName(), search);
        App queryParams = search.getQueryParams();
        EyQueryWrapper<App> queryWrapper = EyWrappers.query();
        queryWrapper.eq(StringUtils.isNotBlank(queryParams.getSysCode()), "sys_code", queryParams.getSysCode());
        queryWrapper.ilike(StringUtils.isNotBlank(queryParams.getAppCode()), "app_code", queryParams.getAppCode());
        queryWrapper.ilike(StringUtils.isNotBlank(queryParams.getAppName()), "app_name", queryParams.getAppName());


        if (queryParams.getQueryExtension() == null) {
            queryWrapper.extension(search.getQueryExtension());
        }

        if (!queryWrapper.isOrdered()) {
            queryWrapper.orderByDesc("update_time");
        }

        IPage<App> iPage = baseMapper.selectPage(GroovyReflectionUtils.convertTo(Page.class, search.getPage(), true), queryWrapper);
        List<App> records = iPage.getRecords();
        search.setRecords(records);
        if (CollectionUtils.isEmpty(records)) {
            search.getPage().setTotalCount(0L);
        } else {
            this.processUserName(records);
            search.getPage().setTotalCount(iPage.getTotal());
        }
        Search<AppRepDTO> resPage = ConvertUtils.convertSearch2SearchDTO(search, AppRepDTO.class);
        systemRepository.processSysName(resPage.getRecords());
        return resPage;
    }

    @Override
    public List<ComboBoxDTO<String>> buildNone() {
        List<ComboBoxDTO<String>> none = new ArrayList<>();
        none.add(new ComboBoxDTO<>(SystemConstants.NONE, SystemConstants.NONE));
        return none;
    }

    @Override
    public List<AppDTO> queryListBySyscode() {
        String sysCode = EyUserContextHolder.get().getSyscode();
        App param = new App();
        param.setSysCode(sysCode);
        List<App> app = this.queryByPara(param);

        if (CollectionUtils.isNotEmpty(app)) {
            List<AppResource> appResources = appResourceMapper.selectList(
                    EyWrappers.lambdaQuery(AppResource.class)
                            .eq(AppResource::getSysCode, sysCode)
            );
            Map<String, String> map = appResources.stream().filter(e -> StringUtils.isNotBlank(e.getResourceCode())).collect(HashMap::new, (m, r) -> m.put(r.getResourceCode(), r.getResourceIcon()), HashMap::putAll);
            return app.stream().map(e -> {
                AppDTO dto = ConvertUtils.convertEntity2DTO(e, AppDTO.class);
                dto.setResourceIcon(map.get(sysCode + "_" + e.getAppCode()));
                return dto;
            }).collect(Collectors.toList());
        }

        return new ArrayList<>();
    }

    @Override
    public List<ComboBoxDTO<String>> queryUser(AppTenUserDTO param, boolean isKeyMail) {
        if (param.getUserEmail() == null) {
            return new ArrayList<>();
        }
        List<TenUser> users = new ArrayList<>();
        if (param.getUserEmail() instanceof String) {
            String email = String.valueOf(param.getUserEmail());
            if (StringUtils.isNotBlank(email)) {
                users = tenUserMapper.selectList(EyWrappers.query(TenUser.class).ilike("user_email", email));
            }

        } else {
            Collection userIds = (Collection) param.getUserEmail();
            if (CollectionUtils.isNotEmpty(userIds)) {
                users = tenUserMapper.selectBatchIds((Collection) param.getUserEmail());
            }

        }
        return users.stream().map(e -> {
            ComboBoxDTO<String> dto = new ComboBoxDTO<>();
            dto.setLabel(e.getUserEmail());
            if (isKeyMail) {
                dto.setValue(e.getUserEmail());
            } else {
                dto.setValue(e.getId());
            }
            return dto;
        }).collect(Collectors.toList());

    }

    @Override
    public AppDTO queryByAppCode(String appCode) {
        String sysCode = EyUserContextHolder.get().getSyscode();
        App param = new App();
        param.setSysCode(sysCode);
        param.setAppCode(appCode);
        App app = this.queryOneByPara(param);

        if (app != null) {
            List<AppResource> appResources = appResourceMapper.selectList(EyWrappers.lambdaQuery(AppResource.class).eq(AppResource::getSysCode, sysCode));
            Map<String, String> map = appResources
                    .stream()
                    .filter(e -> StringUtils.isNotBlank(e.getResourceCode()))
                    .collect(HashMap::new, (m, r) -> m.put(r.getResourceCode(), r.getResourceIcon()), HashMap::putAll);
            AppDTO dto = ConvertUtils.convertEntity2DTO(app, AppDTO.class);
            dto.setResourceIcon(map.get(sysCode + "_" + app.getAppCode()));
            return dto;
        }
        return null;
    }

    @Override
    public void initResource(AppInitResourceDTO dto) {
        App app = queryById(dto.getId());
        Module module = executeLocalInit(dto, app);

        AppInitResourceFeignDTO p = new AppInitResourceFeignDTO();
        p.setModule(ConvertUtils.convertEntity2DTO(module, ModuleFeignDTO.class));
        p.setApp(ConvertUtils.convertEntity2DTO(app, com.ey.tax.cloud.base.dto.AppDTO.class));
        p.setResourceIds(dto.getResourceIds());
        if (module != null) {
            AppCloneFeignService appCloneFeignService = getServiceFeign(app.getSysCode());
            ResponseDTO<AppInitResourceFeignRes> res = appCloneFeignService.appInit(p);
        } else {
            initResourceCallBack(p);
        }

    }

    @Override
    public void initResourceCallBack(AppInitResourceFeignDTO dto) {
        com.ey.tax.cloud.base.dto.AppDTO app = dto.getApp();

        AppResInterDTO resInterDTO = new AppResInterDTO();
        resInterDTO.setSysCode(app.getSysCode());
        resInterDTO.setAppCode(app.getAppCode());
        resInterDTO.setRootResourceIds(dto.getResourceIds());

        ManResParamDTO param = new ManResParamDTO();
        param.setResourceId(dto.getResourceIds());
        //初始化管理资源
        ResponseDTO<ManResDTO> res = manResourceFeignClient.getAppResource(param);
        if (res != null && res.getResult() != null) {
            if (CollectionUtils.isNotEmpty(res.getResult().getResources())) {
                for (ManResourceResDTO r : res.getResult().getResources()) {
                    AppResource ar = ConvertUtils.convertDTO2Entity(r, AppResource.class);
                    resInterDTO.getResources().add(ar);
                }
            }
            if (CollectionUtils.isNotEmpty(res.getResult().getResInterfs())) {
                for (ManResInterfResDTO resInterf : res.getResult().getResInterfs()) {
                    AppResourceInterface ri = ConvertUtils.convertDTO2Entity(resInterf, AppResourceInterface.class);
                    resInterDTO.getResourceInterface().add(ri);
                }
            }
        }
        appResourceService.initAppCommonResource(resInterDTO);

    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void cloneApp(AppCloneDTO dto) {
        App targetApp = queryById(dto.getId());

        List<Module> modules = moduleMapper.selectList(Wrappers.lambdaQuery(Module.class).in(Module::getId, dto.getModuleId()).orderByAsc(Module::getModuleOrder));
        // 检查
        check(targetApp.getSysCode(),dto.getSourceAppCode(),modules);

        App param = new App();
        param.setSysCode(targetApp.getSysCode());
        param.setAppCode(dto.getSourceAppCode());
        App sourceApp = queryOneByPara(param);

        AppCloneFeignService appCloneFeignService = getServiceFeign(targetApp.getSysCode());
        List<String> moduleCodes = new ArrayList<>();
        for (Module module : modules) {
            AppCloneFeignDTO p = new AppCloneFeignDTO();
            ModuleFeignDTO d = ConvertUtils.convertEntity2DTO(module, ModuleFeignDTO.class);
            p.setModule(d);
            p.setSourceApp(ConvertUtils.convertEntity2DTO(sourceApp, com.ey.tax.cloud.base.dto.AppDTO.class));
            p.setTargetApp(ConvertUtils.convertEntity2DTO(targetApp, com.ey.tax.cloud.base.dto.AppDTO.class));
            logger.info("clone moduleId:{} ，参数：{}，开始调用", module.getId(), JacksonUtils.toJson(p));
            ResponseDTO<AppCloneFeignRes> res = appCloneFeignService.appClone(p);
            logger.info("clone moduleId:{} 结束调用", module.getId());
            moduleCodes.add(module.getModuleCode());
        }

        // 克隆module
        appCloneService.cloneModule(sourceApp.getSysCode(), sourceApp.getAppCode(), targetApp.getSysCode(), targetApp.getAppCode(), moduleCodes);


    }

    /**
     * 全量克隆
     * @param dto
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void cloneAppAll(AppCloneAllFeignDTO dto) {
        AppCloneAllResDTO result = new AppCloneAllResDTO();
        for(BaseTableCloneV2 clone : clones){
            clone.clone(dto,result);
        }
    }

    @Override
    public void cloneBaseTable(AppBaseCloneFeignDTO dto) {
        BaseTableClone baseTableClone = baseTabelCloneContext.getBean();
        baseTableClone.setAppCloneService(appCloneService);
        baseTableClone.clone(dto);
    }

    @Override
    public AppCloneFeignService getServiceFeign(String sysCode) {

        BaseGlobalParamsEntity params = globalParamsMapper.selectOne(Wrappers.lambdaQuery(BaseGlobalParamsEntity.class)
                .eq(BaseGlobalParamsEntity::getSysCode, CommonConstant.SystemCode.SYS)
                .eq(BaseGlobalParamsEntity::getAppCode, CommonConstant.SystemCode.NONE)
                .eq(BaseGlobalParamsEntity::getParamCode, SERVICE_ADRESS));
        if (params == null) {
            throwBuildBusinessException(ErrorCodeConstans.APP_CLONE_FEIGN_GLOBAL_PARA_CODE_NOT_CONFIG);
        }
        Map<String, String> feignUrl = params.<Map>convertValue();

        if (feignUrl == null || !feignUrl.containsKey(sysCode)) {
            throwBuildBusinessException(ErrorCodeConstans.APP_CLONE_FEIGN_GLOBAL_PARA_CODE_NOT_CONFIG);
        }

        String url = feignUrl.get(sysCode);

//        url = "http://localhost:8082";

        log.info("clone sysCode:{} feign url:{}", sysCode, url);

        AppCloneFeignFactory factory = new AppCloneFeignFactory(url, messageConverters);
        return factory.getClient();
    }

    private Module executeLocalInit(AppInitResourceDTO dto, App app) {
        if (StringUtils.isBlank(dto.getModuleId())) {
            return null;
        }

        Module module = moduleService.queryById(dto.getModuleId());

        AppInitResourceFeignDTO p = new AppInitResourceFeignDTO();
        p.setResourceIds(dto.getResourceIds());
        p.setApp(ConvertUtils.convertEntity2DTO(app, com.ey.tax.cloud.base.dto.AppDTO.class));

        if (module == null || StringUtils.isBlank(module.getInitDataset())) {
            initResourceCallBack(p);
            return null;
        }

        Long count = sysDatasetMapper.selectCount(Wrappers.lambdaQuery(SysDatasetEntity.class)
                .eq(SysDatasetEntity::getSysCode, module.getSysCode())
                .eq(SysDatasetEntity::getAppCode, module.getAppCode())
                .eq(SysDatasetEntity::getCode, module.getInitDataset()));

        if (count < 1) {
            initResourceCallBack(p);
            return null;
        }

        return module;
    }

    private void check(String sourceSysCode,String sourceAppCode,List<Module> modules){
        if(CollectionUtils.isNotEmpty(modules)){
            List<SysDatasetEntity> datassets = sysDatasetMapper.selectList(Wrappers.lambdaQuery(SysDatasetEntity.class)
                    .eq(SysDatasetEntity::getSysCode,sourceSysCode)
                    .eq(SysDatasetEntity::getAppCode,sourceAppCode));
            Set<String> datasetCodes = datassets.stream().map(SysDatasetEntity::getCode).collect(Collectors.toSet());
            for(Module m:modules){
                if(StringUtils.isBlank(m.getCloneDataset()) || !datasetCodes.contains(m.getCloneDataset())){
                    throwBuildBusinessException(ErrorCodeConstans.APP_MODULE_CLONEDATASET_NOT_EXIST,m.getModuleName(),m.getCloneDataset());
                }
            }

        }


    }
}
