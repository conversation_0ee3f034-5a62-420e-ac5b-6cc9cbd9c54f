package com.ey.tax.cloud.base.dto.maintain;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2023-11-28 16:10:22
 * 
 */
@Data
@Schema(description = "根据条件删除实体")
public class MaintainNoticeDeleteDTO {
    /**
     * 通知标题 COLUMN:title
     */
    @Schema(description = "通知标题", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String title;

    /**
     * 通知内容 COLUMN:content
     */
    @Schema(description = "通知内容", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String content;

    /**
     * 通知开始时间 COLUMN:notice_start_time
     */
    @Schema(description = "通知开始时间", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDateTime noticeStartTime;

    /**
     * 通知结束时间 COLUMN:notice_end_time
     */
    @Schema(description = "通知结束时间", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDateTime noticeEndTime;

    /**
     * 维护开始时间 COLUMN:maintain_start_time
     */
    @Schema(description = "维护开始时间", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDateTime maintainStartTime;

    /**
     * 维护结束时间 COLUMN:maintain_end_time
     */
    @Schema(description = "维护结束时间", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDateTime maintainEndTime;

    /**
     * 作用类型（1：管理端，2:租户端，3:管理端和租户端） COLUMN:type
     */
    @Schema(description = "作用类型（1：管理端，2:租户端，3:管理端和租户端）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Short type;
}