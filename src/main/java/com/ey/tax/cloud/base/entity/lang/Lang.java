package com.ey.tax.cloud.base.entity.lang;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ey.cn.tax.framework.mybatisplus.core.entity.BaseEntity;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2023-09-09 15:50:11
 * 
 */
@Data
@TableName("sys_lang")
public class Lang extends BaseEntity {
    /**
     * 语种编码 COLUMN:lang_code
     */
    private String langCode;

    /**
     * 语种名称 COLUMN:lang_name
     */
    private String langName;

    /**
     * 语种简称 COLUMN:lang_abbr
     */
    private String langAbbr;

    /**
     * 是否默认语种 0 否 1是 COLUMN:is_default
     */
    private Integer isDefault;
}