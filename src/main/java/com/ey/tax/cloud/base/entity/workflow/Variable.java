package com.ey.tax.cloud.base.entity.workflow;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.ey.cn.tax.framework.mybatisplus.core.entity.BaseEntity;
import com.ey.tax.cloud.base.config.JsonMapTypeHandler;
import lombok.Data;

import java.util.Map;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2025-03-10 15:28:33
 * 
 */
@Data
@TableName(value = "tax_flow_variable", autoResultMap = true)
public class Variable extends BaseEntity {
    /**
     * 变量类型 COLUMN:variable_type
     */
    private String variableType;

    /**
     * 变量值 COLUMN:variable_value
     */
    @TableField(typeHandler = JsonMapTypeHandler.class)
    private Map<String, Object> variableValue;

    /**
     * 任务id COLUMN:flow_task_id
     */
    private String flowTaskId;

    /**
     * 实例id COLUMN:flow_instance_id
     */
    private String flowInstanceId;
}