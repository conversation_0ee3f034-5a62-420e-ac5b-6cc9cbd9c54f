package com.ey.tax.cloud.base.dto.dictionary;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-05-07 18:09:35
 * 
 */
@Data
@Schema(description = "新增实体")
public class DictionaryAddDTO {
    /**
     * COLUMN:dict_code
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String dictCode;

    /**
     * COLUMN:dict_cn_name
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String dictCnName;

    /**
     * COLUMN:dict_en_name
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String dictEnName;

    /**
     * COLUMN:property1
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String property1;

    /**
     * COLUMN:property2
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String property2;

    /**
     * COLUMN:property3
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String property3;

    /**
     * COLUMN:sort_index
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer sortIndex;

    /**
     * COLUMN:parent_code
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String parentCode;
}