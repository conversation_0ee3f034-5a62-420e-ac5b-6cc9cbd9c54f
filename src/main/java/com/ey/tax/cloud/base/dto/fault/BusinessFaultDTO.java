package com.ey.tax.cloud.base.dto.fault;

import com.ey.cn.tax.framework.dto.BaseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-05-20 10:36:04
 * 
 */
@Data
@Schema(description = "默认实体")
public class BusinessFaultDTO extends BaseDTO {
    /**
     * 子系统 COLUMN:system_type
     */
    @Schema(description = "子系统")
    private String systemType;

    /**
     * 业务类型,关联数据字典,在数据字典的扩展字段里,加通知邮箱,邮箱可以配置多个,通过逗号分隔 COLUMN:business_type
     */
    @Schema(description = "业务类型,关联数据字典,在数据字典的扩展字段里,加通知邮箱,邮箱可以配置多个,通过逗号分隔")
    private String businessType;

    /**
     * 异常内容 COLUMN:fault_content
     */
    @Schema(description = "异常内容")
    private String faultContent;

    /**
     * 状态（打开,关闭） COLUMN:fault_status
     */
    @Schema(description = "状态（打开,关闭）")
    private String faultStatus;
}