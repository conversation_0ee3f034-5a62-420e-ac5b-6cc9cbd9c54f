package com.ey.tax.cloud.base.dto.workorder;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-05-07 17:52:06
 * 
 */
@Data
@Schema(description = "新增实体")
public class WorkOrderFileAddDTO {
    /**
     * COLUMN:file_path
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String filePath;

    /**
     * COLUMN:file_name
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String fileName;

    /**
     * COLUMN:file_type
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String fileType;

    /**
     * COLUMN:work_order_id
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String workOrderId;
}