package com.ey.tax.cloud.base.service.user.impl;

import com.ey.cn.tax.framework.context.EyUserContextHolder;
import com.ey.cn.tax.framework.mybatisplus.core.query.EyQueryWrapper;
import com.ey.cn.tax.framework.mybatisplus.extension.service.AbstractService;
import com.ey.cn.tax.framework.utils.EmptyUtils;
import com.ey.tax.cloud.base.entity.user.UserLeave;
import com.ey.tax.cloud.base.mapper.user.UserLeaveMapper;
import com.ey.tax.cloud.base.service.user.UserLeaveService;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-11-04 15:59:52
 * 
 */
@Service
public class UserLeaveServiceImpl extends AbstractService<UserLeaveMapper, UserLeave> implements UserLeaveService {

    @Override
    public void submitUserLeaveStatus(UserLeave userLeave) {
        String id = userLeave.getId();
        userLeave.setTenantId(EyUserContextHolder.get().getTenantId());
        userLeave.setUserId(EyUserContextHolder.get().getUserId());
        if(EmptyUtils.isEmpty(id)){
            this.save(userLeave);
        }else{
            this.update(userLeave);
        }
    }

    @Override
    public UserLeave queryUserLeaveStatus(UserLeave userLeave) {
        String userId = EyUserContextHolder.get().getUserId();
        LocalDateTime now = LocalDateTime.now();
        EyQueryWrapper<UserLeave> queryWrapper = new EyQueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.le("start_time", now);
        queryWrapper.ge("end_time", now);
        List<UserLeave> entities = this.baseMapper.selectList(queryWrapper);
        if(EmptyUtils.isNotEmpty(entities)){
            return entities.get(0);
        }
        return null;
    }

    @Override
    public List<UserLeave> queryUserLeaveStatusList(UserLeave userLeave) {
        LocalDateTime now = LocalDateTime.now();
        EyQueryWrapper<UserLeave> queryWrapper = new EyQueryWrapper<>();
        queryWrapper.in("user_id", userLeave.getUserIds());
        queryWrapper.le("start_time", now);
        queryWrapper.ge("end_time", now);
        return this.baseMapper.selectList(queryWrapper);
    }

}