package com.ey.tax.cloud.base.controller.system;

import com.ey.cn.tax.framework.dto.ComboBoxDTO;
import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.dto.SearchDTO;
import com.ey.cn.tax.framework.validator.AddGroup;
import com.ey.cn.tax.framework.validator.DeleteByIdGroup;
import com.ey.cn.tax.framework.validator.QueryByIdGroup;
import com.ey.cn.tax.framework.validator.QueryPageGroup;
import com.ey.cn.tax.framework.validator.UpdateByIdGroup;
import com.ey.cn.tax.framework.web.annotation.EyAuthorize;
import com.ey.cn.tax.framework.web.annotation.EyGetMapping;
import com.ey.cn.tax.framework.web.annotation.EyPostMapping;
import com.ey.cn.tax.framework.web.base.BaseController;
import com.ey.cn.tax.lite.admin.entity.BaseSystemEntity;
import com.ey.tax.cloud.base.dto.SystemBaseInitFeignRes;
import com.ey.tax.cloud.base.dto.SystemInitResourceFeignDTO;
import com.ey.tax.cloud.base.dto.dictionary.DictionaryQueryByIdDTO;
import com.ey.tax.cloud.base.dto.dictionary.DictionaryRepDTO;
import com.ey.tax.cloud.base.dto.dynamic.DownloadDTO;
import com.ey.tax.cloud.base.dto.system.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-07-04 11:08:10
 * 
 */
@Tag(name="系统管理")
public interface SystemController extends BaseController<SystemDTO> {

    @Operation(summary = "查询系统下拉列表", description = "查询系统下拉列表")
    @EyGetMapping(value = "/sysOptions.do")
    ResponseDTO<List<ComboBoxDTO<String>>> querySysOptions();

    /**
     * add System from http
     */
    @Operation(summary = "系统管理-新增", description = "新增一条数据")
    @EyPostMapping(value = "/add.do")
    ResponseDTO<Void> add(@RequestBody @Validated({AddGroup.class}) SystemAddDTO dto);

    @Operation(summary = "系统管理-系统初始化资源/角色", description = "系统初始化资源/角色")
    @EyPostMapping(value = "/initResource.do")
    ResponseDTO<Void> initResource(@RequestBody @Validated SystemInitResourceDTO dto);

    @Operation(summary = "系统管理-系统初始化资源/角色回调", description = "系统初始化资源/角色回调")
    @EyPostMapping(value = "/initResourceCallBack.do")
    ResponseDTO<SystemBaseInitFeignRes> initResourceCallBack(@RequestBody @Validated SystemInitResourceFeignDTO dto);

    @Operation(summary = "根据id查询", description = "根据id查询数据")
    @EyPostMapping(value = "/queryById.do")
    ResponseDTO<SystemRepDTO> queryById(@RequestBody @Validated({QueryByIdGroup.class}) SystemQueryByIdDTO dto);
    /**
     * delete System from http
     */
    @Operation(summary = "根据id删除", description = "根据id删除一条数据")
    @EyPostMapping(value = "/deleteById.do")
    ResponseDTO<Void> deleteById(@RequestBody @Validated({DeleteByIdGroup.class}) SystemDeleteByIdDTO dto);

    /**
     * update System from http
     */
    @Operation(summary = "根据id更新", description = "根据id更新一条数据")
    @EyPostMapping(value = "/updateById.do")
    ResponseDTO<Void> updateById(@RequestBody @Validated({UpdateByIdGroup.class}) SystemUpdateByIdDTO dto);

    /**
     * query System from http
     */
    @Operation(summary = "根据code查询", description = "根据code查询")
    @EyPostMapping(value = "/queryByCode.do")
    @EyAuthorize
    ResponseDTO<SystemRepDTO> queryByCode(@RequestBody SystemQueryByCodeDTO dto);

    /**
     * Page System from http
     */
    @Operation(summary = "分页查询", description = "根据条件分页查询")
    @EyPostMapping(value = "/queryPage.do")
    ResponseDTO<SearchDTO<SystemRepDTO>> queryPage(@RequestBody @Validated({QueryPageGroup.class}) SearchDTO<SystemQueryPageDTO> searchDTO);


    @Operation(summary = "全局参数-下载")
    @EyPostMapping("/download")
    void download(@RequestBody @Validated DownloadDTO dto, HttpServletResponse resp);

    @Operation(summary = "全局参数-上传")
    @EyPostMapping("/upload")
    void upload(@RequestPart("file") MultipartFile file);


    @Operation(summary = "查询系统列表", description = "查询系统列表")
    @EyPostMapping(value = "/queryList.do")
    ResponseDTO<List<BaseSystemEntity>> queryList(@RequestBody SystemQueryDTO param);
}
