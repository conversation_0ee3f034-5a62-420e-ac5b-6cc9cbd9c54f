package com.ey.tax.cloud.base.entity.maintain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ey.cn.tax.framework.mybatisplus.core.entity.BaseEntity;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2023-11-28 16:10:22
 * 
 */
@Data
@TableName("base_maintain_notice")
public class MaintainNotice extends BaseEntity {
    /**
     * 通知标题 COLUMN:title
     */
    private String title;

    /**
     * 通知内容 COLUMN:content
     */
    private String content;

    /**
     * 通知开始时间 COLUMN:notice_start_time
     */
    private LocalDateTime noticeStartTime;

    /**
     * 通知结束时间 COLUMN:notice_end_time
     */
    private LocalDateTime noticeEndTime;

    /**
     * 维护开始时间 COLUMN:maintain_start_time
     */
    private LocalDateTime maintainStartTime;

    /**
     * 维护结束时间 COLUMN:maintain_end_time
     */
    private LocalDateTime maintainEndTime;

    /**
     * 作用类型（1：管理端，2:租户端，3:管理端和租户端） COLUMN:type
     */
    private Short type;
}