package com.ey.tax.cloud.base.controller.pdfconverter;

import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.dto.SearchDTO;
import com.ey.cn.tax.framework.validator.DeleteByIdGroup;
import com.ey.cn.tax.framework.validator.DeleteByIdListGroup;
import com.ey.cn.tax.framework.validator.QueryByIdGroup;
import com.ey.cn.tax.framework.validator.QueryByIdListGroup;
import com.ey.cn.tax.framework.validator.QueryPageGroup;
import com.ey.cn.tax.framework.validator.UpdateByIdGroup;
import com.ey.cn.tax.framework.web.annotation.EyPostMapping;
import com.ey.cn.tax.framework.web.base.BaseController;
import com.ey.tax.cloud.base.dto.pdfconverter.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;

import jakarta.servlet.http.HttpServletResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-06-28 18:19:23
 * 
 */
@Tag(name="PDF上传文件页面")
public interface PDFConverterSourceFilesController extends BaseController<PDFConverterSourceFilesDTO> {

    /**
     * Page PDFConverterSourceFiles from http
     */
    @Operation(summary = "结果页面", description = "当前用户分页查询")
    @EyPostMapping(value = "/queryResultPage.do")
    ResponseDTO<SearchDTO<PDFConverterSourceFilesResultPageRepDTO>> queryResultPage(@RequestBody @Validated({QueryPageGroup.class}) SearchDTO<PDFConverterSourceFilesQueryPageDTO> searchDTO);


    /**
     * delete PDFConverterSourceFiles from http
     */
    @Operation(summary = "根据id删除", description = "根据id删除一条数据")
    @EyPostMapping(value = "/deleteById.do")
    ResponseDTO<Void> deleteById(@RequestBody @Validated({DeleteByIdGroup.class}) PDFConverterSourceFilesDeleteByIdDTO dto);

    /**
     * delete PDFConverterSourceFiles from http
     */
    @Operation(summary = "清空列表", description = "清空当前用户数据")
    @EyPostMapping(value = "/clearData.do")
    ResponseDTO<Void> clearData(@RequestBody PDFConverterSourceFilesDeleteDTO dto);


    /**
     * upload PDFConverterSourceFiles from http
     */
    @Operation(summary = "批量上传", description = "批量文件")
    @EyPostMapping(value = "/uploadBatch.do")
    @Parameter(name = "files",description = "文件",in = ParameterIn.DEFAULT,
            schema = @Schema(name = "files",format = "binary"))
    ResponseDTO<String> uploadBatch(@RequestParam("engagementId") String engagementId,
                                                                      @RequestPart("files") MultipartFile[] files);

    /**
     * upload PDFConverterSourceFiles from http
     */
    @Operation(summary = "上传Zip", description = "上传压缩包")
    @EyPostMapping(value = "/uploadZip.do")
    ResponseDTO<String> uploadZip(@RequestParam("engagementId") String engagementId,
                                                                    @RequestPart("file") MultipartFile file);

    /**
     * queryList PDFConverterSourceFiles from http
     */
    @Operation(summary = "生成页面查询列表", description = "查询数据列表")
    @EyPostMapping(value = "/queryList.do")
    ResponseDTO<List<PDFConverterSourceFilesRepDTO>> queryList(@RequestBody PDFConverterSourceFilesQueryDTO dto);

    /**
     * generate PDFConverterSourceFiles from http
     */
    @Operation(summary = "生成PDF", description = "生成PDF文件")
    @EyPostMapping(value = "/generatePDF.do")
    ResponseDTO<String> generatePDF(@RequestBody List<PDFConverterSourceFilesGeneratePDFDTO> pdfConverterSourceFilesDTO) ;

    /**
     * download PDFConverterSourceFiles from http
     */
    @Operation(summary = "下载Zip结果", description = "下载Zip")
    @EyPostMapping(value = "/download.do")
    ResponseDTO<String> download(@RequestBody PDFConverterSourceFilesDTO pdfConverterSourceFilesDTO, HttpServletResponse resp) ;




    /**
     * Page PDFConverterSourceFiles from http
     */
    @Operation(summary = "分页查询", description = "根据条件分页查询")
    //@EyPostMapping(value = "/queryPage.do")
    ResponseDTO<SearchDTO<PDFConverterSourceFilesRepDTO>> queryPage(@RequestBody @Validated({QueryPageGroup.class}) SearchDTO<PDFConverterSourceFilesQueryPageDTO> searchDTO);

    /**
     * deleteList PDFConverterSourceFiles from http
     */
    @Operation(summary = "根据id列表删除", description = "根据id列表批量删除数据")
    //@EyPostMapping(value = "/deleteByIdList.do")
    ResponseDTO<Void> deleteByIdList(@RequestBody @Validated({DeleteByIdListGroup.class}) PDFConverterSourceFilesDeleteByIdListDTO dto);

    /**
     * update PDFConverterSourceFiles from http
     */
    @Operation(summary = "根据id更新RootFolder", description = "根据id更新一条数据")
    @EyPostMapping(value = "/updateById.do")
    ResponseDTO<String> updateRootFolderById(@RequestBody @Validated({UpdateByIdGroup.class}) PDFConverterSourceFilesUpdateByIdDTO dto);

    /**
     * updateBatch PDFConverterSourceFiles from http
     */
    @Operation(summary = "批量更新", description = "批量更新数据")
    //@EyPostMapping(value = "/updateBatch.do")
    ResponseDTO<Void> updateBatch(@RequestBody List<PDFConverterSourceFilesBatchUpdateDTO> dtos);

    /**
     * query PDFConverterSourceFiles from http
     */
    @Operation(summary = "根据id查询", description = "根据id查询数据")
    //@EyPostMapping(value = "/queryById.do")
    ResponseDTO<PDFConverterSourceFilesRepDTO> queryById(@RequestBody @Validated({QueryByIdGroup.class}) PDFConverterSourceFilesQueryByIdDTO dto);

    /**
     * queryByIdList PDFConverterSourceFiles from http
     */
    @Operation(summary = "根据id列表查询", description = "根据id列表查询数据")
    //@EyPostMapping(value = "/queryByIdList.do")
    ResponseDTO<List<PDFConverterSourceFilesRepDTO>> queryByIdList(@RequestBody @Validated({QueryByIdListGroup.class}) PDFConverterSourceFilesQueryByIdListDTO dto);




    /**
     * Count PDFConverterSourceFiles from http
     */
    @Operation(summary = "查询数量", description = "根据条件查询数量")
    //@EyPostMapping(value = "/queryCount.do")
    ResponseDTO<Long> queryCount(@RequestBody PDFConverterSourceFilesQueryDTO pDFConverterSourceFilesDTO);

    /**
     * One PDFConverterSourceFiles from http
     */
    @Operation(summary = "查询单个数据", description = "根据条件查询一条数据,如果查询到多条则返回异常.")
    //@EyPostMapping(value = "/queryOne.do")
    ResponseDTO<PDFConverterSourceFilesRepDTO> queryOne(@RequestBody PDFConverterSourceFilesQueryDTO pDFConverterSourceFilesDTO);

    /**
     * exist PDFConverterSourceFiles from http
     */
    @Operation(summary = "查询是否存在", description = "根据条件查询是否存在")
    //@EyPostMapping(value = "/exist.do")
    ResponseDTO<Boolean> exist(@RequestBody PDFConverterSourceFilesQueryDTO pDFConverterSourceFilesDTO);
}