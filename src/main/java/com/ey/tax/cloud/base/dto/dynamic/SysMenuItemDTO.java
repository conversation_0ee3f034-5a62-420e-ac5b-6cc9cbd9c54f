package com.ey.tax.cloud.base.dto.dynamic;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 * 动态页面按钮元素参数
 * </p>
 *
 * @author: <PERSON>
 * @date: 18:47 2022/1/29
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(name = "SysMenuItem", description = "动态页面按钮元素参数")
public class SysMenuItemDTO implements Serializable {
    /**
     * 按钮的中文描述信息
     */
    private String menuName;
    /**
     * 按钮元素的唯一标识
     */
    private String uniquelyMark;

    /**
     * 为子应用标识
     */
    private String sysCode;

    /**
     * 按钮对应的api路径
     */
    private String menuRouter;
}
