package com.ey.tax.cloud.base.controller.dashboard.impl;

import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.dto.SearchDTO;
import com.ey.cn.tax.framework.entity.Search;
import com.ey.cn.tax.framework.utils.ConvertUtils;
import com.ey.cn.tax.framework.web.annotation.EyRestController;
import com.ey.cn.tax.framework.web.base.AbstractController;
import com.ey.tax.cloud.base.controller.dashboard.DashboardProjectController;
import com.ey.tax.cloud.base.dto.dashboard.DashboardProjectAddDTO;
import com.ey.tax.cloud.base.dto.dashboard.DashboardProjectBatchUpdateDTO;
import com.ey.tax.cloud.base.dto.dashboard.DashboardProjectDTO;
import com.ey.tax.cloud.base.dto.dashboard.DashboardProjectDeleteByIdDTO;
import com.ey.tax.cloud.base.dto.dashboard.DashboardProjectDeleteByIdListDTO;
import com.ey.tax.cloud.base.dto.dashboard.DashboardProjectQueryByIdDTO;
import com.ey.tax.cloud.base.dto.dashboard.DashboardProjectQueryByIdListDTO;
import com.ey.tax.cloud.base.dto.dashboard.DashboardProjectQueryDTO;
import com.ey.tax.cloud.base.dto.dashboard.DashboardProjectQueryPageDTO;
import com.ey.tax.cloud.base.dto.dashboard.DashboardProjectRepDTO;
import com.ey.tax.cloud.base.dto.dashboard.DashboardProjectUpdateByIdDTO;
import com.ey.tax.cloud.base.entity.dashboard.DashboardProject;
import com.ey.tax.cloud.base.service.dashboard.DashboardProjectService;
import java.util.List;
import java.util.Map;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-11-12 15:36:07
 * 
 */
@EyRestController(path ="/v1/dashboardProject")
public class DashboardProjectControllerImpl extends AbstractController<DashboardProjectService, DashboardProjectDTO, DashboardProject> implements DashboardProjectController {

    /**
     * add DashboardProject from http
     */
    @Override
    public ResponseDTO<Void> add(DashboardProjectAddDTO dto) {
        DashboardProject entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        service.save(entity);
        return ResponseDTO.success();
    }

    /**
     * addList DashboardProject from http
     */
    @Override
    public ResponseDTO<Void> addList(List<DashboardProjectAddDTO> dtos) {
        List<DashboardProject> entitys = ConvertUtils.convertDTOList2EntityList(dtos, entityClass);
        service.save(entitys);
        return ResponseDTO.success();
    }

    /**
     * delete DashboardProject from http
     */
    @Override
    public ResponseDTO<Void> deleteById(DashboardProjectDeleteByIdDTO dto) {
        service.deleteById(dto.getId());
        return ResponseDTO.success();
    }

    /**
     * deleteList DashboardProject from http
     */
    @Override
    public ResponseDTO<Void> deleteByIdList(DashboardProjectDeleteByIdListDTO dto) {
        getService().deleteByIds(dto.getIds());
        return ResponseDTO.success();
    }

    /**
     * update DashboardProject from http
     */
    @Override
    public ResponseDTO<Void> updateById(DashboardProjectUpdateByIdDTO dto) {
        DashboardProject entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        getService().update(entity);
        return ResponseDTO.success();
    }

    /**
     * updateBatch DashboardProject from http
     */
    @Override
    public ResponseDTO<Void> updateBatch(List<DashboardProjectBatchUpdateDTO> dtos) {
        List<DashboardProject> entities = ConvertUtils.convertDTOList2EntityList(dtos, entityClass);
        getService().updateBatchByIds(entities);
        return ResponseDTO.success();
    }

    /**
     * query DashboardProject from http
     */
    @Override
    public ResponseDTO<DashboardProjectRepDTO> queryById(DashboardProjectQueryByIdDTO dto) {
        DashboardProject entity = getService().queryById(dto.getId());
        DashboardProjectRepDTO rDTO = ConvertUtils.convertEntity2DTO(entity, DashboardProjectRepDTO.class);
        return ResponseDTO.success(rDTO);
    }

    /**
     * queryByIdList DashboardProject from http
     */
    @Override
    public ResponseDTO<List<DashboardProjectRepDTO>> queryByIdList(DashboardProjectQueryByIdListDTO dto) {
        List<DashboardProject> entities = getService().queryByIds(dto.getIds());
        return ResponseDTO.success(ConvertUtils.convertEntityList2DTOList(entities, DashboardProjectRepDTO.class));
    }

    /**
     * queryList DashboardProject from http
     */
    @Override
    public ResponseDTO<List<DashboardProjectRepDTO>> queryList(DashboardProjectQueryDTO dto) {
        DashboardProject entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        List<DashboardProject> entities = getService().queryByPara(entity);
        return ResponseDTO.success(ConvertUtils.convertEntityList2DTOList(entities, DashboardProjectRepDTO.class));
    }

    /**
     * Page DashboardProject from http
     */
    @Override
    public ResponseDTO<SearchDTO<DashboardProjectRepDTO>> queryPage(SearchDTO<DashboardProjectQueryPageDTO> searchDTO) {
        Search<DashboardProject> search = ConvertUtils.convertSearchDTO2Search(searchDTO, entityClass);
        getService().queryPageByPara(search);
        return ResponseDTO.success(ConvertUtils.convertSearch2SearchDTO(search, DashboardProjectRepDTO.class));
    }

    /**
     * Count DashboardProject from http
     */
    @Override
    public ResponseDTO<Long> queryCount(DashboardProjectQueryDTO dto) {
        DashboardProject entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        Long count = getService().queryCountByPara(entity);
        return ResponseDTO.success(count);
    }

    /**
     * One DashboardProject from http
     */
    @Override
    public ResponseDTO<DashboardProjectRepDTO> queryOne(DashboardProjectQueryDTO dto) {
        DashboardProject qEntity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        DashboardProject rEntity = getService().queryOneByPara(qEntity);
        DashboardProjectRepDTO rDTO = ConvertUtils.convertEntity2DTO(rEntity, DashboardProjectRepDTO.class);
        return ResponseDTO.success(rDTO);
    }

    /**
     * exist DashboardProject from http
     */
    @Override
    public ResponseDTO<Boolean> exist(DashboardProjectQueryDTO dto) {
        boolean resullt = getService().existByPara(ConvertUtils.convertDTO2Entity(dto, entityClass));
        return ResponseDTO.success(resullt);
    }
}