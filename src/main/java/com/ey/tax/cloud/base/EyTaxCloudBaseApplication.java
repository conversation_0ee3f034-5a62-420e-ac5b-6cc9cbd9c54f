package com.ey.tax.cloud.base;

import com.alicp.jetcache.anno.config.EnableMethodCache;
import com.ey.tax.cloud.app.annotation.EnableExport;
import com.ey.tax.cloud.app.annotation.EnablePdfTool;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;

@EnableAsync
@EnableAspectJAutoProxy(exposeProxy = true, proxyTargetClass = true)
@MapperScan(value = {"com.ey.tax.cloud.*.mapper"})
@SpringBootApplication(scanBasePackages = {
		"com.ey.tax.cloud.tenant",
		"com.ey.tax.cloud.base",
		"com.ey.tax.cloud.admin",
		"com.ey.tax.cloud.file",
})
@EnableFeignClients(basePackages = {
		"com.ey.tax.cloud.tenant",
		"com.ey.tax.cloud.admin.remote.inter",
		"com.ey.tax.cloud.admin.remote.resource",
		"com.ey.tax.cloud.ievers.remote.task",
		"com.ey.tax.cloud.ifs.remote.task",
		"com.ey.tax.cloud.admin.remote.task",
		"com.ey.tax.cloud.admin.remote.user",
		"com.ey.tax.cloud.admin.remote.system",
		"com.ey.tax.cloud.tenant.remote.task",
		"com.ey.tax.cloud.acr.remote.task",
		"com.ey.tax.cloud.message.remote",
		"com.ey.tax.cloud.mail.remote",
		"com.ey.tax.cloud.file.remote",
		"com.ey.tax.cloud.base.remote.app",
		"com.ey.tax.cloud.base.remote.system",
		"com.ey.tax.cloud.base.remote.dictionary",
		"com.ey.tax.cloud.tenant.remote.tenuser",
		"com.ey.tax.cloud.app.remote.dataset"
})
@EnableExport
@EnableMethodCache(basePackages = "com.ey.tax.cloud")
@EnablePdfTool
public class EyTaxCloudBaseApplication {

	public static void main(String[] args) {
		SpringApplication app = new SpringApplication(EyTaxCloudBaseApplication.class);
		app.run(args);
	}

}
