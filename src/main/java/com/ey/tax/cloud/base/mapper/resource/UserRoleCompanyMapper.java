package com.ey.tax.cloud.base.mapper.resource;

import com.ey.cn.tax.framework.mybatisplus.core.mapper.IEyBaseMapper;
import com.ey.tax.cloud.base.entity.resource.UserRoleCompany;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Set;


public interface UserRoleCompanyMapper extends IEyBaseMapper<UserRoleCompany> {


    @Select("""
            
                        select
            	burc.role_id
            from
            	base_user_role_company burc
            inner join base_role br on
            	burc.role_id = br.id
            where
            	burc.is_del = 0
            	and br.is_del =0
            	and burc.user_id = #{userId}
            	and br.sys_code =  #{sysCode}
            """)
    Set<String> selectRole(@Param("sysCode")String sysCode,@Param("userId")String userId );

}
