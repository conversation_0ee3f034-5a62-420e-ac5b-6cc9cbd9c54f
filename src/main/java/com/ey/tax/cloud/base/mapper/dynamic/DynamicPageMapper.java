package com.ey.tax.cloud.base.mapper.dynamic;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ey.cn.tax.framework.mybatisplus.core.mapper.IEyBaseMapper;
import com.ey.tax.cloud.base.entity.dynamic.DynamicPage;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2023-09-09 22:38:04
 * 
 */
public interface DynamicPageMapper extends IEyBaseMapper<DynamicPage> {

    IPage<LinkedHashMap<String, Object>> selectCustomPage(Page page, @Param("tableName") String tableName, @Param(Constants.WRAPPER) QueryWrapper wrapper);

    IPage<LinkedHashMap<String, Object>> selectPage(Page page, @Param("tableName") String tableName, @Param(Constants.WRAPPER) QueryWrapper wrapper);

    /**
     * 查询数据
     *
     * @param sql
     * @return
     */
    @Select("${sql}")
    List<LinkedHashMap<String, Object>> select(@Param(value = "sql") String sql);

}