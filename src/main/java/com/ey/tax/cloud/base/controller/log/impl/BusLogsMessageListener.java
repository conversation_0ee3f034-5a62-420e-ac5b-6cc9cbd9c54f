package com.ey.tax.cloud.base.controller.log.impl;

import com.ey.cn.tax.framework.context.EyUserContextHolder;
import com.ey.tax.cloud.base.entity.log.BusLogsExt;
import com.ey.tax.cloud.base.mapper.log.BusLogsExtMapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class BusLogsMessageListener {

    @Autowired
    protected ObjectMapper om;

    @Autowired
    BusLogsExtMapper busLogsExtMapper;

    @RabbitListener(queues = "${ey.rabbit.props.queue-names.bus-log-add}", ackMode = "AUTO", containerFactory = "consumerBatchContainerFactory")
    public void onAddMessageBatch(List<Message> messages) {
        String bodyString = null;
        List<BusLogsExt> busLogsList = new ArrayList<>();
        try {
            for (Message message : messages) {
                byte[] body = message.getBody();
                bodyString = new String(body);
                BusLogsExt busLogs = om.readValue(bodyString, BusLogsExt.class);
                busLogs.setCreateUid(EyUserContextHolder.get().getUserId());
                busLogs.setUpdateUid(EyUserContextHolder.get().getUserId());
                busLogsList.add(busLogs);
            }
        } catch (JsonProcessingException e) {
            log.error("消息消费失败:{},消息体：{}", e, bodyString);
        }
        busLogsExtMapper.insertBatch(busLogsList);
    }
}
