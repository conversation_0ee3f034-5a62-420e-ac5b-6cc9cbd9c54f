package com.ey.tax.cloud.base.dto.task;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2023-10-24 15:47:41
 * 
 */
@Data
@Schema(description = "根据条件删除实体")
public class TaskInstDeleteDTO {
    /**
     * 系统ID COLUMN:system_id
     */
    @Schema(description = "系统ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String systemId;

    /**
     * 业务数据id COLUMN:business_id
     */
    @Schema(description = "业务数据id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String businessId;

    /**
     * 子id COLUMN:sub_id
     */
    @Schema(description = "子id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String subId;

    /**
     * 租户id COLUMN:tenant_id
     */
    @Schema(description = "租户id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String tenantId;

    /**
     * 用户id或者角色id COLUMN:target_id
     */
    @Schema(description = "用户id或者角色id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String targetId;

    /**
     * 类型:角色,用户 COLUMN:target_type
     */
    @Schema(description = "类型:角色,用户", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer targetType;

    /**
     * 申请时间 COLUMN:apply_time
     */
    @Schema(description = "申请时间", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDateTime applyTime;

    /**
     * 申请状态 COLUMN:apply_status
     */
    @Schema(description = "申请状态", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer applyStatus;

    /**
     * 事件类型 COLUMN:event_type
     */
    @Schema(description = "事件类型", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String eventType;

    /**
     * 事件标题 COLUMN:event_title
     */
    @Schema(description = "事件标题", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String eventTitle;

    /**
     * 待办结果 COLUMN:event_result
     */
    @Schema(description = "待办结果", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer eventResult;

    /**
     * COLUMN:result_time
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDateTime resultTime;

    /**
     * 审批人 COLUMN:approver_id
     */
    @Schema(description = "审批人", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String approverId;

    /**
     * 审批时间 COLUMN:approver_time
     */
    @Schema(description = "审批时间", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDateTime approverTime;
}