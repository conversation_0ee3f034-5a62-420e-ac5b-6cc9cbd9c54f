package com.ey.tax.cloud.base.groovy

import com.ey.cn.tax.framework.context.EyUserContextHolder
import com.ey.cn.tax.framework.dto.ResponseDTO
import com.ey.cn.tax.framework.execution.IExecutionContext
import com.ey.cn.tax.framework.mybatisplus.core.toolkit.EyWrappers
import com.ey.cn.tax.lite.execution.dataset.IDatasetGroovyRunnable
import com.ey.tax.cloud.app.entity.roleResource.AppRoleResource
import com.ey.tax.cloud.app.mapper.roleResource.AppRoleResourceMapper
import org.apache.commons.collections4.CollectionUtils
import org.apache.commons.lang3.StringUtils

import java.util.stream.Collectors

class AppRoleResourceBingding implements IDatasetGroovyRunnable {

    AppRoleResourceMapper appRoleResourceMapper;

    @Override
    public Object run(Object args, Map<String, Object> dependentDatasets, IExecutionContext executionContext) {


        appRoleResourceMapper = executionContext.getBean(AppRoleResourceMapper.class).get();

        String tenantId = EyUserContextHolder.get().getTenantId();
        String sysCode = EyUserContextHolder.get().getSyscode();
        String appCode = EyUserContextHolder.get().getAppcode();

//        if(StringUtils.isEmpty(sysCode) || StringUtils.isEmpty(appCode)){
//            log.info("Syscode或Appcode为空")
//            return null;
//        }

        // 取出入参
        Map<String, Object> argsMap = (Map<String, Object>) args
        String roleId = argsMap.get("id");
        List<String> resourceIds = argsMap.get("ids");
        if(StringUtils.isNotBlank(roleId) && CollectionUtils.isNotEmpty(resourceIds)){
            List<AppRoleResource> list = resourceIds.stream()
                    .map(e->{
                        AppRoleResource t = new AppRoleResource();
                        t.setTenantId(tenantId)
                        t.setSysCode(sysCode);
                        t.setAppCode(appCode);
                        t.setRoleId(roleId);
                        t.setResourceId(e);
                        return t;
                    })
                    .collect(Collectors.toList());
            appRoleResourceMapper.delete(EyWrappers.query(AppRoleResource.class)
                    .eq(StringUtils.isNotBlank(sysCode),"sys_code",sysCode)
                    .eq(StringUtils.isNotBlank(appCode),"app_code",appCode)
                    .eq("role_id",roleId));
            appRoleResourceMapper.insertBatch(list);

        }
        return ResponseDTO.success();
    }
}
