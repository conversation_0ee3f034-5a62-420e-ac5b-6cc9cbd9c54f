package com.ey.tax.cloud.base.enums.workorder;

import com.ey.cn.tax.framework.exception.EyTaxSystemException;
import com.ey.tax.cloud.base.enums.theme.ThemeTypeEnum;

/**
 * 工单状态
 **/
public enum WoStateEnum {
    Submitted("Submitted", "待处理"),
    Processing("Processing", "处理中"),
    ProcessingCompleted("ProcessingCompleted", "处理完成"),
    Close("Close", "关闭"),
    SubmittedUserModify("SubmittedUserModify", "待处理（用户修改）"),
    ProcessingUserModify("ProcessingUserModify", "处理中（用户修改）"),
    ;

    private String code;
    private String des;

    WoStateEnum(String code, String des) {
        this.code = code;
        this.des = des;
    }

    public String getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }

    public static WoStateEnum getEnum(String value) {
        for (WoStateEnum enums : WoStateEnum.values()) {
            if (enums.getCode().equals(value)) {
                return enums;
            }
        }
        throw new EyTaxSystemException("Error: Invalid WoStateEnum type value: " + value);
    }
}
