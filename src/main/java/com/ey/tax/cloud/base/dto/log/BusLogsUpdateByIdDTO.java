package com.ey.tax.cloud.base.dto.log;

import com.ey.cn.tax.framework.validator.UpdateByIdGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2023-12-19 13:51:45
 * 
 */
@Data
@Schema(description = "根据id批量更新实体")
public class BusLogsUpdateByIdDTO {
    
    /**
     * 数据id
     */
    @Schema(description = "数据id")
    @NotBlank(message = "{BusLogsDTO.id.NotBlank}", groups = {UpdateByIdGroup.class})
    private String id;

    /**
     * 业务类型 COLUMN:bus_type
     */
    @Schema(description = "业务类型", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String busType;

    /**
     * 业务子类型 COLUMN:bus_sub_type
     */
    @Schema(description = "业务子类型", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String busSubType;

    /**
     * 日志 COLUMN:bus_log
     */
    @Schema(description = "日志", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Object busLog;
}