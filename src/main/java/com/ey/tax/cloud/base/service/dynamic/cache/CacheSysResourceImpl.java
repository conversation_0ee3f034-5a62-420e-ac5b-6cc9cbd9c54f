package com.ey.tax.cloud.base.service.dynamic.cache;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ey.cn.tax.framework.utils.ConvertUtils;
import com.ey.tax.cloud.base.dto.dynamic.ResourceDTO;
import com.ey.tax.cloud.base.dto.dynamic.RoleResourceDTO;
import com.ey.tax.cloud.base.dto.resource.ButtonResource;
import com.ey.tax.cloud.base.dto.resource.TenRoleResourceDTO;
import com.ey.tax.cloud.base.entity.resource.TenResource;
import com.ey.tax.cloud.base.mapper.resource.TenResourceMapper;
import com.ey.tax.cloud.base.mapper.resource.TenRoleResourceMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

@Component
public class CacheSysResourceImpl implements CacheResourceInterface {

    @Autowired
    TenResourceMapper resourceMapper;

    @Autowired
    TenRoleResourceMapper roleResourceMapper;

    @Override
    public ButtonResource getResource(String sysCode,String appCode, Set<String> range) {

        if(range.contains(rang_tenant_sys)){
            // 租户端系统资源
            List<TenResource> resources = resourceMapper.selectList(Wrappers.lambdaQuery(TenResource.class).eq(TenResource::getResourceType, 2).eq(StringUtils.isNotBlank(sysCode), TenResource::getSysCode, sysCode));
            // 租户端角色资源
            List<TenRoleResourceDTO> roleResources = roleResourceMapper.selectRoleButtonResource(sysCode, null, 2);
            return ButtonResource.builder().resources(ConvertUtils.convertEntityList2DTOList(resources, ResourceDTO.class)).roleResources(ConvertUtils.convertEntityList2DTOList(roleResources, RoleResourceDTO.class)).build();
        }
        return null;
    }
}
