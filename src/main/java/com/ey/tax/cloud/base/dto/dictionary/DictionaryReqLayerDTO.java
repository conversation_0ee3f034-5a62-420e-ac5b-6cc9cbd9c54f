package com.ey.tax.cloud.base.dto.dictionary;

import com.ey.cn.tax.framework.validator.QueryByIdGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-04-12 21:58:03
 */
@Data
@Schema(description = "查询参数实体")
public class DictionaryReqLayerDTO implements Serializable {
    @Schema(description = "字典编码")
    @NotBlank(message = "字典编码不能为空", groups = {QueryByIdGroup.class})
    private String dictCode;

    @Schema(description = "查询第几次或查询到第几次，默认值 1")
    private int layer = 1;

    @Schema(description = "false=返回对应 layer 层的数据，true=返回 dictCode 到 layer 层的所有数据，树结构")
    private boolean includeAll = false;

    @Schema(description = "系统编码")
    private String sysCode;

}