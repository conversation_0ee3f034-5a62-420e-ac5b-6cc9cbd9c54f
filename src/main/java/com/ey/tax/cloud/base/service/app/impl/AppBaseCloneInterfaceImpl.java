package com.ey.tax.cloud.base.service.app.impl;

import com.ey.tax.cloud.app.service.app.AppBaseCloneInterface;
import com.ey.tax.cloud.base.dto.AppBaseCloneFeignDTO;
import org.springframework.stereotype.Service;

import java.util.Arrays;

/**
 * 测试
 */
@Service
public class AppBaseCloneInterfaceImpl implements AppBaseCloneInterface {
    @Override
    public AppBaseCloneFeignDTO buildAppBaseCloneFeignDTO() {
        AppBaseCloneFeignDTO resp = new AppBaseCloneFeignDTO();
//        resp.setDataSetCodes(Arrays.asList("test"));
        resp.setSourceSysCode("ireach");
        resp.setSourceAppCode("QS");
        resp.setTargetAppCode("CL");
        resp.setResourceNames(Arrays.asList("资源管理"));

//        resp.setGlobalParamCodes(Arrays.asList("test"));


        return resp;
    }
}
