package com.ey.tax.cloud.base.dto.database;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-08-13 15:02:16
 * 
 */
@Data
@Schema(description = "根据条件删除实体")
public class TenantSchemaDeleteDTO {
    /**
     * 数据库配置ID COLUMN:db_config_id
     */
    @Schema(description = "数据库配置ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String dbConfigId;

    /**
     * 系统标识 COLUMN:system_id
     */
    @Schema(description = "系统标识", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String systemId;

    /**
     * 数据库名称 COLUMN:db_name
     */
    @Schema(description = "数据库名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String dbName;

    /**
     * 租户ID COLUMN:tenant_id
     */
    @Schema(description = "租户ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String tenantId;

    /**
     * schema名称 COLUMN:schema_name
     */
    @Schema(description = "schema名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String schemaName;
}