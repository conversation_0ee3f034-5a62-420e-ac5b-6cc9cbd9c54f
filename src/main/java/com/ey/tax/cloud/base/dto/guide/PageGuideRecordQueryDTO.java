package com.ey.tax.cloud.base.dto.guide;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2023-12-08 17:18:28
 * 
 */
@Data
@Schema(description = "根据条件查询实体")
public class PageGuideRecordQueryDTO {
    /**
     * 用户id COLUMN:user_id
     */
    @Schema(description = "用户id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String userId;

    /**
     * 已完成导引页面。其json中的key为已完成导引页面的唯一标识，value可扩展其他相关信息 COLUMN:finish_pages
     */
    @Schema(description = "已完成导引页面。其json中的key为已完成导引页面的唯一标识，value可扩展其他相关信息", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String finishPages;
}