package com.ey.tax.cloud.base.dto.workorder;

import com.ey.cn.tax.framework.validator.DeleteByIdGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-02-24 20:13:47
 * 
 */
@Data
@Schema(description = "根据id删除实体")
public class WorkOrderRecordDeleteByIdDTO {
    
    /**
     * 数据id
     */
    @Schema(description = "数据id")
    @NotBlank(message = "{workOrderRecordDTO.id.NotBlank}", groups = {DeleteByIdGroup.class})
    private String id;
}