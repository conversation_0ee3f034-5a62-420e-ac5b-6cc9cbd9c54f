package com.ey.tax.cloud.base.controller.workorder.impl;

import com.ey.cn.tax.framework.context.EyUserContextHolder;
import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.dto.SearchDTO;
import com.ey.cn.tax.framework.entity.Search;
import com.ey.cn.tax.framework.exception.EyTaxBusinessException;
import com.ey.cn.tax.framework.utils.ConvertUtils;
import com.ey.cn.tax.framework.utils.GroovyUtils;
import com.ey.cn.tax.framework.web.annotation.EyRestController;
import com.ey.cn.tax.framework.web.base.AbstractController;
import com.ey.tax.cloud.base.config.PathConfig;
import com.ey.tax.cloud.base.constants.ErrorCodeConstans;
import com.ey.tax.cloud.base.controller.workorder.WorkOrderController;
import com.ey.tax.cloud.base.dto.file.FileTypeDTO;
import com.ey.tax.cloud.base.dto.file.FileTypeUtils;
import com.ey.tax.cloud.base.dto.workorder.*;
import com.ey.tax.cloud.base.entity.workorder.WorkOrder;
import com.ey.tax.cloud.base.entity.workorder.WorkOrderFile;
import com.ey.tax.cloud.base.enums.workorder.WoStateEnum;
import com.ey.tax.cloud.base.service.workorder.WorkOrderFileService;
import com.ey.tax.cloud.base.service.workorder.WorkOrderService;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringEscapeUtils;
import org.apache.poi.poifs.filesystem.FileMagic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.HtmlUtils;

import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

import static org.springframework.http.HttpHeaders.CONTENT_DISPOSITION;
import static org.springframework.http.MediaType.APPLICATION_OCTET_STREAM_VALUE;

/**
 * <AUTHOR>
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * @date 2024-02-24 20:13:47
 */
@EyRestController(path = "/v1/workOrder")
public class WorkOrderControllerImpl extends AbstractController<WorkOrderService, WorkOrderDTO, WorkOrder> implements WorkOrderController {

    @Autowired
    private PathConfig pathConfig;

    @Autowired
    private WorkOrderFileService workOrderFileService;

    @Override
    public ResponseDTO<Void> add(WorkOrderAddDTO dto, MultipartFile[] files) {
        checkFileType(files);
        dto.setWoDescribe(StringEscapeUtils.unescapeHtml4(dto.getWoDescribe()));
        WorkOrder entity = GroovyUtils.convertTo(entityClass, dto);
        service.save(entity, files);
        return ResponseDTO.success();
    }

    /**
     * addList workOrder from http
     */
    @Override
    public ResponseDTO<Void> addList(List<WorkOrderAddDTO> dtos) {
        List<WorkOrder> entitys = GroovyUtils.convertToList(entityClass, dtos);
        service.save(entitys);
        return ResponseDTO.success();
    }

    /**
     * delete workOrder from http
     */
    @Override
    public ResponseDTO<Void> deleteById(WorkOrderDeleteByIdDTO dto) {
        service.deleteById(dto.getId());
        return ResponseDTO.success();
    }

    /**
     * deleteList workOrder from http
     */
    @Override
    public ResponseDTO<Void> deleteByIdList(WorkOrderDeleteByIdListDTO dto) {
        getService().deleteByIds(dto.getIds());
        return ResponseDTO.success();
    }

    /**
     * update workOrder from http
     */
    @Override
    public ResponseDTO<Void> updateById(WorkOrderUpdateByIdDTO dto) {
        WorkOrder entity = GroovyUtils.convertTo(entityClass, dto);
        // 开始处理，设置操作人
        // 处理完成的时候，如果当前指派人为空，也自动记录值 update by liwei 20240515
        if (WoStateEnum.Processing.getCode().equalsIgnoreCase(entity.getWoState()) || WoStateEnum.ProcessingCompleted.getCode().equalsIgnoreCase(entity.getWoState())) {
            entity.setWoCurrProcessUid(EyUserContextHolder.get().getUserId());
            entity.setWoCurrProcessEmail(EyUserContextHolder.get().getUserEmail());
        }
        getService().update(entity, null);
        return ResponseDTO.success();
    }

    /**
     * update workOrder from http
     */
    @Override
    public ResponseDTO<Void> userModify(WorkOrderUpdateByIdDTO dto, MultipartFile[] files) {
        dto.setWoDescribe(StringEscapeUtils.unescapeHtml4(dto.getWoDescribe()));
        WorkOrder entity = GroovyUtils.convertTo(entityClass, dto);
        getService().update(entity, files);
        return ResponseDTO.success();
    }

    @Override
    public ResponseDTO<Void> adminModify(WorkOrderUpdateByIdDTO dto) {
        WorkOrder entity = GroovyUtils.convertTo(entityClass, dto);
        getService().adminModify(entity);
        return ResponseDTO.success();
    }

    /**
     * updateBatch workOrder from http
     */
    @Override
    public ResponseDTO<Void> updateBatch(List<WorkOrderBatchUpdateDTO> dtos) {
        List<WorkOrder> entities = GroovyUtils.convertToList(entityClass, dtos);
        getService().updateBatchByIds(entities);
        return ResponseDTO.success();
    }

    /**
     * query workOrder from http
     */
    @Override
    public ResponseDTO<WorkOrderRepDTO> queryById(WorkOrderQueryByIdDTO dto) {
        WorkOrder entity = getService().queryById(dto.getId());
        WorkOrderRepDTO rDTO = GroovyUtils.convertTo(WorkOrderRepDTO.class, entity);
        rDTO.setUserDetailUrl(getService().generateWorkOrderDetailUrl(entity, false));
        rDTO.setSupportDetailUrl(getService().generateWorkOrderDetailUrl(entity, true));
        WorkOrderFile workOrderFile = new WorkOrderFile();
        workOrderFile.setWorkOrderId(rDTO.getId());
        List<WorkOrderFile> workOrderFiles = workOrderFileService.queryByPara(workOrderFile);
        rDTO.setFiles(GroovyUtils.convertToList(WorkOrderFileRepDTO.class, workOrderFiles));
        if (StringUtils.isNotBlank(rDTO.getWoFilePath())) {
            WorkOrderFileRepDTO workOrderFileRepDTO = new WorkOrderFileRepDTO();
            workOrderFileRepDTO.setFilePath(rDTO.getWoFilePath());
            workOrderFileRepDTO.setFileName(rDTO.getWoFileName());
            workOrderFileRepDTO.setFileType(rDTO.getWoFileType());
            workOrderFileRepDTO.setWorkOrderId(rDTO.getId());
            workOrderFileRepDTO.setCreateTime(rDTO.getCreateTime());
            workOrderFileRepDTO.setId(rDTO.getId());
            rDTO.getFiles().add(workOrderFileRepDTO);
        }
        return ResponseDTO.success(rDTO);
    }

    /**
     * queryByIdList workOrder from http
     */
    @Override
    public ResponseDTO<List<WorkOrderRepDTO>> queryByIdList(WorkOrderQueryByIdListDTO dto) {
        List<WorkOrder> entities = getService().queryByIds(dto.getIds());
        return ResponseDTO.success(GroovyUtils.convertToList(WorkOrderRepDTO.class, entities));
    }

    /**
     * queryList workOrder from http
     */
    @Override
    public ResponseDTO<List<WorkOrderRepDTO>> queryList(WorkOrderQueryDTO dto) {
        WorkOrder entity = GroovyUtils.convertTo(entityClass, dto);
        List<WorkOrder> entities = getService().queryByPara(entity);
        return ResponseDTO.success(GroovyUtils.convertToList(WorkOrderRepDTO.class, entities));
    }

    /**
     * Page workOrder from http
     */
    @Override
    public ResponseDTO<SearchDTO<WorkOrderRepDTO>> queryPage(SearchDTO<WorkOrderQueryPageDTO> searchDTO) {
        Search<WorkOrder> search = ConvertUtils.convertSearchDTO2Search(searchDTO, entityClass);
        getService().queryPageByPara(search, false);
        return ResponseDTO.success(ConvertUtils.convertSearch2SearchDTO(search, WorkOrderRepDTO.class));
    }

    /**
     * Page workOrder from http
     */
    @Override
    public ResponseDTO<SearchDTO<WorkOrderRepDTO>> myWorkOrderQueryPage(SearchDTO<WorkOrderQueryPageDTO> searchDTO) {
        Search<WorkOrder> search = ConvertUtils.convertSearchDTO2Search(searchDTO, entityClass);
        getService().queryPageByPara(search, true);
        return ResponseDTO.success(ConvertUtils.convertSearch2SearchDTO(search, WorkOrderRepDTO.class));
    }

    /**
     * Page workOrder from http
     */
    @Override
    public ResponseDTO<SearchDTO<WorkOrderFileRepDTO>> workOrderFiles(String id) {
        List<WorkOrderFileRepDTO> dto = new ArrayList<>();
        WorkOrder entity = getService().queryById(id);
        WorkOrderFile workOrderFile = new WorkOrderFile();
        workOrderFile.setWorkOrderId(entity.getId());
        List<WorkOrderFile> workOrderFiles = workOrderFileService.queryByPara(workOrderFile);
        dto.addAll(GroovyUtils.convertToList(WorkOrderFileRepDTO.class, workOrderFiles));
        if (StringUtils.isNotBlank(entity.getWoFilePath())) {
            WorkOrderFileRepDTO workOrderFileRepDTO = new WorkOrderFileRepDTO();
            workOrderFileRepDTO.setFilePath(entity.getWoFilePath());
            workOrderFileRepDTO.setFileName(entity.getWoFileName());
            workOrderFileRepDTO.setFileType(entity.getWoFileType());
            workOrderFileRepDTO.setWorkOrderId(entity.getId());
            workOrderFileRepDTO.setCreateTime(entity.getCreateTime());
            workOrderFileRepDTO.setId(entity.getId());
            dto.add(workOrderFileRepDTO);
        }

        SearchDTO searchDTO = new SearchDTO();
        searchDTO.setEntities(dto);
        return ResponseDTO.success(searchDTO);
    }

    @Override
    public ResponseDTO<List<LinkedHashMap<String, Object>>> assignUsers(String id) {
        return ResponseDTO.success(getService().assignUsers(id));
    }

    @Override
    public ResponseDTO<List<LinkedHashMap<String, Object>>> processUsers() {
        return ResponseDTO.success(getService().processUsers());
    }

    /**
     * Count workOrder from http
     */
    @Override
    public ResponseDTO<Long> queryCount(WorkOrderQueryDTO dto) {
        WorkOrder entity = GroovyUtils.convertTo(entityClass, dto);
        Long count = getService().queryCountByPara(entity);
        return ResponseDTO.success(count);
    }

    /**
     * One workOrder from http
     */
    @Override
    public ResponseDTO<WorkOrderRepDTO> queryOne(WorkOrderQueryDTO dto) {
        WorkOrder qEntity = GroovyUtils.convertTo(entityClass, dto);
        WorkOrder rEntity = getService().queryOneByPara(qEntity);
        WorkOrderRepDTO rDTO = GroovyUtils.convertTo(WorkOrderRepDTO.class, rEntity);
        return ResponseDTO.success(rDTO);
    }

    /**
     * exist workOrder from http
     */
    @Override
    public ResponseDTO<Boolean> exist(WorkOrderQueryDTO dto) {
        boolean result = getService().existByPara(GroovyUtils.convertTo(entityClass, dto));
        return ResponseDTO.success(result);
    }

    @Override
    public void download(String id, HttpServletResponse resp) {
        String woFileName = null;
        String woFilePath = null;
        Optional<WorkOrder> optional = this.getService().findById(id);
        if (!optional.isEmpty()) {
            WorkOrder workOrder = optional.get();
            woFileName = workOrder.getWoFileName();
            woFilePath = workOrder.getWoFilePath();
        } else {
            Optional<WorkOrderFile> optionalFile = workOrderFileService.findById(id);
            if (!optionalFile.isEmpty()) {
                WorkOrderFile workOrderFile = optionalFile.get();
                woFileName = workOrderFile.getFileName();
                woFilePath = workOrderFile.getFilePath();
            }
        }

        if (woFileName == null || woFilePath == null) {
            throwBuildBusinessException(ErrorCodeConstans.DATA_NOT_EXIST);
        }

        try {
            resp.setContentType(APPLICATION_OCTET_STREAM_VALUE);
            resp.setHeader(CONTENT_DISPOSITION, "attachment;filename=" + URLEncoder.encode(woFileName, StandardCharsets.UTF_8));
            File tempFile = new File(pathConfig.getWorkorder() + woFilePath);
            FileUtils.copyFile(tempFile, resp.getOutputStream());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void downloadExcel(WorkOrderDownloadDTO dto, HttpServletResponse resp) {
        this.service.download(dto.getIds(), resp);
    }

    private void checkFileType(MultipartFile[] files) {
        if (files == null || files.length == 0) {
            return;
        }

        List<FileTypeDTO> types = Arrays.asList(new FileTypeDTO(".pdf", FileMagic.PDF), new FileTypeDTO(".docx", FileMagic.OOXML), new FileTypeDTO(".xlsx", FileMagic.OOXML), new FileTypeDTO(".png", FileMagic.PNG), new FileTypeDTO(".jpg", FileMagic.JPEG), new FileTypeDTO(".jpeg", FileMagic.JPEG));
        for (MultipartFile file : files) {
            if (file != null && !FileTypeUtils.isExcelFile(file, types, true)) {
                throw new EyTaxBusinessException("仅支持docx/xlsx/pdf/png/jpg/jpeg文件格式！");
            }
        }
    }

    ;
}
