package com.ey.tax.cloud.base.controller.fault.impl;

import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.dto.SearchDTO;
import com.ey.cn.tax.framework.entity.Search;
import com.ey.cn.tax.framework.utils.ConvertUtils;
import com.ey.cn.tax.framework.web.annotation.EyRestController;
import com.ey.cn.tax.framework.web.base.AbstractController;
import com.ey.tax.cloud.base.constants.BusinessFaultConstants;
import com.ey.tax.cloud.base.controller.fault.BusinessFaultController;
import com.ey.tax.cloud.base.dto.fault.BusinessFaultAddDTO;
import com.ey.tax.cloud.base.dto.fault.BusinessFaultBatchUpdateDTO;
import com.ey.tax.cloud.base.dto.fault.BusinessFaultDTO;
import com.ey.tax.cloud.base.dto.fault.BusinessFaultDeleteByIdDTO;
import com.ey.tax.cloud.base.dto.fault.BusinessFaultDeleteByIdListDTO;
import com.ey.tax.cloud.base.dto.fault.BusinessFaultQueryByIdDTO;
import com.ey.tax.cloud.base.dto.fault.BusinessFaultQueryByIdListDTO;
import com.ey.tax.cloud.base.dto.fault.BusinessFaultQueryDTO;
import com.ey.tax.cloud.base.dto.fault.BusinessFaultQueryPageDTO;
import com.ey.tax.cloud.base.dto.fault.BusinessFaultRepDTO;
import com.ey.tax.cloud.base.dto.fault.BusinessFaultUpdateByIdDTO;
import com.ey.tax.cloud.base.entity.dictionary.Dictionary;
import com.ey.tax.cloud.base.entity.fault.BusinessFault;
import com.ey.tax.cloud.base.service.dictionary.DictionaryService;
import com.ey.tax.cloud.base.service.fault.BusinessFaultService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 *
 * <AUTHOR>
 * @date 2024-05-20 10:36:04
 *
 */
@EyRestController(path ="/v1/businessFault")
public class BusinessFaultControllerImpl extends AbstractController<BusinessFaultService, BusinessFaultDTO, BusinessFault> implements BusinessFaultController {

    @Autowired
    private DictionaryService dictionaryService;

    /**
     * add BusinessFault from http
     */
    @Override
    public ResponseDTO<Void> add(BusinessFaultAddDTO dto) {
        BusinessFault entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        service.save(entity);
        return ResponseDTO.success();
    }

    /**
     * addList BusinessFault from http
     */
    @Override
    public ResponseDTO<Void> addList(List<BusinessFaultAddDTO> dtos) {
        List<BusinessFault> entitys = ConvertUtils.convertDTOList2EntityList(dtos, entityClass);
        service.save(entitys);
        return ResponseDTO.success();
    }

    /**
     * delete BusinessFault from http
     */
    @Override
    public ResponseDTO<Void> deleteById(BusinessFaultDeleteByIdDTO dto) {
        service.deleteById(dto.getId());
        return ResponseDTO.success();
    }

    /**
     * deleteList BusinessFault from http
     */
    @Override
    public ResponseDTO<Void> deleteByIdList(BusinessFaultDeleteByIdListDTO dto) {
        getService().deleteByIds(dto.getIds());
        return ResponseDTO.success();
    }

    /**
     * update BusinessFault from http
     */
    @Override
    public ResponseDTO<Void> updateById(BusinessFaultUpdateByIdDTO dto) {
        BusinessFault entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        getService().update(entity);
        return ResponseDTO.success();
    }

    /**
     * updateBatch BusinessFault from http
     */
    @Override
    public ResponseDTO<Void> updateBatch(List<BusinessFaultBatchUpdateDTO> dtos) {
        List<BusinessFault> entities = ConvertUtils.convertDTOList2EntityList(dtos, entityClass);
        getService().updateBatchByIds(entities);
        return ResponseDTO.success();
    }

    /**
     * query BusinessFault from http
     */
    @Override
    public ResponseDTO<BusinessFaultRepDTO> queryById(BusinessFaultQueryByIdDTO dto) {
        BusinessFault entity = getService().queryById(dto.getId());
        BusinessFaultRepDTO rDTO = ConvertUtils.convertEntity2DTO(entity, BusinessFaultRepDTO.class);
        return ResponseDTO.success(rDTO);
    }

    /**
     * queryByIdList BusinessFault from http
     */
    @Override
    public ResponseDTO<List<BusinessFaultRepDTO>> queryByIdList(BusinessFaultQueryByIdListDTO dto) {
        List<BusinessFault> entities = getService().queryByIds(dto.getIds());
        return ResponseDTO.success(ConvertUtils.convertEntityList2DTOList(entities, BusinessFaultRepDTO.class));
    }

    /**
     * queryList BusinessFault from http
     */
    @Override
    public ResponseDTO<List<BusinessFaultRepDTO>> queryList(BusinessFaultQueryDTO dto) {
        BusinessFault entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        List<BusinessFault> entities = getService().queryByPara(entity);
        return ResponseDTO.success(ConvertUtils.convertEntityList2DTOList(entities, BusinessFaultRepDTO.class));
    }

    /**
     * Page BusinessFault from http
     */
    @Override
    public ResponseDTO<SearchDTO<BusinessFaultRepDTO>> queryPage(SearchDTO<BusinessFaultQueryPageDTO> searchDTO) {
        Search<BusinessFault> search = ConvertUtils.convertSearchDTO2Search(searchDTO, entityClass);
        getService().queryPageByPara(search);
        //字典清洗业务类型
        Dictionary dictionary = new Dictionary();
        dictionary.setDictCode(BusinessFaultConstants.BUSINESS_TYPE);
        List<Dictionary> dictionaries = dictionaryService.queryByPara(dictionary);
        //getProperty1为字典的key，getProperty2为字典的value
        Map<String, String> map = new HashMap<>();
        if (dictionaries != null && dictionaries.size() > 0) {
            for (Dictionary dict : dictionaries) {
                map.put(dict.getProperty1(), dict.getProperty2());
            }
        }
        SearchDTO<BusinessFaultRepDTO> businessFaultRepDTOSearchDTO = ConvertUtils.convertSearch2SearchDTO(search, BusinessFaultRepDTO.class);
        for (BusinessFaultRepDTO record : businessFaultRepDTOSearchDTO.getRecords()) {
            record.setBusinessTypeLabel(map.get(record.getBusinessType()));
        }
        return ResponseDTO.success(businessFaultRepDTOSearchDTO);
    }

    /**
     * Count BusinessFault from http
     */
    @Override
    public ResponseDTO<Long> queryCount(BusinessFaultQueryDTO dto) {
        BusinessFault entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        Long count = getService().queryCountByPara(entity);
        return ResponseDTO.success(count);
    }

    /**
     * One BusinessFault from http
     */
    @Override
    public ResponseDTO<BusinessFaultRepDTO> queryOne(BusinessFaultQueryDTO dto) {
        BusinessFault qEntity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        BusinessFault rEntity = getService().queryOneByPara(qEntity);
        BusinessFaultRepDTO rDTO = ConvertUtils.convertEntity2DTO(rEntity, BusinessFaultRepDTO.class);
        return ResponseDTO.success(rDTO);
    }

    /**
     * exist BusinessFault from http
     */
    @Override
    public ResponseDTO<Boolean> exist(BusinessFaultQueryDTO dto) {
        boolean resullt = getService().existByPara(ConvertUtils.convertDTO2Entity(dto, entityClass));
        return ResponseDTO.success(resullt);
    }
}
