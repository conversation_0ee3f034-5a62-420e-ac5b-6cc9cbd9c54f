package com.ey.tax.cloud.base.dto.dashboard;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-11-12 15:36:07
 * 
 */
@Data
@Schema(description = "根据条件删除实体")
public class DashboardProjectDeleteDTO {
    /**
     * 系统 COLUMN:sys_code
     */
    @Schema(description = "系统", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String sysCode;

    /**
     * 邮箱（项目管理的 Member 表数据） COLUMN:email
     */
    @Schema(description = "邮箱（项目管理的 Member 表数据）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String email;

    /**
     * 级别组（base_user） COLUMN:level_group
     */
    @Schema(description = "级别组（base_user）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String levelGroup;

    /**
     * 级别（base_user） COLUMN:level
     */
    @Schema(description = "级别（base_user）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String level;

    /**
     * sl1（base_user） COLUMN:sl1
     */
    @Schema(description = "sl1（base_user）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String sl1;

    /**
     * ssl2（base_user） COLUMN:ssl2
     */
    @Schema(description = "ssl2（base_user）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String ssl2;

    /**
     * ssl3（base_user） COLUMN:ssl3
     */
    @Schema(description = "ssl3（base_user）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String ssl3;

    /**
     * 地区（base_user） COLUMN:region
     */
    @Schema(description = "地区（base_user）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String region;

    /**
     * 城市（base_user） COLUMN:city
     */
    @Schema(description = "城市（base_user）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String city;

    /**
     * gpn（base_user） COLUMN:gpn
     */
    @Schema(description = "gpn（base_user）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String gpn;

    /**
     * 项目id
     */
    @Schema(description = "项目id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String projectId;

    /**
     * 税号
     */
    @Schema(description = "税号", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String taxpayerNo;

    /**
     * 项目创建时间（项目表） COLUMN:project_create_time
     */
    @Schema(description = "项目创建时间（项目表）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDateTime projectCreateTime;

    /**
     * COLUMN:fiscal_year
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String fiscalYear;

    /**
     * COLUMN:fiscal_week
     */
    @Schema(description = "null", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Short fiscalWeek;

    /**
     * 年度（项目表） COLUMN:project_year
     */
    @Schema(description = "年度（项目表）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Short projectYear;

    /**
     * 月度（ifs，项目创建月份；ievers，项目所属期；各项目按需设置）（项目表） COLUMN:project_month
     */
    @Schema(description = "月度（ifs，项目创建月份；ievers，项目所属期；各项目按需设置）（项目表）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Short projectMonth;

    /**
     * 项目金额（Pipeline） COLUMN:project_amount
     */
    @Schema(description = "项目金额（Pipeline）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private BigDecimal projectAmount;

    /**
     * Engagement Code（项目表） COLUMN:engagement_code
     */
    @Schema(description = "Engagement Code（项目表）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String engagementCode;
}