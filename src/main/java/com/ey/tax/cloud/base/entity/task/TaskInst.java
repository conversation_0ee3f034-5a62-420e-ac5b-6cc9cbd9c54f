package com.ey.tax.cloud.base.entity.task;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ey.cn.tax.framework.mybatisplus.core.entity.BaseEntity;
import java.time.LocalDateTime;
import java.util.List;

import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2023-10-24 15:47:41
 * 
 */
@Data
@TableName("base_task_inst")
public class TaskInst extends BaseEntity {
    /**
     * 系统ID COLUMN:system_id
     */
    private String systemId;

    /**
     * 业务数据id COLUMN:business_id
     */
    private String businessId;

    /**
     * 子id COLUMN:sub_id
     */
    private String subId;

    /**
     * 租户id COLUMN:tenant_id
     */
    private String tenantId;

    /**
     * 用户id或者角色id COLUMN:target_id
     */
    private String targetId;

    /**
     * 类型:角色,用户 COLUMN:target_type
     */
    private Integer targetType;

    /**
     * 申请时间 COLUMN:apply_time
     */
    private LocalDateTime applyTime;

    /**
     * 申请状态 COLUMN:apply_status
     */
    private Integer applyStatus;

    /**
     * 事件类型 COLUMN:event_type
     */
    private String eventType;

    /**
     * 事件标题 COLUMN:event_title
     */
    private String eventTitle;

    /**
     * 待办结果 COLUMN:event_result
     */
    private Integer eventResult;

    /**
     * COLUMN:result_time
     */
    private LocalDateTime resultTime;

    /**
     * 审批人 COLUMN:approver_id
     */
    private String approverId;

    /**
     * 审批人
     */
    @TableField(exist = false)
    private String approverName;

    /**
     * 审批时间 COLUMN:approver_time
     */
    private LocalDateTime approverTime;

    /**
     * 备注 COLUMN:remark
     */
    private String remark;

    /**
     * 备注 COLUMN:custom_data
     */
    private String customData;

    /**
     * 备注 COLUMN:approver_data
     */
    private String approverData;

    /**
     * 申请时间 COLUMN:apply_time
     */
    @TableField(exist = false)
    private List<LocalDateTime> applyTimeRange;
}