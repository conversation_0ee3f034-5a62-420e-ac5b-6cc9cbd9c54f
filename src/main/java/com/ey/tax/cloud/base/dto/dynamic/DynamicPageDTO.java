package com.ey.tax.cloud.base.dto.dynamic;

import com.ey.cn.tax.framework.dto.BaseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2023-09-09 22:38:04
 * 
 */
@Data
@Schema(description = "默认实体")
public class DynamicPageDTO extends BaseDTO {
    /**
     * 页面名称 COLUMN:dynamic_page_name
     */
    @Schema(description = "页面名称")
    private String dynamicPageName;

    /**
     * 页面配置，压缩后的json COLUMN:dynamic_page_config
     */
    @Schema(description = "页面配置，压缩后的json")
    private String dynamicPageConfig;

    /**
     * 子应用 COLUMN:sub_app_code
     */
    @Schema(description = "子应用")
    private String subAppCode;

    /**
     * 页面编码，用户自定义 COLUMN:dynamic_page_code
     */
    @Schema(description = "页面编码，用户自定义")
    private String dynamicPageCode;

    /**
     * 页面国际化code COLUMN:page_language_code
     */
    @Schema(description = "页面国际化code")
    private String pageLanguageCode;

    /**
     * 页面标题国际化code COLUMN:page_language_title_code
     */
    @Schema(description = "页面标题国际化code")
    private String pageLanguageTitleCode;

    /**
     * 是否已生成路由（no-未生成；yes-已生成） COLUMN:config_route
     */
    @Schema(description = "是否已生成路由（no-未生成；yes-已生成）")
    private String configRoute;

    /**
     * 关联变量 COLUMN:relation_variables
     */
    @Schema(description = "关联变量")
    private String relationVariables;

    /**
     * 所属属性逻辑设置 COLUMN:relation_search_attr_logic
     */
    @Schema(description = "所属属性逻辑设置")
    private String relationSearchAttrLogic;

    /**
     * 引用的下拉框 COLUMN:relation_options
     */
    @Schema(description = "引用的下拉框")
    private String relationOptions;

    /**
     * 状态 draft 草稿 published 已发布 COLUMN:page_status
     */
    @Schema(description = "状态 draft 草稿 published 已发布")
    private String pageStatus;

    /**
     * 按钮菜单数据 COLUMN:menu_content
     */
    @Schema(description = "按钮菜单数据")
    private String menuContent;

    /**
     * 多语言数据 COLUMN:lang_content
     */
    @Schema(description = "多语言数据")
    private String langContent;

    /**
     * 数据版本 COLUMN:data_version
     */
    @Schema(description = "数据版本")
    private Integer dataVersion;

    /**
     * 原始页面名称 COLUMN:page_original_name
     */
    @Schema(description = "原始页面名称")
    private String pageOriginalName;

    /**
     * 加载API内容 COLUMN:load_api_content
     */
    @Schema(description = "加载API内容")
    private String loadApiContent;

    /**
     * 是否模板 COLUMN:is_template
     */
    @Schema(description = "是否模板")
    private Integer isTemplate;
}
