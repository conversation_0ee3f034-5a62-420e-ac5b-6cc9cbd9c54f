package com.ey.tax.cloud.base.service.workflow.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ey.cn.tax.framework.exception.EyTaxBusinessException;
import com.ey.cn.tax.framework.mybatisplus.core.entity.BaseEntity;
import com.ey.cn.tax.framework.mybatisplus.extension.service.AbstractService;
import com.ey.cn.tax.framework.utils.EmptyUtils;
import com.ey.cn.tax.framework.utils.JacksonUtils;
import com.ey.tax.cloud.base.constants.WorkflowConstants;
import com.ey.tax.cloud.base.dto.workflow.item.ItemParseFlowResultDTO;
import com.ey.tax.cloud.base.dto.workflow.model.ModelDTO;
import com.ey.tax.cloud.base.entity.workflow.Define;
import com.ey.tax.cloud.base.entity.workflow.Item;
import com.ey.tax.cloud.base.entity.workflow.Model;
import com.ey.tax.cloud.base.mapper.workflow.ModelMapper;
import com.ey.tax.cloud.base.service.workflow.DefineService;
import com.ey.tax.cloud.base.service.workflow.ItemService;
import com.ey.tax.cloud.base.service.workflow.ModelService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2025-03-10 15:28:33
 * 
 */
@Service
public class ModelServiceImpl extends AbstractService<ModelMapper, Model> implements ModelService {

    @Autowired
    private DefineService defineService;

    @Lazy
    @Autowired
    private ItemService itemService;

    @Override
    public Model queryDeployedModel(String workflowDefineId) {
        Model qModel = new Model();
        qModel.setModelStatus(WorkflowConstants.MODEL_STATUS_DEPLOYED);
        qModel.setFlowDefineId(workflowDefineId);
        return this.queryOneByPara(qModel);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void deploy(ModelDTO modelDTO) {
        Model model = this.queryById(modelDTO.getId());
        Map<String,Object> params = (Map<String, Object>) JacksonUtils.parse(model.getModelContent());
        ItemParseFlowResultDTO itemDTO = itemService.parseFlowToItem(params,model.getId());
        List<Item> itemList = itemDTO.getItemList();
        String errorInfo = itemService.checkFlowChart(modelDTO.getId(),itemList);
        if (StringUtils.isNotEmpty(errorInfo)){
            throw new EyTaxBusinessException("deployFlowChartError",errorInfo);
        }

        itemService.deleteByModelId(model.getId(),itemList);
        //已部署修改为未部署
        Model update = new Model();
        update.setModelStatus(WorkflowConstants.MODEL_STATUS_UNDEPLOYED);
        this.update(update, Wrappers.<Model>lambdaUpdate()
                .eq(Model::getModelStatus,WorkflowConstants.MODEL_STATUS_DEPLOYED)
                .eq(Model::getFlowDefineId,model.getFlowDefineId()));

        //部署的时候生成版本号，生成规则为 : 年月日时分秒
        Model deploy = new Model();
        deploy.setId(model.getId());
        if (StringUtils.isEmpty(model.getVersion())){
            //生成版本号
            deploy.setVersion("V".concat(generateVersion()));
        }
        deploy.setModelStatus(WorkflowConstants.MODEL_STATUS_DEPLOYED);
        this.updateById(deploy);

        //define未部署修改为已部署
        Define define = new Define();
        define.setId(model.getFlowDefineId());
        define.setFlowStatus(WorkflowConstants.MODEL_STATUS_DEPLOYED);
        defineService.update(define);
    }

    private String generateVersion() {
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();

        // 定义格式化器，格式为：yyyyMMddHHmmss
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

        // 格式化当前时间为版本号
        return now.format(formatter);
    }

    @Override
    public boolean existsFlowInstance(ModelDTO modelDTO) {
        return defineService.existsFlowInstance(modelDTO);
    }

    @Override
    public boolean updateUndeployed(String flowDefineId) {
        Long c = baseMapper.selectCount(Wrappers.<Model>lambdaQuery().eq(Model::getFlowDefineId,flowDefineId));
        if (c == 1){
            Define define = new Define();
            define.setId(flowDefineId);
            define.setFlowStatus(WorkflowConstants.MODEL_STATUS_UNDEPLOYED);
            defineService.update(define);
        }
        return true;
    }
}