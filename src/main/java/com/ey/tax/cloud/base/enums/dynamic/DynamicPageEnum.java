package com.ey.tax.cloud.base.enums.dynamic;

import com.baomidou.mybatisplus.annotation.EnumValue;

public enum DynamicPageEnum {
    QUERY_PARAM_TREE("tree"),
    QUERY_PARAM_TREE_SELECTED_ID("treeSelectedId"),
    QUERY_PARAM_TREE_ASSIGNMENT_PARAM("treeQueryParam"),
    QUERY_PARAM_TREE_PARENT_COLUMN_PARAM("treeParentColumn"),
    QUERY_PARAM_TREE_RESULT_COLUMN_PARAM("treeResultColumn"),
    QUERY_PARAM_TREE_PARENT_COLUMN_PARAM_DEFAULT("parentIdPath"),
    QUERY_PARAM_TREE_RESULT_COLUMN_PARAM_DEFAULT("value"),
    QUERY_PARAM_TREE_RESULT_VALUE_DEFAULT("TREE_RESULT_IS_NULL");

    @EnumValue
    private final String value;

    public String getValue() {
        return this.value;
    }

    private DynamicPageEnum(String value) {
        this.value = value;
    }
}
