package com.ey.tax.cloud.base.dto.guide;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.gson.JsonObject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2023-12-08 17:18:28
 * 
 */
@Data
@Schema(description = "新增实体")
public class PageGuideRecordAddDTO {

    /**
     * 已完成导引页面。其json中的key为已完成导引页面的唯一标识，value可扩展其他相关信息 COLUMN:finish_pages
     */
    @Schema(description = "已完成导引页面。其json中的key为已完成导引页面的唯一标识，value可扩展其他相关信息", requiredMode = Schema.RequiredMode.REQUIRED)
    private ObjectNode finishPages;
}