package com.ey.tax.cloud.base.controller.feign;

import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.web.annotation.EyGetMapping;
import com.ey.cn.tax.framework.web.annotation.EyPostMapping;
import com.ey.tax.cloud.app.dto.resource.AppPermissionDTO;
import com.ey.tax.cloud.app.entity.user.AppUser;
import com.ey.tax.cloud.base.dto.*;
import com.ey.tax.cloud.base.dto.app.AppCloneDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * @date 2023-09-09 22:38:04
 */
@Tag(name = "应用级权限Feign接口")
public interface FeignAppAuthController {

    @Operation(summary = "根据roleCode获取当前角色的用户列表")
    @EyGetMapping(value = "/getUsers/{roleCode}")
    List<AppUser> findByCategoryCode(@PathVariable("roleCode") String roleCode);

    @Operation(summary = "查询分组下的用户")
    @EyGetMapping(value = "/getByGroup")
    ResponseDTO<List<AppUser>> findUsersByGroup(@RequestParam("groupCode")  String groupCode);

    @Operation(summary = "查询系统下应用列表")
    @EyGetMapping(value = "/getAppBySysCode")
    ResponseDTO<List<AppDTO>> findAppBySysCode(@RequestParam("sysCode")  String sysCode);

    @Operation(summary = "新增应用")
    @EyPostMapping(value = "/insertOne")
    int insertOne(@RequestBody AppDTO appDTO);

    @Operation(summary = "获取我的应用列表")
    @EyGetMapping(value = "/getMyAppList")
    List<String> getMyAppList();

    @Operation(summary = "获取我的应用列表，以及应用写的角色")
    @EyGetMapping(value = "/getMyAppInfo")
    List<AppInfoDTO> getMyAppInfo();

    @Operation(summary = "应用克隆所有资源", description = "应用克隆所有资源")
    @EyPostMapping(value = "/cloneApp")
    ResponseDTO<Void> cloneApp(@RequestBody AppCloneAllFeignDTO dto);

    @Operation(summary = "查询应用角色系")
    @EyPostMapping(value = "/getAppRoles")
    ResponseDTO<List<AppRoleDTO>> getAppRoles(@RequestBody AppRoleParam param);

    @Operation(summary = "查询应用下的用户,以及用户角色关系")
    @EyPostMapping(value = "/getAppUserAndRoles")
    ResponseDTO<List<AppUserDTO>> getAppUserAndRoles(@RequestBody AppUserParamDTO param);

    @Operation(summary = "查询应用下的用户")
    @EyPostMapping(value = "/getAppUser")
    ResponseDTO<List<AppUserDTO>> getAppUser(@RequestBody AppUserParamDTO param);

    @Operation(summary = "查询应用下的用户角色关系")
    @EyPostMapping(value = "/getAppUserRoles")
    ResponseDTO<List<AppUserRoleDTO>> getAppUserRoles(@RequestBody AppUserRoleQueryParam param);

    @Operation(summary = "新增应用用户角色关系")
    @EyPostMapping(value = "/addAppUserRole")
    ResponseDTO<Void> addAppUserRole(@RequestBody List<@Valid AppUserRoleParam> userRoles);

    @Operation(summary = "删除应用用户角色关系")
    @EyPostMapping(value = "/removeAppUserRole")
    ResponseDTO<Void> removeAppUserRole(@RequestBody List<String> userRoleIds);

    /**
     * @param appPermissionDTO 权限编码列表
     * @return (resource_code, true/false)
     */
    @Operation(summary = "判断用户是否拥有某资源权限", description = "判断用户是否拥有某资源权限")
    @EyPostMapping(value = "/getPermissions")
    Map<String, Boolean> getPermissions(@RequestBody @Validated AppPermissionDTO appPermissionDTO);

}
