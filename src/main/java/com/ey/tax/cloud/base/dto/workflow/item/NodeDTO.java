package com.ey.tax.cloud.base.dto.workflow.item;

import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Description node
 * @Date 2025/3/10
 */
@Data
@ToString
public class NodeDTO {

    /**
     * 节点ID
     */
    private String id;

    /**
     * 节点类型
     */
    private String type;

    /**
     *  节点x坐标
     */
    private int x;

    /**
     * 节点y坐标
     */
    private int y;

    /**
     * 节点文本信息
     */
    private TextDTO text;

    /**
     * 节点属性（嵌套属性）
     */
    private PropertiesDTO properties;

}
