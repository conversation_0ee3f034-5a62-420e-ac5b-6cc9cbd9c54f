package com.ey.tax.cloud.base.dto.pdfconverter;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-06-28 18:26:21
 * 
 */
@Data
@Schema(description = "根据条件分页查询实体")
public class PDFConverterEngagementsQueryPageDTO {
    /**
     * 项目名 COLUMN:engagement_name
     */
    @Schema(description = "项目名", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String engagementName;

    /**
     * 导出文件名 COLUMN:export_file_name
     */
    @Schema(description = "导出文件名", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String exportFileName;

    /**
     * 导出文件关联file服务文件id COLUMN:related_id
     */
    @Schema(description = "导出文件关联file服务文件id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String relatedId;

    /**
     * 区分上传文件类型 COLUMN:operate_type
     */
    @Schema(description = "区分上传文件类型", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer operateType;
}