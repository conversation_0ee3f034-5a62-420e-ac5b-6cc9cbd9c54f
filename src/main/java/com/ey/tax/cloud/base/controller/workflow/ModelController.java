package com.ey.tax.cloud.base.controller.workflow;

import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.dto.SearchDTO;
import com.ey.cn.tax.framework.validator.AddGroup;
import com.ey.cn.tax.framework.validator.DeleteByIdGroup;
import com.ey.cn.tax.framework.validator.DeleteByIdListGroup;
import com.ey.cn.tax.framework.validator.QueryByIdGroup;
import com.ey.cn.tax.framework.validator.QueryByIdListGroup;
import com.ey.cn.tax.framework.validator.QueryPageGroup;
import com.ey.cn.tax.framework.validator.UpdateByIdGroup;
import com.ey.cn.tax.framework.web.annotation.EyPostMapping;
import com.ey.cn.tax.framework.web.base.BaseController;
import com.ey.tax.cloud.base.dto.workflow.model.ModelAddDTO;
import com.ey.tax.cloud.base.dto.workflow.model.ModelBatchUpdateDTO;
import com.ey.tax.cloud.base.dto.workflow.model.ModelDTO;
import com.ey.tax.cloud.base.dto.workflow.model.ModelDeleteByIdDTO;
import com.ey.tax.cloud.base.dto.workflow.model.ModelDeleteByIdListDTO;
import com.ey.tax.cloud.base.dto.workflow.model.ModelQueryByIdDTO;
import com.ey.tax.cloud.base.dto.workflow.model.ModelQueryByIdListDTO;
import com.ey.tax.cloud.base.dto.workflow.model.ModelQueryDTO;
import com.ey.tax.cloud.base.dto.workflow.model.ModelQueryPageDTO;
import com.ey.tax.cloud.base.dto.workflow.model.ModelRepDTO;
import com.ey.tax.cloud.base.dto.workflow.model.ModelUpdateByIdDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2025-03-10 15:28:33
 * 
 */
@Tag(name="Model")
public interface ModelController extends BaseController<ModelDTO> {
    /**
     * add Model from http
     */
    @Operation(summary = "新增", description = "新增一条数据")
    //@EyPostMapping(value = "/add.do")
    ResponseDTO<Void> add(@RequestBody @Validated({AddGroup.class}) ModelAddDTO dto);

    /**
     * addList Model from http
     */
    @Operation(summary = "批量新增", description = "批量新增数据")
    //@EyPostMapping(value = "/addList.do")
    ResponseDTO<Void> addList(@RequestBody List<ModelAddDTO> dtos);

    /**
     * delete Model from http
     */
    @Operation(summary = "根据id删除", description = "根据id删除一条数据")
    @EyPostMapping(value = "/deleteById.do")
    ResponseDTO<Void> deleteById(@RequestBody @Validated({DeleteByIdGroup.class}) ModelDeleteByIdDTO dto);

    /**
     * deleteList Model from http
     */
    @Operation(summary = "根据id列表删除", description = "根据id列表批量删除数据")
    //@EyPostMapping(value = "/deleteByIdList.do")
    ResponseDTO<Void> deleteByIdList(@RequestBody @Validated({DeleteByIdListGroup.class}) ModelDeleteByIdListDTO dto);

    /**
     * update Model from http
     */
    @Operation(summary = "根据id更新", description = "根据id更新一条数据")
    //@EyPostMapping(value = "/updateById.do")
    ResponseDTO<Void> updateById(@RequestBody @Validated({UpdateByIdGroup.class}) ModelUpdateByIdDTO dto);

    /**
     * updateBatch Model from http
     */
    @Operation(summary = "批量更新", description = "批量更新数据")
    //@EyPostMapping(value = "/updateBatch.do")
    ResponseDTO<Void> updateBatch(@RequestBody List<ModelBatchUpdateDTO> dtos);

    /**
     * query Model from http
     */
    @Operation(summary = "根据id查询", description = "根据id查询数据")
    @EyPostMapping(value = "/queryById.do")
    ResponseDTO<ModelRepDTO> queryById(@RequestBody @Validated({QueryByIdGroup.class}) ModelQueryByIdDTO dto);

    /**
     * queryByIdList Model from http
     */
    @Operation(summary = "根据id列表查询", description = "根据id列表查询数据")
    //@EyPostMapping(value = "/queryByIdList.do")
    ResponseDTO<List<ModelRepDTO>> queryByIdList(@RequestBody @Validated({QueryByIdListGroup.class}) ModelQueryByIdListDTO dto);

    /**
     * queryList Model from http
     */
    @Operation(summary = "查询列表", description = "查询数据列表")
    //@EyPostMapping(value = "/queryList.do")
    ResponseDTO<List<ModelRepDTO>> queryList(@RequestBody ModelQueryDTO dto);

    /**
     * Page Model from http
     */
    @Operation(summary = "分页查询", description = "根据条件分页查询")
    //@EyPostMapping(value = "/queryPage.do")
    ResponseDTO<SearchDTO<ModelRepDTO>> queryPage(@RequestBody @Validated({QueryPageGroup.class}) SearchDTO<ModelQueryPageDTO> searchDTO);

    /**
     * Count Model from http
     */
    @Operation(summary = "查询数量", description = "根据条件查询数量")
    //@EyPostMapping(value = "/queryCount.do")
    ResponseDTO<Long> queryCount(@RequestBody ModelQueryDTO modelDTO);

    /**
     * One Model from http
     */
    @Operation(summary = "查询单个数据", description = "根据条件查询一条数据,如果查询到多条则返回异常.")
    //@EyPostMapping(value = "/queryOne.do")
    ResponseDTO<ModelRepDTO> queryOne(@RequestBody ModelQueryDTO modelDTO);

    /**
     * exist Model from http
     */
    @Operation(summary = "查询是否存在", description = "根据条件查询是否存在")
    //@EyPostMapping(value = "/exist.do")
    ResponseDTO<Boolean> exist(@RequestBody ModelQueryDTO modelDTO);

    @Operation(summary = "部署", description = "部署")
    @EyPostMapping(value = "/deploy.do")
    ResponseDTO<Void> deploy(@RequestBody ModelDTO modelDTO);

    @Operation(summary = "是否可以修改的提示信息", description = "是否可以修改的提示信息")
    @EyPostMapping(value = "/isSave.do")
    ResponseDTO<String> isSave(@RequestBody ModelDTO modelDTO);

}