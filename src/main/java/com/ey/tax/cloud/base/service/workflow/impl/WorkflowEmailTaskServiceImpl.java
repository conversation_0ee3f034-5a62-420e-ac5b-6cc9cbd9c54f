package com.ey.tax.cloud.base.service.workflow.impl;

import com.ey.cn.tax.framework.execution.ExecutionCommand;
import com.ey.cn.tax.framework.utils.EmptyUtils;
import com.ey.cn.tax.framework.utils.JacksonUtils;
import com.ey.cn.tax.framework.utils.ObjectUtils;
import com.ey.cn.tax.lite.api.domain.service.ILiteDatasetExecutionService;
import com.ey.cn.tax.lite.impl.port.input.dto.LiteDatasetExecutionDTO;
import com.ey.tax.cloud.base.constants.WorkflowConstants;
import com.ey.tax.cloud.base.dto.workflow.item.AttributeDTO;
import com.ey.tax.cloud.base.entity.workflow.*;
import com.ey.tax.cloud.base.service.workflow.WorkflowAbstractService;
import com.ey.tax.cloud.base.service.workflow.WorkflowAsyncExecuteService;
import com.ey.tax.cloud.base.service.workflow.WorkflowEmailTaskService;
import com.ey.tax.cloud.base.service.workflow.WorkflowServiceTaskService;
import com.ey.tax.cloud.base.utils.PlaceholderUtils;
import com.ey.tax.cloud.mail.dto.DetailExtensionSendRequest;
import com.ey.tax.cloud.mail.remote.MailFeignClient;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

@Service("WorkflowEmailTaskService")
public class WorkflowEmailTaskServiceImpl extends WorkflowAbstractService<WorkflowEmailTask> implements WorkflowServiceTaskService<WorkflowEmailTask>, WorkflowAsyncExecuteService<WorkflowEmailTask> {

    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ILiteDatasetExecutionService liteDatasetExecutionService;

    @Autowired
    private MailFeignClient mailFeignClient;

    @Override
    public List<WorkflowEmailTask> create(Item item, Instance instance, Map<String, Object> parameters) {
        List<WorkflowEmailTask> workflowEmailTaskList = new ArrayList<>();
        Task task = new Task();
        BeanUtils.copyProperties(item,task);
        task.setId(null);
        task.setTaskStatus(WorkflowConstants.TASK_STATUS_INIT);
        task.setTaskType(item.getItemType());
        task.setSubType(item.getSubType());
        task.setItemId(item.getId());
        task.setFlowInstanceId(instance.getId());
        task.setFlowStep(instance.getFlowStep());
        task.setAutoApprove(WorkflowConstants.TASK_AUTO_APPROVE_YES);
        String executeType = (String)item.getItemAttribute().get(WorkflowConstants.SERVICE_TASK_PROPERTY_EXECUTE_MODE);
        task.setExecuteType(executeType);
        taskService.save(task);
        WorkflowEmailTask workflowEmailTask = this.build(item, instance, null);
        workflowEmailTask.setWorkflowTaskId(task.getId());
        workflowEmailTaskList.add(workflowEmailTask);
        return workflowEmailTaskList;
    }

    @Override
    public WorkflowEmailTask build(Item item, Instance instance, Approve approve) {
        WorkflowEmailTask workflowEmailTask = new WorkflowEmailTask();
        workflowEmailTask.setWorkflowInstanceId(instance.getId());
        return workflowEmailTask;
    }

    @Override
    public ExecuteResult execute(Item item, Instance instance, WorkflowEmailTask workflowEmailTask, Approve approve) {
        ExecuteResult executeResult = new ExecuteResult();
        String executeMode = (String)item.getItemAttribute().get(WorkflowConstants.SERVICE_TASK_PROPERTY_EXECUTE_MODE);
        if(WorkflowConstants.SERVICE_TASK_PROPERTY_EXECUTE_MODE_SYNC.equalsIgnoreCase(executeMode)){
            String result = syncExecute(item, instance, workflowEmailTask, approve);
            executeResult.setExecuteStatus(result);
        }else if(WorkflowConstants.SERVICE_TASK_PROPERTY_EXECUTE_MODE_ASYNC.equalsIgnoreCase(executeMode)){
            aSyncExecute(item, instance, workflowEmailTask, approve);
            executeResult.setExecuteStatus(WorkflowConstants.NEXT_NODE_ASYNC);
        }else {
            String result = syncExecute(item, instance, workflowEmailTask, approve);
            executeResult.setExecuteStatus(result);
        }
        return executeResult;
    }

    public String syncExecute(Item item, Instance instance, WorkflowEmailTask workflowEmailTask, Approve approve){
        String beforeServiceCode = (String)item.getItemAttribute().get(WorkflowConstants.USER_TASK_PROPERTY_BEFORE_SERVICE_CODE);
        String afterServiceCode = (String)item.getItemAttribute().get(WorkflowConstants.USER_TASK_PROPERTY_AFTER_SERVICE_CODE);
        Object result = null;
        if(EmptyUtils.isNotEmpty(beforeServiceCode)){
            LiteDatasetExecutionDTO liteDatasetExecutionDTO = new LiteDatasetExecutionDTO();
            liteDatasetExecutionDTO.setCode(beforeServiceCode);
            liteDatasetExecutionDTO.setArgs(approve.getParameters());
            liteDatasetExecutionDTO.setSysCode(instance.getSysCode());
            liteDatasetExecutionDTO.setAppCode(instance.getAppCode());
            ExecutionCommand cmd = ExecutionCommand.builder().args(approve.getParameters()).build();
            logger.info("sync execute groovy: " + beforeServiceCode + " sysCode: " + instance.getSysCode() + " appCode: " + instance.getAppCode());
            result = liteDatasetExecutionService.execute(liteDatasetExecutionDTO, cmd);
        }
        Map<String, Object> parameters = approve.getParameters();
        Map<String, Object> newParameters = new HashMap<>(parameters);
        if(EmptyUtils.isNotEmpty(result)){
            if(result instanceof Map){
                Map<String, Object> resultMap = (Map<String, Object>)result;
                newParameters.putAll(resultMap);
            }
        }
        //发送邮件
        //newParameters 发送邮件使用的参数
        //其他参数按照这个方式获取
        //String beforeServiceCode = (String)item.getItemAttribute().get(WorkflowConstants.USER_TASK_PROPERTY_BEFORE_SERVICE_CODE);
        this.sendMail(newParameters,item,instance.getSysCode(),instance.getAppCode());
        if(EmptyUtils.isNotEmpty(afterServiceCode)){
            LiteDatasetExecutionDTO liteDatasetExecutionDTO = new LiteDatasetExecutionDTO();
            liteDatasetExecutionDTO.setCode(afterServiceCode);
            liteDatasetExecutionDTO.setArgs(approve.getParameters());
            liteDatasetExecutionDTO.setSysCode(instance.getSysCode());
            liteDatasetExecutionDTO.setAppCode(instance.getAppCode());
            ExecutionCommand cmd = ExecutionCommand.builder().args(approve.getParameters()).build();
            logger.info("sync execute groovy: " + afterServiceCode + " sysCode: " + instance.getSysCode() + " appCode: " + instance.getAppCode());
            liteDatasetExecutionService.execute(liteDatasetExecutionDTO, cmd);
        }
        Log log = new Log();
        log.setFlowStep(instance.getFlowStep());
        log.setFlowInstanceId(instance.getId());
        log.setItemType(item.getItemType());
        log.setSubType(item.getSubType());
        log.setLogType(WorkflowConstants.LOG_TYPE_EXECUTE);
        String executeMode = (String)item.getItemAttribute().get(WorkflowConstants.SERVICE_TASK_PROPERTY_EXECUTE_MODE);
        log.setExecuteType(executeMode);
        log.setExpression(instance.getSysCode() + " sysCode: " + instance.getSysCode() + " appCode: " + instance.getAppCode());
        logService.saveLog(log);
        Task task = taskService.queryById(workflowEmailTask.getWorkflowTaskId());
        task.setOperateType(WorkflowConstants.TASK_OPERATE_TYPE_APPROVE);
        task.setTaskStatus(WorkflowConstants.TASK_STATUS_FINISH);
        taskService.update(task);
        return WorkflowConstants.NEXT_NODE_SUCCESS;
    }

    private void sendMail(Map<String, Object> newParameters, Item item,
                          String sysCode, String appCode) {
        //替换变量
        AttributeDTO.EmailTaskAttributeDTO attributeDTO = (AttributeDTO.EmailTaskAttributeDTO) itemService.attributeToDTO(item);

        DetailExtensionSendRequest request = new DetailExtensionSendRequest();
        request.setTemplateCode(attributeDTO.getEmailTemplate());
        request.setBusinessData(Base64.getEncoder().encodeToString(JacksonUtils.toJson(newParameters).getBytes()));
        request.setTo(replaceVariable(newParameters,attributeDTO.getEmailAddressTo()));
        request.setCc(replaceVariable(newParameters,attributeDTO.getEmailAddressCC()));
        request.setBcc(replaceVariable(newParameters,attributeDTO.getEmailAddressBCC()));
        request.setSysCode(sysCode);
        request.setAppCode(appCode);
        mailFeignClient.sendMailExt(request);
    }

    private List<String> replaceVariable(Map<String, Object> parameters,List<String> variableInfos) {
        Set<String> set = new LinkedHashSet<>();
        if (!CollectionUtils.isEmpty(variableInfos)){
            variableInfos.forEach(v ->{
                String key = PlaceholderUtils.getKey(v);
                if (StringUtils.isNotEmpty(key)){
                    String r = StringUtils.EMPTY;
                    Object vk = parameters.get(key);
                    List<String> rl = new ArrayList<>();
                    if (ObjectUtils.isNotEmpty(vk)){
                        if (vk instanceof List){
                            rl.addAll((Collection<? extends String>) vk);
                        } else if (vk instanceof String){
                            r = vk.toString();
                        }
                    }
                    if (StringUtils.isNotEmpty(r)){
                        set.add(r);
                    }
                    if (!CollectionUtils.isEmpty(rl)){
                        set.addAll(rl);
                    }
                } else {
                    set.add(v);
                }
            });
        }

        if (!CollectionUtils.isEmpty(set)){
            return new ArrayList<>(set);
        }
        return new ArrayList<>();
    }

}
