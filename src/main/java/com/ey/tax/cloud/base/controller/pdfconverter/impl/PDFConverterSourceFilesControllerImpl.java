package com.ey.tax.cloud.base.controller.pdfconverter.impl;

import com.ey.cn.tax.framework.dto.ResponseDTO;
import com.ey.cn.tax.framework.dto.SearchDTO;
import com.ey.cn.tax.framework.utils.ConvertUtils;
import com.ey.cn.tax.framework.web.annotation.EyRestController;
import com.ey.cn.tax.framework.web.base.AbstractController;
import com.ey.tax.cloud.base.controller.pdfconverter.PDFConverterSourceFilesController;
import com.ey.tax.cloud.base.dto.pdfconverter.*;
import com.ey.tax.cloud.base.entity.pdfconverter.PDFConverterSourceFiles;
import com.ey.tax.cloud.base.service.pdfconverter.PDFConverterSourceFilesService;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * @date 2024-06-28 18:19:23
 */
@EyRestController(path = "/v1/pdfconvertersourcefiles")
public class PDFConverterSourceFilesControllerImpl extends AbstractController<PDFConverterSourceFilesService, PDFConverterSourceFilesDTO, PDFConverterSourceFiles> implements PDFConverterSourceFilesController {

    /**
     * Page PDFConverterSourceFiles from http
     */
    @Override
    public ResponseDTO<SearchDTO<PDFConverterSourceFilesResultPageRepDTO>> queryResultPage(SearchDTO<PDFConverterSourceFilesQueryPageDTO> searchDTO) {

        var search = getService().queryResultPage(searchDTO);
        return ResponseDTO.success(ConvertUtils.convertSearch2SearchDTO(search, PDFConverterSourceFilesResultPageRepDTO.class));
    }


    /**
     * delete PDFConverterSourceFiles from http
     */
    @Override
    public ResponseDTO<Void> deleteById(PDFConverterSourceFilesDeleteByIdDTO dto) {
        service.deleteById(dto.getId());
        return ResponseDTO.success();
    }

    @Override
    public ResponseDTO<Void> clearData(PDFConverterSourceFilesDeleteDTO dto) {
        service.clearData(dto.getEngagementId());
        return ResponseDTO.success();
    }

    /**
     * add PDFConverterSourceFiles from http
     */
    @Override
    public ResponseDTO<String> uploadZip(String engagementId, MultipartFile file) {
        try {
            service.saveZipFiles(engagementId, file);
            return ResponseDTO.success();
        } catch (Exception e) {
            return ResponseDTO.errorMessage("400", "上传zip失败: " + e.getMessage());
        }
    }

    /**
     * addList PDFConverterSourceFiles from http
     */
    @Override
    public ResponseDTO<String> uploadBatch(String engagementId, MultipartFile[] files) {
        try {
            service.saveFiles(engagementId, files);
            return ResponseDTO.success();
        } catch (Exception e) {
            return ResponseDTO.errorMessage("400", "上传文件失败: " + e.getMessage());
        }
    }

    /**
     * Page PDFConverterSourceFiles from http
     */
    @Override
    public ResponseDTO<SearchDTO<PDFConverterSourceFilesRepDTO>> queryPage(SearchDTO<PDFConverterSourceFilesQueryPageDTO> searchDTO) {
        var search = getService().queryFileSourcePage(searchDTO);
        return ResponseDTO.success(ConvertUtils.convertSearch2SearchDTO(search, PDFConverterSourceFilesRepDTO.class));
    }

    @Override
    public ResponseDTO<String> generatePDF(@RequestBody List<PDFConverterSourceFilesGeneratePDFDTO> pdfConverterSourceFilesDTO) {
        String fileId = "";
        try {
            logger.info("Start to generatePDF for {}", pdfConverterSourceFilesDTO.toString());
            fileId = getService().generatePDF(pdfConverterSourceFilesDTO);
            ResponseDTO<String> result = ResponseDTO.success();
            result.setMessage("生成PDF中，请点击刷新查看");
            return result;
        } catch (Exception e) {
            return ResponseDTO.errorMessage("400", "生成PDF失败: " + e.getMessage());
        }
    }


    @Override
    public ResponseDTO<String> download(@RequestBody PDFConverterSourceFilesDTO dto, HttpServletResponse resp) {
        try {
            getService().download(dto, resp);
            return ResponseDTO.success();
        } catch (Exception e) {
            return ResponseDTO.errorMessage("400", "下载压缩包失败: " + e.getMessage());
        }
    }


    /**
     * deleteList PDFConverterSourceFiles from http
     */
    @Override
    public ResponseDTO<Void> deleteByIdList(PDFConverterSourceFilesDeleteByIdListDTO dto) {
        getService().deleteByIds(dto.getIds());
        return ResponseDTO.success();
    }

    /**
     * update PDFConverterSourceFiles from http
     */
    @Override
    public ResponseDTO<String> updateRootFolderById(PDFConverterSourceFilesUpdateByIdDTO dto) {
        PDFConverterSourceFiles entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        var message = getService().updateRootFolder(entity);
        if (StringUtils.isBlank(message)) {
            return ResponseDTO.success();
        }
        return ResponseDTO.errorMessage("", message);
    }

    /**
     * updateBatch PDFConverterSourceFiles from http
     */
    @Override
    public ResponseDTO<Void> updateBatch(List<PDFConverterSourceFilesBatchUpdateDTO> dtos) {
        List<PDFConverterSourceFiles> entities = ConvertUtils.convertDTOList2EntityList(dtos, entityClass);
        getService().updateBatchByIds(entities);
        return ResponseDTO.success();
    }

    /**
     * query PDFConverterSourceFiles from http
     */
    @Override
    public ResponseDTO<PDFConverterSourceFilesRepDTO> queryById(PDFConverterSourceFilesQueryByIdDTO dto) {
        PDFConverterSourceFiles entity = getService().queryById(dto.getId());
        PDFConverterSourceFilesRepDTO rDTO = ConvertUtils.convertEntity2DTO(entity, PDFConverterSourceFilesRepDTO.class);
        return ResponseDTO.success(rDTO);
    }

    /**
     * queryByIdList PDFConverterSourceFiles from http
     */
    @Override
    public ResponseDTO<List<PDFConverterSourceFilesRepDTO>> queryByIdList(PDFConverterSourceFilesQueryByIdListDTO dto) {
        List<PDFConverterSourceFiles> entities = getService().queryByIds(dto.getIds());
        return ResponseDTO.success(ConvertUtils.convertEntityList2DTOList(entities, PDFConverterSourceFilesRepDTO.class));
    }

    /**
     * queryList PDFConverterSourceFiles from http
     */
    @Override
    public ResponseDTO<List<PDFConverterSourceFilesRepDTO>> queryList(PDFConverterSourceFilesQueryDTO dto) {
        PDFConverterSourceFiles entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        List<PDFConverterSourceFiles> entities = getService().queryByPara(entity);
        if (entities.isEmpty()) {
            return ResponseDTO.success(new ArrayList<PDFConverterSourceFilesRepDTO>());
        }
        return ResponseDTO.success(getService().queryList(entities));
    }


    /**
     * Count PDFConverterSourceFiles from http
     */
    @Override
    public ResponseDTO<Long> queryCount(PDFConverterSourceFilesQueryDTO dto) {
        PDFConverterSourceFiles entity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        Long count = getService().queryCountByPara(entity);
        return ResponseDTO.success(count);
    }

    /**
     * One PDFConverterSourceFiles from http
     */
    @Override
    public ResponseDTO<PDFConverterSourceFilesRepDTO> queryOne(PDFConverterSourceFilesQueryDTO dto) {
        PDFConverterSourceFiles qEntity = ConvertUtils.convertDTO2Entity(dto, entityClass);
        PDFConverterSourceFiles rEntity = getService().queryOneByPara(qEntity);
        PDFConverterSourceFilesRepDTO rDTO = ConvertUtils.convertEntity2DTO(rEntity, PDFConverterSourceFilesRepDTO.class);
        return ResponseDTO.success(rDTO);
    }

    /**
     * exist PDFConverterSourceFiles from http
     */
    @Override
    public ResponseDTO<Boolean> exist(PDFConverterSourceFilesQueryDTO dto) {
        boolean resullt = getService().existByPara(ConvertUtils.convertDTO2Entity(dto, entityClass));
        return ResponseDTO.success(resullt);
    }
}
