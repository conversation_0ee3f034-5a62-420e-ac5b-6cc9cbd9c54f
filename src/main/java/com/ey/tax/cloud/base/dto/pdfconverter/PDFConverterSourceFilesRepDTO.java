package com.ey.tax.cloud.base.dto.pdfconverter;

import com.ey.cn.tax.framework.dto.BaseRepDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-06-28 18:19:23
 * 
 */
@Data
@Schema(description = "查询返回实体")
public class PDFConverterSourceFilesRepDTO extends BaseRepDTO {
    /**
     * 上传文件名或文件夹路径 COLUMN:path_name
     */
    @Schema(description = "上传文件名或文件夹路径")
    private String pathName;

    /**
     * 父级文件夹路径 COLUMN:parent_id
     */
    @Schema(description = "父级文件夹路径")
    private String parentId;

    /**
     * 区分文件或文件夹 COLUMN:type
     */
    @Schema(description = "区分文件或文件夹")
    private Integer type;


    /**
     * 生成pdf的密码 COLUMN:password
     */
    @Schema(description = "生成pdf的密码")
    private String password;

    /**
     * 关联File服务的id COLUMN:related_id
     */
    @Schema(description = "关联File服务的id")
    private String relatedId;

    /**
     * 关联项目表id COLUMN:engagement_id
     */
    @Schema(description = "关联项目表id")
    private String engagementId;

    @Schema(description = "子节点")
    private List<PDFConverterSourceFilesRepDTO> children;
}