package com.ey.tax.cloud.base.dto.user;

import com.ey.cn.tax.framework.validator.DeleteByIdListGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-11-04 15:59:52
 * 
 */
@Data
@Schema(description = "根据id列表删除实体")
public class UserLeaveDeleteByIdListDTO {
    
    /**
     * 数据id列表
     */
    @Schema(description = "数据id列表")
    @NotNull(message = "{UserLeaveDTO.ids.NotNull}", groups = {DeleteByIdListGroup.class})
    @Size(min = 1, message = "{UserLeaveDTO.ids.Size}", groups = {DeleteByIdListGroup.class})
    private List<String> ids;
}