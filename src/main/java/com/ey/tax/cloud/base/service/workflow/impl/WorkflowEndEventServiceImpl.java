package com.ey.tax.cloud.base.service.workflow.impl;

import com.ey.tax.cloud.base.constants.WorkflowConstants;
import com.ey.tax.cloud.base.entity.workflow.*;
import com.ey.tax.cloud.base.service.workflow.WorkflowAbstractService;
import com.ey.tax.cloud.base.service.workflow.WorkflowEventService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service("WorkflowEndEventService")
public class WorkflowEndEventServiceImpl extends WorkflowAbstractService<WorkflowEndEvent> implements WorkflowEventService<WorkflowEndEvent> {

    @Override
    public List<WorkflowEndEvent> create(Item item, Instance instance, Map<String, Object> parameters) {
        List<WorkflowEndEvent> workflowEndEventList = new ArrayList<WorkflowEndEvent>();
        workflowEndEventList.add(this.build(item, instance, null));
        return workflowEndEventList;
    }

    @Override
    public WorkflowEndEvent build(Item item, Instance instance, Approve approve) {
        WorkflowEndEvent workflowEndEvent = new WorkflowEndEvent();
        workflowEndEvent.setWorkflowInstanceId(instance.getId());
        return workflowEndEvent;
    }

    @Override
    public ExecuteResult execute(Item item, Instance instance, WorkflowEndEvent workflowEndEvent, Approve approve) {
        ExecuteResult executeResult = new ExecuteResult();
        executeResult.setExecuteStatus(WorkflowConstants.NEXT_NODE_END);
        return executeResult;
    }

}
