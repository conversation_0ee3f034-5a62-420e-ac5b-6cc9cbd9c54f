package com.ey.tax.cloud.base.dto.app;

import com.ey.cn.tax.framework.validator.BatchUpdateGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-07-04 14:57:19
 * 
 */
@Data
@Schema(description = "根据id批量更新实体")
public class AppBatchUpdateDTO {
    
    /**
     * 数据id
     */
    @Schema(description = "数据id")
    @NotBlank(message = "{AppDTO.id.NotBlank}", groups = {BatchUpdateGroup.class})
    private String id;

    /**
     * 系统编码 COLUMN:sys_code
     */
    @Schema(description = "系统编码", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String sysCode;

    /**
     * 应用编码 COLUMN:app_code
     */
    @Schema(description = "应用编码", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String appCode;

    /**
     * 应用名称 COLUMN:app_name
     */
    @Schema(description = "应用名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String appName;
}