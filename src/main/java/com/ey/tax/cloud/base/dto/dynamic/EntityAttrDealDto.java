package com.ey.tax.cloud.base.dto.dynamic;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Auther: Dylan DY Li
 * @Date: 2024/3/18 3:08 PM
 * @description:
 */

@Data
@Schema(name = "VariableAttrDealDto", description = "动态页面规则处理")
public class EntityAttrDealDto {
    /**
     * 表字段
     */
    private String field;
    /**
     * 写入前处理
     */
    private String writeDataProcess;
    /**
     * 读取后处理
     */
    private String readDataProcess;

    /**
     * 自定义规则处理(Groovy脚本)
     */
    private String variableAttrDealScript;
}
