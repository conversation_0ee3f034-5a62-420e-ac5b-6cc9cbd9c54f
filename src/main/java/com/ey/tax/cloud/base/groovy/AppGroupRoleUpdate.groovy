package com.ey.tax.cloud.base.groovy

import com.ey.cn.tax.framework.context.EyUserContextHolder
import com.ey.cn.tax.framework.dto.ResponseDTO
import com.ey.cn.tax.framework.execution.IExecutionContext
import com.ey.cn.tax.framework.mybatisplus.core.toolkit.EyWrappers
import com.ey.cn.tax.lite.execution.dataset.IDatasetGroovyRunnable
import com.ey.tax.cloud.app.entity.group.AppGroup
import com.ey.tax.cloud.app.entity.groupRole.AppGroupRole
import com.ey.tax.cloud.app.mapper.group.AppGroupMapper
import com.ey.tax.cloud.app.mapper.groupRole.AppGroupRoleMapper
import org.apache.commons.collections4.CollectionUtils
import org.apache.commons.lang3.StringUtils

import java.util.stream.Collectors

class AppGroupRoleUpdate implements IDatasetGroovyRunnable {

    AppGroupMapper appGroupMapper;
    AppGroupRoleMapper appGroupRoleMapper;

    @Override
    public Object run(Object args, Map<String, Object> dependentDatasets, IExecutionContext executionContext) {

        appGroupRoleMapper = executionContext.getBean(AppGroupRoleMapper.class).get();
        appGroupMapper = executionContext.getBean(AppGroupMapper.class).get();

        Map<String,Object> arg = (Map<String,Object>)args;

        String sysCode = EyUserContextHolder.get().getSyscode();
        String appCode = EyUserContextHolder.get().getAppcode();

        String groupId = arg.get("id");
        String groupName = arg.get("groupName");
        List<String> roleIds = arg.get("roleIds");
        if(StringUtils.isNotBlank(groupName) && StringUtils.isNotBlank(groupName)){
            AppGroup group = new AppGroup();
            group.setId(groupId);
            group.setGroupName(groupName);
            group.setGroupCode(arg.get("groupCode"))
            if(CollectionUtils.isNotEmpty(roleIds)){
                List<AppGroupRole> grs =  roleIds.stream().map (e->{
                    AppGroupRole gr = new AppGroupRole();
                    gr.setSysCode(sysCode);
                    gr.setAppCode(appCode);
                    gr.setGroupId(groupId);
                    gr.setRoleId(e);
                    return gr;
                }).collect(Collectors.toList());
                appGroupRoleMapper.delete(EyWrappers.query(AppGroupRole.class)
                        .eq("group_id",groupId));
                appGroupRoleMapper.insertBatch(grs);
            }
            appGroupMapper.updateById(group);

        }
        return ResponseDTO.success();
    }
}

