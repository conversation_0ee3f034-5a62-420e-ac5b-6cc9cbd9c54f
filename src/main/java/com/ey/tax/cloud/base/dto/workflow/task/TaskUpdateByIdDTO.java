package com.ey.tax.cloud.base.dto.workflow.task;

import com.ey.cn.tax.framework.validator.UpdateByIdGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2025-03-10 15:28:32
 * 
 */
@Data
@Schema(description = "根据id批量更新实体")
public class TaskUpdateByIdDTO {
    
    /**
     * 数据id
     */
    @Schema(description = "数据id")
    @NotBlank(message = "{TaskDTO.id.NotBlank}", groups = {UpdateByIdGroup.class})
    private String id;

    /**
     * 流程实例id COLUMN:flow_instance_id
     */
    @Schema(description = "流程实例id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String flowInstanceId;

    /**
     * 流程模型id COLUMN:flow_model_id
     */
    @Schema(description = "流程模型id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String flowModelId;

    /**
     * 流程定义id COLUMN:flow_define_id
     */
    @Schema(description = "流程定义id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String flowDefineId;

    /**
     * 处理类型 COLUMN:operate_type
     */
    @Schema(description = "处理类型", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String operateType;

    /**
     * 分配人 COLUMN:task_assignor
     */
    @Schema(description = "分配人", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String taskAssignor;

    /**
     * 处理人 COLUMN:task_transactor
     */
    @Schema(description = "处理人", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String taskTransactor;

    /**
     * 任务类型 COLUMN:task_type
     */
    @Schema(description = "任务类型", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String taskType;

    /**
     * 任务状态 COLUMN:task_status
     */
    @Schema(description = "任务状态", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String taskStatus;

    /**
     * 任务序列 COLUMN:flow_step
     */
    @Schema(description = "任务序列", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer flowStep;

    /**
     * 备注 COLUMN:task_comment
     */
    @Schema(description = "备注", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String taskComment;

    /**
     * 流程itemId COLUMN:item_id
     */
    @Schema(description = "流程itemId", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String itemId;

    /**
     * item名称 COLUMN:item_name
     */
    @Schema(description = "item名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String itemName;
}