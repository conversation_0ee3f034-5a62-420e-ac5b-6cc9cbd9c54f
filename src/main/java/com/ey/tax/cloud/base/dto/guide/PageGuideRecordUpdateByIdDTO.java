package com.ey.tax.cloud.base.dto.guide;

import com.ey.cn.tax.framework.validator.UpdateByIdGroup;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2023-12-08 17:18:28
 * 
 */
@Data
@Schema(description = "根据id批量更新实体")
public class PageGuideRecordUpdateByIdDTO {
    
    /**
     * 数据id
     */
    @Schema(description = "数据id")
    @NotBlank(message = "{PageGuideRecordDTO.id.NotBlank}", groups = {UpdateByIdGroup.class})
    private String id;

    /**
     * 已完成导引页面。其json中的key为已完成导引页面的唯一标识，value可扩展其他相关信息 COLUMN:finish_pages
     */
    @Schema(description = "已完成导引页面。其json中的key为已完成导引页面的唯一标识，value可扩展其他相关信息", requiredMode = Schema.RequiredMode.REQUIRED)
    private ObjectNode finishPages;
}