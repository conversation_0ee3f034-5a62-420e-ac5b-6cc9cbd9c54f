package com.ey.tax.cloud.base.dto.database;

import com.ey.cn.tax.framework.validator.DeleteByIdListGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-08-13 15:02:16
 * 
 */
@Data
@Schema(description = "根据id列表删除实体")
public class TenantSchemaDeleteByIdListDTO {
    
    /**
     * 数据id列表
     */
    @Schema(description = "数据id列表")
    @NotNull(message = "{TenantSchemaDTO.ids.NotNull}", groups = {DeleteByIdListGroup.class})
    @Size(min = 1, message = "{TenantSchemaDTO.ids.Size}", groups = {DeleteByIdListGroup.class})
    private List<String> ids;
}