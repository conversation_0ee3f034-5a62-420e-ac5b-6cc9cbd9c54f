package com.ey.tax.cloud.base.dto.dictionary;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @ProjectName: ey-tax-cloud-base
 * @ClassName: DictionaryFeignReqDTO
 * @Description: Feign接口请求
 * @Author: Linna
 * @Date: 2024/9/14 17:54
 */
@Data
public class DictionaryFeignReqDTO {

    private static final long serialVersionUID = 1L;
    @Schema(
            description = "dictCodes"
    )
    private List<String> dictCodes;

    @Schema(
            description = "P1"
    )
    private String property1 ;

    @Schema(
            description = "P2"
    )
    private String property2 ;

    @Schema(
            description = "P3"
    )
    private String property3 ;
}
