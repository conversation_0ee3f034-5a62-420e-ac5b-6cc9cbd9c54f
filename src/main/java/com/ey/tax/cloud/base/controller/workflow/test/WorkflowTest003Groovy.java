package com.ey.tax.cloud.base.controller.workflow.test;

import com.ey.cn.tax.framework.execution.IExecutionContext;
import com.ey.cn.tax.lite.execution.dataset.IDatasetGroovyRunnable;

import java.util.Map;

class WorkflowTest003Groovy implements IDatasetGroovyRunnable {

    @Override
    public Object run(Object args, Map<String, Object> dependentDatasets, IExecutionContext executionContext) {
        System.out.println("************************************************");
        try {
            Thread.sleep(5000);
        }catch (InterruptedException e){
            e.printStackTrace();
        }
        System.out.println("************************************************");
        return null;
    }
}

