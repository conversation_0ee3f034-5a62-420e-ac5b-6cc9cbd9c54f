package com.ey.tax.cloud.base.dto.workflow.item;

import com.ey.cn.tax.framework.validator.UpdateByIdGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2025-03-10 15:28:31
 * 
 */
@Data
@Schema(description = "根据id批量更新实体")
public class ItemUpdateByIdDTO {
    
    /**
     * 数据id
     */
    @Schema(description = "数据id")
    @NotBlank(message = "{ItemDTO.id.NotBlank}", groups = {UpdateByIdGroup.class})
    private String id;

    /**
     * 流程定义id COLUMN:flow_define_id
     */
    @Schema(description = "流程定义id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String flowDefineId;

    /**
     * 流程模型id COLUMN:flow_model_id
     */
    @Schema(description = "流程模型id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String flowModelId;

    /**
     * 来源引用 COLUMN:source_ref
     */
    @Schema(description = "来源引用", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String sourceRef;

    /**
     * 目标引用 COLUMN:target_ref
     */
    @Schema(description = "目标引用", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String targetRef;

    /**
     * item类型 COLUMN:item_type
     */
    @Schema(description = "item类型", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String itemType;

    /**
     * 节点属性 COLUMN:item_attribute
     */
    @Schema(description = "节点属性", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Object itemAttribute;

    /**
     * 组件id COLUMN:item_id
     */
    @Schema(description = "组件id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String itemId;

    /**
     * 子类型 COLUMN:sub_type
     */
    @Schema(description = "子类型", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String subType;

    /**
     * 元素名称 COLUMN:item_name
     */
    @Schema(description = "元素名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String itemName;
}