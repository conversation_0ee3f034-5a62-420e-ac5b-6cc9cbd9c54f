package com.ey.tax.cloud.base.dto.system;

import com.ey.cn.tax.framework.dto.BaseRepDTO;
import com.ey.tax.cloud.base.constants.LanguageTagConstans;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Set;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-07-04 11:08:10
 * 
 */
@Data
@Schema(description = "查询返回实体")
public class SystemRepDTO extends BaseRepDTO {
    /**
     * 系统编码，全局唯一 COLUMN:sys_code
     */
    @Schema(description = "系统编码，全局唯一")
    private String sysCode;

    /**
     * 系统名称 COLUMN:sys_name
     */
    @Schema(description = "系统名称")
    private String sysName;

    /**
     * 状态 启用 enable 禁用 disabled COLUMN:sys_state
     */
    @Schema(description = "状态 启用 enable 禁用 disabled")
    private String sysState;

    /**
     * 系统来源，平台内部 inside 平台外部 external COLUMN:sys_source
     */
    @Schema(description = "系统来源，平台内部 inside 平台外部 external")
    private String sysSource;

    /**
     * 显示顺序，1 2 3 COLUMN:sort_index
     */
    @Schema(description = "显示顺序，1 2 3")
    private Integer sortIndex;

    /**
     * 路由 COLUMN:route
     */
    @Schema(description = "路由")
    private String route;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    @Schema(description = "父系统编码")
    private String parentCode;

    @Schema(description = "图标")
    private String icon;

    @Schema(description = "默认语言，zh-CN:中文，en-US:英文", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String langCode;

    @Schema(description = "默认语言，zh-CN:中文，en-US:英文", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String langName;
    private Set<String> usableSchemas;

    private String isCheckRole;
    public String getLangName() {
        if(StringUtils.isBlank(langCode))
            return null;
        switch (langCode){
            case LanguageTagConstans.ZH_CN ->  langName = "简体中文";
            case LanguageTagConstans.EN_US ->  langName = "English";
        }
        return langName;
    }
}
