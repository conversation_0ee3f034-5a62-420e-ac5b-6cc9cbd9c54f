package com.ey.tax.cloud.base.dto.globalParams;

import com.ey.cn.tax.framework.validator.DeleteByIdListGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2024-07-03 16:08:45
 * 
 */
@Data
@Schema(description = "根据id列表删除实体")
public class GlobalParamsDeleteByIdListDTO {
    
    /**
     * 数据id列表
     */
    @Schema(description = "数据id列表")
    @NotNull(message = "{GlobalParamsDTO.ids.NotNull}", groups = {DeleteByIdListGroup.class})
    @Size(min = 1, message = "{GlobalParamsDTO.ids.Size}", groups = {DeleteByIdListGroup.class})
    private List<String> ids;
}
