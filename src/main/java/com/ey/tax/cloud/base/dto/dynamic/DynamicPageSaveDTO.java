package com.ey.tax.cloud.base.dto.dynamic;

import com.ey.cn.tax.framework.utils.JacksonUtils;
import com.ey.cn.tax.framework.utils.StringCompressUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 动态页面管理实体类
 * </p>
 *
 * @author: <PERSON>
 * @date: 18:47 2022/1/29
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(name = "SysDynamicPageSaveParam", description = "动态页面管理实体类")
public class DynamicPageSaveDTO implements Serializable {
    private String id;
    /**
     * 子应用
     */
    private String subAppCode;
    /**
     * 页面名称
     */
    @NotBlank(message = "页面名称不能为空")
    private String dynamicPageName;
    /**
     * 页面配置，压缩后的json
     */
    @NotBlank(message = "页面配置不能为空")
    private String dynamicPageConfig;
    /**
     * 页面编码，用户自定义
     */
    @NotBlank(message = "页面编码不能为空")
    private String dynamicPageCode;
    /**
     * 是否已生成路由（NO-未生成；YES-已生成）
     */
    private String configRoute = "NO";


    /**
     * 多语言信息
     */
    private SysLangContentDTO langContent;

    /**
     * 动态页面下拉列表集合
     */
    private List<String> dropCodeList;


    /**
     * 动态页面按钮元素集合
     */
    private List<SysMenuItemDTO> menuContent;

    /**
     * 动态页面搜索集合
     */
    private Map<String, Map<String, String>> searchContent;

    /**
     * 动态页面初始化api
     */
    private List<String> loadApis;

    /**
     * 系统编码 sys_code
     */
    @Schema(description = "系统编码", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String sysCode;

    /**
     * 应用编码 app_code
     */
    @Schema(description = "应用编码", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String appCode;

    @Schema(description = "模板状态")
    private Integer isTemplate;
    /**
     * 页面code
     */
    public String getPageLanguageCode() {
        if (langContent == null) {
            return "";
        }
        return langContent.getPageLanguageCode();
    }

    /**
     * 页面titlecode
     */
    public String getPageLanguageTitleCode() {
        if (langContent == null) {
            return "";
        }
        return langContent.getPageLanguageTitleCode();
    }

    /**
     * 页面名称
     */
    public String getPageLanguageDesc() {
        if (langContent == null) {
            return "";
        }
        return langContent.getPageLanguageDesc();
    }

    public String getLangContentStr() {
        if (langContent == null) {
            return "";
        }
        return JacksonUtils.toJson(langContent);
    }

    public String getRelationOptions() {
        if (CollectionUtils.isNotEmpty(dropCodeList)) {
            return JacksonUtils.toJson(dropCodeList);
        }
        return "";
    }

    public String getMenuContentStr() {
        if (CollectionUtils.isNotEmpty(menuContent)) {
            return JacksonUtils.toJson(menuContent);
        }
        return "";
    }

    public String getRelationSearchAttrLogic() {
        if (searchContent != null) {
            return JacksonUtils.toJson(searchContent);
        }
        return "";
    }

    public String getLoadApiContent() {
        if (CollectionUtils.isNotEmpty(loadApis)) {
            return JacksonUtils.toJson(loadApis);
        }
        return "";
    }

    public String getCompressedDynamicPageConfig() {
        return StringCompressUtil.compress(dynamicPageConfig);
    }

    public String getPageStatus() {
        if (StringUtils.isBlank(id)) {
            return "draft";
        }
        return null;
    }

    public String getPageOriginalName() {
        if (StringUtils.isBlank(id)) {
            return dynamicPageName;
        }
        return null;
    }
}
