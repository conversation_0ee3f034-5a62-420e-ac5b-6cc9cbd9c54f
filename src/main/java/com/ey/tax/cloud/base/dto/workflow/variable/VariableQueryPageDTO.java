package com.ey.tax.cloud.base.dto.workflow.variable;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2025-03-10 15:28:33
 * 
 */
@Data
@Schema(description = "根据条件分页查询实体")
public class VariableQueryPageDTO {
    /**
     * 变量类型 COLUMN:variable_type
     */
    @Schema(description = "变量类型", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String variableType;

    /**
     * 变量值 COLUMN:variable_value
     */
    @Schema(description = "变量值", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Object variableValue;

    /**
     * 任务id COLUMN:flow_task_id
     */
    @Schema(description = "任务id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String flowTaskId;

    /**
     * 实例id COLUMN:flow_instance_id
     */
    @Schema(description = "实例id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String flowInstanceId;
}