package com.ey.tax.cloud.base.service.workflow.impl;

import com.ey.cn.tax.framework.utils.EmptyUtils;
import com.ey.tax.cloud.base.entity.workflow.*;
import com.ey.tax.cloud.base.service.workflow.WorkflowAbstractGatewayService;
import com.ey.tax.cloud.base.service.workflow.WorkflowGatewayService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("WorkflowExclusiveGatewayService")
public class WorkflowExclusiveGatewayServiceImpl extends WorkflowAbstractGatewayService<WorkflowExclusiveGateway> implements WorkflowGatewayService<WorkflowExclusiveGateway> {

    @Override
    public WorkflowExclusiveGateway getInstance() {
        return new WorkflowExclusiveGateway();
    }

    @Override
    public String checkNextList(List<Item> nextItemList) {
        if(EmptyUtils.isEmpty(nextItemList)){
            return "next item list size: 0";
        }else if(nextItemList.size() > 1){
            return "next item list size: " + nextItemList.size();
        }
        return null;
    }

}
