package com.ey.tax.cloud.base.service.guide;

import com.ey.cn.tax.framework.service.BaseService;
import com.ey.tax.cloud.base.dto.guide.PageGuideRecordUpdateAndAddDTO;
import com.ey.tax.cloud.base.entity.guide.PageGuideRecord;

/**
 * @Description auto create by ey-tax-core-tool-V3.0 for Ti
 * 
 * <AUTHOR>
 * @date 2023-12-08 17:18:28
 * 
 */
public interface PageGuideRecordService extends BaseService<PageGuideRecord> {

    void updateAndAdd(PageGuideRecord pageGuideRecord);
}