E0000020001=Data does not exist
E0000020002=File upload failed
SYSTEM_EXIST_ERROR=System already exists
APP_EXIST_ERROR=The application already exists
GLOBAL_PARAM_EXIST_ERROR=The global parameter already exists
E0000020003=The page code must start with a capital English letter and can only contain numbers and English letters with a length between 2-40
E0000020004=Code already exists!
E0000020006=The code must start with an English letter and can only contain numbers and English letters with a length between 2-30
PARAM_CODE_EXIST=Code already exists!
PARAM_NAME_EXIST=The dataset name already exists!
DYNAMIC_PAGE_ENTITY_NOT_EXIST_ERROR=The configuration information does not exist. Please refresh the page and try again!
UPDATE_TYPE_EMPTY_ERROR=The update type cannot be empty!
LITE_VARIABLE_SQL_END_SYMBOL_ERROR=SQL statements should not end with [;]!
SQL_CONFIG_CHECK_ERROR=SQL verification failed
LITE_VARIABLE_SQL_CONFIG_EMPTY_ERROR=SQL configuration cannot be empty!
LITE_VARIABLE_CHECK_RULE_CONVERT_ERROR=Validation rule parameter conversion failed!
LITE_VARIABLE_FIELD_IS_NULL_ERROR=Field cannot be empty!
LITE_VARIABLE_CHECK_RULE_FIELD_UNIQUE_ERROR=Validation rule field parameter duplication exception
LITE_VARIABLE_DEAL_RULE_CONVERT_ERROR=Processing rule parameter conversion failed!
LITE_VARIABLE_DEAL_RULE_FIELD_UNIQUE_ERROR=Handling rule parameter fields with duplicate exceptions
E0000020007=Dynamic page control configuration error in global parameters
HEADER_SYSCODE_IS_NULL = The request header Syscode is empty
HEADER_APPCODE_IS_NULL = Request header Appcode is empty
APPTENANTUSERSERVICE_NO_IMPLEMENTS = AppTenantUserService has no implementation class
APP_USR_DISABLED = User is disabled
APP_CLONE_FEIGN_GLOBAL_PARA_CODE_NOT_CONFIG=AppCloneFeignURL is not configured
APP_MODULE_CLONEDATASET_NOT_EXIST = Application module :{0}, data set encoding :{1} Clone script does not exist
AUTH_TIME_OUT=Not within the authorized time
HAVE_ERROR_LEVEL=There are errors in the job levels: {0}
APP_ROLE_CODE_HAVE_EXIST=The role code already exists.