spring:
  main:
    allow-bean-definition-overriding: true
  application:
    name: cloud-target-server
  cloud:
    openfeign:
      client:
        config:
          default:
            request-interceptors:
              - com.ey.tax.cloud.autoconfig.EyFeignCallTypeDefaultFeignConfig
              - com.ey.cn.tax.framework.feign.EyFeignInnerHeadersRewriteInterceptor
    kubernetes:
      discovery:
        namespaces: tax-dev
      client:
        namespace: tax-dev
  messages:
    basename: error/targetErrorMessages,messages/error/error_messages,messages/error/comm_error_messages,validation/targetValidationMessages,messages/validation/validation_messages
  datasource:
    dynamic:
      primary: target #设置默认的数据源或者数据源组,默认值即为master
      strict: true #严格匹配数据源,默认false. true未匹配到指定数据源时抛异常,false使用默认数据源
      datasource:
        target:
          driver-class-name: com.p6spy.engine.spy.P6SpyDriver
          url: *******************************************************************************************************
          username: tax_cloud_target_user
          password: target*u2n1b1eW2
          hikari:
            # target库特定的Hikari配置
            maximum-pool-size: 100      # 解决连接池耗尽问题
            minimum-idle: 5            # 最小空闲连接数
            connection-timeout: 30000  # 连接超时(毫秒)
            idle-timeout: 600000       # 空闲连接超时(毫秒)
            max-lifetime: 1800000      # 连接最大生命周期(毫秒)
            validation-timeout: 5000   # 验证超时(毫秒)
            connection-test-query: SELECT 1  # 连接测试查询
            leak-detection-threshold: 30000  # 连接泄漏检测阈值
        tiger:
          driver-class-name: com.p6spy.engine.spy.P6SpyDriver
          url: *******************************************************************************************************
          username: tax_cloud_target_user
          password: target*u2n1b1eW2
  data:
    redis:
      host: r-uf6ikt9mj3xcsoymhc.redis.rds.aliyuncs.com
      password: Redis256^&*
      port: 6379
      database: 2

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
  global-config:
    db-config:
      capital-mode: true
      id-type: ASSIGN_ID
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
      logic-delete-field: "IS_DEL" #全局逻辑删除字段值 3.3.0开始支持
      table-prefix: sa_
    super-mapper-class: com.ey.cn.tax.framework.mybatisplus.core.mapper.IEyBaseMapper
    banner: false #关闭banner
  type-aliases-package: com.ey.com.tax.**.entity
  mapper-locations: classpath*:mapper/*Mapper.xml

server:
  port: 8082
  servlet:
    context-path: /

feign:
  ucc:
    name: ucc
    url: http://dev-ucc.ey.com.cn
  admin:
    name: admin
    url: localhost:8080
  tenant:
    name: tenant
    url: localhost:8081
  sys:
    name: sys
    url: localhost:8085
  base:
    name: base
    url: localhost:8083
  file:
    name: file
    url: localhost:8084
  mail:
    name: mail
    url: localhost:8084

mybatis:
  configuration:
    log-impl: org.apache.ibatis.logging.log4j2.Log4j2Impl
    #log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

ey:
  log:
    path: tax-cloud-target
  app:
    systemId: target
    i18n:
      default-language: zh
    error:
      prefix: target-
  web:
    default-call-type: dynamic

target:
  kip-config-capture: target_kip_config_capture
  kip-monitor: false
  mail-type: test

tigeroar:
  url: https://bcp-openapi-preuat.ey.com.cn
  appId: ETIP
  clientId: 3128369f6e45450bbd00c8b452509ca7
  appSecret: 45a1a3100acc4eb284f8b2d1b9c980fe
  tokenPath: /rpc-api/system/oauth2/token/client/create
  categoryPath: /rpc-api/system/v2/category-data/getCategoryData
  dictPath: /rpc-api/system/dict_data/get

# Xxl-Job分布式定时任务调度中心
xxl:
  job:
    admin:
      # 调度中心部署跟地址 [选填]：如调度中心集群部署存在多个地址则用逗号分隔。
      addresses: http://localhost:8083/
      # addresses: http://*************:9090/xxl-job-admin
    # 执行器通讯TOKEN [选填]：非空时启用 系统默认 default_token
    accessToken: default_token
    executor:
      # 执行器的应用名称
      appname: ${spring.application.name}-executor
      # 执行器注册 [选填]：优先使用该配置作为注册地址
      # address: ""
      # 执行器IP [选填]：默认为空表示自动获取IP
      # ip: ""
      # 执行器端口号 [选填]：小于等于0则自动获取；默认端口为9999
      port: 8099
      # 执行器运行日志文件存储磁盘路径 [选填] ：需要对该路径拥有读写权限；为空则使用默认路径；
      logpath: ./logs/jobhandler
      #logpath: /data/logs/mls/job
      # 执行器日志文件保存天数 [选填] ： 过期日志自动清理, 限制值大于等于3时生效; 否则, 如-1, 关闭自动清理功能；
      logretentiondays: 7
      register: false
jetcache:
  statIntervalMinutes: 15
  areaInCacheName: false
  local:
    default:
      type: caffeine #other choose：caffeine
      keyConvertor: jackson #other choose：fastjson/jackson
      limit: 100
  remote:
    default:
      type: redis
      keyConvertor: jackson #other choose：fastjson/jackson
      broadcastChannel: cacheNotify
      valueEncoder: java #other choose：kryo/kryo5
      valueDecoder: java #other choose：kryo/kryo5
      poolConfig:
        minIdle: 5
        maxIdle: 20
        maxTotal: 50
      host: ${spring.data.redis.host}
      port: ${spring.data.redis.port}
      password: ${spring.data.redis.password}
      database: ${spring.data.redis.database}

dataphin:
  #url: https://7efeed4ac5b94e42b40fd1ab3f5a6715-cn-shanghai-vpc.alicloudapi.com
  host: https://7efeed4ac5b94e42b40fd1ab3f5a6715-cn-shanghai-vpc.alicloudapi.com
  url: https://ti.ey.com.cn/etip-dev/targetapi/v1/dataphin/getBusiness.do
  stage: PRE
  env: PRE
  appKey: 204191153
  appSecret: dJfXKZFZoZb0tSjvl8fImM0m5nvMckCl
  appId: 12043
