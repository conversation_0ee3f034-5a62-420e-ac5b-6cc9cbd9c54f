<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ey.tax.cloud.base.mapper.resource.ManRoleResMapper" >


    <select id="selectRoleButtonResource" resultType="com.ey.tax.cloud.base.dto.dynamic.RoleResourceDTO">
        select
            'man' as sys_code,
            'none' as app_code,
            br2.resource_name ,
            br2.id as resource_id,
            br.role_name ,
            br.id as role_id
        from
            base_man_role_res brr
                inner join base_man_role br on
                brr.role_id = br.id
                inner join base_man_resource br2 on
                brr.resource_id = br2.id
        where
            brr.is_del = 0
          and br.is_del = 0
          and br2.is_del = 0
          and br2.resource_type = 2


    </select>

</mapper>
