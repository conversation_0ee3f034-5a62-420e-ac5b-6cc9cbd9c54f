<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ey.tax.cloud.base.mapper.dictionary.DictionaryMapper" >
  
  <resultMap id="EyTaxResultMap" type="com.ey.tax.cloud.base.entity.dictionary.Dictionary" >
    <id column="id" property="id" jdbcType="VARCHAR" />
    <result column="create_uid" property="createUid" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_uid" property="updateUid" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="is_del" property="isDel" jdbcType="SMALLINT" />
    <result column="auto_test" property="autoTest" jdbcType="SMALLINT" />
    <result column="dict_code" property="dictCode" jdbcType="VARCHAR" />
    <result column="dict_name" property="dictName" jdbcType="VARCHAR" />
    <result column="dict_cn_name" property="dictCnName" jdbcType="VARCHAR" />
    <result column="dict_en_name" property="dictEnName" jdbcType="VARCHAR" />
    <result column="is_enable" property="isEnable" jdbcType="SMALLINT" />
    <result column="property1" property="property1" jdbcType="VARCHAR" />
    <result column="property2" property="property2" jdbcType="VARCHAR" />
    <result column="property3" property="property3" jdbcType="VARCHAR" />
    <result column="sort_index" property="sortIndex" jdbcType="INTEGER" />
    <result column="parent_code" property="parentCode" jdbcType="VARCHAR" />
  </resultMap>

  <select id="selectByDictCode" resultType="com.ey.tax.cloud.base.entity.dictionary.Dictionary">
    with recursive _temp as (
      select * from base_dictionary
                where is_del = 0 and is_enable = 1 and dict_code = #{dictCode} and sys_code = #{sysCode}
                and app_code in
                <foreach collection="appCodes" index="index" item="item" open="(" separator="," close=")">
                  #{item}
                </foreach>
      union all
      select bd.* from base_dictionary bd join _temp on bd.parent_code = _temp.dict_code and bd.sys_code = #{sysCode} and bd.is_del = 0 and bd.is_enable = 1
                and bd.app_code in
                <foreach collection="appCodes" index="index" item="item" open="(" separator="," close=")">
                  #{item}
                </foreach>
    )
    select * from _temp order by parent_code, sort_index;
  </select>


  <update id="deleteAllById">
    WITH RECURSIVE to_delete AS (
      SELECT id, dict_code, sys_code, app_code
      FROM tax_cloud_base.base_dictionary
      WHERE id = #{id}
      UNION ALL
      SELECT d.id, d.dict_code, d.sys_code, d.app_code
      FROM tax_cloud_base.base_dictionary d
             INNER JOIN to_delete td ON d.parent_code = td.dict_code
    )
    UPDATE tax_cloud_base.base_dictionary bd
    SET is_del = '1'
    WHERE bd.id IN (SELECT id FROM to_delete)
      AND bd.sys_code = (SELECT sys_code FROM to_delete LIMIT 1)
      AND bd.app_code = (SELECT app_code FROM to_delete LIMIT 1)
  </update>
</mapper>