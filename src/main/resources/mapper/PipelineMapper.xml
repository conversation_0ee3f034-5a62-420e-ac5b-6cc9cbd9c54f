<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ey.tax.cloud.targets.mapper.pipeline.PipelineMapper">

    <resultMap id="EyTaxResultMap" type="com.ey.tax.cloud.targets.entity.pipeline.Pipeline">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="create_uid" property="createUid" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_uid" property="updateUid" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="is_del" property="isDel" jdbcType="SMALLINT"/>
        <result column="auto_test" property="autoTest" jdbcType="SMALLINT"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="english_name" property="englishName" jdbcType="VARCHAR"/>
        <result column="client_id" property="clientId" jdbcType="VARCHAR"/>
        <result column="industry_sector" property="industrySector" jdbcType="VARCHAR"/>
        <result column="industry_sector" property="industrySubSector" jdbcType="VARCHAR"/>
        <result column="company_type" property="companyType" typeHandler="com.ey.tax.cloud.targets.config.JsonTypeHandler"/>
        <result column="stock_exchange" property="stockExchange" typeHandler="com.ey.tax.cloud.targets.config.JsonTypeHandler"/>
        <result column="is_sec" property="isSec" jdbcType="INTEGER"/>
        <result column="head_office_location" property="headOfficeLocation" jdbcType="VARCHAR"/>
        <result column="client_channel" property="clientChannel" jdbcType="VARCHAR"/>
        <result column="customer_classification" property="customerClassification" jdbcType="VARCHAR"/>
        <result column="pipeline_code" property="pipelineCode" jdbcType="VARCHAR"/>
        <result column="pipeline_name" property="pipelineName" jdbcType="VARCHAR"/>
        <result column="local_service_code" property="localServiceCode" jdbcType="VARCHAR"/>
        <result column="product_code" property="productCode" jdbcType="VARCHAR"/>
        <result column="product_name" property="productName" jdbcType="VARCHAR"/>
        <result column="estimated_win_odds" property="estimatedWinOdds" jdbcType="INTEGER"/>
        <result column="anticipated_win_date" property="anticipatedWinDate" jdbcType="DATE"/>
        <result column="is_competitive_bid" property="isCompetitiveBid" jdbcType="INTEGER"/>
        <result column="reason" property="reason" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="is_eic_self" property="isEicSelf" jdbcType="INTEGER"/>
        <result column="efforts_reason" property="effortsReason" jdbcType="VARCHAR"/>
        <result column="outcome" property="outcome" jdbcType="VARCHAR"/>
        <result column="psm_prime_office" property="psmPrimeOffice" jdbcType="VARCHAR"/>
        <result column="risk_conclusion" property="riskConclusion" jdbcType="VARCHAR"/>
        <result column="pursuit_leader_gpn" property="pursuitLeaderGpn" jdbcType="VARCHAR"/>
        <result column="pursuit_leader_id" property="pursuitLeaderId" jdbcType="VARCHAR"/>
        <result column="oppr_partner_gpn" property="opprPartnerGpn" jdbcType="VARCHAR"/>
        <result column="oppr_partner_id" property="opprPartnerId" jdbcType="VARCHAR"/>
        <result column="team_anticipated_manager_gpn" property="teamAnticipatedManagerGpn" jdbcType="VARCHAR"/>
        <result column="team_anticipated_manager_id" property="teamAnticipatedManagerId" jdbcType="VARCHAR"/>
        <result column="amount" property="amount" jdbcType="NUMERIC"/>
        <result column="amount_usd" property="amountUsd" jdbcType="NUMERIC"/>
        <result column="currency" property="currency" jdbcType="VARCHAR"/>
        <result column="is_inclusive_vat" property="isInclusiveVat" jdbcType="INTEGER"/>
        <result column="pace_status" property="paceStatus" jdbcType="INTEGER"/>
        <result column="location_code" property="locationCode" jdbcType="VARCHAR"/>
        <result column="event_code" property="eventCode" jdbcType="VARCHAR"/>
        <result column="is_subcode" property="isSubcode" jdbcType="VARCHAR"/>
        <result column="contract" property="contract" jdbcType="VARCHAR"/>
        <result column="closed" property="closed" jdbcType="INTEGER"/>
        <result column="contractor_used" property="contractorUsed" jdbcType="VARCHAR"/>
        <result column="outsource" property="outsource" jdbcType="NUMERIC"/>
        <result column="confirm_status" property="confirmStatus" jdbcType="VARCHAR"/>
       <result column="test_json" property="testJson" jdbcType="VARCHAR"/>
        <result column="pursuit_fy" property="pursuitFy" jdbcType="VARCHAR"/>
        <result column="won_fy" property="wonFy" jdbcType="VARCHAR"/>
        <result column="efforts_approve" property="effortsApprove" jdbcType="VARCHAR"/>
        <result column="won_time" property="wonTime" jdbcType="TIMESTAMP"/>
        <result column="is_same" property="isSame" jdbcType="INTEGER"/>
        <result column="is_blank" property="isBlank" jdbcType="INTEGER"/>
        <result column="account_id" property="accountId" jdbcType="VARCHAR"/>
    </resultMap>
    <resultMap id="EyTaxResultMap3" type="com.ey.tax.cloud.targets.entity.pipeline.PipelineForMemo">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="create_uid" property="createUid" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_uid" property="updateUid" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="is_del" property="isDel" jdbcType="SMALLINT"/>
        <result column="auto_test" property="autoTest" jdbcType="SMALLINT"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="english_name" property="englishName" jdbcType="VARCHAR"/>
        <result column="client_id" property="clientId" jdbcType="VARCHAR"/>
        <result column="industry_sector" property="industrySector" jdbcType="VARCHAR"/>
        <result column="industry_sector" property="industrySubSector" jdbcType="VARCHAR"/>
        <result column="company_type" property="companyType" typeHandler="com.ey.tax.cloud.targets.config.JsonTypeHandler"/>
        <result column="stock_exchange" property="stockExchange" typeHandler="com.ey.tax.cloud.targets.config.JsonTypeHandler"/>
        <result column="is_sec" property="isSec" jdbcType="INTEGER"/>
        <result column="head_office_location" property="headOfficeLocation" jdbcType="VARCHAR"/>
        <result column="client_channel" property="clientChannel" jdbcType="VARCHAR"/>
        <result column="customer_classification" property="customerClassification" jdbcType="VARCHAR"/>
        <result column="pipeline_code" property="pipelineCode" jdbcType="VARCHAR"/>
        <result column="pipeline_name" property="pipelineName" jdbcType="VARCHAR"/>
        <result column="local_service_code" property="localServiceCode" jdbcType="VARCHAR"/>
        <result column="product_code" property="productCode" jdbcType="VARCHAR"/>
        <result column="product_name" property="productName" jdbcType="VARCHAR"/>
        <result column="estimated_win_odds" property="estimatedWinOdds" jdbcType="INTEGER"/>
        <result column="anticipated_win_date" property="anticipatedWinDate" jdbcType="DATE"/>
        <result column="is_competitive_bid" property="isCompetitiveBid" jdbcType="INTEGER"/>
        <result column="reason" property="reason" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="is_eic_self" property="isEicSelf" jdbcType="INTEGER"/>
        <result column="efforts_reason" property="effortsReason" jdbcType="VARCHAR"/>
        <result column="outcome" property="outcome" jdbcType="VARCHAR"/>
        <result column="psm_prime_office" property="psmPrimeOffice" jdbcType="VARCHAR"/>
        <result column="risk_conclusion" property="riskConclusion" jdbcType="VARCHAR"/>
        <result column="pursuit_leader_gpn" property="pursuitLeaderGpn" jdbcType="VARCHAR"/>
        <result column="pursuit_leader_id" property="pursuitLeaderId" jdbcType="VARCHAR"/>
        <result column="oppr_partner_gpn" property="opprPartnerGpn" jdbcType="VARCHAR"/>
        <result column="oppr_partner_id" property="opprPartnerId" jdbcType="VARCHAR"/>
        <result column="team_anticipated_manager_gpn" property="teamAnticipatedManagerGpn" jdbcType="VARCHAR"/>
        <result column="team_anticipated_manager_id" property="teamAnticipatedManagerId" jdbcType="VARCHAR"/>
        <result column="amount" property="amount" jdbcType="NUMERIC"/>
        <result column="amount_usd" property="amountUsd" jdbcType="NUMERIC"/>
        <result column="currency" property="currency" jdbcType="VARCHAR"/>
        <result column="is_inclusive_vat" property="isInclusiveVat" jdbcType="INTEGER"/>
        <result column="pace_status" property="paceStatus" jdbcType="INTEGER"/>
        <result column="location_code" property="locationCode" jdbcType="VARCHAR"/>
        <result column="event_code" property="eventCode" jdbcType="VARCHAR"/>
        <result column="is_subcode" property="isSubcode" jdbcType="VARCHAR"/>
        <result column="contract" property="contract" jdbcType="VARCHAR"/>
        <result column="closed" property="closed" jdbcType="INTEGER"/>
        <result column="contractor_used" property="contractorUsed" jdbcType="VARCHAR"/>
        <result column="outsource" property="outsource" jdbcType="NUMERIC"/>
        <result column="confirm_status" property="confirmStatus" jdbcType="VARCHAR"/>
       <result column="test_json" property="testJson" jdbcType="VARCHAR"/>
        <result column="pursuit_fy" property="pursuitFy" jdbcType="VARCHAR"/>
        <result column="won_fy" property="wonFy" jdbcType="VARCHAR"/>
        <result column="efforts_approve" property="effortsApprove" jdbcType="VARCHAR"/>
        <result column="sales_delivery_credit_amount" property="salesDeliveryCreditAmount" jdbcType="NUMERIC"/>
        <result column="sales_delivery_credit_ratio" property="salesDeliveryCreditRatio" jdbcType="NUMERIC"/>
        <result column="f_ner" property="fNer" jdbcType="NUMERIC"/>
        <result column="sales_delivery_credit" property="salesDeliveryCredit" jdbcType="VARCHAR"/>
        <result column="use_amount" property="useAmount" jdbcType="NUMERIC"/>
    </resultMap>
    <resultMap id="EyTaxResultMap2" type="com.ey.tax.cloud.targets.entity.pipeline.PipelineSales">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="create_uid" property="createUid" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_uid" property="updateUid" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="is_del" property="isDel" jdbcType="SMALLINT"/>
        <result column="auto_test" property="autoTest" jdbcType="SMALLINT"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="english_name" property="englishName" jdbcType="VARCHAR"/>
        <result column="client_id" property="clientId" jdbcType="VARCHAR"/>
        <result column="industry_sector" property="industrySector" jdbcType="VARCHAR"/>
        <result column="industry_sector" property="industrySubSector" jdbcType="VARCHAR"/>
        <result column="company_type" property="companyType" typeHandler="com.ey.tax.cloud.targets.config.JsonTypeHandler"/>
        <result column="stock_exchange" property="stockExchange" typeHandler="com.ey.tax.cloud.targets.config.JsonTypeHandler"/>
        <result column="is_sec" property="isSec" jdbcType="INTEGER"/>
        <result column="head_office_location" property="headOfficeLocation" jdbcType="VARCHAR"/>
        <result column="client_channel" property="clientChannel" jdbcType="VARCHAR"/>
        <result column="customer_classification" property="customerClassification" jdbcType="VARCHAR"/>
        <result column="pipeline_code" property="pipelineCode" jdbcType="VARCHAR"/>
        <result column="pipeline_name" property="pipelineName" jdbcType="VARCHAR"/>
        <result column="local_service_code" property="localServiceCode" jdbcType="VARCHAR"/>
        <result column="product_code" property="productCode" jdbcType="VARCHAR"/>
        <result column="product_name" property="productName" jdbcType="VARCHAR"/>
        <result column="estimated_win_odds" property="estimatedWinOdds" jdbcType="INTEGER"/>
        <result column="anticipated_win_date" property="anticipatedWinDate" jdbcType="DATE"/>
        <result column="is_competitive_bid" property="isCompetitiveBid" jdbcType="INTEGER"/>
        <result column="reason" property="reason" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="is_eic_self" property="isEicSelf" jdbcType="INTEGER"/>
        <result column="efforts_reason" property="effortsReason" jdbcType="VARCHAR"/>
        <result column="outcome" property="outcome" jdbcType="VARCHAR"/>
        <result column="psm_prime_office" property="psmPrimeOffice" jdbcType="VARCHAR"/>
        <result column="risk_conclusion" property="riskConclusion" jdbcType="VARCHAR"/>
        <result column="pursuit_leader_gpn" property="pursuitLeaderGpn" jdbcType="VARCHAR"/>
        <result column="pursuit_leader_id" property="pursuitLeaderId" jdbcType="VARCHAR"/>
        <result column="oppr_partner_gpn" property="opprPartnerGpn" jdbcType="VARCHAR"/>
        <result column="oppr_partner_id" property="opprPartnerId" jdbcType="VARCHAR"/>
        <result column="team_anticipated_manager_gpn" property="teamAnticipatedManagerGpn" jdbcType="VARCHAR"/>
        <result column="team_anticipated_manager_id" property="teamAnticipatedManagerId" jdbcType="VARCHAR"/>
        <result column="amount" property="amount" jdbcType="NUMERIC"/>
        <result column="amount_usd" property="amountUsd" jdbcType="NUMERIC"/>
        <result column="currency" property="currency" jdbcType="VARCHAR"/>
        <result column="is_inclusive_vat" property="isInclusiveVat" jdbcType="INTEGER"/>
        <result column="pace_status" property="paceStatus" jdbcType="INTEGER"/>
        <result column="location_code" property="locationCode" jdbcType="VARCHAR"/>
        <result column="event_code" property="eventCode" jdbcType="VARCHAR"/>
        <result column="is_subcode" property="isSubcode" jdbcType="VARCHAR"/>
        <result column="contract" property="contract" jdbcType="VARCHAR"/>
        <result column="closed" property="closed" jdbcType="INTEGER"/>
        <result column="contractor_used" property="contractorUsed" jdbcType="VARCHAR"/>
        <result column="outsource" property="outsource" jdbcType="NUMERIC"/>
        <result column="confirm_status" property="confirmStatus" jdbcType="VARCHAR"/>
       <result column="test_json" property="testJson" jdbcType="VARCHAR"/>
        <result column="pursuit_fy" property="pursuitFy" jdbcType="VARCHAR"/>
        <result column="won_fy" property="wonFy" jdbcType="VARCHAR"/>
        <result column="efforts_approve" property="effortsApprove" jdbcType="VARCHAR"/>
        <result column="sales_delivery_credit" property="salesDeliveryCredit" jdbcType="VARCHAR"/>
        <result column="sales_delivery_credit_type" property="salesDeliveryCreditType" jdbcType="VARCHAR"/>
        <result column="sales_delivery_credit_ratio" property="salesDeliveryCreditRatio" jdbcType="NUMERIC"/>
        <result column="sales_delivery_credit_amount" property="salesDeliveryCreditAmount" jdbcType="NUMERIC"/>
        <!--outboundRelated-->
        <result column="outbound_related" property="outboundRelated" jdbcType="VARCHAR"/>
        <!--outboundCountry-->
        <result column="outbound_country" property="outboundCountry" jdbcType="VARCHAR"/>
        <!-- wonTime -->
        <result column="won_time" property="wonTime" jdbcType="TIMESTAMP"/>
        <result column="is_same" property="isSame" jdbcType="INTEGER"/>
        <!--isBlank-->
        <result column="is_blank" property="isBlank" jdbcType="INTEGER"/>
        <!--accountId-->
        <result column="account_id" property="accountId" jdbcType="VARCHAR"/>
        <!--accountname-->
        <result column="accountName" property="accountName" jdbcType="VARCHAR"/>
        <!--fNer-->
        <result column="ner" property="ner" jdbcType="NUMERIC"/>
        <!--fTer-->
        <result column="ter" property="ter" jdbcType="NUMERIC"/>
        <result column="totalner" property="totalner" jdbcType="NUMERIC"/>
        <!--use_amount-->
        <result column="use_amount" property="useAmount" jdbcType="NUMERIC"/>
    </resultMap>

    <resultMap id="EyTaxTPCResultMap" type="com.ey.tax.cloud.targets.entity.pipeline.Pipeline">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="create_uid" property="createUid" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_uid" property="updateUid" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="is_del" property="isDel" jdbcType="SMALLINT"/>
        <result column="auto_test" property="autoTest" jdbcType="SMALLINT"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="english_name" property="englishName" jdbcType="VARCHAR"/>
        <result column="client_id" property="clientId" jdbcType="VARCHAR"/>
        <result column="industry_sector" property="industrySector" jdbcType="VARCHAR"/>
        <result column="industry_sector" property="industrySubSector" jdbcType="VARCHAR"/>
        <result column="company_type" property="companyType" typeHandler="com.ey.tax.cloud.targets.config.JsonTypeHandler"/>
        <result column="stock_exchange" property="stockExchange" typeHandler="com.ey.tax.cloud.targets.config.JsonTypeHandler"/>
        <result column="is_sec" property="isSec" jdbcType="INTEGER"/>
        <result column="head_office_location" property="headOfficeLocation" jdbcType="VARCHAR"/>
        <result column="client_channel" property="clientChannel" jdbcType="VARCHAR"/>
        <result column="customer_classification" property="customerClassification" jdbcType="VARCHAR"/>
        <result column="pipeline_code" property="pipelineCode" jdbcType="VARCHAR"/>
        <result column="pipeline_name" property="pipelineName" jdbcType="VARCHAR"/>
        <result column="local_service_code" property="localServiceCode" jdbcType="VARCHAR"/>
        <result column="product_code" property="productCode" jdbcType="VARCHAR"/>
        <result column="product_name" property="productName" jdbcType="VARCHAR"/>
        <result column="estimated_win_odds" property="estimatedWinOdds" jdbcType="INTEGER"/>
        <result column="anticipated_win_date" property="anticipatedWinDate" jdbcType="DATE"/>
        <result column="is_competitive_bid" property="isCompetitiveBid" jdbcType="INTEGER"/>
        <result column="reason" property="reason" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="is_eic_self" property="isEicSelf" jdbcType="INTEGER"/>
        <result column="efforts_reason" property="effortsReason" jdbcType="VARCHAR"/>
        <result column="outcome" property="outcome" jdbcType="VARCHAR"/>
        <result column="psm_prime_office" property="psmPrimeOffice" jdbcType="VARCHAR"/>
        <!--        <result column="anticipated_start_date" property="anticipatedStartDate" jdbcType="DATE"/>
                <result column="anticipated_close_date" property="anticipatedCloseDate" jdbcType="DATE"/>-->
        <result column="risk_conclusion" property="riskConclusion" jdbcType="VARCHAR"/>
        <result column="pursuit_leader_gpn" property="pursuitLeaderGpn" jdbcType="VARCHAR"/>
        <result column="pursuit_leader_id" property="pursuitLeaderId" jdbcType="VARCHAR"/>
        <result column="oppr_partner_gpn" property="opprPartnerGpn" jdbcType="VARCHAR"/>
        <result column="oppr_partner_id" property="opprPartnerId" jdbcType="VARCHAR"/>
        <result column="team_anticipated_manager_gpn" property="teamAnticipatedManagerGpn" jdbcType="VARCHAR"/>
        <result column="team_anticipated_manager_id" property="teamAnticipatedManagerId" jdbcType="VARCHAR"/>
        <result column="amount" property="amount" jdbcType="NUMERIC"/>
        <result column="amount_usd" property="amountUsd" jdbcType="NUMERIC"/>
        <result column="currency" property="currency" jdbcType="VARCHAR"/>
        <result column="is_inclusive_vat" property="isInclusiveVat" jdbcType="INTEGER"/>
        <result column="pace_status" property="paceStatus" jdbcType="INTEGER"/>
        <result column="location_code" property="locationCode" jdbcType="VARCHAR"/>
        <result column="event_code" property="eventCode" jdbcType="VARCHAR"/>
        <result column="is_subcode" property="isSubcode" jdbcType="VARCHAR"/>
        <result column="contract" property="contract" jdbcType="VARCHAR"/>
        <result column="closed" property="closed" jdbcType="INTEGER"/>
        <result column="contractor_used" property="contractorUsed" jdbcType="VARCHAR"/>
        <result column="outsource" property="outsource" jdbcType="NUMERIC"/>
        <result column="confirm_status" property="confirmStatus" jdbcType="VARCHAR"/>
        <result column="test_json" property="testJson" jdbcType="VARCHAR"/>
        <result column="pursuit_fy" property="pursuitFy" jdbcType="VARCHAR"/>
        <result column="won_fy" property="wonFy" jdbcType="VARCHAR"/>
        <result column="efforts_approve" property="effortsApprove" jdbcType="VARCHAR"/>
        <result column="scales" property="scales" jdbcType="NUMERIC"/>
        <result column="tpcMemoevnue" property="tpcMemoevnue" jdbcType="NUMERIC"/>
        <result column="adviceName" property="adviceName" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="selectListFromMainAndApprove" resultMap="EyTaxResultMap">
       SELECT
    <if test="ew.sqlSelect != null">
        ${ew.sqlSelect}
    </if>
    <if test="ew.sqlSelect == null">
        *
    </if>
        from (SELECT distinct __t.create_uid,
                              __t.create_time,
                              __t.update_uid,
                              __t.update_time,
                              __t.is_del,
                              __t.auto_test,
                              __t.remark,
                              __t.id,
                              __t.client_name,
                              __t.client_code,
                              __t.industry_sector,
                              __t.industry_sub_sector,
                              __t.company_type,
                              __t.stock_exchange,
                              __t.is_sec,
                              __t.head_office_location,
                              __t.client_channel,
                              __t.customer_classification,
                              __t.pipeline_code,
                              __t.pipeline_name,
                              __t.local_service_code,
                              __t.product_id,
                              __t.win_rate,
                              __t.anticipated_win_date,
                              __t.lead_source,
                              __t.won_reason,
                              __t.description,
                              __t.eic_own_effort,
                              __t.efforts_description,
                              __t.status,
                              __t.psm_prime_office,
                              __t.kickoff_week,
                              __t.closing_week,
                              __t.delivery_risk,
                              __t.pursuit_leader_id,
                              __t.oppr_partner_id,
                              __t.eng_manager_id,
                              __t.amount,
                              __t.amount_usd,
                              __t.currency,
                              __t.is_inclusive_vat,
                              __t.location_code,
                              __t.event,
                              __t.contract,
                              __t.closed,
                              __t.outsource,
                              __t.confirm_status,
                              __t.pursuit_fy,
                              __t.won_fy,
                              __t.efforts_approve,
                              __t.other_event,
                              __t.crm,
                              __t.listed,
                              __t.location,
                              __t.project_type,
                              __t.is_subcode,
                              __t.contractor_used,
                              __t.pace_status,
                              __t.engagemant_status,
                              __t.category_of_the_disputes,
                              __t.description_of_the_disputes,
                              __t.the_advisor_s_contuibution_to_this_case,
                              __t.resources_province_city,
                              __t.resources_tax_authority,
                              __t.eng_code,
                              __t.first_confirm_date,
                              __t.bidding,
                              __t.recurring,
                              __t.update_fy,
                              __t.source_type,
                              __t.master_code,
                              __t.amount_currency,
                              __t.is_same,
                                __t.is_blank,
                                __t.account_id
              FROM tax_cloud_target.target_pipeline __t
                       left join tax_cloud_target.target_pipeline_sales_delivery u on __t.id = u.pipeline_id and
                                                                                      (__t.confirm_status is null or
                                                                                       __t.confirm_status not in
                                                                                       ('Amendment Submitted',
                                                                                        'Returned'))
                       left join tax_cloud_target.target_user_attribute a on u.sales_delivery_credit = a.user_id

               ${ew.customSqlSegment}

              union all
              SELECT distinct __t.create_uid,
                  __t.create_time,
                  __t.update_uid,
                  __t.update_time,
                  __t.is_del,
                  __t.auto_test,
                  __t.remark,
                  __t.id,
                  __t.client_name,
                  __t.client_code,
                  __t.industry_sector,
                  __t.industry_sub_sector,
                  __t.company_type,
                  __t.stock_exchange,
                  __t.is_sec,
                  __t.head_office_location,
                  __t.client_channel,
                  __t.customer_classification,
                  __t.pipeline_code,
                  __t.pipeline_name,
                  __t.local_service_code,
                  __t.product_id,
                  __t.win_rate,
                  __t.anticipated_win_date,
                  __t.lead_source,
                  __t.won_reason,
                  __t.description,
                  __t.eic_own_effort,
                  __t.efforts_description,
                  __t.status,
                  __t.psm_prime_office,
                  __t.kickoff_week,
                  __t.closing_week,
                  __t.delivery_risk,
                  __t.pursuit_leader_id,
                  __t.oppr_partner_id,
                  __t.eng_manager_id,
                  __t.amount,
                  __t.amount_usd,
                  __t.currency,
                  __t.is_inclusive_vat,
                  __t.location_code,
                  __t.event,
                  __t.contract,
                  __t.closed,
                  __t.outsource,
                  __t.confirm_status,
                  __t.pursuit_fy,
                  __t.won_fy,
                  __t.efforts_approve,
                  __t.other_event,
                  __t.crm,
                  __t.listed,
                  __t.location,
                  __t.project_type,
                  __t.is_subcode,
                  __t.contractor_used,
                  __t.pace_status,
                  __t.engagemant_status,
                  __t.category_of_the_disputes,
                  __t.description_of_the_disputes,
                  __t.the_advisor_s_contuibution_to_this_case,
                  __t.resources_province_city,
                  __t.resources_tax_authority,
                  __t.eng_code,
                  __t.first_confirm_date,
                  __t.bidding,
                  __t.recurring,
                  __t.update_fy,
                  __t.source_type,
                  __t.master_code,
                  __t.amount_currency,
                  0,0,__t.account_id
              FROM tax_cloud_target.target_pipeline_approve __t
                       left join tax_cloud_target.target_pipeline_sales_delivery_approve u
                                 on __t.id = u.pipeline_id and
                                    __t.confirm_status in ('Amendment Submitted', 'Returned')
                       left join tax_cloud_target.target_user_attribute a on u.sales_delivery_credit = a.user_id

               ${ew.customSqlSegment} ) f  ${order.customSqlSegment}
    </select>
    <select id="selectListNew" resultMap="EyTaxResultMap2">
        select *
        from (SELECT distinct __t.create_uid,
                              __t.create_time,
                              __t.outbound_related,
                              __t.outbound_country,
                              __t.update_uid,
                              __t.update_time,
                              __t.is_del,
                              __t.auto_test,
                              __t.remark,
                              __t.id,
                              __t.client_name,
                              __t.client_code,
                              __t.industry_sector,
                              __t.industry_sub_sector,
                              __t.company_type,
                              __t.stock_exchange,
                              __t.is_sec,
                              __t.head_office_location,
                              __t.client_channel,
                              __t.customer_classification,
                              __t.pipeline_code,
                              __t.pipeline_name,
                              __t.local_service_code,
                              __t.product_id,
                              __t.win_rate,
                              __t.anticipated_win_date,
                              __t.lead_source,
                              __t.won_reason,
                              __t.description,
                              __t.eic_own_effort,
                              __t.efforts_description,
                              __t.status,
                              __t.psm_prime_office,
                              __t.kickoff_week,
                              __t.closing_week,
                              __t.delivery_risk,
                              __t.pursuit_leader_id,
                              __t.oppr_partner_id,
                              __t.eng_manager_id,
                              __t.amount,
                              __t.amount_usd,
                              __t.currency,
                              __t.is_inclusive_vat,
                              __t.location_code,
                              __t.event,
                              __t.contract,
                              __t.closed,
                              __t.outsource,
                              __t.confirm_status,
                              __t.pursuit_fy,
                              __t.won_fy,
                              __t.efforts_approve,
                              __t.other_event,
                              __t.crm,
                              __t.listed,
                              __t.location,
                              __t.project_type,
                              __t.is_subcode,
                              __t.contractor_used,
                              __t.pace_status,
                              __t.engagemant_status,
                              __t.category_of_the_disputes,
                              __t.description_of_the_disputes,
                              __t.the_advisor_s_contuibution_to_this_case,
                              __t.resources_province_city,
                              __t.resources_tax_authority,
                              __t.eng_code,
                              __t.first_confirm_date,
                              __t.bidding,
                              __t.recurring,
                              __t.update_fy,
                              __t.source_type,
                              __t.master_code,
                              __t.amount_currency,
                              __t.won_time,
                                __t.is_same,
                                __t.is_blank,
                                __t.account_id,
                              u.sales_delivery_credit,
                              u.sales_delivery_credit_type,
                              u.sales_delivery_credit_ratio,
                              u.sales_delivery_credit_amount,
                              u.use_amount,
                              c.english_name as accountName,
                              b.ner,
                                b.ter,
                                d.ner as totalner
              FROM tax_cloud_target.target_pipeline __t
                       left join tax_cloud_target.target_pipeline_sales_delivery u on __t.id = u.pipeline_id
                       left join (select pipeline_id, sum(ner) as ner, sum(ter) as ter,fiscal_year as busyear,user_id from tax_cloud_target.target_my_business_self where is_del = 0 and fiscal_year = '${fy}' and tag='ytd' group by pipeline_id,fiscal_year,user_id) b on __t.id = b.pipeline_id and u.sales_delivery_credit=b.user_id
                       left join (select pipeline_id, sum(case when kpi_amount &lt;  0 then 0 else kpi_amount end ) as ner,fiscal_year as busyear,sales_delivery_credit from tax_cloud_target.target_pipeline_sales_business where is_del = 0 and fiscal_year = '${fy}'  group by pipeline_id,fiscal_year,sales_delivery_credit) d on __t.id = d.pipeline_id and u.sales_delivery_credit = d.sales_delivery_credit
                       left join tax_cloud_target.sync_tax_client c on __t.account_id = c.client_id
               ${ew.customSqlSegment} ) t
    </select>
    <select id="selectPageNew" resultMap="EyTaxResultMap2">
        select *
        from (SELECT distinct __t.create_uid,
                              __t.create_time,
                              __t.outbound_related,
                              __t.outbound_country,
                              __t.update_uid,
                              __t.update_time,
                              __t.is_del,
                              __t.auto_test,
                              __t.remark,
                              __t.id,
                              __t.client_name,
                              __t.client_code,
                              __t.industry_sector,
                              __t.industry_sub_sector,
                              __t.company_type,
                              __t.stock_exchange,
                              __t.is_sec,
                              __t.head_office_location,
                              __t.client_channel,
                              __t.customer_classification,
                              __t.pipeline_code,
                              __t.pipeline_name,
                              __t.local_service_code,
                              __t.product_id,
                              __t.win_rate,
                              __t.anticipated_win_date,
                              __t.lead_source,
                              __t.won_reason,
                              __t.description,
                              __t.eic_own_effort,
                              __t.efforts_description,
                              __t.status,
                              __t.psm_prime_office,
                              __t.kickoff_week,
                              __t.closing_week,
                              __t.delivery_risk,
                              __t.pursuit_leader_id,
                              __t.oppr_partner_id,
                              __t.eng_manager_id,
                              __t.amount,
                              __t.amount_usd,
                              __t.currency,
                              __t.is_inclusive_vat,
                              __t.location_code,
                              __t.event,
                              __t.contract,
                              __t.closed,
                              __t.outsource,
                              __t.confirm_status,
                              __t.pursuit_fy,
                              __t.won_fy,
                              __t.efforts_approve,
                              __t.other_event,
                              __t.crm,
                              __t.listed,
                              __t.location,
                              __t.project_type,
                              __t.is_subcode,
                              __t.contractor_used,
                              __t.pace_status,
                              __t.engagemant_status,
                              __t.category_of_the_disputes,
                              __t.description_of_the_disputes,
                              __t.the_advisor_s_contuibution_to_this_case,
                              __t.resources_province_city,
                              __t.resources_tax_authority,
                              __t.eng_code,
                              __t.first_confirm_date,
                              __t.bidding,
                              __t.recurring,
                              __t.update_fy,
                              __t.source_type,
                              __t.master_code,
                              __t.amount_currency,
                              __t.won_time,
                                __t.is_same,
                                __t.is_blank,
                                __t.account_id,
                              u.sales_delivery_credit,
                              u.sales_delivery_credit_type,
                              u.sales_delivery_credit_ratio,
                              u.sales_delivery_credit_amount,
                              u.use_amount,
                              c.english_name as accountName,
                              b.ner,
                                b.ter,
                                d.ner as totalner
              FROM tax_cloud_target.target_pipeline __t
                       left join tax_cloud_target.target_pipeline_sales_delivery u on __t.id = u.pipeline_id
                       left join (select pipeline_id, sum(ner) as ner, sum(ter) as ter,fiscal_year as busyear,user_id from tax_cloud_target.target_my_business_self where is_del = 0 and fiscal_year = '${fy}' and tag='ytd' group by pipeline_id,fiscal_year,user_id) b on __t.id = b.pipeline_id and u.sales_delivery_credit=b.user_id
                       left join (select pipeline_id, sum(case when kpi_amount &lt;  0 then 0 else kpi_amount end) as ner,fiscal_year as busyear,sales_delivery_credit from tax_cloud_target.target_pipeline_sales_business where is_del = 0 and fiscal_year = '${fy}'  group by pipeline_id,fiscal_year,sales_delivery_credit) d on __t.id = d.pipeline_id and u.sales_delivery_credit = d.sales_delivery_credit
                       left join tax_cloud_target.sync_tax_client c on __t.account_id = c.client_id
               ${ew.customSqlSegment} ) t
    </select>
    <select id="selectListBusiness" resultMap="EyTaxResultMap2">
        select *
        from (SELECT distinct __t.create_uid,
                              __t.create_time,
                              __t.outbound_related,
                              __t.outbound_country,
                              __t.update_uid,
                              __t.update_time,
                              __t.is_del,
                              __t.auto_test,
                              __t.remark,
                              __t.id,
                              __t.client_name,
                              __t.client_code,
                              __t.industry_sector,
                              __t.industry_sub_sector,
                              __t.company_type,
                              __t.stock_exchange,
                              __t.is_sec,
                              __t.head_office_location,
                              __t.client_channel,
                              __t.customer_classification,
                              __t.pipeline_code,
                              __t.pipeline_name,
                              __t.local_service_code,
                              __t.product_id,
                              __t.win_rate,
                              __t.anticipated_win_date,
                              __t.lead_source,
                              __t.won_reason,
                              __t.description,
                              __t.eic_own_effort,
                              __t.efforts_description,
                              __t.status,
                              __t.psm_prime_office,
                              __t.kickoff_week,
                              __t.closing_week,
                              __t.delivery_risk,
                              __t.pursuit_leader_id,
                              __t.oppr_partner_id,
                              __t.eng_manager_id,
                              __t.amount,
                              __t.amount_usd,
                              __t.currency,
                              __t.is_inclusive_vat,
                              __t.location_code,
                              __t.event,
                              __t.contract,
                              __t.closed,
                              __t.outsource,
                              __t.confirm_status,
                              __t.pursuit_fy,
                              __t.won_fy,
                              __t.efforts_approve,
                              __t.other_event,
                              __t.crm,
                              __t.listed,
                              __t.location,
                              __t.project_type,
                              __t.is_subcode,
                              __t.contractor_used,
                              __t.pace_status,
                              __t.engagemant_status,
                              __t.category_of_the_disputes,
                              __t.description_of_the_disputes,
                              __t.the_advisor_s_contuibution_to_this_case,
                              __t.resources_province_city,
                              __t.resources_tax_authority,
                              __t.eng_code,
                              __t.first_confirm_date,
                              __t.bidding,
                              __t.recurring,
                              __t.update_fy,
                              __t.source_type,
                              __t.master_code,
                              __t.amount_currency,
                              __t.won_time,
                              __t.is_same,
                              __t.is_blank,
                              __t.account_id,
                              c.english_name as accountName,
                              b.ner,
                              b.ter


              FROM tax_cloud_target.target_pipeline __t
                       right join (select pipeline_id,sum(f_ner) as ner ,sum(f_ter) as ter,fiscal_year as busyear from tax_cloud_target.target_my_business where is_del = 0  and pipeline_id is not null group by pipeline_id ,fiscal_year) b on __t.id = b.pipeline_id
                       left join tax_cloud_target.sync_tax_client c on __t.account_id = c.client_id
                  ${ew.customSqlSegment} ) t
    </select>

    <select id="selectListEngCode" resultMap="EyTaxResultMap2">
        select *
        from (SELECT distinct __t.create_uid,
                              __t.create_time,
                              __t.outbound_related,
                              __t.outbound_country,
                              __t.update_uid,
                              __t.update_time,
                              __t.is_del,
                              __t.auto_test,
                              __t.remark,
                              __t.id,
                              __t.client_name,
                              __t.client_code,
                              __t.industry_sector,
                              __t.industry_sub_sector,
                              __t.company_type,
                              __t.stock_exchange,
                              __t.is_sec,
                              __t.head_office_location,
                              __t.client_channel,
                              __t.customer_classification,
                              __t.pipeline_code,
                              __t.pipeline_name,
                              __t.local_service_code,
                              __t.product_id,
                              __t.win_rate,
                              __t.anticipated_win_date,
                              __t.lead_source,
                              __t.won_reason,
                              __t.description,
                              __t.eic_own_effort,
                              __t.efforts_description,
                              __t.status,
                              __t.psm_prime_office,
                              __t.kickoff_week,
                              __t.closing_week,
                              __t.delivery_risk,
                              __t.pursuit_leader_id,
                              b.engagement_partner_id as oppr_partner_id,
                              b.engagement_manager_id as eng_manager_id,
                              __t.amount,
                              __t.amount_usd,
                              __t.currency,
                              __t.is_inclusive_vat,
                              __t.location_code,
                              __t.event,
                              __t.contract,
                              __t.closed,
                              __t.outsource,
                              __t.confirm_status,
                              __t.pursuit_fy,
                              __t.won_fy,
                              __t.efforts_approve,
                              __t.other_event,
                              __t.crm,
                              __t.listed,
                              __t.location,
                              __t.project_type,
                              __t.is_subcode,
                              __t.contractor_used,
                              __t.pace_status,
                              __t.engagemant_status,
                              __t.category_of_the_disputes,
                              __t.description_of_the_disputes,
                              __t.the_advisor_s_contuibution_to_this_case,
                              __t.resources_province_city,
                              __t.resources_tax_authority,
                              b.engagement_code as eng_code,
                              __t.first_confirm_date,
                              __t.bidding,
                              __t.recurring,
                              __t.update_fy,
                              __t.source_type,
                              __t.master_code,
                              __t.amount_currency,
                              __t.won_time,
                              __t.is_same,
                              __t.is_blank,
                              __t.account_id,
                              c.english_name as accountName,
                              b.ner,
                              b.ter


              FROM tax_cloud_target.target_pipeline __t
                       right join (select pipeline_id,engagement_code,engagement_partner_id,engagement_manager_id,sum(f_ner) as ner ,sum(f_ter) as ter,fiscal_year as busyear from tax_cloud_target.target_my_business where is_del = 0  and pipeline_id is not null group by pipeline_id ,fiscal_year,engagement_code,engagement_partner_id,engagement_manager_id) b on __t.id = b.pipeline_id
                       left join tax_cloud_target.sync_tax_client c on __t.account_id = c.client_id
                  ${ew.customSqlSegment} ) t
    </select>
    <select id="selectPageEngCode" resultMap="EyTaxResultMap2">
        select *
        from (SELECT distinct __t.create_uid,
                              __t.create_time,
                              __t.outbound_related,
                              __t.outbound_country,
                              __t.update_uid,
                              __t.update_time,
                              __t.is_del,
                              __t.auto_test,
                              __t.remark,
                              __t.id,
                              __t.client_name,
                              __t.client_code,
                              __t.industry_sector,
                              __t.industry_sub_sector,
                              __t.company_type,
                              __t.stock_exchange,
                              __t.is_sec,
                              __t.head_office_location,
                              __t.client_channel,
                              __t.customer_classification,
                              __t.pipeline_code,
                              __t.pipeline_name,
                              __t.local_service_code,
                              __t.product_id,
                              __t.win_rate,
                              __t.anticipated_win_date,
                              __t.lead_source,
                              __t.won_reason,
                              __t.description,
                              __t.eic_own_effort,
                              __t.efforts_description,
                              __t.status,
                              __t.psm_prime_office,
                              __t.kickoff_week,
                              __t.closing_week,
                              __t.delivery_risk,
                              __t.pursuit_leader_id,
                              b.engagement_partner_id as oppr_partner_id,
                              b.engagement_manager_id as eng_manager_id,
                              __t.amount,
                              __t.amount_usd,
                              __t.currency,
                              __t.is_inclusive_vat,
                              __t.location_code,
                              __t.event,
                              __t.contract,
                              __t.closed,
                              __t.outsource,
                              __t.confirm_status,
                              __t.pursuit_fy,
                              __t.won_fy,
                              __t.efforts_approve,
                              __t.other_event,
                              __t.crm,
                              __t.listed,
                              __t.location,
                              __t.project_type,
                              __t.is_subcode,
                              __t.contractor_used,
                              __t.pace_status,
                              __t.engagemant_status,
                              __t.category_of_the_disputes,
                              __t.description_of_the_disputes,
                              __t.the_advisor_s_contuibution_to_this_case,
                              __t.resources_province_city,
                              __t.resources_tax_authority,
                              b.engagement_code as eng_code,
                              __t.first_confirm_date,
                              __t.bidding,
                              __t.recurring,
                              __t.update_fy,
                              __t.source_type,
                              __t.master_code,
                              __t.amount_currency,
                              __t.won_time,
                              __t.is_same,
                              __t.is_blank,
                              __t.account_id,
                              c.english_name as accountName,
                              b.ner,
                              b.ter


              FROM tax_cloud_target.target_pipeline __t
                       right join (select pipeline_id,engagement_code,engagement_partner_id,engagement_manager_id,sum(f_ner) as ner ,sum(f_ter) as ter,fiscal_year as busyear from tax_cloud_target.target_my_business where is_del = 0  and pipeline_id is not null group by pipeline_id ,fiscal_year,engagement_code,engagement_partner_id,engagement_manager_id) b on __t.id = b.pipeline_id
                       left join tax_cloud_target.sync_tax_client c on __t.account_id = c.client_id
                  ${ew.customSqlSegment} ) t
    </select>


    <select id="selectListForAuth" resultMap="EyTaxResultMap">
       SELECT
    <if test="ew.sqlSelect != null">
        ${ew.sqlSelect}
    </if>
    <if test="ew.sqlSelect == null">
        *
    </if>
        FROM tax_cloud_target.target_pipeline __t   ${ew.customSqlSegment}
        and ( 1=0
        <if test="isSelf ==true">
            or id in (select p.pipeline_id
            from tax_cloud_target.target_pipeline_sales_delivery_data p
            left join tax_cloud_target.target_user_attribute a on p.sales_delivery_credit = a.user_id
            ${queryWrapperForUser.customSqlSegment}  and p.sales_delivery_credit_type != 3  and a.status=1  and a.fiscal_year = '${fy}' and a.is_del=0  and p.sales_delivery_credit_ratio>=50
            and sales_delivery_credit = '${userId}'
            )
        </if>
        <if test="group !=null">
              or id in (select p.pipeline_id
                from tax_cloud_target.target_pipeline_sales_delivery_data p
                left join tax_cloud_target.target_user_attribute a on p.sales_delivery_credit = a.user_id
                ${queryWrapperForUser.customSqlSegment}  and p.sales_delivery_credit_type != 3
            and (p.won_fy='${fy}'  or p.pursuit_fy='${fy}' )
        and a.status=1  and a.fiscal_year = '${fy}' and a.is_del=0  and p.sales_delivery_credit_ratio>=50
        <if test="group !=null">
            <foreach item="entry" index="key" collection="group" open="and ( " separator=" or" close=")">
                <foreach item="detail" index="key2" collection="entry" open=" ( " separator=" and" close=")">
                    <if test="detail.limitType==1">
                        <if test='detail.detailType=="1"'>
                            <if test="detail.detailValue==1">
                                   a.sl1 = (select b.sl1 from tax_cloud_target.target_user_attribute b where b.user_id = '${userId}' and b.fiscal_year = '${fy}' and b.is_del=0 limit 1)
                            </if>
                            <if test="detail.detailValue==2">
                                   a.ssl2 = (select b.ssl2 from tax_cloud_target.target_user_attribute b where b.user_id = '${userId}' and b.fiscal_year = '${fy}'  and b.is_del=0 limit 1)
                            </if>
                            <if test="detail.detailValue==3">
                                   a.ssl3 = (select b.ssl3 from tax_cloud_target.target_user_attribute b where b.user_id = '${userId}' and b.fiscal_year = '${fy}'  and b.is_del=0 limit 1)
                            </if>
                        </if>
                        <if test='detail.detailType=="2"'>
                            <if test="detail.detailValue==1">
                                   a.region = (select b.region from tax_cloud_target.target_user_attribute b where b.user_id = '${userId}' and b.fiscal_year = '${fy}'  and b.is_del=0 limit 1)
                            </if>
                            <if test="detail.detailValue==2">
                                   a.city = (select b.city from tax_cloud_target.target_user_attribute b where b.user_id = '${userId}' and b.fiscal_year = '${fy}'  and b.is_del=0 limit 1)
                            </if>
                        </if>
                        <if test='detail.detailType=="3"'>
                            <if test="detail.detailValue=='levelGroup'">
                                   a.level_group = (select b.level_group from tax_cloud_target.target_user_attribute b where b.user_id = '${userId}' and b.fiscal_year = '${fy}'  and b.is_del=0 limit 1)
                            </if>
                            <if test="detail.detailValue=='level'">
                                   a.level = (select b.level from tax_cloud_target.target_user_attribute b where b.user_id = '${userId}' and b.fiscal_year = '${fy}'  and b.is_del=0 limit 1)
                            </if>
                        </if>
                        <if test='detail.detailType=="userTag"'>
                            a.user_tag = (select b.user_tag from tax_cloud_target.target_user_attribute b where b.user_id = '${userId}' and b.fiscal_year = '${fy}' and b.is_del=0 limit 1)
                        </if>
                    </if>
                    <if test="detail.limitType==2">
                        <if test='detail.detailType=="1" or detail.detailType=="2" or detail.detailType=="3" or detail.detailType=="userTag" '>
                            <foreach item="detailValue" index="key3" collection="detail.detailValues" open="  ( " separator=" or" close=")">
                                <if test='detail.detailType=="1"'>
                                    a.ssl3 = '${detailValue}'
                                </if>
                                <if test='detail.detailType=="2"'>
                                    a.city = '${detailValue}'
                                </if>
                                <if test='detail.detailType=="3"'>
                                    a.level = '${detailValue}'
                                </if>
                                <if test='detail.detailType=="userTag"'>
                                    a.user_tag = '${detailValue}'
                                </if>
                            </foreach>
                        </if>
                        <if test='detail.detailType=="4" or detail.detailType=="5" or detail.detailType=="6" or detail.detailType=="7"or detail.detailType=="salesCreditType"'>
                            <foreach item="detailValue" index="key3" collection="detail.detailValues" open="  ( " separator=" or" close=")">
                                <if test='detail.detailType=="4"'> /*Client HQ*/
                                    p.head_office_location = '${detailValue}'
                                </if>
                                <if test='detail.detailType=="5"'>
                                    /* Client Type */
                                    jsonb_exists(p.company_type, #{detailValue})
                                </if>
                                <if test='detail.detailType=="6"'> /*Sector*/
                                    p.industry_sector = '${detailValue}'
                                </if>
                                <if test='detail.detailType=="7"'> /*Client client_channel*/
                                    p.client_channel = '${detailValue}'
                                </if>
                                /*salesCreditType*/
                                <if test='detail.detailType=="salesCreditType"'>
                                    p.sales_delivery_credit_type = ${detailValue}
                                </if>
                            </foreach>
                        </if>
                    </if>
                </foreach>
            </foreach>

        </if>

                )
        </if>

        ) ${order.customSqlSegment}
    </select>
    <select id="selectListForAuthMemo" resultMap="EyTaxResultMap3">
        SELECT  __t.create_uid, create_time, update_uid, update_time, is_del, auto_test, remark, row_number() over (), client_name, client_code, industry_sector, industry_sub_sector, company_type, stock_exchange, is_sec, head_office_location, client_channel, customer_classification, pipeline_code, pipeline_name, local_service_code, product_id, win_rate, anticipated_win_date, lead_source, won_reason, description, eic_own_effort, efforts_description, status, psm_prime_office, kickoff_week, closing_week, delivery_risk, pursuit_leader_id, oppr_partner_id, eng_manager_id, amount, amount_usd, currency, is_inclusive_vat, location_code, event, contract, closed, outsource, confirm_status, pursuit_fy, won_fy, efforts_approve, other_event, crm, listed, location, project_type, is_subcode, contractor_used, pace_status, engagemant_status, category_of_the_disputes, description_of_the_disputes, the_advisor_s_contuibution_to_this_case, resources_province_city, resources_tax_authority, eng_code, first_confirm_date, bidding, recurring, update_fy, source_type, master_code, amount_currency, outbound_related, outbound_country, coe_approve, tpc_approve, won_time,
        u.sales_delivery_credit,use_amount,sum( case when use_amount =1 then 0 else (sales_delivery_credit_amount-coalesce(sales_delivery_credit_amount_last,0)) end) sales_delivery_credit_amount,sum(sales_delivery_credit_ratio) sales_delivery_credit_ratio, sum(f_ner )f_Ner
        FROM tax_cloud_target.target_pipeline __t
        left join (select pipeline_id, (case when kpi_amount &lt;  0 then 0 else kpi_amount end) f_ner,sales_delivery_credit from tax_cloud_target.target_pipeline_sales_business  where is_del = 0 and fiscal_year = '${fy}'  ) b on b.pipeline_id = __t.id

        left join (select sales_delivery_credit_type,sales_delivery_credit_amount,pipeline_id,sales_delivery_credit_ratio,sales_delivery_credit,id as uid,use_amount,sales_delivery_credit_amount_last  from tax_cloud_target.target_pipeline_sales_delivery where  is_del = 0 )u on __t.id = u.pipeline_id and b.sales_delivery_credit = u.sales_delivery_credit
           ${ew.customSqlSegment} AND b.sales_delivery_credit IS NOT NULL
        and ( 1=0
        <if test="isSelf ==true">
            or u.uid in (select p.id
            from tax_cloud_target.target_pipeline_sales_delivery_data p
            left join tax_cloud_target.target_user_attribute a on p.sales_delivery_credit = a.user_id
            ${queryWrapperForUser.customSqlSegment}   and a.status=1  and a.fiscal_year = '${fy}' and a.is_del=0
            and sales_delivery_credit = '${userId}'
            )
        </if>
        <if test="group !=null">
              or u.uid in (select p.id
                from tax_cloud_target.target_pipeline_sales_delivery_data p
                left join tax_cloud_target.target_user_attribute a on p.sales_delivery_credit = a.user_id
                ${queryWrapperForUser.customSqlSegment}
        and a.status=1  and a.fiscal_year = '${fy}' and a.is_del=0
        <if test="group !=null">
            <foreach item="entry" index="key" collection="group" open="and ( " separator=" or" close=")">
                <foreach item="detail" index="key2" collection="entry" open=" ( " separator=" and" close=")">
                    <if test="detail.limitType==1">
                        <if test='detail.detailType=="1"'>
                            <if test="detail.detailValue==1">
                                   a.sl1 = (select b.sl1 from tax_cloud_target.target_user_attribute b where b.user_id = '${userId}' and b.fiscal_year = '${fy}' and b.is_del=0 limit 1)
                            </if>
                            <if test="detail.detailValue==2">
                                   a.ssl2 = (select b.ssl2 from tax_cloud_target.target_user_attribute b where b.user_id = '${userId}' and b.fiscal_year = '${fy}'  and b.is_del=0 limit 1)
                            </if>
                            <if test="detail.detailValue==3">
                                   a.ssl3 = (select b.ssl3 from tax_cloud_target.target_user_attribute b where b.user_id = '${userId}' and b.fiscal_year = '${fy}'  and b.is_del=0 limit 1)
                            </if>
                        </if>
                        <if test='detail.detailType=="2"'>
                            <if test="detail.detailValue==1">
                                   a.region = (select b.region from tax_cloud_target.target_user_attribute b where b.user_id = '${userId}' and b.fiscal_year = '${fy}'  and b.is_del=0 limit 1)
                            </if>
                            <if test="detail.detailValue==2">
                                   a.city = (select b.city from tax_cloud_target.target_user_attribute b where b.user_id = '${userId}' and b.fiscal_year = '${fy}'  and b.is_del=0 limit 1)
                            </if>
                        </if>
                        <if test='detail.detailType=="3"'>
                            <if test="detail.detailValue=='levelGroup'">
                                   a.level_group = (select b.level_group from tax_cloud_target.target_user_attribute b where b.user_id = '${userId}' and b.fiscal_year = '${fy}'  and b.is_del=0 limit 1)
                            </if>
                            <if test="detail.detailValue=='level'">
                                   a.level = (select b.level from tax_cloud_target.target_user_attribute b where b.user_id = '${userId}' and b.fiscal_year = '${fy}'  and b.is_del=0 limit 1)
                            </if>
                        </if>
                        <if test='detail.detailType=="userTag"'>
                            a.user_tag = (select b.user_tag from tax_cloud_target.target_user_attribute b where b.user_id = '${userId}' and b.fiscal_year = '${fy}' and b.is_del=0 limit 1)
                        </if>
                    </if>
                    <if test="detail.limitType==2">
                        <if test='detail.detailType=="1" or detail.detailType=="2" or detail.detailType=="3" or detail.detailType=="userTag" '>
                            <foreach item="detailValue" index="key3" collection="detail.detailValues" open="  ( " separator=" or" close=")">
                                <if test='detail.detailType=="1"'>
                                    a.ssl3 = '${detailValue}'
                                </if>
                                <if test='detail.detailType=="2"'>
                                    a.city = '${detailValue}'
                                </if>
                                <if test='detail.detailType=="3"'>
                                    a.level = '${detailValue}'
                                </if>
                                <if test='detail.detailType=="userTag"'>
                                    a.user_tag = '${detailValue}'
                                </if>
                            </foreach>
                        </if>
                        <if test='detail.detailType=="4" or detail.detailType=="5" or detail.detailType=="6" or detail.detailType=="7"or detail.detailType=="salesCreditType"'>
                            <foreach item="detailValue" index="key3" collection="detail.detailValues" open="  ( " separator=" or" close=")">
                                <if test='detail.detailType=="4"'> /*Client HQ*/
                                    p.head_office_location = '${detailValue}'
                                </if>
                                <if test='detail.detailType=="5"'>
                                    /* Client Type */
                                    jsonb_exists(p.company_type, #{detailValue})
                                </if>
                                <if test='detail.detailType=="6"'> /*Sector*/
                                    p.industry_sector = '${detailValue}'
                                </if>
                                <if test='detail.detailType=="7"'> /*Client client_channel*/
                                    p.client_channel = '${detailValue}'
                                </if>
                                /*salesCreditType*/
                                <if test='detail.detailType=="salesCreditType"'>
                                    p.sales_delivery_credit_type = ${detailValue}
                                </if>
                            </foreach>
                        </if>
                    </if>
                </foreach>
            </foreach>

        </if>

                )
        </if>

        )  group by  __t.create_uid, create_time, update_uid, update_time, is_del, auto_test, remark, id, client_name, client_code, industry_sector, industry_sub_sector, company_type, stock_exchange, is_sec, head_office_location, client_channel, customer_classification, pipeline_code, pipeline_name, local_service_code, product_id, win_rate, anticipated_win_date, lead_source, won_reason, description, eic_own_effort, efforts_description, status, psm_prime_office, kickoff_week, closing_week, delivery_risk, pursuit_leader_id, oppr_partner_id, eng_manager_id, amount, amount_usd, currency, is_inclusive_vat, location_code, event, contract, closed, outsource, confirm_status, pursuit_fy, won_fy, efforts_approve, other_event, crm, listed, location, project_type, is_subcode, contractor_used, pace_status, engagemant_status, category_of_the_disputes, description_of_the_disputes, the_advisor_s_contuibution_to_this_case, resources_province_city, resources_tax_authority, eng_code, first_confirm_date, bidding, recurring, update_fy, source_type, master_code, amount_currency, outbound_related, outbound_country, coe_approve, tpc_approve, won_time,u.sales_delivery_credit,use_amount
        ${order.customSqlSegment}
    </select>

    <select id="selectListForTPCAuth" resultMap="EyTaxTPCResultMap">
        SELECT _t.*,d.sales_delivery_credit AS adviceName ,d.sales_delivery_credit_amount as tpcMemoevnue,d.sales_delivery_credit_amount as tpc_memoevnue,d.sales_delivery_credit_ratio as scales
        FROM (select * from  tax_cloud_target.target_pipeline __t  ${ew.customSqlSegment} and ( 1=0 /* 1=1 测试用，配好权限后要改成1=0*/
        <if test="isSelf ==true">
            or id in (select p.pipeline_id
            from tax_cloud_target.target_pipeline_sales_delivery_data p
            left join tax_cloud_target.target_user_attribute a on p.sales_delivery_credit = a.user_id
            ${queryWrapperForUser.customSqlSegment}  and p.sales_delivery_credit_type != 3  and a.status=1  and a.fiscal_year = '${fy}' and a.is_del=0
            and sales_delivery_credit = '${userId}'
            )
        </if>
        <if test="group !=null">
         or  id in (select p.pipeline_id
                from tax_cloud_target.target_pipeline_sales_delivery_data p
                left join tax_cloud_target.target_user_attribute a on p.sales_delivery_credit = a.user_id
                ${queryWrapperForUser.customSqlSegment}   and (p.won_fy='${fy}'  or p.pursuit_fy='${fy}' )  and a.status=1 and a.fiscal_year = p.won_fy and a.is_del=0
        <if test="group !=null">
            <foreach item="entry" index="key" collection="group" open="and ( " separator=" or" close=")">

            <foreach item="detail" index="key2" collection="entry" open="  ( " separator=" and" close=")">
                    <if test="detail.limitType==1">
                        <if test='detail.detailType=="1"'>
                            <if test="detail.detailValue==1">
                                a.sl1 = (select b.sl1 from tax_cloud_target.target_user_attribute b where b.user_id = '${userId}' and b.is_del=0 limit 1)
                            </if>
                            <if test="detail.detailValue==2">
                                a.ssl2 = (select b.ssl2 from tax_cloud_target.target_user_attribute b where b.user_id = '${userId}' and b.is_del=0 limit 1)
                            </if>
                            <if test="detail.detailValue==3">
                                a.ssl3 = (select b.ssl3 from tax_cloud_target.target_user_attribute b where b.user_id = '${userId}' and b.is_del=0 limit 1)
                            </if>
                        </if>
                        <if test='detail.detailType=="2"'>
                            <if test="detail.detailValue==1">
                                a.region = (select b.region from tax_cloud_target.target_user_attribute b where b.user_id = '${userId}'  and b.fiscal_year = '${fy}' and b.is_del=0 limit 1)
                            </if>
                            <if test="detail.detailValue==2">
                                a.city = (select b.city from tax_cloud_target.target_user_attribute b where b.user_id = '${userId}' and b.fiscal_year = '${fy}' and b.is_del=0 limit 1)
                            </if>
                        </if>
                        <if test='detail.detailType=="3"'>
                            <if test="detail.detailValue=='levelGroup'">
                                a.level_group = (select b.level_group from tax_cloud_target.target_user_attribute b where b.user_id = '${userId}' and b.fiscal_year = '${fy}' and b.is_del=0 limit 1)
                            </if>
                            <if test="detail.detailValue=='level'">
                                a.level = (select b.level from tax_cloud_target.target_user_attribute b where b.user_id = '${userId}' and b.fiscal_year = '${fy}' and b.is_del=0 limit 1)
                            </if>

                        </if>
                        <if test='detail.detailType=="userTag"'>
                            a.user_tag = (select b.user_tag from tax_cloud_target.target_user_attribute b where b.user_id = '${userId}' and b.fiscal_year = '${fy}' and b.is_del=0 limit 1)
                        </if>
                    </if>
                    <if test="detail.limitType==2">
                        <if test='detail.detailType=="1" or detail.detailType=="2" or detail.detailType=="3" or detail.detailType=="userTag" '>
                            <foreach item="detailValue" index="key3" collection="detail.detailValues" open="  ( " separator=" or" close=")">
                                <if test='detail.detailType=="1"'>
                                    a.ssl3 = '${detailValue}'
                                </if>
                                <if test='detail.detailType=="2"'>
                                    a.city = '${detailValue}'
                                </if>
                                <if test='detail.detailType=="3"'>
                                    a.level = '${detailValue}'
                                </if>
                             <if test='detail.detailType=="userTag"'>
                                    a.user_tag = '${detailValue}'
                                </if>
                            </foreach>
                        </if>
                        <if test='detail.detailType=="4" or detail.detailType=="5" or detail.detailType=="6" or detail.detailType=="7"'>
                            <foreach item="detailValue" index="key3" collection="detail.detailValues" open="  ( " separator=" or" close=")">
                                <if test='detail.detailType=="4"'> /*Client HQ*/
                                    p.head_office_location = '${detailValue}'
                                </if>
                                <if test='detail.detailType=="5"'>
                                    /* Client Type */
                                    jsonb_exists(p.company_type, #{detailValue})
                                </if>
                                <if test='detail.detailType=="6"'> /*Sector*/
                                    p.industry_sector = '${detailValue}'
                                </if>
                                <if test='detail.detailType=="7"'> /*Client client_channel*/
                                    p.client_channel = '${detailValue}'
                                </if>
                            </foreach>
                        </if>
                    </if>
                </foreach>
            </foreach>

        </if>
                )
        </if>

        )   ) _t      left join target_pipeline_sales_delivery d on _t.id = d.pipeline_id  and d.sales_delivery_credit_type in (3,8) and d.is_del=0
        left join  target_user_attribute a on d.sales_delivery_credit = a.user_id  and a.fiscal_year = _t.won_fy and a.is_del=0
        where d.id is not null and   a.user_tag = 'TPC Advisor' and a.buddy_partner_email = (select user_email from target_user_attribute b where b.user_id = '${userId}' and b.is_del=0 limit 1)
         ${order.customSqlSegment}
    </select>
    <select id="selectListForTPCVK" resultMap="EyTaxTPCResultMap">
        SELECT _t.*,d.sales_delivery_credit AS adviceName ,d.sales_delivery_credit_amount as tpcMemoevnue,d.sales_delivery_credit_amount as tpc_memoevnue,d.sales_delivery_credit_ratio as scales
        FROM (select * from  tax_cloud_target.target_pipeline __t  ${ew.customSqlSegment} and ( 1=0 /* 1=1 测试用，配好权限后要改成1=0*/
        <if test="isSelf ==true">
            or id in (select p.pipeline_id
            from tax_cloud_target.target_pipeline_sales_delivery_data p
            left join tax_cloud_target.target_user_attribute a on p.sales_delivery_credit = a.user_id
            ${queryWrapperForUser.customSqlSegment}  and p.sales_delivery_credit_type != 3  and a.status=1  and a.fiscal_year = '${fy}' and a.is_del=0
            and sales_delivery_credit = '${userId}'
            )
        </if>
        <if test="group !=null">
         or  id in (select p.pipeline_id
                from tax_cloud_target.target_pipeline_sales_delivery_data p
                left join tax_cloud_target.target_user_attribute a on p.sales_delivery_credit = a.user_id
                ${queryWrapperForUser.customSqlSegment}  and (p.won_fy='${fy}'  or p.pursuit_fy='${fy}' )  and a.status=1 and a.fiscal_year = p.won_fy and a.is_del=0
        <if test="group !=null">
            <foreach item="entry" index="key" collection="group" open="and ( " separator=" or" close=")">

                <foreach item="detail" index="key2" collection="entry" open="  ( " separator=" and" close=")">
                    <if test="detail.limitType==1">
                        <if test='detail.detailType=="1"'>
                            <if test="detail.detailValue==1">
                                a.sl1 = (select b.sl1 from tax_cloud_target.target_user_attribute b where b.user_id = '${userId}' and b.fiscal_year = '${fy}' and b.is_del=0 limit 1)
                            </if>
                            <if test="detail.detailValue==2">
                                a.ssl2 = (select b.ssl2 from tax_cloud_target.target_user_attribute b where b.user_id = '${userId}' and b.fiscal_year = '${fy}' and b.is_del=0 limit 1)
                            </if>
                            <if test="detail.detailValue==3">
                                a.ssl3 = (select b.ssl3 from tax_cloud_target.target_user_attribute b where b.user_id = '${userId}' and b.fiscal_year = '${fy}' and b.is_del=0 limit 1)
                            </if>
                            <if test='detail.detailValue=="userTag"'>
                                a.user_tag = (select b.user_tag from tax_cloud_target.target_user_attribute b where b.user_id = '${userId}' and b.fiscal_year = '${fy}' and b.is_del=0 limit 1)
                            </if>
                        </if>
                        <if test='detail.detailType=="2"'>
                            <if test="detail.detailValue==1">
                                a.region = (select b.region from tax_cloud_target.target_user_attribute b where b.user_id = '${userId}' and b.fiscal_year = '${fy}' and b.is_del=0 limit 1)
                            </if>
                            <if test="detail.detailValue==2">
                                a.city = (select b.city from tax_cloud_target.target_user_attribute b where b.user_id = '${userId}' and b.fiscal_year = '${fy}' and b.is_del=0 limit 1)
                            </if>
                        </if>
                        <if test='detail.detailType=="3"'>
                            <if test="detail.detailValue=='levelGroup'">
                                a.level_group = (select b.level_group from tax_cloud_target.target_user_attribute b where b.user_id = '${userId}' and b.fiscal_year = '${fy}' and b.is_del=0 limit 1)
                            </if>
                            <if test="detail.detailValue=='level'">
                                a.level = (select b.level from tax_cloud_target.target_user_attribute b where b.user_id = '${userId}' and b.fiscal_year = '${fy}' and b.is_del=0 limit 1)
                            </if>
                        </if>
                    </if>
                    <if test="detail.limitType==2">
                        <if test='detail.detailType=="1" or detail.detailType=="2" or detail.detailType=="3" or detail.detailType=="userTag" '>
                            <foreach item="detailValue" index="key3" collection="detail.detailValues" open="  ( " separator=" or" close=")">
                                <if test='detail.detailType=="1"'>
                                    a.ssl3 = '${detailValue}'
                                </if>
                                <if test='detail.detailType=="2"'>
                                    a.city = '${detailValue}'
                                </if>
                                <if test='detail.detailType=="3"'>
                                    a.level = '${detailValue}'
                                </if>
                                <if test='detail.detailType=="userTag"'>
                                    a.user_tag = '${detailValue}'
                                </if>
                            </foreach>
                        </if>
                        <if test='detail.detailType=="4" or detail.detailType=="5" or detail.detailType=="6" or detail.detailType=="7"'>
                            <foreach item="detailValue" index="key3" collection="detail.detailValues" open="  ( " separator=" or" close=")">
                                <if test='detail.detailType=="4"'> /*Client HQ*/
                                    p.head_office_location = '${detailValue}'
                                </if>
                                <if test='detail.detailType=="5"'>
                                    /* Client Type */
                                    jsonb_exists(p.company_type, #{detailValue})
                                    /*else*/
                                    /*jsonb_exists(p.company_type, #{detailValue})*/
                                </if>

                                <if test='detail.detailType=="6"'> /*Sector*/
                                    p.industry_sector = '${detailValue}'
                                </if>
                                <if test='detail.detailType=="7"'> /*Client client_channel*/
                                    p.client_channel = '${detailValue}'
                                </if>
                            </foreach>
                        </if>
                    </if>
                </foreach>
            </foreach>

        </if>
                )
        </if>

        )   ) _t      left join target_pipeline_sales_delivery d on _t.id = d.pipeline_id  and d.sales_delivery_credit_type in (3,8) and d.is_del=0
        left join  target_user_attribute a on d.sales_delivery_credit = a.user_id  and a.fiscal_year = _t.won_fy and a.is_del=0
        where d.id is not null and a.buddy_partner_email is not null and a.user_tag = 'TPC Advisor'
         ${order.customSqlSegment}
    </select>

    <select id="selectUsers" >
        select user_id from tax_cloud_target.target_user_attribute a where a.is_del=0 and a.fiscal_year = '${fy}'
        <foreach item="entry" index="key" collection="group" open="and ( " separator=" or" close=")">

        <foreach item="detail" index="key2" collection="entry" open="  ( " separator=" and" close=")">
            <if test="detail.limitType==1">
                <if test='detail.detailType=="1"'>
                    <if test="detail.detailValue==1">
                        a.sl1 = (select b.sl1 from tax_cloud_target.target_user_attribute b where b.user_id = '${userId}' and b.fiscal_year = '${fy}' and b.is_del=0 limit 1)
                    </if>
                    <if test="detail.detailValue==2">
                        a.ssl2 = (select b.ssl2 from tax_cloud_target.target_user_attribute b where b.user_id = '${userId}' and b.fiscal_year = '${fy}' and b.is_del=0 limit 1)
                    </if>
                    <if test="detail.detailValue==3">
                        a.ssl3 = (select b.ssl3 from tax_cloud_target.target_user_attribute b where b.user_id = '${userId}' and b.fiscal_year = '${fy}'  and b.is_del=0 limit 1)
                    </if>
                </if>
                <if test='detail.detailType=="2"'>
                    <if test="detail.detailValue==1">
                        a.region = (select b.region from tax_cloud_target.target_user_attribute b where b.user_id = '${userId}' and b.fiscal_year = '${fy}'  and b.is_del=0 limit 1)
                    </if>
                    <if test="detail.detailValue==2">
                        a.city = (select b.city from tax_cloud_target.target_user_attribute b where b.user_id = '${userId}' and b.fiscal_year = '${fy}' and b.is_del=0 limit 1)
                    </if>
                </if>
                <if test='detail.detailType=="3"'>
                    <if test="detail.detailValue=='levelGroup'">
                        a.level_group = (select b.level_group from tax_cloud_target.target_user_attribute b where b.user_id = '${userId}' and b.fiscal_year = '${fy}' and b.is_del=0 limit 1)
                    </if>
                    <if test="detail.detailValue=='level'">
                        a.level = (select b.level from tax_cloud_target.target_user_attribute b where b.user_id = '${userId}' and b.fiscal_year = '${fy}' and b.is_del=0 limit 1)
                    </if>
                </if>
                <if test='detail.detailValue=="userTag"'>
                    a.user_tag = (select b.user_tag from tax_cloud_target.target_user_attribute b where b.user_id = '${userId}' and b.fiscal_year = '${fy}' and b.is_del=0 limit 1)
                </if>
            </if>
            <if test="detail.limitType==2">
                <if test='detail.detailType=="1" or detail.detailType=="2" or detail.detailType=="3" or detail.detailType=="userTag" '>
                    <foreach item="detailValue" index="key3" collection="detail.detailValues" open="  ( " separator=" or" close=")">
                        <if test='detail.detailType=="1"'>
                            a.ssl3 = '${detailValue}'
                        </if>
                        <if test='detail.detailType=="2"'>
                            a.city = '${detailValue}'
                        </if>
                        <if test='detail.detailType=="3"'>
                            a.level = '${detailValue}'
                        </if>
                        <if test='detail.detailType=="userTag"'>
                            a.user_tag = '${detailValue}'
                        </if>
                    </foreach>
                </if>
            </if>
        </foreach>
        </foreach>

    </select>



</mapper>
