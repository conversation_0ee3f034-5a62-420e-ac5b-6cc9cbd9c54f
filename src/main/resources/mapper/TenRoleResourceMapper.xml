<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ey.tax.cloud.base.mapper.resource.TenRoleResourceMapper" >

<!--    <select id="selectRoleResource" resultType="com.ey.tax.cloud.base.dto.resource.TenRoleResourceDTO">-->
<!--        select-->
<!--            br.resource_name ,-->
<!--            br.sys_code ,-->
<!--            br.app_code ,-->
<!--            br.resource_code,-->
<!--            br.id as resource_id,-->
<!--            br.parent_id,-->
<!--            brr.role_id-->
<!--        from-->
<!--            base_resource br-->
<!--            left join base_role_resource brr on-->
<!--            br.id = brr.resource_id and brr.is_del =0-->
<!--        where-->
<!--        br.is_del = 0-->
<!--            <if test="resourceType != null ">-->
<!--                and br.resource_type = ${resourceType}-->
<!--            </if>-->
<!--            <if test="sysCode != null and sysCode != ''">-->
<!--                and br.sys_code = #{sysCode}-->
<!--            </if>-->
<!--            <if test="appCode != null and appCode != ''">-->
<!--                and br.app_code =#{appCode}-->
<!--            </if>-->

<!--    </select>-->

    <select id="selectRoleButtonResource" resultType="com.ey.tax.cloud.base.dto.resource.TenRoleResourceDTO">
        select
            br2.sys_code ,
            br2.app_code ,
            br2.resource_name ,
            br2.id as resource_id,
            br.role_name ,
            br.id as role_id
        from
            base_role_resource brr
            inner join base_role br on
            brr.role_id = br.id
            inner join base_resource br2 on
            brr.resource_id = br2.id
        where
            brr.is_del = 0
            and br.is_del = 0
            and br2.is_del = 0
            and br2.resource_type = 2
            <if test="sysCode != null and sysCode != ''">
                and br.sys_code = #{sysCode}
                and br2.sys_code = #{sysCode}
            </if>
            <if test="appCode != null and appCode != ''">
                and br.app_code =#{appCode}
            </if>

    </select>

</mapper>
