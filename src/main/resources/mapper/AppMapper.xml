<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ey.tax.cloud.base.mapper.app.AppMapper" >

    <select id="selectPages" resultType="com.ey.tax.cloud.base.dto.app.AppRepDTO">
        select
            bs.sys_name ,
            ba.*
        from
            base_app ba
                left join base_system bs on
                ba.sys_code = bs.sys_code
        where
            bs.is_del = 0
          and ba.is_del = 0
          <if test="queryParams.sysCode != null ">
              and ba.sys_code = #{queryParams.sysCode}
          </if>
        <if test="queryParams.appCode != null ">
            and ba.app_code ilike &apos;%&apos;||#{queryParams.appCode}||&apos;%&apos;
        </if>
        <if test="queryParams.appName != null ">
            and ba.app_name ilike &apos;%&apos;||#{queryParams.appName}||&apos;%&apos;
        </if>

    </select>


    <select id="selectUserIdsByParams" resultType="string" parameterType="list">
        select
            distinct user_id
        from
            base_app_user_group
        where
            is_del = 0
          and group_id in
              <foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
                #{item}
              </foreach>
    </select>

</mapper>
