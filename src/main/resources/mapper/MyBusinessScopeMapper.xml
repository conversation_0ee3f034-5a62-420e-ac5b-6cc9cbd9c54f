<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ey.tax.cloud.targets.mapper.business.MyBusinessScopeMapper" >

  <resultMap id="EyTaxResultMap" type="com.ey.tax.cloud.targets.entity.business.MyBusinessScope" >
    <id column="id" property="id" jdbcType="VARCHAR" />
    <result column="create_uid" property="createUid" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_uid" property="updateUid" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="is_del" property="isDel" jdbcType="SMALLINT" />
    <result column="auto_test" property="autoTest" jdbcType="SMALLINT" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="fiscal_year" property="fiscalYear" jdbcType="VARCHAR" />
    <result column="fiscal_week" property="fiscalWeek" jdbcType="VARCHAR" />
    <result column="pipeline_name" property="pipelineName" jdbcType="VARCHAR" />
    <result column="pipeline_code" property="pipelineCode" jdbcType="VARCHAR" />
    <result column="pace" property="pace" jdbcType="INTEGER" />
    <result column="billing" property="billing" jdbcType="INTEGER" />
    <result column="billing_total_budget" property="billingTotalBudget" jdbcType="NUMERIC" />
    <result column="billing_billed" property="billingBilled" jdbcType="NUMERIC" />
    <result column="billing_unbilled" property="billingUnbilled" jdbcType="NUMERIC" />
    <result column="collection" property="collection" jdbcType="INTEGER" />
    <result column="uncollected" property="uncollected" jdbcType="NUMERIC" />
    <result column="completed" property="completed" jdbcType="INTEGER" />
    <result column="percentage" property="percentage" jdbcType="NUMERIC" />
    <result column="allocated_value" property="allocatedValue" jdbcType="NUMERIC" />
    <result column="ter" property="ter" jdbcType="NUMERIC" />
    <result column="ner" property="ner" jdbcType="NUMERIC" />
    <result column="revenue_day" property="revenueDay" jdbcType="NUMERIC" />
    <result column="wip" property="wip" jdbcType="NUMERIC" />
    <result column="billing_amount" property="billingAmount" jdbcType="NUMERIC" />
    <result column="collection_amount" property="collectionAmount" jdbcType="NUMERIC" />
    <result column="ar" property="ar" jdbcType="NUMERIC" />
    <result column="user_id" property="userId" jdbcType="VARCHAR" />
    <result column="tag" property="tag" jdbcType="VARCHAR" />
    <result column="level" property="level" jdbcType="VARCHAR" />
    <result column="ssl3" property="ssl3" jdbcType="VARCHAR" />
    <result column="city" property="city" jdbcType="VARCHAR" />
    <result column="region" property="region" jdbcType="VARCHAR" />
    <result column="ssl2" property="ssl2" jdbcType="VARCHAR" />
    <result column="sl1" property="sl1" jdbcType="VARCHAR" />
    <result column="level_group" property="levelGroup" jdbcType="VARCHAR" />
    <result column="group_id" property="groupId" jdbcType="VARCHAR" />
  </resultMap>

  <select id="selectListForVK" resultMap="EyTaxResultMap">
    select s.* from target_my_business_scope s left join  (select  id , oppr_partner_id from target_pipeline where is_del=0) p on s.pipeline_id = p.id
      ${ew.customSqlSegment} and s.is_del=0 and s.user_id = p.oppr_partner_id
    order by s.create_time desc
  </select>


  <select id="findBusinessUnionAll" resultType="java.util.Map" parameterType="com.ey.tax.cloud.targets.entity.business.MyBusinessScope">
      select coalesce(sum(sum_e_ner),0) as ner, coalesce(sum(sum_e_ter),0) as ter, coalesce(sum(zero_col),0) as amount, coalesce(sum(sum_e_net_unbld_inv),0) as wip, coalesce(sum(sum_e_tot_ar),0) as ar from (
    <!-- 第一部分 SELECT -->
    SELECT
        <!-- tag=etd 时 使用e_ner, e_ter, e_net_unbld_inv, e_tot_ar 、tag=ytd 时 使用f_ner, f_ter, f_net_unbld_inv, f_tot_ar -->
        SUM(
    <choose>
      <when test="tag == 'etd'">
        e_ner
      </when>
      <otherwise>
        f_ner
      </otherwise>
    </choose>
    ) AS sum_e_ner,
    SUM(
    <choose>
      <when test="tag == 'etd'">
        e_ter
      </when>
      <otherwise>
        f_ter
      </otherwise>
    </choose>
    ) AS sum_e_ter,
    sum(contract_amount) AS zero_col,
    SUM(
    <choose>
      <when test="tag == 'etd'">
        e_net_unbld_inv
      </when>
      <otherwise>
        f_net_unbld_inv
      </otherwise>
    </choose>
    ) AS sum_e_net_unbld_inv,
    SUM(
    <choose>
      <when test="tag == 'etd'">
        e_tot_ar+e_ar_tax
      </when>
      <otherwise>
        f_tot_ar+f_ar_tax
      </otherwise>
    </choose>
    ) AS sum_e_tot_ar

    FROM tax_cloud_target.target_my_business
    <where>
     pipeline_id is null  and is_del=0
      <!--  fiscal_year IN (...) -->
      <if test="fiscalYearList != null and fiscalYearList.size() > 0">
        AND fiscal_year IN
        <foreach collection="fiscalYearList" item="fy" open="(" separator="," close=")">
          #{fy}
        </foreach>
      </if>

      <!-- engagement_code LIKE -->
      <if test="engCodeLike != null and engCodeLike != ''">
        AND engagement_code ILIKE CONCAT('%', #{engCodeLike}, '%')
      </if>

        <!-- statusList engagement_status-->
        <if test="statusList != null and statusList.size() > 0">
            AND engagement_status IN
            <foreach collection="statusList" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>

        <!-- engagementName -->

        <if test="engagementName != null and engagementName != ''">
            AND engagement_name ILIKE CONCAT('%', #{engagementName}, '%')
        </if>

      <!-- (engagement_partner_id = userId OR engagement_manager_id = userId) -->
      <if test="userId != null">
        AND (
        engagement_partner_id = #{userId}
        OR engagement_manager_id = #{userId}
        )
      </if>
      <!--  fiscalYear -->
        <if test="fiscalYear != null and fiscalYear != ''">
            AND fiscal_year = #{fiscalYear}
        </if>
      <!--  pipelineName 查询为空-->
        <if test="pipelineName != null and pipelineName != ''">
            AND 1=0
        </if>
        <!--clientName 查询为空-->
        <if test="clientName != null and clientName != ''">
            AND 1=0
        </if>

    </where>

    UNION ALL

    <!-- 第二部分 SELECT -->
    SELECT
    SUM(ner)           AS sum_e_ner,
    SUM(ter)           AS sum_e_ter,
    SUM(p.amount)      AS zero_col,
    SUM(wip)           AS sum_e_net_unbld_inv,
    SUM(ar)            AS sum_e_tot_ar
    FROM
        <!--type = scope时使用target_my_business_scope表，否则 使用target_my_business_self表-->
        <choose>
            <when test="type == 'scope'">
                tax_cloud_target.target_my_business_scope s
            </when>
            <otherwise>
                tax_cloud_target.target_my_business_self s
            </otherwise>
        </choose>

    LEFT JOIN tax_cloud_target.target_pipeline p
    ON s.pipeline_id = p.id
    <where>
        1=1  and s.is_del=0 and p.is_del=0
        and fiscal_week = (select max(fiscal_week) from tax_cloud_target.target_my_business_scope where is_del=0 and fiscal_year = #{fiscalYear})

      <!-- tag -->
        <if test="tag != null and tag != ''">
            AND s.tag = #{tag}
        </if>
      <!-- user_id = #{userId} -->
      <if test="userId != null">
        AND user_id = #{userId}
      </if>
      <if test="fiscalYearList != null and fiscalYearList.size() > 0">
        AND fiscal_year IN
        <foreach collection="fiscalYearList" item="fy" open="(" separator="," close=")">
          #{fy}
        </foreach>
      </if>
    <!--  pipelineName -->
        <if test="pipelineName != null and pipelineName != ''">
            AND p.pipeline_name ILIKE CONCAT('%', #{pipelineName}, '%')
        </if>
    <!--clientName -->
        <if test="clientName != null and clientName != ''">
            AND p.client_name ILIKE CONCAT('%', #{clientName}, '%')
        </if>
    <!--  fiscalYear -->
        <if test="fiscalYear != null and fiscalYear != ''">
            AND s.fiscal_year = #{fiscalYear}
        </if>
    </where>
    ) t

  </select>
</mapper>
