<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.ey.tax.cloud</groupId>
	<artifactId>ey-tax-cloud-target</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<name>ey-tax-cloud-target</name>
	<description>Ey Tax Cloud Target Project</description>

	<parent>
		<groupId>com.ey.cn.tax</groupId>
		<artifactId>ey-tax-framework</artifactId>
		<version>1.4.0-RELEASE</version>
	</parent>

	<properties>
		<revision>0.0.1-SNAPSHOT</revision>
		<package-name>ey-tax-cloud-target</package-name>
		<start-class>com.ey.tax.cloud.targets.EyTaxCloudTargetApplication</start-class>
	</properties>
	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.ey.cn.tax.cloud</groupId>
				<artifactId>ey-tax-cloud</artifactId>
				<version>1.3.0-RELEASE</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>
	<dependencies>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-test</artifactId>
		</dependency>

		<dependency>
			<groupId>com.ey.cn.tax</groupId>
			<artifactId>ey-tax-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>com.ey.cn.tax</groupId>
			<artifactId>ey-tax-starter-mybatisplus</artifactId>
		</dependency>
		<dependency>
			<groupId>org.xerial</groupId>
			<artifactId>sqlite-jdbc</artifactId>
			<version>3.43.0.0</version>  <!-- 推荐使用最新稳定版 -->
		</dependency>
		<dependency>
			<groupId>p6spy</groupId>
			<artifactId>p6spy</artifactId>
		</dependency>
		<dependency>
			<groupId>org.postgresql</groupId>
			<artifactId>postgresql</artifactId>
		</dependency>
		<dependency>
			<groupId>com.ey.cn.tax.cloud</groupId>
			<artifactId>ey-tax-cloud-starter-commons</artifactId>
		</dependency>
		<dependency>
			<groupId>com.ey.cn.tax</groupId>
			<artifactId>ey-tax-starter-k8s</artifactId>
		</dependency>
		<dependency>
			<groupId>com.ey.cn.tax</groupId>
			<artifactId>ey-tax-starter-gcexcel</artifactId>
		</dependency>
		<dependency>
			<groupId>com.ey.cn.tax</groupId>
			<artifactId>ey-tax-starter-lite-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
		</dependency>
        <dependency>
            <groupId>com.ey.cn.tax</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>

		<dependency>
			<groupId>com.grapecity.documents</groupId>
			<artifactId>gcexcel</artifactId>
			<version>7.0.5</version>
		</dependency>
        <dependency>
            <groupId>com.ey.cn.tax</groupId>
            <artifactId>ey-tax-core</artifactId>
        </dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-source-plugin</artifactId>
			</plugin>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
		</plugins>
	</build>
</project>
