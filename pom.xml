<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.ey.tax.cloud</groupId>
    <artifactId>ey-tax-cloud-base</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>ey-tax-cloud-base</name>
    <description>Ey Tax Cloud Base Project</description>

    <parent>
        <groupId>com.ey.cn.tax</groupId>
        <artifactId>ey-tax-framework</artifactId>
        <version>1.5.0-SNAPSHOT</version>
    </parent>

    <properties>
        <revision>0.0.1-SNAPSHOT</revision>
        <package-name>ey-tax-cloud-base</package-name>
        <start-class>com.ey.tax.cloud.base.EyTaxCloudBaseApplication</start-class>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.ey.cn.tax.cloud</groupId>
                <artifactId>ey-tax-cloud</artifactId>
                <version>1.3.0-RELEASE</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>com.ey.cn.tax</groupId>
            <artifactId>ey-tax-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>alibabacloud-devops20210625</artifactId>
            <version>5.0.2</version>
        </dependency>
        <dependency>
            <groupId>com.ey.cn.tax</groupId>
            <artifactId>ey-tax-starter-mybatisplus</artifactId>
        </dependency>
        <dependency>
            <groupId>p6spy</groupId>
            <artifactId>p6spy</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ey.cn.tax</groupId>
            <artifactId>ey-tax-starter-lite-admin</artifactId>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ey.cn.tax.cloud</groupId>
            <artifactId>ey-tax-cloud-starter-commons</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ey.cn.tax</groupId>
            <artifactId>ey-tax-starter-k8s</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ey.cn.tax</groupId>
            <artifactId>ey-tax-starter-alibaba-dingtalk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ey.cn.tax</groupId>
            <artifactId>ey-tax-starter-rabbit</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ey.cn.tax</groupId>
            <artifactId>ey-tax-starter-aspose</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ey.cn.tax</groupId>
            <artifactId>ey-tax-starter-gc-plus</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ey.cn.tax.cloud</groupId>
            <artifactId>ey-tax-app</artifactId>
            <version>1.2.0-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>2.5.1</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
